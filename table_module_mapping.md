# Database Tables Mapped to Modules and Modules-QA with Relationships

## Overview
This document maps database tables to their corresponding modules in the codebase and shows the relationships between tables.

### Key Relationship Patterns
- **Core Entities**: `vessel`, `company`, `user`, `port_master`, `country`
- **Common Foreign Keys**: Most tables reference `companyId`, `createdUserId`, `updatedUserId`
- **Junction Tables**: Many tables with underscores create many-to-many relationships
- **Audit Trail**: Standard fields: `id`, `deleted`, `createdAt`, `updatedAt`

---

## Tables Mapped to `src/modules/` Directory

### analytical-report
**Tables:**
- `analytical_report_adjusted`
- `analytical_report_config`
- `analytical_report_repeated_finding`

**Key Relationships:**
- `analytical_report_config` → `company` (companyId)
- `analytical_report_repeated_finding` → `vessel`, `company`

### app-type-property
**Tables:**
- `app_type_property`

**Key Relationships:**
- `app_type_property` → `company` (companyId)

### attachment-kit
**Tables:**
- `attachment_kit`

**Key Relationships:**
- `attachment_kit` → `company` (companyId)
- `attachment_kit` → `user` (createdUserId, updatedUserId)

### audit-checklist
**Tables:**
- `audit_checklist` ⭐ (Primary)
- `audit_checklist_history`
- `audit_checklist_master_table`
- `chk_question`
- `chk_question_answer`
- `chk_question_master_table`
- `fill_audit_checklist`
- `fill_audit_checklist_question`

**Key Relationships:**
- `audit_checklist` → `company` (companyId)
- `audit_checklist` → `user` (createdUserId, updatedUserId)
- `fill_audit_checklist` → `audit_checklist`, `vessel`, `planning_request`
- `chk_question` → `audit_checklist`
- `chk_question_answer` → `chk_question`

### audit-log
**Tables:**
- `audit_log`

**Key Relationships:**
- `audit_log` → `company` (companyId)
- `audit_log` → `user` (createdUserId)

### audit-time-table
**Tables:**
- `audit_time_table`
- `audit_time_table_calendar`
- `audit_time_table_history`

**Key Relationships:**
- `audit_time_table` → `company` (companyId)
- `audit_time_table` → `user` (createdUserId, updatedUserId)

### audit-type
**Tables:**
- `audit_type` ⭐ (Primary)
- `authority_audit_type`
- `iar_audit_type`
- `internal_inspection_audit_type`
- `report_template_audit_type`
- `rof_audit_type`

**Key Relationships:**
- `audit_type` → `company` (companyId)
- `authority_audit_type` → `authority_master`, `audit_type`
- `iar_audit_type` → `internal_audit_report`, `audit_type`

### audit-workspace
**Tables:**
- `audit_workspace`

**Key Relationships:**
- `audit_workspace` → `company` (companyId)
- `audit_workspace` → `user` (createdUserId, updatedUserId)

### authority-master
**Tables:**
- `authority_master` ⭐ (Primary)
- `authority_event_type`
- `inspection_mapping_authority_master`

**Key Relationships:**
- `authority_master` → `company` (companyId)
- `authority_event_type` → `authority_master`, `event_type`
- `inspection_mapping_authority_master` → `inspection_mapping`, `authority_master`

### available-area
**Tables:**
- `available_area`
- `available_areas_ports`

**Key Relationships:**
- `available_area` → `company` (companyId)
- `available_areas_ports` → `available_area`, `port_master`

### category
**Tables:**
- `category` ⭐ (Primary)
- `category_mapping`
- `main_category`
- `second_category`
- `third_category`

**Key Relationships:**
- `category` → `company` (companyId)
- `category_mapping` → `category`
- `main_category` → `company` (companyId) - Standalone category table
- `second_category` → `company` (companyId) - Standalone category table
- `third_category` → `company` (companyId) - Standalone category table

### cause-analysis-master
**Tables:**
- `cause_analysis_master`
- `cause_main_category`
- `cause_sub_category`

**Key Relationships:**
- `cause_analysis_master` → `company` (companyId)
- `cause_main_category` → `cause_analysis_master`
- `cause_sub_category` → `cause_main_category`

### cause-mapping
**Tables:**
- `cause_mapping`
- `cause_one_mapping`
- `cause_two_mapping`

**Key Relationships:**
- `cause_mapping` → `company` (companyId)
- `cause_one_mapping` → `cause_mapping`
- `cause_two_mapping` → `cause_one_mapping`

### cdi
**Tables:**
- `cdi`

**Key Relationships:**
- `cdi` → `company` (companyId)

### charter-owner
**Tables:**
- `charter_owner`

**Key Relationships:**
- `charter_owner` → `company` (companyId)

### company
**Tables:**
- `company` ⭐ (Primary - Core Entity)
- `company_company_type`
- `company_feature_version`

**Key Relationships:**
- `company` → `group` (groupId)
- `company` → `country` (countryId)
- `company` → `company` (parentId - Self-referencing)
- `company_company_type` → `company`, `company_type`
- `company_feature_version` → `company`

### company-configuration
**Tables:**
- `company_configuration`
- `common_configuration`

**Key Relationships:**
- `company_configuration` → `company` (companyId)
- `common_configuration` → `company` (companyId)

### company-type
**Tables:**
- `company_type`

**Key Relationships:**
- `company_type` → `company` (companyId)

### corrective-action-request
**Tables:**
- `corrective_action_request`
- `corrective_action_plan`

**Key Relationships:**
- `corrective_action_request` → `company` (companyId)
- `corrective_action_request` → `vessel` (vesselId)
- `corrective_action_plan` → `corrective_action_request`

### country
**Tables:**
- `country` ⭐ (Primary - Core Entity)

**Key Relationships:**
- Referenced by: `company`, `user`, `vessel`, `port_master`

### department-master
**Tables:**
- `department` ⭐ (Primary)
- `department_rank`
- `iar_department`
- `report_finding_department`
- `report_finding_item_department`
- `ship_department`
- `shore_department`
- `user_department`

**Key Relationships:**
- `department` → `company` (companyId)
- `department_rank` → `department`, `rank`
- `user_department` → `user`, `department`
- `ship_department` → `department`
- `shore_department` → `department`

### device-control
**Tables:**
- `device_control`

**Key Relationships:**
- `device_control` → `company` (companyId)

### division
**Tables:**
- `division`
- `division_user`

**Key Relationships:**
- `division` → `company` (companyId)
- `division_user` → `division`, `user`

### division-mapping
**Tables:**
- `division_mapping`

**Key Relationships:**
- `division_mapping` → `division`

### dms
**Tables:**
- `dms`

**Key Relationships:**
- `dms` → `company` (companyId)

### experiences
**Tables:**
- `experience`

**Key Relationships:**
- `experience` → `company` (companyId)

### feedback
**Tables:**
- `feedback`

**Key Relationships:**
- `feedback` → `company` (companyId)

### fleet
**Tables:**
- `fleet`

**Key Relationships:**
- `fleet` → `company` (companyId)

### focus-request
**Tables:**
- `focus_request` ⭐ (Primary)
- `focus_request_vessel_type`
- `pr_focus_request`

**Key Relationships:**
- `focus_request` → `company` (companyId)
- `focus_request_vessel_type` → `focus_request` (focusRequestId), `vessel_type` (vesselTypeId) - Junction table
- `pr_focus_request` → `planning_request`, `focus_request`

### group
**Tables:**
- `group`

**Key Relationships:**
- `group` → `company` (companyId)
- Referenced by: `company` (groupId)

### homepage
**Tables:**
- `homepage_remark`

**Key Relationships:**
- `homepage_remark` → `company` (companyId)

### inspection-mapping
**Tables:**
- `inspection_mapping` ⭐ (Primary)
- `inspection_mapping_audit_checklist`
- `inspection_mapping_authority_master`
- `inspection_mapping_history`
- `inspection_mapping_standard_master`
- `insp_map_nat_finding`

**Key Relationships:**
- `inspection_mapping` → `company` (companyId)
- `inspection_mapping_audit_checklist` → `inspection_mapping`, `audit_checklist`
- `inspection_mapping_authority_master` → `inspection_mapping`, `authority_master`
- `inspection_mapping_standard_master` → `inspection_mapping`, `standard_master`

### inspector-time-off
**Tables:**
- `inspector_time_off`

**Key Relationships:**
- `inspector_time_off` → `company` (companyId)
- `inspector_time_off` → `user` (inspectorId)

### internal-audit-report
**Tables:**
- `internal_audit_report` ⭐ (Primary)
- `internal_audit_report_comment`
- `internal_audit_report_history`
- `internal_audit_report_office_comment`

**Key Relationships:**
- `internal_audit_report` → `company` (companyId)
- `internal_audit_report` → `user` (createdUserId, updatedUserId)
- `internal_audit_report_comment` → `internal_audit_report`

### license-certification
**Tables:**
- `license_certification`

**Key Relationships:**
- `license_certification` → `company` (companyId)

### location
**Tables:**
- `location`

**Key Relationships:**
- `location` → `country` (countryId)
- Referenced by: `planning_request` (locationId)

### mail-send
**Tables:**
- `mail_send`

**Key Relationships:**
- `mail_send` → `company` (companyId)

### mail-template
**Tables:**
- `mail_template` ⭐ (Primary)
- `mail_type`
- `mail_vessel_type`
- `mail_attachment`
- `mail_follow_up`
- `mail_inspection_report`
- `mail_planning`
- `mail_report`

**Key Relationships:**
- `mail_template` → `company` (companyId)
- `mail_vessel_type` → `mail_template`, `vessel_type`
- `mail_attachment` → `mail_template`
- `mail_planning` → `mail_template`, `planning_request`

### mobile-config
**Tables:**
- `mobile_config`

**Key Relationships:**
- `mobile_config` → `company` (companyId)

### nature_finding
**Tables:**
- `nature_finding`

**Key Relationships:**
- `nature_finding` → `company` (companyId)

### package
**Tables:**
- `package`

**Key Relationships:**
- `package` → `company` (companyId)

### planning-request
**Tables:**
- `planning_request` ⭐ (Primary)
- `planning_request_audit_checklist`
- `planning_request_audit_type`
- `planning_request_auditor`
- `planning_request_department`
- `planning_request_history`
- `iar_planning_request`
- `planing_drawing_request`
- `rof_planning_request`

**Key Relationships:**
- `planning_request` → `vessel` (vesselId)
- `planning_request` → `company` (companyId, auditCompanyId)
- `planning_request` → `port_master` (fromPortId, toPortId)
- `planning_request` → `user` (leadAuditorId, createdUserId, updatedUserId)
- `planning_request` → `department` (departmentId)
- `planning_request` → `voyage_type` (voyageTypeId)
- `planning_request_audit_checklist` → `planning_request`, `audit_checklist`
- `planning_request_audit_type` → `planning_request`, `audit_type`
- `planning_request_auditor` → `planning_request`, `user`

### pms
**Tables:**
- `planed_maintenance_system`

**Key Relationships:**
- `planed_maintenance_system` → `company` (companyId)

### port-master
**Tables:**
- `port_master` ⭐ (Primary - Core Entity)

**Key Relationships:**
- `port_master` → `company` (companyId)
- `port_master` → `country` (countryId)
- `port_master` → `user` (createdUserId, updatedUserId)
- Referenced by: `planning_request`, `incident_investigation`, `external_inspections`

### power-bi-config
**Tables:**
- `power_bi_config`

**Key Relationships:**
- `power_bi_config` → `company` (companyId)

### prefix
**Tables:**
- `prefix`

**Key Relationships:**
- `prefix` → `company` (companyId)

### priority-master
**Tables:**
- `priority_master`

**Key Relationships:**
- `priority_master` → `company` (companyId)

### provided-inspection
**Tables:**
- `provided_inspection`

**Key Relationships:**
- `provided_inspection` → `company` (companyId)

### provider-config
**Tables:**
- `provider_config`

**Key Relationships:**
- `provider_config` → `company` (companyId)

### psc-action-master
**Tables:**
- `psc_action_master`

**Key Relationships:**
- `psc_action_master` → `company` (companyId)

### psc-deficiency-master
**Tables:**
- `psc_deficiency_master`

**Key Relationships:**
- `psc_deficiency_master` → `company` (companyId)

### rank-master
**Tables:**
- `rank` ⭐ (Primary)
- `ship_rank`
- `ship_rank_departments`
- `shore_rank`
- `shore_rank_departments`

**Key Relationships:**
- `rank` → `company` (companyId)
- `ship_rank` → `rank`
- `shore_rank` → `rank`
- `ship_rank_departments` → `ship_rank`, `ship_department`
- `shore_rank_departments` → `shore_rank`, `shore_department`

### recover-password-request
**Tables:**
- `recover_password_request`

**Key Relationships:**
- `recover_password_request` → `user` (userId)

### repeated-finding
**Tables:**
- `repeated_finding`

**Key Relationships:**
- `repeated_finding` → `company` (companyId)

### report-finding
**Tables:**
- `report_finding_form`
- `report_finding_history`
- `report_finding_item`
- `report_finding_department`
- `report_finding_item_department`

**Key Relationships:**
- `report_finding_form` → `company` (companyId)
- `report_finding_item` → `report_finding_form`
- `report_finding_department` → `report_finding_form`, `department`
- `report_finding_item_department` → `report_finding_item`, `department`

### request-trial
**Tables:**
- `request_trial`

**Key Relationships:**
- `request_trial` → `company` (companyId)

### risk-matrix-master
**Tables:**
- `risk_matrix_master` ⭐ (Primary)
- `risk_matrix_cell`
- `risk_level_mapping`
- `risk_value_mapping`

**Key Relationships:**
- `risk_matrix_master` → `company` (companyId)
- `risk_matrix_cell` → `risk_matrix_master`
- `risk_level_mapping` → `risk_matrix_master`
- `risk_value_mapping` → `risk_matrix_master`

### sdr
**Tables:**
- `sdr`

**Key Relationships:**
- `sdr` → `company` (companyId)

### sms
**Tables:**
- `sms`

**Key Relationships:**
- `sms` → `company` (companyId)

### subscription-package
**Tables:**
- `subscription_package`
- `subscription`
- `subscription_usage`

**Key Relationships:**
- `subscription` → `company` (companyId)
- `subscription_usage` → `subscription`

### topic
**Tables:**
- `topic`

**Key Relationships:**
- `topic` → `company` (companyId)

### transfer-data
**Tables:**
- `transfer_data`
- `transfer_type`
- `transformed_data`

**Key Relationships:**
- `transfer_data` → `company` (companyId)
- `transfer_type` → `transfer_data`

### travel-document
**Tables:**
- `travel_document`

**Key Relationships:**
- `travel_document` → `company` (companyId)

### user
**Tables:**
- `user` ⭐ (Primary - Core Entity)
- `user_role`
- `user_role_permission`

**Key Relationships:**
- `user` → `company` (companyId, parentCompanyId)
- `user` → `rank` (rankId)
- `user` → `department` (primaryDepartmentId)
- `user` → `country` (countryId)
- `user_role` → `user`, `role`
- `user_role_permission` → `user_role`, `permission`

### user-assignment
**Tables:**
- `user_assignment`

**Key Relationships:**
- `user_assignment` → `user` (userId)
- `user_assignment` → `company` (companyId)

### value-change-history
**Tables:**
- `value_change_history`

**Key Relationships:**
- `value_change_history` → `company` (companyId)

### value-management
**Tables:**
- `value_management`

**Key Relationships:**
- `value_management` → `company` (companyId)

### vessel
**Tables:**
- `vessel` ⭐ (Primary - Core Entity)
- `vessel_blacklist_mou`
- `vessel_charterer`
- `vessel_company_feedback`
- `vessel_company_feedback_categorization_master`
- `vessel_company_feedback_history`
- `vessel_doc_holder`
- `vessel_general_history`
- `vessel_kiseki_test`
- `vessel_log_sync_data`
- `vessel_master_new`
- `vessel_owner`
- `vessel_owner_business`
- `vessel_screening`
- `vessel_screening_cargo`
- `vessel_screening_history`
- `vessel_screening_pic`
- `vessel_screening_port`
- `vessel_screening_remark`
- `vessel_screening_summary`
- `vessel_screening_summary_attachment_remark`
- `vessel_screening_summary_web_service`
- `vessel_user`
- `svm_vessel_master_new`

**Key Relationships:**
- `vessel` → `company` (companyId)
- `vessel` → `vessel_type` (vesselTypeId)
- `vessel` → `classification_society` (classificationSocietyId)
- `vessel` → `vessel_doc_holder` (docHolderId)
- `vessel` → `crew_grouping` (crewGroupingId)
- `vessel` → `country` (countryId, shipyardCountryId)
- `vessel` → `user` (createdUserId, updatedUserId)
- `vessel_charterer` → `vessel`, `company`
- `vessel_owner` → `vessel`, `company`
- `vessel_screening` → `vessel`, `company`
- `vessel_user` → `vessel`, `user`

### vessel-type
**Tables:**
- `vessel_type` ⭐ (Primary)
- `focus_request_vessel_type`
- `mail_vessel_type`
- `plans_drawings_vessel_type`
- `report_template_vessel_type`

**Key Relationships:**
- `vessel_type` → `company` (companyId)
- `focus_request_vessel_type` → `focus_request` (focusRequestId), `vessel_type` (vesselTypeId) - Junction table
- `mail_vessel_type` → `mail_template`, `vessel_type` - Junction table
- `plans_drawings_vessel_type` → `plans_drawings`, `vessel_type` - Junction table
- `report_template_vessel_type` → `report_template`, `vessel_type` - Junction table

### viq
**Tables:**
- `viq` ⭐ (Primary)
- `viq_main_category`
- `viq_reference`
- `viq_sub_category`

**Key Relationships:**
- `viq` → `company` (companyId)
- `viq_main_category` → `viq` (viqId)
- `viq_sub_category` → `viq_main_category` (viqMainCategoryId)
- `viq_reference` → `viq`

### voyage-type
**Tables:**
- `voyage_type` ⭐ (Primary)
- `voyage`
- `voyage_master_details`
- `voyage_masters_cargos`
- `voyage_status`

**Key Relationships:**
- `voyage_type` → `company` (companyId)
- `voyage` → `voyage_type`
- `voyage_master_details` → `voyage`
- `voyage_masters_cargos` → `voyage`, `cargo`
- `voyage_status` → `voyage`

### watchlist
**Tables:**
- `watchlist`

**Key Relationships:**
- `watchlist` → `company` (companyId)

### widget
**Tables:**
- `widget`

**Key Relationships:**
- `widget` → `company` (companyId)

---

## Tables Mapped to `src/modules-qa/` Directory

### cargo
**Tables:**
- `cargo` ⭐ (Primary)
- `vessel_screening_cargo`
- `voyage_masters_cargos`

**Key Relationships:**
- `cargo` → `company` (companyId)
- `vessel_screening_cargo` → `vessel_screening`, `cargo` - Junction table
- `voyage_masters_cargos` → `voyage_master_details`, `cargo` - Junction table

### cargo-type
**Tables:**
- `cargo_type`

**Key Relationships:**
- `cargo_type` → `company` (companyId)

### categorization-master
**Tables:**
- `categorization_master`
- `vessel_company_feedback_categorization_master`

**Key Relationships:**
- `categorization_master` → `company` (companyId)
- `vessel_company_feedback_categorization_master` → `vessel_company_feedback`, `categorization_master`

### class-dispensations
**Tables:**
- `class_dispensations`
- `class_dispensations_request`

**Key Relationships:**
- `class_dispensations` → `company` (companyId)
- `class_dispensations` → `vessel` (vesselId)
- `class_dispensations_request` → `class_dispensations`

### classification-society
**Tables:**
- `classification_society`

**Key Relationships:**
- `classification_society` → `company` (companyId)
- Referenced by: `vessel` (classificationSocietyId)

### crew-grouping
**Tables:**
- `crew_grouping`
- `crew_grouping_officer_country`
- `crew_grouping_rating_country`

**Key Relationships:**
- `crew_grouping` → `company` (companyId)
- `crew_grouping_officer_country` → `crew_grouping`, `country`
- `crew_grouping_rating_country` → `crew_grouping`, `country`

### cviq-chapter
**Tables:**
- `cviq_chapter_master`

**Key Relationships:**
- `cviq_chapter_master` → `company` (companyId)

### cviq-conditionality
**Tables:**
- `cviq_conditionality_master`

**Key Relationships:**
- `cviq_conditionality_master` → `company` (companyId)

### cviq-detail-mapping
**Tables:**
- `cviq_detail_mapping_master`

**Key Relationships:**
- `cviq_detail_mapping_master` → `company` (companyId)

### cviq-version
**Tables:**
- `cviq_version_master`

**Key Relationships:**
- `cviq_version_master` → `company` (companyId)

### dry-docking
**Tables:**
- `dry_docking`
- `dry_docking_request`

**Key Relationships:**
- `dry_docking` → `company` (companyId)
- `dry_docking` → `vessel` (vesselId)
- `dry_docking_request` → `dry_docking`

### element-master
**Tables:**
- `element_master`
- `standard_master_elements`

**Key Relationships:**
- `element_master` → `company` (companyId)
- `standard_master_elements` → `standard_master`, `element_master`

### event-type
**Tables:**
- `event_type` ⭐ (Primary)
- `authority_event_type`

**Key Relationships:**
- `event_type` → `company` (companyId)
- `authority_event_type` → `authority_master`, `event_type`

### external-inspections
**Tables:**
- `external_inspections` ⭐ (Primary)
- `external_inspections_auditor_user`
- `external_inspections_request`
- `external_inspection_report`

**Key Relationships:**
- `external_inspections` → `vessel` (vesselId)
- `external_inspections` → `company` (companyId)
- `external_inspections` → `port_master` (portId, terminalId)
- `external_inspections` → `event_type` (eventTypeId)
- `external_inspections` → `authority_master` (authorityId)
- `external_inspections` → `user` (createdUserId, updatedUserId)
- `external_inspections_auditor_user` → `external_inspections`, `user`
- `external_inspections_request` → `external_inspections`

### incident-investigation
**Tables:**
- `incident_investigation` ⭐ (Primary)
- `incident_investigation_comment`
- `incident_investigation_history`
- `incident_investigation_incident_master`
- `incident_investigation_remark`
- `incident_investigation_request`
- `incident_investigation_review`
- `incident_investigation_secondary_incident_master`
- `incident_investigation_secondary_sub_incident_type`
- `incident_investigation_sub_incident_type`

**Key Relationships:**
- `incident_investigation` → `company` (companyId)
- `incident_investigation` → `vessel` (vesselId)
- `incident_investigation` → `port_master` (portId, portToId)
- `incident_investigation` → `voyage_type` (voyageTypeId)
- `incident_investigation` → `risk_matrix_master` (riskMatrixId)
- `incident_investigation` → `priority_master` (potentialPriorityMasterId, observedPriorityMasterId, finalPriorityMasterId)
- `incident_investigation` → `user` (createdUserId, updatedUserId)
- `incident_investigation_incident_master` → `incident_investigation`, `incident_master`
- `incident_investigation_sub_incident_type` → `incident_investigation`, `sub_incident_type`

### incident-master
**Tables:**
- `incident_master` ⭐ (Primary)
- `incident_main_category`
- `incident_second_sub_category`
- `incident_sub_category`
- `incident_investigation_incident_master`
- `incident_investigation_secondary_incident_master`

**Key Relationships:**
- `incident_master` → `company` (companyId)
- `incident_main_category` → `incident_investigation` (incidentInvestigationId)
- `incident_main_category` → `cause_mapping` (causeMappingId)
- `incident_sub_category` → `incident_main_category` (incidentMainCategoryId)
- `incident_second_sub_category` → `incident_sub_category` (incidentSubCategoryId)
- `incident_investigation_incident_master` → `incident_investigation`, `incident_master`

### injury
**Tables:**
- `injury`
- `injury_request`

**Key Relationships:**
- `injury` → `company` (companyId)
- `injury` → `vessel` (vesselId)
- `injury_request` → `injury`

### injury-body
**Tables:**
- `injury_body`

**Key Relationships:**
- `injury_body` → `company` (companyId)

### injury-master
**Tables:**
- `injury_master`

**Key Relationships:**
- `injury_master` → `company` (companyId)

### internal-inspections
**Tables:**
- `internal_inspections`
- `internal_inspections_request`

**Key Relationships:**
- `internal_inspections` → `company` (companyId)
- `internal_inspections` → `vessel` (vesselId)
- `internal_inspections_request` → `internal_inspections`

### maintenance-performance
**Tables:**
- `maintenance_performance`
- `maintenance_performance_request`

**Key Relationships:**
- `maintenance_performance` → `company` (companyId)
- `maintenance_performance` → `vessel` (vesselId)
- `maintenance_performance_request` → `maintenance_performance`

### other-sms-records
**Tables:**
- `other_sms_records`
- `other_sms_records_request`

**Key Relationships:**
- `other_sms_records` → `company` (companyId)
- `other_sms_records` → `vessel` (vesselId)
- `other_sms_records_request` → `other_sms_records`

### other-technical-records
**Tables:**
- `other_tech_records`
- `other_tech_records_request`

**Key Relationships:**
- `other_tech_records` → `company` (companyId)
- `other_tech_records` → `vessel` (vesselId)
- `other_tech_records_request` → `other_tech_records`

### pilot-terminal-feedback
**Tables:**
- `pilot_terminal_feedback`
- `pilot_terminal_feedback_checklist`
- `pilot_terminal_feedback_history`

**Key Relationships:**
- `pilot_terminal_feedback` → `company` (companyId)
- `pilot_terminal_feedback` → `vessel` (vesselId)
- `pilot_terminal_feedback_checklist` → `pilot_terminal_feedback`
- `pilot_terminal_feedback_history` → `pilot_terminal_feedback`

### plans-drawings
**Tables:**
- `plans_drawings`
- `planing_drawing_request`

**Key Relationships:**
- `plans_drawings` → `company` (companyId)
- `plans_drawings` → `vessel` (vesselId)
- `planing_drawing_request` → `plans_drawings`

### plans-drawings-master
**Tables:**
- `plans_drawings_master`
- `plans_drawings_vessel_type`

**Key Relationships:**
- `plans_drawings_master` → `company` (companyId)
- `plans_drawings_vessel_type` → `plans_drawings`, `vessel_type`

### port-state-control
**Tables:**
- `port_state_control`
- `port_state_control_auditor_user`
- `port_state_control_request`
- `port_state_inspection_report`

**Key Relationships:**
- `port_state_control` → `company` (companyId)
- `port_state_control` → `vessel` (vesselId)
- `port_state_control` → `port_master` (portId)
- `port_state_control_auditor_user` → `port_state_control`, `user`
- `port_state_control_request` → `port_state_control`

### right-ship
**Tables:**
- `right_ship`
- `right_ship_restrictions`

**Key Relationships:**
- `right_ship` → `company` (companyId)
- `right_ship` → `vessel` (vesselId)
- `right_ship_restrictions` → `right_ship`

### risk-factor
**Tables:**
- `risk_factor`

**Key Relationships:**
- `risk_factor` → `company` (companyId)

### self-assessment
**Tables:**
- `self_assessment` ⭐ (Primary)
- `self_assessment_acknowledge_review`
- `self_assessment_internal_note`
- `self_assessment_review_general_comments`
- `fill_sa_checklist_question`
- `sa_finding_item`

**Key Relationships:**
- `self_assessment` → `company` (companyId, auditCompanyId)
- `self_assessment` → `authority_master` (authorityId)
- `self_assessment` → `standard_master` (standardMasterId)
- `self_assessment` → `audit_workspace` (auditWorkspaceId)
- `self_assessment` → `fill_audit_checklist` (fillAuditChecklistId)
- `self_assessment` → `user` (createdUserId, updatedUserId)
- `self_assessment_acknowledge_review` → `self_assessment`
- `fill_sa_checklist_question` → `self_assessment`
- `sa_finding_item` → `self_assessment`

### ship-particular
**Tables:**
- `ship_particular`

**Key Relationships:**
- `ship_particular` → `company` (companyId)
- `ship_particular` → `vessel` (vesselId)

### sire-viq
**Tables:**
- `sire_viq`
- `sire_inspection_report`

**Key Relationships:**
- `sire_viq` → `company` (companyId)
- `sire_viq` → `vessel` (vesselId)
- `sire_inspection_report` → `sire_viq`

### standard-master
**Tables:**
- `standard_master` ⭐ (Primary)
- `standard_master_elements`
- `standard_module_label`
- `inspection_mapping_standard_master`

**Key Relationships:**
- `standard_master` → `company` (companyId)
- `standard_master_elements` → `standard_master`, `element_master`
- `standard_module_label` → `standard_master`
- `inspection_mapping_standard_master` → `inspection_mapping`, `standard_master`

### sub-incident-type
**Tables:**
- `sub_incident_type`
- `incident_investigation_sub_incident_type`
- `incident_investigation_secondary_sub_incident_type`

**Key Relationships:**
- `sub_incident_type` → `company` (companyId)
- `incident_investigation_sub_incident_type` → `incident_investigation`, `sub_incident_type`
- `incident_investigation_secondary_sub_incident_type` → `incident_investigation`, `sub_incident_type`

### survey
**Tables:**
- `survey_class_info`
- `survey_class_info_request`

**Key Relationships:**
- `survey_class_info` → `company` (companyId)
- `survey_class_info` → `vessel` (vesselId)
- `survey_class_info_request` → `survey_class_info`

---

## Additional Database Tables (Not Directly Mapped to Modules)

### System/Infrastructure Tables
- `migrations` - TypeORM migration tracking
- `typeorm_metadata` - TypeORM metadata
- `spatial_ref_sys` - PostGIS spatial reference systems
- `geometry_columns` - PostGIS geometry columns
- `geography_columns` - PostGIS geography columns

### Common/Shared Tables
- `action` - System actions
- `adminlog` - Admin activity logging
- `city` - City master data
- `compliance_answer` - Compliance responses
- `cthd` - Customer hierarchy data
- `data_grid_template` - Grid templates
- `email` - Email records
- `email_log` - Email logging
- `feature` - Feature flags
- `field_config` - Field configurations
- `finding_item_history` - Finding item history
- `follow_up` - Follow-up records
- `follow_up_comment` - Follow-up comments
- `hardware_soc` - Hardware SOC data
- `iam_meta_config` - IAM metadata configuration
- `iar_report_header` - IAR report headers
- `iar_report_header_description` - IAR report descriptions
- `iar_user` - IAR user associations
- `issue_summary` - Issue summaries
- `label_config` - Label configurations
- `master_table` - Master table data
- `meta_config` - Meta configurations
- `module_config` - Module configurations
- `module_label_config` - Module label configurations
- `observed_risk` - Observed risk data
- `onboard_finding_item` - Onboard finding items
- `password_expiry_configuration` - Password expiry settings
- `permission` - Permission definitions
- `process_soc` - Process SOC data
- `province` - Province master data
- `push` - Push notifications
- `remark` - General remarks
- `report_header` - Report headers
- `report_template` - Report templates
- `report_template_history` - Report template history
- `review_prep_comment` - Review prep comments
- `rof_office_comment` - ROF office comments
- `rof_user` - ROF user associations
- `role` - Role definitions
- `role_permission` - Role-permission mappings
- `self_declaration` - Self declarations
- `self_declaration_comment` - Self declaration comments
- `self_declaration_document` - Self declaration documents
- `self_declaration_history` - Self declaration history
- `self_declaration_reference` - Self declaration references
- `signed_file_history` - Signed file history
- `statistic_previous_finding_items` - Statistical data
- `tech_issue_note` - Technical issue notes
- `terminal` - Terminal master data
- `test_table` - Test table
- `updated_file_history` - Updated file history
- `upload` - File uploads
- `workflow` - Workflow definitions
- `workflow_role` - Workflow roles
- `zenith_potential_risk_data` - Zenith risk data

### Comment Tables (Various modules)
- `cap_comment` - CAP comments
- `cdr_comment` - CDR comments
- `ddr_comment` - DDR comments
- `eir_comment` - EIR comments
- `iir_comment` - IIR comments
- `ir_comment` - IR comments
- `mpr_comment` - MPR comments
- `osrr_comment` - OSRR comments
- `otrr_comment` - OTRR comments
- `pd_comment` - PD comments
- `pdr_comment` - PDR comments
- `psr_comment` - PSR comments
- `ptf_comment` - PTF comments
- `scr_comment` - SCR comments

### Verification Tables
- `car_verification` - CAR verification
- `car_verification_planning` - CAR verification planning

---

## Summary Statistics

### Modules Directory (`src/modules/`):
- **47 module directories**
- **~200+ database tables**
- **Core modules**: vessel, company, user, planning-request, audit-checklist

### Modules-QA Directory (`src/modules-qa/`):
- **26 module directories**
- **~80+ database tables**
- **Core modules**: incident-investigation, self-assessment, external-inspections

### Key Relationships:
- **Central entities**: `company`, `vessel`, `user`, `port_master`, `country`
- **Most tables reference**: `companyId`, `createdUserId`, `updatedUserId`
- **Junction tables**: Enable many-to-many relationships
- **Hierarchical data**: Categories, incidents, departments

---

## Database Architecture Notes

1. **Multi-tenancy**: Most tables are company-scoped via `companyId`
2. **Audit Trail**: Standard soft delete and timestamp fields
3. **User Tracking**: Created/updated user tracking on most entities
4. **Hierarchical Data**: Support for nested categories and organizational structures
5. **Flexible Relationships**: Many junction tables for complex relationships
6. **Geographic Support**: PostGIS integration for location-based features
7. **Workflow Support**: Status fields and workflow tables for process management 