import { MigrationInterface, QueryRunner } from 'typeorm';

export class incidentIdColumnAddInMailPlanning1753167041819 implements MigrationInterface {
  name = 'incidentIdColumnAddInMailPlanning1753167041819';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "public"."mail_planning" ALTER COLUMN "planningRequestId" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "public"."mail_planning" ADD "incidentInvestigationId" uuid`,
    );
    await queryRunner.query(
      `ALTER TABLE "public"."mail_planning" ADD CONSTRAINT "FK_0ba3d386ef3f0d5d74dc4343f16" FOREIGN KEY ("incidentInvestigationId") REFERENCES "incident_investigation"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
