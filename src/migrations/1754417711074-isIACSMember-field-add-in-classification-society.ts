import {MigrationInterface, QueryRunner} from "typeorm";

export class isIACSMemberFieldAddInClassificationSociety1754417711074 implements MigrationInterface {
    name = 'isIACSMemberFieldAddInClassificationSociety1754417711074'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "public"."classification_society" ADD "isIACSMember" boolean NOT NULL DEFAULT false`);

        await queryRunner.query(`UPDATE "public"."classification_society" SET "isIACSMember" = true WHERE "code" IN ('ABS', 'BV', 'CCS', 'CRS', 'NK', 'DNV', 'IRS', 'KR', 'LR', 'PRS', 'RINA', 'RS', 'TL')`)
    }

    public async down(queryRunner: QueryRunner): Promise<void> {}

}
