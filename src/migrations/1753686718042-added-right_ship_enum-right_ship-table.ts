import { MigrationInterface, QueryRunner } from 'typeorm';

export class addedRightShipEnumRightShipTable1753686718042 implements MigrationInterface {
  name = 'addedRightShipEnumRightShipTable1753686718042';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Update reference enum
    await queryRunner.query(
      `ALTER TYPE "public"."vessel_screening_summary_reference_enum" RENAME TO "vessel_screening_summary_reference_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."vessel_screening_summary_reference_enum" AS ENUM('Condition of Class/Dispensations', 'Survey/Class Info', 'Maintenance Performance', 'Other Technical Records', 'Dry Docking', 'Incidents', 'Injuries', 'Other SMS Records', 'Port State Control', 'External Inspections', 'Internal Inspections/Audits', 'Safety Engagement', 'Plans and Drawings', 'Pilot/Terminal Feedback', 'Charterer Inspection', 'Vessel/Company Feedback', 'Right Ship')`,
    );
    await queryRunner.query(
      `ALTER TABLE "public"."vessel_screening_summary" ALTER COLUMN "reference" TYPE "public"."vessel_screening_summary_reference_enum" USING "reference"::"text"::"public"."vessel_screening_summary_reference_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."vessel_screening_summary_reference_enum_old"`);

    // Update tabName enum
    await queryRunner.query(
      `ALTER TYPE "public"."vessel_screening_summary_tabname_enum" RENAME TO "vessel_screening_summary_tabname_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."vessel_screening_summary_tabname_enum" AS ENUM('Technical', 'Safety Management', 'Inspections', 'Safety Engagement', 'Ship Particulars', 'Pilot/Terminal Feedback', 'Basic Info', 'Right Ship')`,
    );

    // Update all tables that use the tabName enum
    await queryRunner.query(
      `ALTER TABLE "public"."vessel_screening_summary" ALTER COLUMN "tabName" TYPE "public"."vessel_screening_summary_tabname_enum" USING "tabName"::"text"::"public"."vessel_screening_summary_tabname_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "public"."vessel_screening_summary_attachment_remark" ALTER COLUMN "tabName" TYPE "public"."vessel_screening_summary_tabname_enum" USING "tabName"::"text"::"public"."vessel_screening_summary_tabname_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "public"."vessel_screening_summary_web_service" ALTER COLUMN "tabName" TYPE "public"."vessel_screening_summary_tabname_enum" USING "tabName"::"text"::"public"."vessel_screening_summary_tabname_enum"`,
    );

    // Now safe to drop the old enum
    await queryRunner.query(`DROP TYPE "public"."vessel_screening_summary_tabname_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
