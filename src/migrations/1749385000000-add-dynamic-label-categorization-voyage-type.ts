import { MigrationInterface, QueryRunner } from 'typeorm';

export class addDynamicLabelCategorizationVoyageType1749385000000 implements MigrationInterface {
  name = 'addDynamicLabelCategorizationVoyageType1749385000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 1. Add Categorization Master module config
    await queryRunner.query(
      `INSERT INTO module_config
       (deleted, "parentId", "defaultLabel", "userDefinedLabel", "language", "key", "isDefault", "order")
       VALUES
       (false, (select id from module_config where "key" = 'Configuration::QA' and "isDefault" = true order by "updatedAt" desc limit 1), 
              'Categorization Master', 'Categorization Master', 'en', 
              'Configuration::QA::Categorization Master', true, 930);`,
    );

    // 2. Add Categorization Master labels
    await queryRunner.query(
      `INSERT INTO label_config
       ("moduleId", "userDefinedLabel", "defaultLabel", "language", "key", "isDefault", "isHide")
       SELECT (select id from module_config where "key" = 'Configuration::QA::Categorization Master' and "isDefault" = true order by "updatedAt" desc limit 1), 
              val, val, 'en', val, true, false
       FROM unnest(ARRAY['Action', 'Category Code', 'Category Name', 'Category Type', 'Status', 
           'Description', 'Created user', 'Created date', 'Updated date', 'Updated user', 'Create New',
           'Cancel', 'Save', 'Delete?', 'Categorization Master', 'Type', 
           'Active', 'Inactive', 'Add Category', 'Remove', 'Parent Category',
           'Confirm Delete', 'Are you sure you want to delete this record? This action cannot be undone and you will not be able to recover any data.',
           'Unsaved Changes', 'You have made changes that have not been saved yet. Are you sure you want to proceed with this action?',
           'Category Details', 'Sub-Categories']) AS t(val);`,
    );

    // 3. Add Voyage Type module config
    await queryRunner.query(
      `INSERT INTO module_config
       (deleted, "parentId", "defaultLabel", "userDefinedLabel", "language", "key", "isDefault", "order")
       VALUES
       (false, (select id from module_config where "key" = 'Quality Assurance' and "isDefault" = true order by "updatedAt" desc limit 1), 
              'Voyage Type', 'Voyage Type', 'en', 
              'Configuration::Common::Voyage Type', true, 940);`,
    );

    // 4. Add Voyage Type labels
    await queryRunner.query(
      `INSERT INTO label_config
       ("moduleId", "userDefinedLabel", "defaultLabel", "language", "key", "isDefault", "isHide")
       SELECT (select id from module_config where "key" = 'Configuration::Common::Voyage Type' and "isDefault" = true order by "updatedAt" desc limit 1), 
              val, val, 'en', val, true, false
       FROM unnest(ARRAY['Action', 'Code', 'Name', 'Status', 'Description', 
           'Created user', 'Created date', 'Updated date', 'Updated user', 'Create New',
           'Cancel', 'Save', 'Delete?', 'Voyage Type', 
           'Active', 'Inactive', 'Order',
           'Confirm Delete', 'Are you sure you want to delete this record? This action cannot be undone and you will not be able to recover any data.',
           'Unsaved Changes', 'You have made changes that have not been saved yet. Are you sure you want to proceed with this action?']) AS t(val);`,
    );

    // 5. Add module_label_config mappings for Categorization Master (List action)
    await queryRunner.query(
      `INSERT INTO module_label_config
                ("moduleId", "labelId", "action")
                SELECT lc."moduleId" , lc.id, 'List' from label_config lc
                where "moduleId" = (select id from module_config where "key" = 'Configuration::QA::Categorization Master' and "isDefault" = true order by "updatedAt" desc limit 1)
                and lc."key" in ('Action', 'Category Code', 'Category Name', 'Category Type', 'Status', 
                'Created user', 'Created date', 'Updated date', 'Updated user', 'Categorization Master', 
                'Create New', 'Active', 'Inactive');`,
    );

    // 6. Add module_label_config mappings for Categorization Master (View action)
    await queryRunner.query(
      `INSERT INTO module_label_config
                ("moduleId", "labelId", "action")
                SELECT lc."moduleId" , lc.id, 'View' from label_config lc
                where "moduleId" = (select id from module_config where "key" = 'Configuration::QA::Categorization Master' and "isDefault" = true order by "updatedAt" desc limit 1)
                and lc."key" in ('Category Code', 'Category Name', 'Category Type', 'Status', 'Description', 
                'Created user', 'Created date', 'Updated date', 'Updated user', 'Categorization Master', 
                'Parent Category', 'Sub-Categories', 'Type', 'Active', 'Inactive', 'Category Details');`,
    );

    // 7. Add module_label_config mappings for Categorization Master (Create action)
    await queryRunner.query(
      `INSERT INTO module_label_config
                ("moduleId", "labelId", "action")
                SELECT lc."moduleId" , lc.id, 'Create' from label_config lc
                where "moduleId" = (select id from module_config where "key" = 'Configuration::QA::Categorization Master' and "isDefault" = true order by "updatedAt" desc limit 1)
                and lc."key" in ('Category Code', 'Category Name', 'Category Type', 'Status', 'Description', 
                'Categorization Master', 'Parent Category', 'Type', 'Active', 'Inactive', 'Cancel', 'Save', 
                'Add Category', 'Remove', 'Category Details');`,
    );

    // 8. Add module_label_config mappings for Categorization Master (Edit action)
    await queryRunner.query(
      `INSERT INTO module_label_config
                ("moduleId", "labelId", "action")
                SELECT lc."moduleId" , lc.id, 'Edit' from label_config lc
                where "moduleId" = (select id from module_config where "key" = 'Configuration::QA::Categorization Master' and "isDefault" = true order by "updatedAt" desc limit 1)
                and lc."key" in ('Category Code', 'Category Name', 'Category Type', 'Status', 'Description', 
                'Categorization Master', 'Parent Category', 'Type', 'Active', 'Inactive', 'Cancel', 'Save', 
                'Add Category', 'Remove', 'Delete?', 'Confirm Delete', 'Unsaved Changes', 'Category Details');`,
    );

    // 9. Add module_label_config mappings for Voyage Type (List action)
    await queryRunner.query(
      `INSERT INTO module_label_config
                ("moduleId", "labelId", "action")
                SELECT lc."moduleId" , lc.id, 'List' from label_config lc
                where "moduleId" = (select id from module_config where "key" = 'Configuration::Common::Voyage Type' and "isDefault" = true order by "updatedAt" desc limit 1)
                and lc."key" in ('Action', 'Code', 'Name', 'Status', 'Order', 'Created user', 
                'Created date', 'Updated date', 'Updated user', 'Voyage Type', 'Create New', 
                'Active', 'Inactive');`,
    );

    // 10. Add module_label_config mappings for Voyage Type (View action)
    await queryRunner.query(
      `INSERT INTO module_label_config
                ("moduleId", "labelId", "action")
                SELECT lc."moduleId" , lc.id, 'View' from label_config lc
                where "moduleId" = (select id from module_config where "key" = 'Configuration::Common::Voyage Type' and "isDefault" = true order by "updatedAt" desc limit 1)
                and lc."key" in ('Code', 'Name', 'Status', 'Description', 'Order',
                'Created user', 'Created date', 'Updated date', 'Updated user', 
                'Voyage Type', 'Active', 'Inactive');`,
    );

    // 11. Add module_label_config mappings for Voyage Type (Create action)
    await queryRunner.query(
      `INSERT INTO module_label_config
                ("moduleId", "labelId", "action")
                SELECT lc."moduleId" , lc.id, 'Create' from label_config lc
                where "moduleId" = (select id from module_config where "key" = 'Configuration::Common::Voyage Type' and "isDefault" = true order by "updatedAt" desc limit 1)
                and lc."key" in ('Code', 'Name', 'Status', 'Description', 'Order',
                'Voyage Type', 'Active', 'Inactive', 'Cancel', 'Save');`,
    );

    // 12. Add module_label_config mappings for Voyage Type (Edit action)
    await queryRunner.query(
      `INSERT INTO module_label_config
                ("moduleId", "labelId", "action")
                SELECT lc."moduleId" , lc.id, 'Edit' from label_config lc
                where "moduleId" = (select id from module_config where "key" = 'Configuration::Common::Voyage Type' and "isDefault" = true order by "updatedAt" desc limit 1)
                and lc."key" in ('Code', 'Name', 'Status', 'Description', 'Order',
                'Voyage Type', 'Active', 'Inactive', 'Cancel', 'Save', 'Delete?', 'Confirm Delete', 'Unsaved Changes');`,
    );

    // Add popup messages for both modules
    await queryRunner.query(
      `INSERT INTO label_config
       ("moduleId", "userDefinedLabel", "defaultLabel", "language", "key", "isDefault", "isHide")
       VALUES
       ( (select id from module_config where "key" = 'Configuration::QA::Categorization Master' and "isDefault" = true order by "updatedAt" desc limit 1),
        'This category has subcategories. Deleting it will also delete all subcategories.', 
        'This category has subcategories. Deleting it will also delete all subcategories.', 
        'en', 'DeleteSubcategoriesWarning', true, false),
        ( (select id from module_config where "key" = 'Configuration::Common::Voyage Type' and "isDefault" = true order by "updatedAt" desc limit 1),
        'Order must be unique across all voyage types.', 
        'Order must be unique across all voyage types.', 
        'en', 'UniqueOrderWarning', true, false);`,
    );

    // Map popup messages to actions
    await queryRunner.query(
      `INSERT INTO module_label_config
                ("moduleId", "labelId", "action")
                SELECT lc."moduleId" , lc.id, 'Create' from label_config lc
                where "moduleId" = (select id from module_config where "key" = 'Configuration::Common::Voyage Type' and "isDefault" = true order by "updatedAt" desc limit 1)
                and lc."key" = 'UniqueOrderWarning';
       
       INSERT INTO module_label_config
                ("moduleId", "labelId", "action")
                SELECT lc."moduleId" , lc.id, 'Edit' from label_config lc
                where "moduleId" = (select id from module_config where "key" = 'Configuration::Common::Voyage Type' and "isDefault" = true order by "updatedAt" desc limit 1)
                and lc."key" = 'UniqueOrderWarning';
                
       INSERT INTO module_label_config
                ("moduleId", "labelId", "action")
                SELECT lc."moduleId" , lc.id, 'Edit' from label_config lc
                where "moduleId" = (select id from module_config where "key" = 'Configuration::QA::Categorization Master' and "isDefault" = true order by "updatedAt" desc limit 1)
                and lc."key" = 'DeleteSubcategoriesWarning';`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
