import { MigrationInterface, QueryRunner } from 'typeorm';

export class addFinalRiskScoreRequestTables1752401725342 implements MigrationInterface {
  name = 'addFinalRiskScoreRequestTables1752401725342';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add finalRisk and finalScore columns to injury_request table
    await queryRunner.query(`ALTER TABLE "public"."injury_request" ADD "finalRisk" smallint`);
    await queryRunner.query(`ALTER TABLE "public"."injury_request" ADD "finalScore" real`);

    // Add finalRisk and finalScore columns to port_state_control_request table
    await queryRunner.query(
      `ALTER TABLE "public"."port_state_control_request" ADD "finalRisk" smallint`,
    );
    await queryRunner.query(
      `ALTER TABLE "public"."port_state_control_request" ADD "finalScore" real`,
    );

    // Add finalRisk and finalScore columns to class_dispensations_request table
    await queryRunner.query(
      `ALTER TABLE "public"."class_dispensations_request" ADD "finalRisk" smallint`,
    );
    await queryRunner.query(
      `ALTER TABLE "public"."class_dispensations_request" ADD "finalScore" real`,
    );

    // Add finalRisk and finalScore columns to survey_class_info_request table
    await queryRunner.query(
      `ALTER TABLE "public"."survey_class_info_request" ADD "finalRisk" smallint`,
    );
    await queryRunner.query(
      `ALTER TABLE "public"."survey_class_info_request" ADD "finalScore" real`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
