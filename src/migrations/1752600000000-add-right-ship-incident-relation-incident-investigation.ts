import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddRightShipIncidentRelationIncidentInvestigation1752600000000
  implements MigrationInterface {
  name = 'AddRightShipIncidentRelationIncidentInvestigation1752600000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add rightShipIncidentId column
    await queryRunner.query(
      `ALTER TABLE "incident_investigation" ADD COLUMN "rightShipIncidentId" uuid`,
    );

    // Add foreign key constraint for rightShipIncidentId
    await queryRunner.query(
      `ALTER TABLE "incident_investigation" ADD CONSTRAINT "FK_incident_investigation_right_ship_incident" FOREIGN KEY ("rightShipIncidentId") REFERENCES "right_ship_incidents"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );

    // Add index for better query performance
    await queryRunner.query(
      `CREATE INDEX "IDX_incident_investigation_rightShipIncidentId" ON "incident_investigation" ("rightShipIncidentId")`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
