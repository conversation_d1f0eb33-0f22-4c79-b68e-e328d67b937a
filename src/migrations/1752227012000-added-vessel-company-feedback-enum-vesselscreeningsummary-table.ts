import { MigrationInterface, QueryRunner } from 'typeorm';

export class addedVesselCompanyFeedbackEnumVesselscreeningsummaryTable1752227012000 implements MigrationInterface {
  name = 'addedVesselCompanyFeedbackEnumVesselscreeningsummaryTable1752227012000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TYPE "public"."vessel_screening_summary_reference_enum" RENAME TO "vessel_screening_summary_reference_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."vessel_screening_summary_reference_enum" AS ENUM('Condition of Class/Dispensations', 'Survey/Class Info', 'Maintenance Performance', 'Other Technical Records', 'Dry Docking', 'Incidents', 'Injuries', 'Other SMS Records', 'Port State Control', 'External Inspections', 'Internal Inspections/Audits', 'Safety Engagement', 'Plans and Drawings', 'Pilot/Terminal Feedback', 'Charterer Inspection', 'Vessel/Company Feedback')`,
    );
    await queryRunner.query(
      `ALTER TABLE "public"."vessel_screening_summary" ALTER COLUMN "reference" TYPE "public"."vessel_screening_summary_reference_enum" USING "reference"::"text"::"public"."vessel_screening_summary_reference_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."vessel_screening_summary_reference_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
