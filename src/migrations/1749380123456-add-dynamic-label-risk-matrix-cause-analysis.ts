import { MigrationInterface, QueryRunner } from 'typeorm';

export class addDynamicLabelRiskMatrixCauseAnalysis1749380123456 implements MigrationInterface {
  name = 'addDynamicLabelRiskMatrixCauseAnalysis1749380123456';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 1. Add Risk Matrix module config
    await queryRunner.query(
      `INSERT INTO module_config
       (deleted, "parentId", "defaultLabel", "userDefinedLabel", "language", "key", "isDefault", "order")
       VALUES
       (false, (select id from module_config where "key" = 'Configuration::Common' and "isDefault" = true order by "updatedAt" desc limit 1), 
              'Risk Matrix', 'Risk Matrix', 'en', 
              'Configuration::Common::Risk Matrix', true, 910);`,
    );

    // 2. Add Risk Matrix labels
    await queryRunner.query(
      `INSERT INTO label_config
       ("moduleId", "userDefinedLabel", "defaultLabel", "language", "key", "isDefault", "isHide")
       SELECT (select id from module_config where "key" = 'Configuration::Common::Risk Matrix' and "isDefault" = true order by "updatedAt" desc limit 1), 
              val, val, 'en', val, true, false
       FROM unnest(ARRAY['Action', 'Matrix Code', 'Rows', 'Columns', 'Row Names', 'Column Names', 'Status', 
           'Risk Value', 'Color', 'Cell Positions', 'Priority Level', 'Created user', 'Created date', 'Updated date',
           'Updated user', 'Create New', 'Cancel', 'Save', 'Delete?', 'Risk Matrix', 'Risk Values', 
           'Level Mappings', 'Active', 'Inactive', 'Add Value', 'Add Level', 'Remove', 'Select Priority', 
           'Confirm Delete', 'Are you sure you want to delete this record? This action cannot be undone and you will not be able to recover any data.',
           'Unsaved Changes', 'You have made changes that have not been saved yet. Are you sure you want to proceed with this action?',
           'Cell Editor', 'Risk Matrix Configuration']) AS t(val);`,
    );

    // 3. Add Cause Analysis Master module config
    await queryRunner.query(
      `INSERT INTO module_config
       (deleted, "parentId", "defaultLabel", "userDefinedLabel", "language", "key", "isDefault", "order")
       VALUES
       (false, (select id from module_config where "key" = 'Configuration::Common' and "isDefault" = true order by "updatedAt" desc limit 1), 
              'Cause Analysis Master', 'Cause Analysis Master', 'en', 
              'Configuration::Common::Cause Analysis Master', true, 920);`,
    );

    // 4. Add Cause Analysis labels
    await queryRunner.query(
      `INSERT INTO label_config
       ("moduleId", "userDefinedLabel", "defaultLabel", "language", "key", "isDefault", "isHide")
       SELECT (select id from module_config where "key" = 'Configuration::Common::Cause Analysis Master' and "isDefault" = true order by "updatedAt" desc limit 1), 
              val, val, 'en', val, true, false
       FROM unnest(ARRAY['Action', 'Cause Type', 'Version Number', 'Reference Number', 'Status', 'Main Category',
           'Main Category No', 'Main Category Name', 'Sub Category', 'Sub Category Name', 'Sub Reference Number', 'Level',
           'Potential Risk', 'Created user', 'Created date', 'Updated date', 'Updated user', 'Create New', 'Cancel', 
           'Save', 'Delete?', 'Cause Analysis Master', 'Basic', 'Immediate', 'Control Action Needs', 'Type of Loss',
           'Active', 'Inactive', 'Add Main Category', 'Add Sub Category', 'Remove', 'Select Risk Level',
           'Confirm Delete', 'Are you sure you want to delete this record? This action cannot be undone and you will not be able to recover any data.',
           'Unsaved Changes', 'You have made changes that have not been saved yet. Are you sure you want to proceed with this action?']) AS t(val);`,
    );

    // 5. Add module_label_config mappings for Risk Matrix (List action)
    await queryRunner.query(
      `INSERT INTO module_label_config
                ("moduleId", "labelId", "action")
                SELECT lc."moduleId" , lc.id, 'List' from label_config lc
                where "moduleId" = (select id from module_config where "key" = 'Configuration::Common::Risk Matrix' and "isDefault" = true order by "updatedAt" desc limit 1)
                and lc."key" in ('Action', 'Matrix Code', 'Rows', 'Columns', 'Status', 'Created user', 'Created date', 
                'Updated date', 'Updated user', 'Risk Matrix', 'Create New', 'Active', 'Inactive');`,
    );

    // 6. Add module_label_config mappings for Risk Matrix (View action)
    await queryRunner.query(
      `INSERT INTO module_label_config
                ("moduleId", "labelId", "action")
                SELECT lc."moduleId" , lc.id, 'View' from label_config lc
                where "moduleId" = (select id from module_config where "key" = 'Configuration::Common::Risk Matrix' and "isDefault" = true order by "updatedAt" desc limit 1)
                and lc."key" in ('Matrix Code', 'Rows', 'Columns', 'Row Names', 'Column Names', 'Status', 'Risk Value', 
                'Color', 'Cell Positions', 'Priority Level', 'Created user', 'Created date', 'Updated date', 'Updated user', 
                'Risk Matrix', 'Risk Values', 'Level Mappings', 'Active', 'Inactive', 'Cell Editor', 'Risk Matrix Configuration');`,
    );

    // 7. Add module_label_config mappings for Risk Matrix (Create action)
    await queryRunner.query(
      `INSERT INTO module_label_config
                ("moduleId", "labelId", "action")
                SELECT lc."moduleId" , lc.id, 'Create' from label_config lc
                where "moduleId" = (select id from module_config where "key" = 'Configuration::Common::Risk Matrix' and "isDefault" = true order by "updatedAt" desc limit 1)
                and lc."key" in ('Matrix Code', 'Rows', 'Columns', 'Row Names', 'Column Names', 'Status', 'Risk Value', 
                'Color', 'Cell Positions', 'Priority Level', 'Risk Matrix', 'Risk Values', 'Level Mappings', 
                'Active', 'Inactive', 'Cancel', 'Save', 'Add Value', 'Add Level', 'Remove', 'Select Priority', 
                'Cell Editor', 'Risk Matrix Configuration');`,
    );

    // 8. Add module_label_config mappings for Risk Matrix (Edit action)
    await queryRunner.query(
      `INSERT INTO module_label_config
                ("moduleId", "labelId", "action")
                SELECT lc."moduleId" , lc.id, 'Edit' from label_config lc
                where "moduleId" = (select id from module_config where "key" = 'Configuration::Common::Risk Matrix' and "isDefault" = true order by "updatedAt" desc limit 1)
                and lc."key" in ('Matrix Code', 'Rows', 'Columns', 'Row Names', 'Column Names', 'Status', 'Risk Value', 
                'Color', 'Cell Positions', 'Priority Level', 'Risk Matrix', 'Risk Values', 'Level Mappings', 
                'Active', 'Inactive', 'Cancel', 'Save', 'Add Value', 'Add Level', 'Remove', 'Select Priority',
                'Delete?', 'Confirm Delete', 'Unsaved Changes', 'Cell Editor', 'Risk Matrix Configuration');`,
    );

    // 9. Add module_label_config mappings for Cause Analysis (List action)
    await queryRunner.query(
      `INSERT INTO module_label_config
                ("moduleId", "labelId", "action")
                SELECT lc."moduleId" , lc.id, 'List' from label_config lc
                where "moduleId" = (select id from module_config where "key" = 'Configuration::Common::Cause Analysis Master' and "isDefault" = true order by "updatedAt" desc limit 1)
                and lc."key" in ('Action', 'Cause Type', 'Version Number', 'Reference Number', 'Status', 'Created user', 
                'Created date', 'Updated date', 'Updated user', 'Cause Analysis Master', 'Create New', 
                'Basic', 'Immediate', 'Control Action Needs', 'Type of Loss', 'Active', 'Inactive');`,
    );

    // 10. Add module_label_config mappings for Cause Analysis (View action)
    await queryRunner.query(
      `INSERT INTO module_label_config
                ("moduleId", "labelId", "action")
                SELECT lc."moduleId" , lc.id, 'View' from label_config lc
                where "moduleId" = (select id from module_config where "key" = 'Configuration::Common::Cause Analysis Master' and "isDefault" = true order by "updatedAt" desc limit 1)
                and lc."key" in ('Cause Type', 'Version Number', 'Reference Number', 'Status', 'Main Category',
                'Main Category No', 'Main Category Name', 'Sub Category', 'Sub Category Name', 'Sub Reference Number', 
                'Level', 'Potential Risk', 'Created user', 'Created date', 'Updated date', 'Updated user', 
                'Cause Analysis Master', 'Basic', 'Immediate', 'Control Action Needs', 'Type of Loss',
                'Active', 'Inactive');`,
    );

    // 11. Add module_label_config mappings for Cause Analysis (Create action)
    await queryRunner.query(
      `INSERT INTO module_label_config
                ("moduleId", "labelId", "action")
                SELECT lc."moduleId" , lc.id, 'Create' from label_config lc
                where "moduleId" = (select id from module_config where "key" = 'Configuration::Common::Cause Analysis Master' and "isDefault" = true order by "updatedAt" desc limit 1)
                and lc."key" in ('Cause Type', 'Version Number', 'Status', 'Main Category',
                'Main Category No', 'Main Category Name', 'Sub Category', 'Sub Category Name', 'Sub Reference Number', 
                'Level', 'Potential Risk', 'Cause Analysis Master', 'Basic', 'Immediate', 'Control Action Needs', 
                'Type of Loss', 'Active', 'Inactive', 'Cancel', 'Save', 'Add Main Category', 'Add Sub Category', 
                'Remove', 'Select Risk Level');`,
    );

    // 12. Add module_label_config mappings for Cause Analysis (Edit action)
    await queryRunner.query(
      `INSERT INTO module_label_config
                ("moduleId", "labelId", "action")
                SELECT lc."moduleId" , lc.id, 'Edit' from label_config lc
                where "moduleId" = (select id from module_config where "key" = 'Configuration::Common::Cause Analysis Master' and "isDefault" = true order by "updatedAt" desc limit 1)
                and lc."key" in ('Cause Type', 'Version Number', 'Status', 'Main Category',
                'Main Category No', 'Main Category Name', 'Sub Category', 'Sub Category Name', 'Sub Reference Number', 
                'Level', 'Potential Risk', 'Cause Analysis Master', 'Basic', 'Immediate', 'Control Action Needs', 
                'Type of Loss', 'Active', 'Inactive', 'Cancel', 'Save', 'Add Main Category', 'Add Sub Category', 
                'Remove', 'Select Risk Level', 'Delete?', 'Confirm Delete', 'Unsaved Changes');`,
    );

    // Add popup messages for both modules
    await queryRunner.query(
      `INSERT INTO label_config
       ("moduleId", "userDefinedLabel", "defaultLabel", "language", "key", "isDefault", "isHide")
       VALUES
       ( (select id from module_config where "key" = 'Configuration::Common::Risk Matrix' and "isDefault" = true order by "updatedAt" desc limit 1),
        'Only one risk matrix can be active at a time. Setting this matrix as active will deactivate any currently active matrix.', 
        'Only one risk matrix can be active at a time. Setting this matrix as active will deactivate any currently active matrix.', 
        'en', 'ActiveMatrixWarning', true, false),
        ( (select id from module_config where "key" = 'Configuration::Common::Cause Analysis Master' and "isDefault" = true order by "updatedAt" desc limit 1),
        'Only one cause analysis master can be active per cause type (except Type of Loss). Setting this as active will deactivate any currently active record of the same type.', 
        'Only one cause analysis master can be active per cause type (except Type of Loss). Setting this as active will deactivate any currently active record of the same type.', 
        'en', 'ActiveCauseWarning', true, false);`,
    );

    // Map popup messages to actions
    await queryRunner.query(
      `INSERT INTO module_label_config
                ("moduleId", "labelId", "action")
                SELECT lc."moduleId" , lc.id, 'Create' from label_config lc
                where "moduleId" = (select id from module_config where "key" = 'Configuration::Common::Risk Matrix' and "isDefault" = true order by "updatedAt" desc limit 1)
                and lc."key" = 'ActiveMatrixWarning';
       
       INSERT INTO module_label_config
                ("moduleId", "labelId", "action")
                SELECT lc."moduleId" , lc.id, 'Edit' from label_config lc
                where "moduleId" = (select id from module_config where "key" = 'Configuration::Common::Risk Matrix' and "isDefault" = true order by "updatedAt" desc limit 1)
                and lc."key" = 'ActiveMatrixWarning';
                
       INSERT INTO module_label_config
                ("moduleId", "labelId", "action")
                SELECT lc."moduleId" , lc.id, 'Create' from label_config lc
                where "moduleId" = (select id from module_config where "key" = 'Configuration::Common::Cause Analysis Master' and "isDefault" = true order by "updatedAt" desc limit 1)
                and lc."key" = 'ActiveCauseWarning';
                
       INSERT INTO module_label_config
                ("moduleId", "labelId", "action")
                SELECT lc."moduleId" , lc.id, 'Edit' from label_config lc
                where "moduleId" = (select id from module_config where "key" = 'Configuration::Common::Cause Analysis Master' and "isDefault" = true order by "updatedAt" desc limit 1)
                and lc."key" = 'ActiveCauseWarning';`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
