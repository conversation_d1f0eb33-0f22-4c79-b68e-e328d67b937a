import { MigrationInterface, QueryRunner } from 'typeorm';

export class addedCauseMappingIdIncidentInvestigationTable1753181610586
  implements MigrationInterface
{
  name = 'addedCauseMappingIdIncidentInvestigationTable1753181610586';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "public"."incident_investigation" ADD "causeMappingId" uuid`,
    );
    await queryRunner.query(
      `ALTER TABLE "public"."incident_investigation" ADD CONSTRAINT "FK_f0ced1723eb0603baa0ce9f7629" FOREIGN KEY ("causeMappingId") REFERENCES "cause_mapping"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
