import { MigrationInterface, QueryRunner } from 'typeorm';

export class addRiskColumnsVesselCompanyFeedback1752405619484 implements MigrationInterface {
  name = 'addRiskColumnsVesselCompanyFeedback1752405619484';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add potentialRisk column (smallint, nullable)
    await queryRunner.query(
      `ALTER TABLE "public"."vessel_company_feedback" ADD "potentialRisk" smallint`,
    );

    // Add potentialScore column (real, nullable)
    await queryRunner.query(
      `ALTER TABLE "public"."vessel_company_feedback" ADD "potentialScore" real`,
    );

    // Add observedRisk column (smallint, nullable)
    await queryRunner.query(
      `ALTER TABLE "public"."vessel_company_feedback" ADD "observedRisk" smallint`,
    );

    // Add observedScore column (real, nullable)
    await queryRunner.query(
      `ALTER TABLE "public"."vessel_company_feedback" ADD "observedScore" real`,
    );

    // Add finalRisk column (smallint, nullable)
    await queryRunner.query(
      `ALTER TABLE "public"."vessel_company_feedback" ADD "finalRisk" smallint`,
    );

    // Add finalScore column (real, nullable)
    await queryRunner.query(`ALTER TABLE "public"."vessel_company_feedback" ADD "finalScore" real`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
