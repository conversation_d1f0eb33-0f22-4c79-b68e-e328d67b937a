import { MigrationInterface, QueryRunner } from 'typeorm';

export class createIncidentCategoryTables1751531432357 implements MigrationInterface {
  name = 'createIncidentCategoryTables1751531432357';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "incident_main_category" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "deleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "causeType" character varying NOT NULL, "mainCategoryId" uuid NOT NULL, "mainCategoryName" character varying NOT NULL, "causeMappingId" uuid NOT NULL, "incidentInvestigationId" uuid NOT NULL, "companyId" uuid NOT NULL, "createdUserId" uuid NOT NULL, "updatedUserId" uuid, CONSTRAINT "PK_bb496dbabfe89351bd2c838bbd2" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "incident_main_category" ADD CONSTRAINT "FK_2c11bb049b5a784964aed8916e5" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "incident_main_category" ADD CONSTRAINT "FK_48c9c4e5b4c3430787f87b0e102" FOREIGN KEY ("createdUserId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "incident_main_category" ADD CONSTRAINT "FK_adcb2b1059ccfa1ca45742624d8" FOREIGN KEY ("updatedUserId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "incident_main_category" ADD CONSTRAINT "FK_9220821d61f92216d6cca1b13af" FOREIGN KEY ("causeMappingId") REFERENCES "cause_mapping"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "incident_main_category" ADD CONSTRAINT "FK_aca011f2c769287fcd834df0371" FOREIGN KEY ("incidentInvestigationId") REFERENCES "incident_investigation"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `CREATE TABLE "incident_second_sub_category" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "deleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "secSubCategoryId" uuid NOT NULL, "secSubCategoryName" character varying NOT NULL, "companyId" uuid NOT NULL, "createdUserId" uuid NOT NULL, "updatedUserId" uuid, "incidentSubCategoryId" uuid, CONSTRAINT "PK_68d6668d908cbba1eddddac0b14" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "incident_sub_category" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "deleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "subCategoryId" uuid NOT NULL, "subCategoryName" character varying NOT NULL, "incidentMainCategoryId" uuid NOT NULL, "companyId" uuid NOT NULL, "createdUserId" uuid NOT NULL, "updatedUserId" uuid, CONSTRAINT "PK_0e8c2c6d9c96b42c82ff6382354" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "incident_second_sub_category" ADD CONSTRAINT "FK_0f66a6f0e625cd7ae25e56a7501" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "incident_second_sub_category" ADD CONSTRAINT "FK_39263483a36df17ea7d3fbc4e43" FOREIGN KEY ("createdUserId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "incident_second_sub_category" ADD CONSTRAINT "FK_3df6b198bc4df4281d5c80eefd2" FOREIGN KEY ("updatedUserId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "incident_second_sub_category" ADD CONSTRAINT "FK_ab5a5d93b801bd2c40bcf6ce483" FOREIGN KEY ("incidentSubCategoryId") REFERENCES "incident_sub_category"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "incident_sub_category" ADD CONSTRAINT "FK_7036e357d3664e61cbe4a5681a3" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "incident_sub_category" ADD CONSTRAINT "FK_0f8f8d9c34d015d8c33b8dc3ca7" FOREIGN KEY ("createdUserId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "incident_sub_category" ADD CONSTRAINT "FK_3b55eba7978296b72bb0e6bd443" FOREIGN KEY ("updatedUserId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "incident_sub_category" ADD CONSTRAINT "FK_8b6bf9775f41c1d05a3a200451c" FOREIGN KEY ("incidentMainCategoryId") REFERENCES "incident_main_category"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
