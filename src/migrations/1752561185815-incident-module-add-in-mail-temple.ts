import { MigrationInterface, QueryRunner } from 'typeorm';

export class incidentModuleAddInMailTemple1752561185815 implements MigrationInterface {
  name = 'incidentModuleAddInMailTemple1752561185815';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TYPE "public"."mail_template_module_enum" RENAME TO "mail_template_module_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."mail_template_module_enum" AS ENUM('Planning', 'Inspection Report', 'Inspection Follow Up', 'Report of Finding', 'CAP Submission', 'Incident', 'Car', 'Cap', 'Finding Item')`,
    );
    await queryRunner.query(
      `ALTER TABLE "public"."mail_template" ALTER COLUMN "module" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "public"."mail_template" ALTER COLUMN "module" TYPE "public"."mail_template_module_enum" USING "module"::"text"::"public"."mail_template_module_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "public"."mail_template" ALTER COLUMN "module" SET DEFAULT 'Planning'`,
    );
    await queryRunner.query(`DROP TYPE "public"."mail_template_module_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
