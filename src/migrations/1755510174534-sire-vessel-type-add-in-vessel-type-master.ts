import {MigrationInterface, QueryRunner} from "typeorm";

export class sireVesselTypeAddInVesselTypeMaster1755510174534 implements MigrationInterface {
    name = 'sireVesselTypeAddInVesselTypeMaster1755510174534'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "public"."vessel_type" ADD "isSIRECVIQ" boolean NOT NULL DEFAULT false`);
        await queryRunner.query(`CREATE TYPE "public"."vessel_type_sirevesseltype_enum" AS ENUM('LNG', 'LPG', 'Petroleum', 'Chemical', 'Non-Tankers')`);
        await queryRunner.query(`ALTER TABLE "public"."vessel_type" ADD "sireVesselType" "public"."vessel_type_sirevesseltype_enum"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {}

}
