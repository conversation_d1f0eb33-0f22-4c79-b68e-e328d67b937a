import { MigrationInterface, QueryRunner } from 'typeorm';

export class createWhiteGreyBlackListTable1754313552658 implements MigrationInterface {
  name = 'createWhiteGreyBlackListTable1754313552658';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "black_list_country" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "deleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "countryCode" text NOT NULL, "countryName" text NOT NULL, "countryId" integer NOT NULL, CONSTRAINT "REL_cfe2bd1b130bb375226bed8309" UNIQUE ("countryId"), CONSTRAINT "PK_5f8493ec59ba05ef4b774805181" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "grey_list_country" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "deleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "countryCode" text NOT NULL, "countryName" text NOT NULL, "countryId" integer NOT NULL, CONSTRAINT "REL_18df672880688f4b077852e981" UNIQUE ("countryId"), CONSTRAINT "PK_412313173b9bd6e61a2c5cbada1" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "white_list_country" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "deleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "countryCode" text NOT NULL, "countryName" text NOT NULL, "countryId" integer NOT NULL, CONSTRAINT "REL_8cf916332d2c33a01ba58f7fa1" UNIQUE ("countryId"), CONSTRAINT "PK_070efc8d98a5ea3d9a9126b41ac" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "black_list_country" ADD CONSTRAINT "FK_cfe2bd1b130bb375226bed83097" FOREIGN KEY ("countryId") REFERENCES "country"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "grey_list_country" ADD CONSTRAINT "FK_18df672880688f4b077852e9818" FOREIGN KEY ("countryId") REFERENCES "country"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "white_list_country" ADD CONSTRAINT "FK_8cf916332d2c33a01ba58f7fa1e" FOREIGN KEY ("countryId") REFERENCES "country"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
