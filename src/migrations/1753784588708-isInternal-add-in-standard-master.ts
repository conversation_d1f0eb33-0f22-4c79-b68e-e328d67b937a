import { MigrationInterface, QueryRunner } from 'typeorm';

export class isInternalAddInStandardMaster1753784588708 implements MigrationInterface {
  name = 'isInternalAddInStandardMaster1753784588708';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "public"."standard_master" ADD "isInternal" boolean NOT NULL DEFAULT false`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
