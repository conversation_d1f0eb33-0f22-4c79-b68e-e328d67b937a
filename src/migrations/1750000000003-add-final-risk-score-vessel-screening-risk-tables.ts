import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddFinalRiskScoreVesselScreeningRiskTables1750000000003 implements MigrationInterface {
  name = 'AddFinalRiskScoreVesselScreeningRiskTables1750000000003';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add finalRisk and finalScore columns to all vessel screening risk tables
    const tables = [
      'external_inspections_request',
      'internal_inspections_request',
      'other_sms_records_request',
      'other_tech_records_request',
      'dry_docking_request',
      'maintenance_performance_request',
    ];

    for (const table of tables) {
      // Add finalRisk column with smallint type (nullable)
      await queryRunner.query(`ALTER TABLE "${table}" ADD "finalRisk" smallint`);

      // Add finalScore column with real type (nullable)
      await queryRunner.query(`ALTER TABLE "${table}" ADD "finalScore" real`);
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> { }
}