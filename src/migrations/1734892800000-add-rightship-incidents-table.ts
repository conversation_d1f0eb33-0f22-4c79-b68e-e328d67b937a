import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddRightshipIncidentsTable1734892800000 implements MigrationInterface {
  name = 'AddRightshipIncidentsTable1734892800000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "right_ship_incidents" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "deleted" boolean NOT NULL DEFAULT false,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        "incidentId" integer NOT NULL,
        "incidentDate" date NOT NULL,
        "imo" integer NOT NULL,
        "status" character varying,
        "detailedStatus" character varying,
        "incidentSource" character varying,
        "flagAtTimeOfIncident" character varying,
        "docAtTimeOfIncident" integer,
        "severity" character varying,
        "activeInSafetyScore" boolean NOT NULL DEFAULT false,
        "incidentMasterId" uuid,
        "assistanceGiven" character varying,
        "casualtyOrDemolition" character varying,
        "totalLoss" boolean NOT NULL DEFAULT false,
        "numberKilled" integer NOT NULL DEFAULT 0,
        "numberMissing" integer NOT NULL DEFAULT 0,
        "sustainedSignificantDamage" boolean NOT NULL DEFAULT false,
        "pollutionOccured" boolean NOT NULL DEFAULT false,
        "pollutionDetails" character varying,
        "numberSeriousInjuries" integer NOT NULL DEFAULT 0,
        "preciseText" text,
        "complimentaryText" text,
        "companyId" uuid NOT NULL,
        "vesselId" uuid,
        "rightShipId" uuid,
        CONSTRAINT "PK_right_ship_incidents" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_right_ship_incidents_incidentId" UNIQUE ("incidentId")
      )`,
    );

    await queryRunner.query(
      `CREATE INDEX "IDX_right_ship_incidents_imo" ON "right_ship_incidents" ("imo")`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_right_ship_incidents_companyId" ON "right_ship_incidents" ("companyId")`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_right_ship_incidents_vesselId" ON "right_ship_incidents" ("vesselId")`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_right_ship_incidents_incidentDate" ON "right_ship_incidents" ("incidentDate")`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_right_ship_incidents_incidentMasterId" ON "right_ship_incidents" ("incidentMasterId")`,
    );

    await queryRunner.query(
      `ALTER TABLE "right_ship_incidents" ADD CONSTRAINT "FK_right_ship_incidents_company" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "right_ship_incidents" ADD CONSTRAINT "FK_right_ship_incidents_vessel" FOREIGN KEY ("vesselId") REFERENCES "vessel"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "right_ship_incidents" ADD CONSTRAINT "FK_right_ship_incidents_right_ship" FOREIGN KEY ("rightShipId") REFERENCES "right_ship"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "right_ship_incidents" ADD CONSTRAINT "FK_right_ship_incidents_incident_master" FOREIGN KEY ("incidentMasterId") REFERENCES "incident_master"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
} 