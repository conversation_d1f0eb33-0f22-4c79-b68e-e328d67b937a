import { MigrationInterface, QueryRunner } from 'typeorm';

export class createVesselRiskRequestTable1754251115347 implements MigrationInterface {
  name = 'createVesselRiskRequestTable1754251115347';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "vessel_risk_request" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "vesselScreeningId" uuid NOT NULL, "potentialRisk" smallint, "potentialScore" real, "observedRisk" smallint, "observedScore" real, "finalRisk" smallint, "finalScore" real, "timeLoss" boolean, "vesselId" uuid, "count" integer NOT NULL DEFAULT '0', CONSTRAINT "PK_3ec6e003f7f6bf362aaef0962f7" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."vessel_screening_summary_reference_enum" RENAME TO "vessel_screening_summary_reference_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."vessel_screening_summary_reference_enum" AS ENUM('Condition of Class/Dispensations', 'Survey/Class Info', 'Maintenance Performance', 'Other Technical Records', 'Dry Docking', 'Incidents', 'Injuries', 'Other SMS Records', 'Port State Control', 'External Inspections', 'Internal Inspections/Audits', 'Safety Engagement', 'Plans and Drawings', 'Pilot/Terminal Feedback', 'Charterer Inspection', 'Vessel/Company Feedback', 'Right Ship', 'Vessel General Risk', 'Doc Holder', 'Charterer', 'Vessel Owner')`,
    );
    await queryRunner.query(
      `ALTER TABLE "public"."vessel_screening_summary" ALTER COLUMN "reference" TYPE "public"."vessel_screening_summary_reference_enum" USING "reference"::"text"::"public"."vessel_screening_summary_reference_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."vessel_screening_summary_reference_enum_old"`);
    await queryRunner.query(
      `ALTER TABLE "vessel_risk_request" ADD CONSTRAINT "FK_80124335d2c2d3405003f4831bd" FOREIGN KEY ("vesselScreeningId") REFERENCES "vessel_screening"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "vessel_risk_request" ADD CONSTRAINT "FK_82dace29e003099df7e90d5187b" FOREIGN KEY ("vesselId") REFERENCES "vessel"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(`ALTER TABLE "vessel" ADD "isITF" boolean`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
