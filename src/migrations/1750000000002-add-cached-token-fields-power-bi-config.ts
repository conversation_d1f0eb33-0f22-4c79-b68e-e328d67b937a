import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddCachedTokenFieldsPowerBiConfig1750000000002 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add cached token fields to power_bi_config table
    await queryRunner.query(`
      ALTER TABLE "power_bi_config"
      ADD COLUMN "accessToken" text NULL,
      ADD COLUMN "tokenExpiration" timestamp NULL;

      COMMENT ON COLUMN "power_bi_config"."accessToken" IS 'Cached Power BI access token';
      COMMENT ON COLUMN "power_bi_config"."tokenExpiration" IS 'Cached Power BI token expiration timestamp';
    `);
  }

  public async down(): Promise<void> {}
}
