import { MigrationInterface, QueryRunner } from 'typeorm';

export class scoreStringAddIncidentInvestigation1754979265970 implements MigrationInterface {
  name = 'scoreStringAddIncidentInvestigation1754979265970';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."incident_investigation_potentialriskstate_enum" AS ENUM('VERY LOW', 'LOW', 'MEDIUM', 'HIGH', 'VERY HIGH')`,
    );
    await queryRunner.query(
      `ALTER TABLE "public"."incident_investigation" ADD "potentialRiskState" "public"."incident_investigation_potentialriskstate_enum"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."incident_investigation_observedriskstate_enum" AS ENUM('VERY LOW', 'LOW', 'MEDIUM', 'HIGH', 'VERY HIGH')`,
    );
    await queryRunner.query(
      `ALTER TABLE "public"."incident_investigation" ADD "observedRiskState" "public"."incident_investigation_observedriskstate_enum"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."incident_investigation_finalriskstate_enum" AS ENUM('VERY LOW', 'LOW', 'MEDIUM', 'HIGH', 'VERY HIGH')`,
    );
    await queryRunner.query(
      `ALTER TABLE "public"."incident_investigation" ADD "finalRiskState" "public"."incident_investigation_finalriskstate_enum"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
