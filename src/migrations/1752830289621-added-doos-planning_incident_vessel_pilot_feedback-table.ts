import { MigrationInterface, QueryRunner } from 'typeorm';

export class addedDoosPlanningIncidentVesselPilotFeedbackTable1752830289621
  implements MigrationInterface
{
  name = 'addedDoosPlanningIncidentVesselPilotFeedbackTable1752830289621';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "public"."pilot_terminal_feedback" ADD "doos" character varying(3)`,
    );
    await queryRunner.query(
      `ALTER TABLE "public"."vessel_company_feedback" ADD "doos" character varying(3)`,
    );
    await queryRunner.query(
      `ALTER TABLE "public"."incident_investigation" ADD "doos" character varying(3)`,
    );
    await queryRunner.query(
      `ALTER TABLE "public"."planning_request" ADD "doos" character varying(3)`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
