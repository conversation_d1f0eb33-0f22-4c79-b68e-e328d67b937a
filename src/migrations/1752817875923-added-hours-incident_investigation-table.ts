import { MigrationInterface, QueryRunner } from 'typeorm';

export class addedHoursIncidentInvestigationTable1752817875923 implements MigrationInterface {
  name = 'addedHoursIncidentInvestigationTable1752817875923';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "public"."incident_investigation" ADD "hours" smallint DEFAULT '0'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
