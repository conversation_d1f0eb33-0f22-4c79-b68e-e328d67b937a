import { MigrationInterface, QueryRunner } from 'typeorm';

export class createChartererInspectionRequestTable1754569684881 implements MigrationInterface {
  name = 'createChartererInspectionRequestTable1754569684881';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "charterer_inspection_request" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "vesselScreeningId" uuid NOT NULL, "potentialRisk" smallint, "potentialScore" real, "observedRisk" smallint, "observedScore" real, "finalRisk" smallint, "finalScore" real, "timeLoss" boolean, "vesselId" uuid, CONSTRAINT "PK_c78d227e21832078a460ab13018" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "charterer_inspection_request" ADD CONSTRAINT "FK_35c7f70a7a765c951127b2f4686" FOREIGN KEY ("vesselScreeningId") REFERENCES "vessel_screening"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "charterer_inspection_request" ADD CONSTRAINT "FK_e9fb5d7c9384e41dd6d257c9c05" FOREIGN KEY ("vesselId") REFERENCES "vessel"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
