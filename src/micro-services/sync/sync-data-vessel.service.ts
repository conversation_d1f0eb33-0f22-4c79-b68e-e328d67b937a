import { Injectable } from '@nestjs/common';
import axios, { AxiosRequestConfig } from 'axios';
import { LoggerCommon } from 'svm-nest-lib-v3';
import APP_CONFIG from '../../configs/app.config';

@Injectable()
export class SyncDataVesselService {
  public static async connectLoginService() {
    try {
      const paramLogin = {
        username: APP_CONFIG.ENV.CONFIG_SYNC_DATA_VESSEL.USERNAME,
        password: APP_CONFIG.ENV.CONFIG_SYNC_DATA_VESSEL.PASSWORD,
      };
      return await axios({
        url: `${APP_CONFIG.ENV.CONFIG_SYNC_DATA_VESSEL.URL}/login`,
        headers: {
          // Accept: 'application/json',
          // 'Content-Type': 'application/json',
        },
        method: 'POST',
        data: paramLogin,
      })
        .then((re) => {
          console.log(re.data);
          return re.data;
        })
        .catch((e) => console.log(e));
    } catch (ex) {
      LoggerCommon.error('[SyncDataVesselService] connectLoginService error: ', ex.message || ex);
      throw ex;
    }
  }

  public static async getDataVesselMaster(token: string, fromDate?: string, toDate?: string) {
    try {
      const configAxios: AxiosRequestConfig = {
        url: `${APP_CONFIG.ENV.CONFIG_SYNC_DATA_VESSEL.URL}/vessel-master/retrieve-data`,
        headers: {
          // Accept: 'application/json',
          // 'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        method: 'POST',
      };
      if (fromDate && toDate) {
        Object.assign(configAxios, { data: { from_date: fromDate, to_date: toDate } });
      }
      return await axios(configAxios)
        .then((re) => {
          // console.log(re);
          return re.data;
        })
        .catch((e) => console.log(e.data));
    } catch (ex) {
      LoggerCommon.error('[SyncDataVesselService] getDataVesselMaster error: ', ex.message || ex);
      throw ex;
    }
  }

  public static async getDataRightShip(token: string, fromDate?: string, toDate?: string) {
    try {
      const configAxios: AxiosRequestConfig = {
        url: `${APP_CONFIG.ENV.CONFIG_SYNC_DATA_VESSEL.URL}/right-ship/retrieve-data`,
        headers: {
          // Accept: 'application/json',
          // 'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        method: 'POST',
      };
      if (fromDate && toDate) {
        Object.assign(configAxios, { data: { from_date: fromDate, to_date: toDate } });
      }
      return await axios(configAxios)
        .then((re) => {
          // console.log(re);
          return re.data;
        })
        .catch((e) => console.log(e.data));
    } catch (ex) {
      LoggerCommon.error('[SyncDataVesselService] getDataRightShip error: ', ex.message || ex);
      throw ex;
    }
  }

  public static async getDataVoyage(token: string, fromDate?: string, toDate?: string) {
    try {
      const configAxios: AxiosRequestConfig = {
        url: `${APP_CONFIG.ENV.CONFIG_SYNC_DATA_VESSEL.URL}/voyage-master/retrieve-data`,
        headers: {
          // Accept: 'application/json',
          // 'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        method: 'POST',
      };
      if (fromDate && toDate) {
        Object.assign(configAxios, { data: { from_date: fromDate, to_date: toDate } });
      }
      return await axios(configAxios)
        .then((re) => {
          // console.log(re);
          return re.data;
        })
        .catch((e) => console.log(e.data));
    } catch (ex) {
      LoggerCommon.error('[SyncDataVesselService] getDataVoyage error: ', ex.message || ex);
      throw ex;
    }
  }

  public static async getDataVoyageMasterDetails(
    token: string,
    fromDate?: string,
    toDate?: string,
  ) {
    try {
      const configAxios: AxiosRequestConfig = {
        url: `${APP_CONFIG.ENV.CONFIG_SYNC_DATA_VESSEL.URL}/voyage-master-details/retrieve-data`,
        headers: {
          // Accept: 'application/json',
          // 'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        method: 'POST',
      };
      if (fromDate && toDate) {
        Object.assign(configAxios, { data: { from_date: fromDate, to_date: toDate } });
      }
      return await axios(configAxios)
        .then((re) => {
          // console.log(re);
          return re.data;
        })
        .catch((e) => console.log(e.data));
    } catch (ex) {
      LoggerCommon.error(
        '[SyncDataVesselService] getDataVoyageMasterDetails error: ',
        ex.message || ex,
      );
      throw ex;
    }
  }

  public static async getDataRightShipIncidents(token: string, fromDate?: string, toDate?: string) {
    try {
      const configAxios: AxiosRequestConfig = {
        url: `${APP_CONFIG.ENV.CONFIG_SYNC_DATA_VESSEL.URL}/rightship-incidents/retrieve-data`,
        headers: {
          // Accept: 'application/json',
          // 'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        method: 'POST',
      };
      if (fromDate && toDate) {
        Object.assign(configAxios, { data: { from_date: fromDate, to_date: toDate } });
      }
      return await axios(configAxios)
        .then((re) => {
          // console.log(re);
          return re.data;
        })
        .catch((e) => console.log(e.data));
    } catch (ex) {
      LoggerCommon.error(
        '[SyncDataVesselService] getDataVoyageMasterDetails error: ',
        ex.message || ex,
      );
      throw ex;
    }
  }
}
