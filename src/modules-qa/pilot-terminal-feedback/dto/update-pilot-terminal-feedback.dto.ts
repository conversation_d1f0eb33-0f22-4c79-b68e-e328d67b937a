import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsDateString,
  IsEnum,
  IsIn,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsUUID,
  MaxLength,
  ValidateNested,
} from 'class-validator';

import { Transform, Type } from 'class-transformer';

import { PilotTerminalFeedbackCheckListDto } from './create-pilot-terminal-feedback.dto';
import { PTFCommentDto } from './PTF-comment.dto';
import { VesselScreeningObservedRiskEnum } from '../../../commons/enums';
import { PilotTerminalFeedbackStatusEnum } from '../entity/pilot-terminal-feedback.entity';
import { decryptAttachmentValues } from '../../../commons/functions';

export class UpdatePilotTerminalFeedbackDto {
  @ApiProperty({ type: 'string' })
  @IsOptional()
  @IsUUID()
  vesselId?: string;

  @ApiProperty({ type: 'string' })
  @IsOptional()
  feedbackType?: string;

  @ApiProperty({
    type: 'string',
    description: 'ISO string',
    example: '2011-10-05T14:48:00.000Z',
    required: false,
  })
  @IsOptional()
  // @Transform((value: string) => value.substr(0, 10))
  @IsDateString({ strict: true })
  // @IsAfterCurrentDayTZ('timezone')
  dateOfInteraction?: string;

  @ApiProperty({ type: 'string' })
  @IsOptional()
  @IsUUID()
  terminalId?: string;

  @ApiProperty({ type: 'string' })
  @IsOptional()
  @IsUUID()
  portId?: string;

  // @ApiProperty({ type: 'string' })
  // @IsOptional()
  // country?: string;

  @ApiProperty({ type: 'number', required: false })
  @IsOptional()
  @IsNumber()
  countryId: number;

  @ApiProperty({ type: 'string' })
  @IsOptional()
  @MaxLength(32)
  pilotAgeArea?: string;

  @ApiProperty({ type: 'string' })
  @IsOptional()
  @MaxLength(2000)
  feedBack?: string;

  @ApiProperty({ type: [String], required: false })
  @IsOptional()
  @IsArray()
  @Transform((value: string[]) => {
    const newValue = decryptAttachmentValues(value);
    return newValue;
  })
  attachments: string[] = [];

  @Type(() => PilotTerminalFeedbackCheckListDto)
  @ApiProperty({
    type: [PilotTerminalFeedbackCheckListDto],
    description: 'Check list',
    required: false,
  })
  @ValidateNested({ each: true })
  @IsOptional()
  pilotTerminalFeedbackChecklists?: PilotTerminalFeedbackCheckListDto[];

  @ApiProperty({ type: 'number', required: false })
  @IsOptional()
  @IsInt()
  @IsIn(Object.values(VesselScreeningObservedRiskEnum))
  public observedRisk?: VesselScreeningObservedRiskEnum;

  @ApiProperty({
    type: 'number',
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  observedScore?: number;

  @ApiProperty({ type: Boolean, required: false })
  @IsOptional()
  @Type(() => Boolean)
  @IsBoolean()
  timeLoss?: boolean;

  @ApiProperty({ type: [PTFCommentDto], required: false })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => PTFCommentDto)
  @IsArray()
  @ArrayMinSize(1)
  comments?: PTFCommentDto[];

  @ApiProperty({ type: 'string', enum: PilotTerminalFeedbackStatusEnum })
  @IsOptional()
  @IsEnum(PilotTerminalFeedbackStatusEnum)
  status?: PilotTerminalFeedbackStatusEnum;

  @ApiProperty({ type: 'string' })
  @IsUUID()
  voyageTypeId: string;

  @ApiProperty({ type: 'string', required: false, enum: ['Yes', 'No'] })
  @IsOptional()
  @IsIn(['Yes', 'No'])
  doos?: string;
}
