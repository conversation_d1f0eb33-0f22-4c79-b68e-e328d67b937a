import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsOptional, IsUUID } from 'class-validator';
import { ListQueryDto } from '../../../commons/dtos';
import { IsGreaterThanOrEqual } from 'svm-nest-lib-v3';
import { DataType, FilterField } from '../../../utils';
export class ListPilotTerminalFeedbackDto extends ListQueryDto {
  @ApiProperty({ type: String, required: false })
  @IsOptional()
  @IsUUID('all')
  vesselId: string;

  @ApiProperty({ type: String, required: false })
  @IsOptional()
  feedbackType?: string;

  @ApiProperty({
    type: 'string',
    description: 'ISO 8601 date time',
    example: '2021-11-21T12:09:32.142z',
    required: false,
  })
  @IsOptional()
  @IsDateString({ strict: true })
  interactionDateFrom?: string;

  @ApiProperty({
    type: 'string',
    description: 'ISO 8601 date time',
    example: '2021-11-28T12:09:32.142z',
    required: false,
  })
  @IsOptional()
  @IsDateString({ strict: true })
  @IsGreaterThanOrEqual('interactionDateFrom', { message: 'common.INVALID_DATE_RANGE' })
  interactionDateTo?: string;
}

export const PILOT_TERMINAL_FEED_BACK_FILTER_FIELDS: FilterField[] = [
  {
    field: 'refId',
    column: '"pilotTerminalFeedback_refId"',
    type: DataType.TEXT,
  },
  {
    field: 'vesselName',
    column: '"vessel_name"',
    type: DataType.TEXT,
  },
  {
    field: 'imo',
    column: '"vessel_imoNumber"',
    type: DataType.TEXT,
  },
  {
    field: 'riskScore',
    column: '"pilotTerminalFeedback_score"',
    type: DataType.NUMBER,
  },
  {
    field: 'status',
    column: '"pilotTerminalFeedback_status"',
    type: DataType.TEXT,
  },
  {
    field: 'feedbackType',
    column: '"pilotTerminalFeedback_feedbackType"',
    type: DataType.TEXT,
  },
  {
    field: 'feedbackByUser',
    column: '"createdUser_username"',
    type: DataType.TEXT,
  },
  {
    field: 'terminal',
    column: '"terminal_name"',
    type: DataType.TEXT,
  },
  {
    field: 'port',
    column: '"port_name"',
    type: DataType.TEXT,
  },
  {
    field: 'country',
    column: '"port_name"',
    type: DataType.TEXT,
  },
  {
    field: 'pilotage',
    column: '"pilotTerminalFeedback_pilotAgeArea"',
    type: DataType.TEXT,
  },
  {
    field: 'createdByUser',
    column: '"createdUser_username"',
    type: DataType.TEXT,
  },
  {
    field: 'createdDate',
    column: '"pilotTerminalFeedback_createdAt"',
    type: DataType.DATE,
  },
  {
    field: 'updatedByUser',
    column: '"updatedUser_username"',
    type: DataType.TEXT,
  },
  {
    field: 'updatedDate',
    column: '"pilotTerminalFeedback_updatedAt"',
    type: DataType.DATE,
  },
  {
    field: 'dateOfInteraction',
    column: '"pilotTerminalFeedback_dateOfInteraction"',
    type: DataType.DATE,
  },
  {
    field: 'dateOfInteraction_Month',
    column: '"pilotTerminalFeedback_dateOfInteraction_Month"',
    type: DataType.TEXT,
  },
  {
    field: 'dateOfInteraction_Year',
    column: '"pilotTerminalFeedback_dateOfInteraction_Year"',
    type: DataType.TEXT,
  },
  {
    field: 'voyageType',
    column: '"voyageType_name"',
    type: DataType.TEXT,
  },
  {
    field: 'doos',
    column: '"pilotTerminalFeedback_doos"',
    type: DataType.TEXT,
  },
];

export enum TitlePilotTerminalFeedback {
  VESSEL_NAME = 'Vessel name',
  IMO = 'IMO',
  RISK_SCORE = 'Risk score',
  FEEDBACK_TYPE = 'Feedback Type',
  FEEDBACK_BY_USER = 'Feedback by User',
  STATUS = 'Status',
  DATE_OF_INTERACTION = 'Date of Interaction',
  TERMINAL = 'Terminal',
  PORT = 'Port',
  COUNTRY = 'Country',
  PILOTAGE = 'Pilotage',
  CREATED_DATE = 'Created Date',
  UPDATED_DATE = 'Updated Date',
  UPDATED_BY_USER = 'Updated by User',
  REF_ID = 'Ref.ID',
  DATE_OF_INTERACTION_MONTH = 'dateOfInteraction_Month',
  DATE_OF_INTERACTION_YEAR = 'dateOfInteraction_Year',
}
