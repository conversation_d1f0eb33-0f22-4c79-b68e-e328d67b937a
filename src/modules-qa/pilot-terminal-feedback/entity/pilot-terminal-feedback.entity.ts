import { IdentifyEntity } from 'svm-nest-lib-v3';
import { AfterLoad, Column, Entity, ManyToOne, OneToMany } from 'typeorm';
import {
  VesselScreeningObservedRiskEnum,
  VesselScreeningPotentialRiskEnum,
} from '../../../commons/enums';
import { hashAttachmentValues } from '../../../commons/functions';
import { Company } from '../../../modules/company/company.entity';
import { PortMaster } from '../../../modules/port-master/port-master.entity';
import { User } from '../../../modules/user/user.entity';
import { Vessel } from '../../../modules/vessel/entity/vessel.entity';
import { Terminal } from '../../terminal/terminal.entity';
import { PilotTerminalFeedbackChecklist } from './pilot-terminal-feedback-checklist.entity';
import { PilotTerminalFeedbackHistory } from './pilot-terminal-feedback-history.entity';
import { PTFComment } from './PTF-comment.entity';
import { Country } from '../../../modules/country/country.entity';
import { VoyageType } from '../../../modules/voyage-type/entities/voyage-type.entity';

export enum PilotTerminalFeedbackStatusEnum {
  DRAFT = 'Draft',
  SUBMITTED = 'Submitted',
}

@Entity()
export class PilotTerminalFeedback extends IdentifyEntity {
  @Column({ type: 'uuid', nullable: true })
  public vesselId: string;

  @Column()
  public feedbackType: string;

  @Column({ type: 'timestamp' })
  public dateOfInteraction: Date;

  @Column({ type: 'integer', nullable: true })
  public dateOfInteraction_Year: number;

  @Column({ type: 'varchar', length: 3, nullable: true })
  public dateOfInteraction_Month: string;

  @Column()
  public refId: string;

  @Column({ type: 'uuid', nullable: true })
  public portId?: string;

  @Column({ type: 'uuid', nullable: true })
  public terminalId?: string;

  // @Column({ nullable: true })
  // public country: string;

  @Column({ type: 'int4', nullable: true })
  public countryId: number;

  @Column({ nullable: true })
  public pilotAgeArea: string;

  @Column({ nullable: true })
  public feedBack: string;

  @Column({
    type: 'float4',
    nullable: true,
  })
  public score: number;

  @Column({
    type: 'smallint',
    enum: VesselScreeningPotentialRiskEnum,
    // default: VesselScreeningPotentialRiskEnum.LOW,
    nullable: true,
  })
  public potentialRisk: VesselScreeningPotentialRiskEnum;

  @Column({
    type: 'float4',
    nullable: true,
  })
  public potentialScore: number;

  @Column({
    type: 'smallint',
    enum: VesselScreeningObservedRiskEnum,
    // default: VesselScreeningObservedRiskEnum.LOW,
    nullable: true,
  })
  public observedRisk: VesselScreeningObservedRiskEnum;

  @Column({
    type: 'float4',
    nullable: true,
  })
  public observedScore: number;

  @Column({
    // default: false,
    nullable: true,
  })
  public timeLoss: boolean;

  @Column({ default: false, nullable: true })
  public updatedObservedRisk: boolean;

  @Column({
    type: 'uuid',
    array: true,
    default: [],
  })
  public attachments: string[];

  @Column({
    nullable: true,
    enum: PilotTerminalFeedbackStatusEnum,
    default: PilotTerminalFeedbackStatusEnum.DRAFT,
    type: 'enum',
  })
  public status: PilotTerminalFeedbackStatusEnum;

  @Column({ type: 'uuid' })
  public companyId: string;

  @Column({ type: 'uuid', nullable: true })
  public createdUserId: string;

  @Column({ type: 'uuid', nullable: true })
  public updatedUserId?: string;

  @Column({ type: 'uuid', nullable: true })
  public voyageTypeId: string;

  @Column({ type: 'varchar', length: 3, nullable: true })
  public doos?: string;
  //Relation

  @ManyToOne(() => Company, { onDelete: 'CASCADE' })
  company: Company;

  @ManyToOne(() => Vessel, {
    onDelete: 'NO ACTION',
  })
  vessel: Vessel;

  @ManyToOne(() => PortMaster, {
    onDelete: 'NO ACTION',
  })
  port: PortMaster;

  @ManyToOne(() => Terminal, {
    onDelete: 'NO ACTION',
  })
  terminal: Terminal;

  @ManyToOne(() => User, { onDelete: 'NO ACTION' })
  createdUser: User;

  @ManyToOne(() => User, { onDelete: 'NO ACTION' })
  updatedUser: User;

  @ManyToOne(() => Country, { onDelete: 'NO ACTION' })
  country: Country;

  @OneToMany(
    () => PilotTerminalFeedbackHistory,
    (pilotTerminalFeedbackHistory) => pilotTerminalFeedbackHistory.pilotTerminalFeedback,
  )
  pilotTerminalFeedbackHistories: PilotTerminalFeedbackHistory[];

  @OneToMany(
    () => PilotTerminalFeedbackChecklist,
    (pilotTerminalFeedbackChecklist) => pilotTerminalFeedbackChecklist.pilotTerminalFeedback,
  )
  pilotTerminalFeedbackChecklists: PilotTerminalFeedbackChecklist[];

  @OneToMany(() => PTFComment, (PTFComment) => PTFComment.pilotTerminalFeedback)
  PTFComments: PTFComment[];

  @ManyToOne(() => VoyageType, { onDelete: 'NO ACTION' })
  voyageType: VoyageType;

  @AfterLoad()
  async transformAttachment() {
    this.attachments = await hashAttachmentValues(this.attachments);
  }
}
