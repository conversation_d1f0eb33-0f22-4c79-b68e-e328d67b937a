import {
  VesselScreeningSummary,
  VesselScreeningSummaryReferenceEnum,
  VesselScreeningSummaryStatusEnum,
  VesselScreeningSummaryTabNameEnum,
} from '../entity/vessel-screening-summary.entity';

export class VesselScreeningSummaryBusiness {
  static preparedDataCreateVesselScreeningSummary(vesselScreeningId: string, isPending?: boolean) {
    const references = Object.values(VesselScreeningSummaryReferenceEnum);
    const preparedVesselScreeningSummary: VesselScreeningSummary[] = [];
    for (const reference of references) {
      const vesselScreeningSummary = {
        vesselScreeningId: vesselScreeningId,
        status: VesselScreeningSummaryStatusEnum.OPEN,
        reference: reference,
      } as VesselScreeningSummary;
      if (
        reference === VesselScreeningSummaryReferenceEnum.SURVEY_CLASS_INFO ||
        reference === VesselScreeningSummaryReferenceEnum.CONDITION_OF_CLASS_DISPENSATIONS ||
        reference === VesselScreeningSummaryReferenceEnum.MAINTENANCE_PERFORMANCE ||
        reference === VesselScreeningSummaryReferenceEnum.OTHER_TECHNICAL_RECORDS ||
        reference === VesselScreeningSummaryReferenceEnum.DRY_DOCKING
      ) {
        vesselScreeningSummary.tabName = VesselScreeningSummaryTabNameEnum.TECHNICAL;
      } else if (
        reference === VesselScreeningSummaryReferenceEnum.INJURIES ||
        reference === VesselScreeningSummaryReferenceEnum.OTHER_SMS_RECORDS ||
        reference === VesselScreeningSummaryReferenceEnum.INCIDENTS
      ) {
        vesselScreeningSummary.tabName = VesselScreeningSummaryTabNameEnum.SAFETY_MANAGEMENT;
      } else if (
        reference === VesselScreeningSummaryReferenceEnum.PORT_STATE_CONTROL ||
        reference === VesselScreeningSummaryReferenceEnum.EXTERNAL_INSPECTIONS ||
        reference === VesselScreeningSummaryReferenceEnum.INTERNAL_INSPECTIONS_AUDITS || 
        reference === VesselScreeningSummaryReferenceEnum.CHARTERER_INSPECTION
      ) {
        vesselScreeningSummary.tabName = VesselScreeningSummaryTabNameEnum.INSPECTIONS;
      } else if (reference === VesselScreeningSummaryReferenceEnum.SAFETY_ENGAGEMENT) {
        vesselScreeningSummary.tabName = VesselScreeningSummaryTabNameEnum.SAFETY_ENGAGEMENT;
      } else if (
        reference === VesselScreeningSummaryReferenceEnum.PLAN_AND_DRAWINGS ||
        reference === VesselScreeningSummaryReferenceEnum.VESSEL_GENERAL_RISK ||
        reference === VesselScreeningSummaryReferenceEnum.DOC_HOLDER ||
        reference === VesselScreeningSummaryReferenceEnum.CHARTERER ||
        reference === VesselScreeningSummaryReferenceEnum.VESSEL_OWNER
      ) {
        vesselScreeningSummary.tabName = VesselScreeningSummaryTabNameEnum.SHIP_PARTICULARS;
        if (isPending) {
          vesselScreeningSummary.status = VesselScreeningSummaryStatusEnum.PENDING_INFO;
        }
      } else if (reference === VesselScreeningSummaryReferenceEnum.PILOT_TERMINAL_FEEDBACK) {
        vesselScreeningSummary.tabName = VesselScreeningSummaryTabNameEnum.PILOT_TERMINAL_FEEDBACK;
      } else if (reference === VesselScreeningSummaryReferenceEnum.VESSEL_COMPANY_FEEDBACK) {
        vesselScreeningSummary.tabName = VesselScreeningSummaryTabNameEnum.PILOT_TERMINAL_FEEDBACK;
      } else if (reference === VesselScreeningSummaryReferenceEnum.RIGHT_SHIP) {
        vesselScreeningSummary.tabName = VesselScreeningSummaryTabNameEnum.RIGHT_SHIP;
      }

      preparedVesselScreeningSummary.push(vesselScreeningSummary);
    }
    return preparedVesselScreeningSummary;
  }
}
