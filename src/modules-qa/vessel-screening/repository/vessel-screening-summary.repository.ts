import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Token<PERSON>ayload<PERSON>odel, TypeORMRepository } from 'svm-nest-lib-v3';
import { Connection, EntityManager, EntityRepository } from 'typeorm';
import {
  PilotTerminalFeedbackStatusEnum,
  VesselScreeningPotentialRiskEnum,
  VesselCompanyFeedbackStatusEnum,
} from '../../../commons/enums';
import { ClassDispensationsRequest } from '../../class-dispensations/entity/class-dispensations-request.entity';
import {
  IncidentInvestigation,
  IncidentInvestigationStatus,
} from '../../incident-investigation/entity/incident-investigation.entity';
import { InjuryRequest } from '../../injury/entity/injury-request.entity';
import { PilotTerminalFeedback } from '../../pilot-terminal-feedback/entity/pilot-terminal-feedback.entity';
import { PortStateControlRequest } from '../../port-state-control/entity/port-state-control-request.entity';
import { SurveyClassInfoRequest } from '../../survey/survey-class-info/entity/survey-class-info-request.entity';
import { VesselCompanyFeedback } from '../../vessel-company-feedback/entity/vessel-company-feedback.entity';
import { UpdateVesselScreeningSummaryDto } from '../dto/update-vessel-screening-summary.dto';
import {
  VesselScreeningSummary,
  VesselScreeningSummaryReferenceEnum,
  VesselScreeningSummaryStatusEnum,
  VesselScreeningSummaryReviewStatusEnum,
} from '../entity/vessel-screening-summary.entity';
import {
  VesselScreening,
  VesselScreeningReviewStatusEnum,
} from '../entity/vessel-screening.entity';
import { VesselRiskRequest } from 'src/modules/vessel/entity/vessel-risk-request.entity';
import { ChartererInspectionRequest } from 'src/modules/corrective-action-request/entities/charterer-inspection-request.entity';

@EntityRepository(VesselScreeningSummary)
export class VesselScreeningSummaryRepository extends TypeORMRepository<VesselScreeningSummary> {
  constructor(private readonly connection: Connection) {
    super();
  }

  // common update vsm
  async updateVesselScreeningSummaryByRef(
    manager: EntityManager,
    reference: string,
    vesselScreeningId: string,
    vesselId?: string,
    companyId?: string,
    isChangeRiskValue?: boolean,
  ) {
    let vesselScreeningSummary;
    let vesselScreeningSummaryIds: { id: string }[] = [];
    if (
      reference === VesselScreeningSummaryReferenceEnum.INCIDENTS ||
      reference === VesselScreeningSummaryReferenceEnum.PILOT_TERMINAL_FEEDBACK ||
      reference === VesselScreeningSummaryReferenceEnum.VESSEL_COMPANY_FEEDBACK
    ) {
      vesselScreeningSummaryIds = await this.createQueryBuilder('vesselScreeningSummary')
        .leftJoin('vesselScreeningSummary.vesselScreening', 'vesselScreening')
        .where(
          'vesselScreening.vesselId = :vesselId and vesselScreening.deleted = false and vesselScreeningSummary.reference = :reference',
          {
            vesselId,
            reference,
          },
        )
        .select(['vesselScreeningSummary.id'])
        .getMany();
      if (vesselScreeningSummaryIds) {
        for (let i = 0; i < vesselScreeningSummaryIds.length; i++) {
          const vesselScreeningSummary = await this.findOne({
            where: {
              reference,
              id: vesselScreeningSummaryIds[i].id,
            },
          });
          //TODO: SEPARATE FUNCTION HERE
          //START updateReviewStatus: if current review status = Open and have risk change -> change review status(in section summary and in tab)  to INPROGRESS
          if (
            isChangeRiskValue &&
            vesselScreeningSummary.status === VesselScreeningSummaryStatusEnum.OPEN
          ) {
            await this.update(vesselScreeningSummary.id, {
              status: VesselScreeningSummaryStatusEnum.IN_PROGRESS,
            });

            await manager.update(
              VesselScreening,
              { id: vesselScreeningSummary.vesselScreeningId },
              { status: VesselScreeningReviewStatusEnum.IN_PROGRESS },
            );
          }
          if (isChangeRiskValue && !vesselScreeningSummary.reviewStatus) {
            await manager.update(
              VesselScreeningSummary,
              {
                vesselScreeningId: vesselScreeningSummary.vesselScreeningId,
                tabName: vesselScreeningSummary.tabName,
              },
              { reviewStatus: VesselScreeningSummaryReviewStatusEnum.IN_PROGRESS },
            );
            await manager.update(
              VesselScreening,
              { id: vesselScreeningSummary.vesselScreeningId },
              { reviewStatus: VesselScreeningReviewStatusEnum.IN_PROGRESS },
            );
          }
        }
      }
    } else {
      vesselScreeningSummary = await this.findOne({
        where: {
          reference,
          vesselScreeningId,
        },
      });

      if (!vesselScreeningSummary) {
        throw new BaseError({ status: 404, message: 'vesselScreeningSummary.NOT_FOUND' });
      }
      //TODO: SEPARATE FUNCTION HERE
      //START updateReviewStatus: if current review status = Open and have risk change -> change review status(in section summary and in tab)  to INPROGRESS
      if (
        isChangeRiskValue &&
        vesselScreeningSummary.status === VesselScreeningSummaryStatusEnum.OPEN
      ) {
        await this.update(vesselScreeningSummary.id, {
          status: VesselScreeningSummaryStatusEnum.IN_PROGRESS,
        });

        await manager.update(
          VesselScreening,
          { id: vesselScreeningSummary.vesselScreeningId },
          { status: VesselScreeningReviewStatusEnum.IN_PROGRESS },
        );
      }

      if (isChangeRiskValue && !vesselScreeningSummary.reviewStatus) {
        await manager.update(
          VesselScreeningSummary,
          {
            vesselScreeningId,
            tabName: vesselScreeningSummary.tabName,
          },
          { reviewStatus: VesselScreeningSummaryReviewStatusEnum.IN_PROGRESS },
        );

        await manager.update(
          VesselScreening,
          { id: vesselScreeningSummary.vesselScreeningId },
          { reviewStatus: VesselScreeningReviewStatusEnum.IN_PROGRESS },
        );
      }
    }
    let transportData;
    switch (reference) {
      case VesselScreeningSummaryReferenceEnum.INJURIES:
        transportData = await manager
          .createQueryBuilder(InjuryRequest, 'injuryRequest')
          .innerJoin('injuryRequest.injury', 'injury')
          .where(
            'injuryRequest.vesselScreeningId = :vesselScreeningId AND injury.deleted = false',
            {
              vesselScreeningId,
            },
          )
          .getMany();
        break;
      case VesselScreeningSummaryReferenceEnum.SURVEY_CLASS_INFO:
        transportData = await manager
          .createQueryBuilder(SurveyClassInfoRequest, 'surveyClassInfoRequest')
          .innerJoin('surveyClassInfoRequest.surveyClassInfo', 'surveyClassInfo')
          .where(
            'surveyClassInfoRequest.vesselScreeningId = :vesselScreeningId AND surveyClassInfo.deleted = false',
            {
              vesselScreeningId,
            },
          )
          .getMany();
        break;

      case VesselScreeningSummaryReferenceEnum.CONDITION_OF_CLASS_DISPENSATIONS:
        transportData = await manager
          .createQueryBuilder(ClassDispensationsRequest, 'classDispensationsRequest')
          .innerJoin('classDispensationsRequest.classDispensations', 'classDispensations')
          .where(
            'classDispensationsRequest.vesselScreeningId = :vesselScreeningId AND classDispensations.deleted = false',
            {
              vesselScreeningId,
            },
          )
          .getMany();
        break;

      case VesselScreeningSummaryReferenceEnum.PORT_STATE_CONTROL:
        transportData = await manager
          .createQueryBuilder(PortStateControlRequest, 'portStateControlRequest')
          .innerJoin('portStateControlRequest.PortStateControl', 'portStateControl')
          .where(
            'portStateControlRequest.vesselScreeningId = :vesselScreeningId AND portStateControl.deleted = false',
            {
              vesselScreeningId,
            },
          )
          .getMany();
        break;

      case VesselScreeningSummaryReferenceEnum.CHARTERER_INSPECTION:
        transportData = await manager
          .createQueryBuilder(ChartererInspectionRequest, 'chartererInspectionRequest')
          .where('chartererInspectionRequest.vesselScreeningId = :vesselScreeningId', {
            vesselScreeningId,
          })
          .getMany();
        break;

      case VesselScreeningSummaryReferenceEnum.INCIDENTS:
        transportData = await manager
          .createQueryBuilder(IncidentInvestigation, 'incidentInvestigation')
          .where(
            'incidentInvestigation.vesselId = :vesselId AND incidentInvestigation.deleted = false AND  incidentInvestigation.companyId = :companyId AND incidentInvestigation.status = :incidentStatus',
            {
              vesselId,
              companyId,
              incidentStatus: IncidentInvestigationStatus.CLOSEOUT,
            },
          )
          .getMany();
        break;

      case VesselScreeningSummaryReferenceEnum.PILOT_TERMINAL_FEEDBACK:
        transportData = await manager
          .createQueryBuilder(PilotTerminalFeedback, 'pilotTerminalFeedback')
          .where(
            `pilotTerminalFeedback.vesselId = :vesselId 
            AND pilotTerminalFeedback.deleted = false 
            AND  pilotTerminalFeedback.companyId = :companyId
            AND pilotTerminalFeedback.status = :status`,
            {
              vesselId,
              companyId,
              status: PilotTerminalFeedbackStatusEnum.SUBMITTED,
            },
          )
          .getMany();
        break;

      case VesselScreeningSummaryReferenceEnum.VESSEL_COMPANY_FEEDBACK:
        transportData = await manager
          .createQueryBuilder(VesselCompanyFeedback, 'vesselCompanyFeedback')
          .where(
            `vesselCompanyFeedback.vesselId = :vesselId 
            AND vesselCompanyFeedback.deleted = false 
            AND  vesselCompanyFeedback.companyId = :companyId
            AND vesselCompanyFeedback.status = :status`,
            {
              vesselId,
              companyId,
              status: VesselCompanyFeedbackStatusEnum.SUBMITTED,
            },
          )
          .getMany();
        break;

      case VesselScreeningSummaryReferenceEnum.VESSEL_GENERAL_RISK:
        transportData = await manager
          .createQueryBuilder(VesselRiskRequest, 'vesselRiskRequest')
          .where('vesselRiskRequest.vesselScreeningId = :vesselScreeningId', {
            vesselScreeningId,
          })
          .getMany();
        break;

      default:
        return null;
    }
    let preparedVesselScreeningSummary;
    if (reference === VesselScreeningSummaryReferenceEnum.PILOT_TERMINAL_FEEDBACK) {
      preparedVesselScreeningSummary = this._aggregateRiskSummary(transportData, reference);
    } if (reference === VesselScreeningSummaryReferenceEnum.VESSEL_GENERAL_RISK) {
      preparedVesselScreeningSummary = this._aggregateRiskSummary(transportData, reference);
    } else {
      preparedVesselScreeningSummary = this._aggregateRiskSummary(transportData);
    }
    if (
      vesselScreeningSummaryIds.length > 0 &&
      (reference === VesselScreeningSummaryReferenceEnum.INCIDENTS ||
        reference === VesselScreeningSummaryReferenceEnum.PILOT_TERMINAL_FEEDBACK ||
        reference === VesselScreeningSummaryReferenceEnum.VESSEL_COMPANY_FEEDBACK)
    ) {
      for (let i = 0; i < vesselScreeningSummaryIds.length; i++) {
        await this.update({ id: vesselScreeningSummaryIds[i].id }, preparedVesselScreeningSummary);
      }
    }

    if (
      reference !== VesselScreeningSummaryReferenceEnum.INCIDENTS &&
      reference !== VesselScreeningSummaryReferenceEnum.PILOT_TERMINAL_FEEDBACK &&
      reference !== VesselScreeningSummaryReferenceEnum.VESSEL_COMPANY_FEEDBACK
    ) {
      return await this.update({ id: vesselScreeningSummary.id }, preparedVesselScreeningSummary);
    }
  }

  private _aggregateRiskSummary(
    transportData: {
      potentialRisk: number;
      potentialScore: number;
      observedRisk: number;
      observedScore: number;
      finalRisk?: number;
      finalScore?: number;
      timeLoss: boolean;
      count?: number;
    }[],
    reference?: string,
  ) {
    let totalPotentialScore = 0;
    let countPotentialScore = 0;
    let totalObservedScore = 0;
    let countObservedScore = 0;
    let totalFinalScore = 0;
    let countFinalScore = 0;
    let timeLoss = null;

    if (reference === VesselScreeningSummaryReferenceEnum.VESSEL_GENERAL_RISK) {
      for (let i = 0; i < transportData.length; i++) {
        if (typeof transportData[i].potentialScore === 'number') {
          totalPotentialScore += transportData[i].potentialScore;
          countPotentialScore = transportData[i].count || 0;
        }
        if (typeof transportData[i].observedScore === 'number') {
          totalObservedScore += transportData[i].observedScore;
          countObservedScore = transportData[i].count || 0;
        }
        if (typeof transportData[i].finalScore === 'number') {
          totalFinalScore += transportData[i].finalScore;
          countFinalScore = transportData[i].count || 0;
        }
        if (transportData[i].timeLoss) {
          timeLoss = true;
        }
        if (timeLoss === null && transportData[i].timeLoss === false) {
          timeLoss = false;
        }
      }
    } else {
      for (let i = 0; i < transportData.length; i++) {
        if (typeof transportData[i].potentialScore === 'number') {
          totalPotentialScore += transportData[i].potentialScore;
          countPotentialScore++;
        }
        if (typeof transportData[i].observedScore === 'number') {
          totalObservedScore += transportData[i].observedScore;
          countObservedScore++;
        }
        if (typeof transportData[i].finalScore === 'number') {
          totalFinalScore += transportData[i].finalScore;
          countFinalScore++;
        }
        if (transportData[i].timeLoss) {
          timeLoss = true;
        }
        if (timeLoss === null && transportData[i].timeLoss === false) {
          timeLoss = false;
        }
      }
    }

    const potentialScore =
      countPotentialScore > 0 ? totalPotentialScore / countPotentialScore : null;
    const observedScore = countObservedScore > 0 ? totalObservedScore / countObservedScore : null;
    const finalScore = countFinalScore > 0 ? totalFinalScore / countFinalScore : null;

    // terminal feedback case
    // if (reference === VesselScreeningSummaryReferenceEnum.PILOT_TERMINAL_FEEDBACK) {
    //   potentialRisk = this._convertScoreToRiskForTerminalFeedback(potentialScore);
    //   observedRisk = this._convertScoreToRiskForTerminalFeedback(observedScore);
    // } else {
    // common case
    const potentialRisk = this._convertScoreToRiskCommon(potentialScore);
    const observedRisk = this._convertScoreToRiskCommon(observedScore);
    const finalRisk = this._convertScoreToRiskCommon(finalScore);
    // }

    const preparedVesselScreeningSummary = {
      potentialRisk,
      potentialScore,
      observedRisk,
      observedScore,
      finalRisk,
      finalScore,
      timeLoss,
    };

    return preparedVesselScreeningSummary;
  }

  private _convertScoreToRiskForTerminalFeedback(score: number) {
    let risk: VesselScreeningPotentialRiskEnum;
    if (score === 10) {
      risk = VesselScreeningPotentialRiskEnum.NEGLIGIBLE;
    } else if (score >= 0 && score < 2) {
      risk = VesselScreeningPotentialRiskEnum.HIGH;
    } else if (score >= 2 && score < 5) {
      risk = VesselScreeningPotentialRiskEnum.MEDIUM;
    } else if (score >= 5 && score < 10) {
      risk = VesselScreeningPotentialRiskEnum.LOW;
    } else {
      risk = null;
    }
    return risk;
  }

  private _convertScoreToRiskCommon(score: number) {
    let risk: VesselScreeningPotentialRiskEnum;
    if (score === 0) {
      risk = VesselScreeningPotentialRiskEnum.NEGLIGIBLE;
    } else if (score > 0 && score <= 2) {
      risk = VesselScreeningPotentialRiskEnum.LOW;
    } else if (score > 2 && score <= 5) {
      risk = VesselScreeningPotentialRiskEnum.MEDIUM;
    } else if (score > 5 && score <= 10) {
      risk = VesselScreeningPotentialRiskEnum.HIGH;
    } else {
      risk = null;
    }
    return risk;
  }

  async updateVSSummary(
    vesselScreeningSummaryId: string,
    use: TokenPayloadModel,
    body: UpdateVesselScreeningSummaryDto,
  ) {
    try {
      const updateResult = await this.update(
        {
          id: vesselScreeningSummaryId,
        },
        body,
      );
      if (updateResult.affected > 0) {
        return updateResult.affected;
      } else {
        throw new BaseError({ status: 404, message: 'vesselScreeningSummary.NOT_FOUND' });
      }
    } catch (ex) {
      LoggerCommon.error(
        '[VesselScreeningSummaryRepository] updateVSSummary error ',
        ex.message || ex,
      );
      throw ex;
    }
  }
}
