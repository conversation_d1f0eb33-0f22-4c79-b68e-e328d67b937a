import { chain } from 'lodash';
import moment from 'moment';
import {
  <PERSON><PERSON>rror,
  CommonStatus,
  <PERSON>gger<PERSON>ommon,
  RoleScopeCheck,
  TokenPayloadModel,
  TypeORMRepository,
  Utils,
} from 'svm-nest-lib-v3';
import { Connection, EntityRepository, getConnection, In, Not } from 'typeorm';
import {
  CompanyLevelEnum,
  IncidentInvestigationReviewStatus,
  LostTimeInjuryEnum,
  PlanningRequestStatus,
  PortStateControlDetentionEnum,
  SelfAssessmentMonthEnum,
  VesselScreeningFilterRiskEnum,
  VesselScreeningFinalRiskEnum,
  VesselScreeningObservedRiskEnum,
  VesselScreeningPotentialRiskEnum,
  VesselScreeningStatusEnum,
} from '../../../commons/enums';
import { IncidentInvestigation } from '../../../modules-qa/incident-investigation/entity/incident-investigation.entity';
import { RightShip } from '../../../modules-qa/right-ship/right-ship.entity';
import { CompanyFeatureVersionRepository } from '../../../modules/commons/company-feature-version/company-feature-version.repository';
import { FeatureVersionConfig } from '../../../modules/commons/company-feature-version/feature-version.config';
import { CompanyRepository } from '../../../modules/company/company.repository';
import { PlanningRequest } from '../../../modules/planning-request/entities/planning-request.entity';
import { PlanningRequestHistoryRepository } from '../../../modules/planning-request/repositories/planning-request-history.repository';
import { User } from '../../../modules/user/user.entity';
import { UserRepository } from '../../../modules/user/user.repository';
import { VesselRepository } from '../../../modules/vessel/vessel.repository';
import {
  convertFilterField,
  createSelectSql,
  createWhereSql,
  handleGetDataForAGGrid,
  leadingZero,
  MySet,
  PayloadAGGridDto,
} from '../../../utils';
import { Cargo } from '../../cargo/cargo.entity';
import { ClassDispensationsRequest } from '../../class-dispensations/entity/class-dispensations-request.entity';
import { ClassDispensations } from '../../class-dispensations/entity/class-dispensations.entity';
import { ClassDispensationsRepository } from '../../class-dispensations/repository/class-dispensations.repository';
import { DryDockingRepository } from '../../dry-docking/repository/dry-docking.repository';
import { ExternalInspectionsRepository } from '../../external-inspections/repository/external-inspections.repository';
import { IncidentInvestigationRepository } from '../../incident-investigation/repository/incident-investigation.repository';
import { InjuryRequest } from '../../injury/entity/injury-request.entity';
import { InjuryRepository } from '../../injury/repository/injury.repository';
import { InternalInspectionsRepository } from '../../internal-inspections/repository/internal-inspections.repository';
import { MaintenancePerformanceRepository } from '../../maintenance-performance/repository/maintenance-performance.repository';
import { OtherSmsRecordsRepository } from '../../other-sms-records/repository/other-sms-records.repository';
import { OtherTechRecordsRepository } from '../../other-technical-records/repository/other-technical-records.repository';
import { PilotTerminalFeedbackRepository } from '../../pilot-terminal-feedback/repository/pilot-terminal-feedback.repository';
import { PlansDrawingsMasterRepository } from '../../plans-drawings-master/plans-drawings-master.repository';
import { PlansDrawingsRepository } from '../../plans-drawings/repository/plans-drawings.repository';
import { PortStateControlRequest } from '../../port-state-control/entity/port-state-control-request.entity';
import { PortStateControl } from '../../port-state-control/entity/port-state-control.entity';
import { PortStateControlRepository } from '../../port-state-control/repository/port-state-control.repository';
import { SurveyClassInfoRequest } from '../../survey/survey-class-info/entity/survey-class-info-request.entity';
import { SurveyClassInfo } from '../../survey/survey-class-info/entity/survey-class-info.entity';
import { SurveyClassInfoRepository } from '../../survey/survey-class-info/repository/survey-class-info.repository';
import { VesselScreeningSummaryBusiness } from '../business/vessel-screening-summary.business';
import {
  CreateVesselScreeningDto,
  CreateVesselScreeningRemarkDto,
  CreateVesselScreeningSummaryDto,
  DetailVesselScreeningSummaryDTO,
  ListVesselScreeningDto,
  UpdateReViewStatusVesselScreeningSummaryDTO,
  UpdateVesselScreeningDto,
  VESSEL_SCREENING_FILTER_FIELDS,
} from '../dto';
import { VesselScreeningHistory } from '../entity/vessel-screening-history.entity';
import { VesselScreeningPort } from '../entity/vessel-screening-port.entity';
import { VesselScreeningRemark } from '../entity/vessel-screening-remark.entity';
import { VesselScreeningSummaryAttachmentRemark } from '../entity/vessel-screening-summary-attachment-remark.entity';
import {
  VesselScreeningSummary,
  VesselScreeningSummaryReferenceEnum,
  VesselScreeningSummaryReviewStatusEnum,
  VesselScreeningSummaryStatusEnum,
  VesselScreeningSummaryTabNameEnum,
} from '../entity/vessel-screening-summary.entity';
import {
  VesselScreening,
  VesselScreeningReviewStatusEnum,
} from '../entity/vessel-screening.entity';
import { VesselScreeningSummaryRepository } from './vessel-screening-summary.repository';
import { VoyageMasterDetails } from '../../vessel-scheduler-module/entity/vessel-scheduler.entity';
import { CargoTypeRepository } from 'src/modules-qa/cargo-type/cargo-type.repository';
import { CargoRepository } from '../../cargo/cargo.repository';
import { PortMasterRepository } from 'src/modules/port-master/port-master.repository';
import { TransferTypeRepository } from '../../transfer-type/transfer-type.repository';
import { _supportWhereDOCChartererOwner } from '../../../commons/functions';
import { DateRangeDto } from 'src/modules-qa/summary/dto';
import { vesselScreeningSummaryReferenceDTO } from '../dto/vessel-screening-summary-reference.dto';
import { VesselCompanyFeedbackRepository } from '../../vessel-company-feedback/repository/vessel-company-feedback.repository';
import { Vessel } from 'src/modules/vessel/entity/vessel.entity';
import { ShipParticular } from 'src/modules-qa/ship-particular/ship-particular.entity';
import { Country } from 'src/modules/country/country.entity';
import { VesselRiskRequest } from 'src/modules/vessel/entity/vessel-risk-request.entity';
import { BlackListCountry } from 'src/modules/vessel/entity/black-list-country.entity';
import { GreyListCountry } from 'src/modules/vessel/entity/grey-list-country.entity';
import { WhiteListCountry } from 'src/modules/vessel/entity/white-list-country.entity';
import { ClassificationSociety } from 'src/modules-qa/classification-society/classification-society.entity';
import { ReportFindingFormRepository } from 'src/modules/report-finding/repositories/report-finding-form.repository';
import { ChartererInspectionRequest } from 'src/modules/corrective-action-request/entities/charterer-inspection-request.entity';

@EntityRepository(VesselScreening)
export class VesselScreeningRepository extends TypeORMRepository<VesselScreening> {
  constructor(private readonly connection: Connection) {
    super();
  }

  async _migrateData() {
    const rawQuery = `SELECT
                        vs.id as "id"
                      FROM
                        vessel_screening vs
                      LEFT JOIN  vessel_screening_summary vss
                      ON vs.id = vss."vesselScreeningId"
                      WHERE
                        vss."vesselScreeningId" is null AND
                        vs.deleted = false;`;
    const vesselScreeningIds = await this.connection.query(rawQuery);
    if (vesselScreeningIds.length > 0) {
      for (let i = 0; i < vesselScreeningIds.length; i++) {
        const preparedVesselScreeningSummary: VesselScreeningSummary[] =
          VesselScreeningSummaryBusiness.preparedDataCreateVesselScreeningSummary(
            vesselScreeningIds[i].id,
          );
        await this.manager.insert(VesselScreeningSummary, preparedVesselScreeningSummary);
      }
    }
    return;
  }

  async createVesselScreening(user: TokenPayloadModel, body: CreateVesselScreeningDto) {
    try {
      // Prepare general info
      const currYear = moment().year();
      return await this.connection.transaction(async (manager) => {
        const vesselScreeningExistedStatusInProgress = await manager.findOne(VesselScreening, {
          where: {
            status: VesselScreeningStatusEnum.IN_PROGRESS,
            deleted: false,
            companyId: user.companyId,
            vesselId: body.vesselId,
          },
        });
        if (vesselScreeningExistedStatusInProgress) {
          throw new BaseError({ message: 'vesselScreening.EXISTED_STATUS_IN_PROGRESS' });
        }
        //Validate dto
        await this._validateCreateVesselScreeningDto(body, user);

        const counter = await this.manager
          .getCustomRepository(CompanyFeatureVersionRepository)
          .getNextVersion({
            manager,
            companyId: user.companyId,
            feature: FeatureVersionConfig.VESSEL_SCREENING_REQUEST_NO,
            year: Number(currYear),
          });
        const serialNumber = leadingZero(counter, 3);
        const prepareVesselScreening = {
          ...body,
          requestNo: `${moment().format('YYYYMMDD')}-${serialNumber}`,
          companyId: user.companyId,
          createdUserId: user.id,
          cargos: body.cargoIds.map((cargoId) => ({ id: cargoId } as Cargo)),
        };
        Reflect.deleteProperty(prepareVesselScreening, 'ports');

        const vesselScreeningCreated = await manager.save(VesselScreening, {
          ...prepareVesselScreening,
        });
        // extract month and year
        const monthList = Object.keys(SelfAssessmentMonthEnum).map(
          (key) => SelfAssessmentMonthEnum[key],
        );
        const createdAtMonth = vesselScreeningCreated.createdAt.getMonth();
        const finalCreatedDateMonth = monthList[createdAtMonth];
        const createdAtYear = vesselScreeningCreated.updatedAt.getFullYear();
        // updating the createdAt_Month and createdAt_Year columns after saving vesselScreening.
        await manager.save(VesselScreening, {
          id: vesselScreeningCreated.id,
          createdAt_Year: createdAtYear,
          createdAt_Month: finalCreatedDateMonth,
        });

        // saving the vessel screening status history
        await manager.insert(VesselScreeningHistory, {
          vesselScreeningId: vesselScreeningCreated.id,
          status: vesselScreeningCreated.status,
          remark: vesselScreeningCreated.remark,
          createdUserId: vesselScreeningCreated.createdUserId,
        });
        // manager.insert(VesselScreeningHistory, preparedVesselScreeningHistory),
        // create Vessel Screening Port
        const preparedVSPorts: VesselScreeningPort[] = [];
        for (let i = 0; i < body.ports.length; i++) {
          const vsPort = body.ports[i];
          preparedVSPorts.push({
            portId: vsPort.portId,
            terminalId: vsPort.terminalId ? vsPort.terminalId : null,
            berth: vsPort.berth,
            layCanDate: new Date(vsPort.layCanDate),
            vesselScreeningId: vesselScreeningCreated.id,
          } as VesselScreeningPort);
        }
        await manager.save(VesselScreeningPort, preparedVSPorts);

        // add default potential risk injury
        const vesselInjuries = await this.manager
          .getCustomRepository(InjuryRepository)
          .listGetAllDataByVesselId(user.companyId, body.vesselId);
        if (vesselInjuries && vesselInjuries.length > 0) {
          const injuryRequests: InjuryRequest[] = [];
          for (const item of vesselInjuries) {
            const injuryRequest = {
              injuryId: item.id,
              vesselScreeningId: vesselScreeningCreated.id,
              // potentialScore: 1,
              // observedScore: 1,
              // timeLoss: false,
            } as InjuryRequest;
            if (
              item.injuryMaster.name &&
              (item.injuryMaster.name.toLowerCase() === 'fatality' ||
                item.injuryMaster.name.toLowerCase().indexOf('disability') > -1)
            ) {
              injuryRequest.potentialRisk = VesselScreeningPotentialRiskEnum.HIGH;
              injuryRequest.observedRisk = VesselScreeningObservedRiskEnum.HIGH;
              injuryRequest.finalRisk = VesselScreeningFinalRiskEnum.HIGH;
              injuryRequest.potentialScore = 10;
              injuryRequest.observedScore = 10;
              injuryRequest.finalScore = 10;
            } else if (item.lostTime === LostTimeInjuryEnum.YES) {
              injuryRequest.potentialRisk = VesselScreeningPotentialRiskEnum.MEDIUM;
              injuryRequest.observedRisk = VesselScreeningObservedRiskEnum.MEDIUM;
              injuryRequest.finalRisk = VesselScreeningFinalRiskEnum.MEDIUM;
              injuryRequest.potentialScore = 5;
              injuryRequest.observedScore = 5;
              injuryRequest.finalScore = 5;
            } else {
              injuryRequest.potentialRisk = VesselScreeningPotentialRiskEnum.LOW;
              injuryRequest.observedRisk = VesselScreeningObservedRiskEnum.LOW;
              injuryRequest.finalRisk = VesselScreeningFinalRiskEnum.LOW;
              injuryRequest.potentialScore = 2;
              injuryRequest.observedScore = 2;
              injuryRequest.finalScore = 2;
            }
            injuryRequests.push(injuryRequest);
          }
          await manager.insert(InjuryRequest, injuryRequests);
        }
        // add default potential risk port state control
        const vesselPortStateControls = await manager.find(PortStateControl, {
          where: {
            deleted: false,
            companyId: user.companyId,
            vesselId: body.vesselId,
          },
          relations: ['portStateInspectionReports'],
        });
        if (vesselPortStateControls && vesselPortStateControls.length > 0) {
          const portStateControlRequests: PortStateControlRequest[] = [];
          for (const item of vesselPortStateControls) {
            const portStateControlRequest = {
              portStateControlId: item.id,
              vesselScreeningId: vesselScreeningCreated.id,
              // potentialScore: 1,
              // observedScore: 1,
              // timeLoss: false,
            } as PortStateControlRequest;
            if (item.detention === PortStateControlDetentionEnum.YES) {
              portStateControlRequest.potentialRisk = VesselScreeningPotentialRiskEnum.HIGH;
              portStateControlRequest.observedRisk = VesselScreeningObservedRiskEnum.HIGH;
              portStateControlRequest.finalRisk = VesselScreeningFinalRiskEnum.HIGH;
              portStateControlRequest.potentialScore = 10;
              portStateControlRequest.observedScore = 10;
              portStateControlRequest.finalScore = 10;
            } else if (item.noFindings === true || item.portStateInspectionReports.length === 0) {
              portStateControlRequest.potentialRisk = VesselScreeningPotentialRiskEnum.NEGLIGIBLE;
              portStateControlRequest.observedRisk = VesselScreeningObservedRiskEnum.NEGLIGIBLE;
              portStateControlRequest.finalRisk = VesselScreeningFinalRiskEnum.NEGLIGIBLE;
              portStateControlRequest.potentialScore = 0;
              portStateControlRequest.observedScore = 0;
              portStateControlRequest.finalScore = 0;
            } else {
              portStateControlRequest.potentialRisk = VesselScreeningPotentialRiskEnum.MEDIUM;
              portStateControlRequest.observedRisk = VesselScreeningObservedRiskEnum.MEDIUM;
              portStateControlRequest.finalRisk = VesselScreeningFinalRiskEnum.MEDIUM;
              portStateControlRequest.potentialScore = 5;
              portStateControlRequest.observedScore = 5;
              portStateControlRequest.finalScore = 5;
            }
            portStateControlRequests.push(portStateControlRequest);
          }
          await manager.insert(PortStateControlRequest, portStateControlRequests);
        }
        const { observedScore, potentialScore } = await this.manager
          .getCustomRepository(ReportFindingFormRepository)
          .getAllFollowUpsByVesselAndCompany(body?.vesselId, user?.companyId, user);
        await manager.insert(ChartererInspectionRequest, {
          vesselId: body?.vesselId,
          observedScore: observedScore || 0,
          potentialScore: potentialScore || 0,
          vesselScreeningId: vesselScreeningCreated?.id,
        });
        // add default potential risk class dispensations
        const vesselClassDispensations = await manager.find(ClassDispensations, {
          where: {
            deleted: false,
            companyId: user.companyId,
            vesselId: body.vesselId,
          },
        });
        if (vesselClassDispensations && vesselClassDispensations.length > 0) {
          const classDispensationsRequests: ClassDispensationsRequest[] = [];
          for (const item of vesselClassDispensations) {
            const classDispensationsRequest = {
              classDispensationsId: item.id,
              vesselScreeningId: vesselScreeningCreated.id,
              // potentialScore: 1,
              // observedRisk: VesselScreeningObservedRiskEnum.LOW,
              // observedScore: 1,
              // timeLoss: false,
            } as ClassDispensationsRequest;
            if (item.expiryDate > item.issueDate) {
              classDispensationsRequest.potentialRisk = VesselScreeningPotentialRiskEnum.HIGH;
              classDispensationsRequest.observedRisk = VesselScreeningObservedRiskEnum.HIGH;
              classDispensationsRequest.finalRisk = VesselScreeningFinalRiskEnum.HIGH;
              classDispensationsRequest.potentialScore = 10;
              classDispensationsRequest.observedScore = 10;
              classDispensationsRequest.finalScore = 10;
            } else {
              classDispensationsRequest.potentialRisk = VesselScreeningPotentialRiskEnum.MEDIUM;
              classDispensationsRequest.observedRisk = VesselScreeningObservedRiskEnum.MEDIUM;
              classDispensationsRequest.finalRisk = VesselScreeningFinalRiskEnum.MEDIUM;
              classDispensationsRequest.potentialScore = 5;
              classDispensationsRequest.observedScore = 5;
              classDispensationsRequest.finalScore = 5;
            }
            classDispensationsRequests.push(classDispensationsRequest as ClassDispensationsRequest);
          }
          await manager.insert(ClassDispensationsRequest, classDispensationsRequests);
        }

        // add default potential risk survey class info
        const vesselSurveyClassInfos = await manager.find(SurveyClassInfo, {
          where: {
            deleted: false,
            companyId: user.companyId,
            vesselId: body.vesselId,
          },
        });
        if (vesselSurveyClassInfos && vesselSurveyClassInfos.length > 0) {
          const surveyClassInfoRequests: SurveyClassInfoRequest[] = [];
          for (const item of vesselSurveyClassInfos) {
            const surveyClassInfoRequest = {
              surveyClassInfoId: item.id,
              vesselScreeningId: vesselScreeningCreated.id,
              // potentialScore: 1,
              // observedScore: 1,
              // timeLoss: false,
            } as SurveyClassInfoRequest;
            if (item.anyExpiredCertificates === true) {
              surveyClassInfoRequest.potentialRisk = VesselScreeningPotentialRiskEnum.HIGH;
              surveyClassInfoRequest.observedRisk = VesselScreeningObservedRiskEnum.HIGH;
              surveyClassInfoRequest.finalRisk = VesselScreeningFinalRiskEnum.HIGH;
              surveyClassInfoRequest.potentialScore = 10;
              surveyClassInfoRequest.observedScore = 10;
              surveyClassInfoRequest.finalScore = 10;
            } else {
              surveyClassInfoRequest.potentialRisk = VesselScreeningPotentialRiskEnum.LOW;
              surveyClassInfoRequest.observedRisk = VesselScreeningObservedRiskEnum.LOW;
              surveyClassInfoRequest.finalRisk = VesselScreeningFinalRiskEnum.LOW;
              surveyClassInfoRequest.potentialScore = 2;
              surveyClassInfoRequest.observedScore = 2;
              surveyClassInfoRequest.finalScore = 2;
            }
            surveyClassInfoRequests.push(surveyClassInfoRequest as SurveyClassInfoRequest);
          }
          await manager.insert(SurveyClassInfoRequest, surveyClassInfoRequests);
        }

        // add default potential risk vessel info
        const vesselInfo = await manager.findOne(Vessel, {
          where: {
            deleted: false,
            companyId: user.companyId,
            id: body.vesselId,
          },
        });

        if (vesselInfo) {
          // await this.manager
          //   .getCustomRepository(VesselRepository)
          //   .updateVesselRiskRequest(body.vesselId, vesselInfo, vesselScreeningCreated.id);

          // vessel risk request
          let potentialScore = 0;
          let observedScore = 0;
          let count = 0;

          // calculate age risk
          if (vesselInfo?.buildDate) {
            const buildDate = new Date(vesselInfo.buildDate);
            const currentDate = new Date();
            const age = currentDate.getFullYear() - buildDate.getFullYear();
            if (age >= 15.00) {
              potentialScore += 10;
              observedScore += 10;
            } else if (age >= 10.01 && age < 14.99) {
              potentialScore += 5;
              observedScore += 5;
            } else if (age >= 5.01 && age < 10.00) {
              potentialScore += 2;
              observedScore += 2;
            } else {
              potentialScore += 0;
              observedScore += 0;
            }
          }
          count++;

          // calculate flag risk
          if (vesselInfo?.countryId) {
            const country = await manager.findOne(Country, {
              where: {
                id: vesselInfo.countryId,
              },
            });
            if (country) {
              const whiteListCountries = await manager.find(WhiteListCountry, {
                where: {
                  countryId: country.id,
                },
              });
              const greyListCountries = await manager.find(GreyListCountry, {
                where: {
                  countryId: country.id,
                },
              });
              const blackListCountries = await manager.find(BlackListCountry, {
                where: {
                  countryId: country.id,
                },
              });
              if (whiteListCountries.length > 0) {
                potentialScore += 2;
                observedScore += 2;
              } else if (greyListCountries.length > 0) {
                potentialScore += 10;
                observedScore += 10;
              } else if (blackListCountries.length > 0) {
                potentialScore += 10;
                observedScore += 10;
              } else {
                potentialScore += 0;
                observedScore += 0;
              }
            }
          }
          count++;

          // calculate classification society risk
          if (vesselInfo?.classificationSocietyId) {
            const IACSMember = await manager.findOne(ClassificationSociety, {
              where: {
                id: vesselInfo.classificationSocietyId,
                deleted: false,
                isIACSMember: true,
              },
            });
            if (!IACSMember) {
              potentialScore += 10;
              observedScore += 10;
            } else {
              potentialScore += 0;
              observedScore += 0;
            }
          }
          count++;

          // calculate shipyard risk
          if (vesselInfo?.shipyardName == 'JSC United Shipbuilding Corporation' || vesselInfo?.shipyardName == 'Kuzey Star Shipyard' || vesselInfo?.shipyardName == 'Penglai Jutal Offshore Engineering Heavy Industry Co., Ltd.') {
            potentialScore += 10;
            observedScore += 10;
          } else {
            potentialScore += 2;
            observedScore += 2;
          }
          count++;

          // calculate ITF risk
          if (vesselInfo?.isITF === false) {
            potentialScore += 2;
            observedScore += 2;
          } else if (vesselInfo?.isITF === true) {
            potentialScore += 0;
            observedScore += 0;
          }
          count++;

          // calculate right ship restrictions risk
          if (vesselInfo?.rightShipRestrictions && vesselInfo?.rightShipRestrictions.length > 0) {
            const vesselRestrictions = vesselInfo?.rightShipRestrictions.some((rightShipRestriction) => {
              return rightShipRestriction?.restrictionType === 'Vessel';
            });
            count++;
            const companyRestrictions = vesselInfo?.rightShipRestrictions.some((rightShipRestriction) => {
              return rightShipRestriction?.restrictionType === 'Company';
            });
            count++;
            if (vesselRestrictions) {
              potentialScore += 10;
              observedScore += 10;
            } else if (companyRestrictions) {
              potentialScore += 10;
              observedScore += 10;
            }
          } else {
            potentialScore += 0;
            observedScore += 0;
            count++;
            count++;
          }

          // calculate blacklist on MOU website risk
          if (vesselInfo?.blacklistOnMOUWebsite === true) {
            potentialScore += 10;
            observedScore += 10;
          } else {
            potentialScore += 0;
            observedScore += 0;
          }
          count++;

          const vesselRiskRequestParams = {
            potentialScore: potentialScore,
            observedScore: observedScore,
            vesselId: body.vesselId,
            vesselScreeningId: vesselScreeningCreated.id,
            count: count,
          };
          console.log('count: ', count);
          await manager.insert(VesselRiskRequest, vesselRiskRequestParams);
        }

        const vessel = await manager
          .getCustomRepository(VesselRepository)
          .createQueryBuilder('vessel')
          .leftJoin('vessel.vesselType', 'vesselType')
          .where('vessel.id = :vesselId', { vesselId: body.vesselId })
          .select(['vessel.id'])
          .addSelect(['vesselType.name'])
          .getOne();

        const numberOfPlanDrawingPending: number = await manager
          .getCustomRepository(PlansDrawingsRepository)
          ._countPending(body.vesselId, user.companyId, vessel.vesselType.name);

        let isPending: boolean;
        if (numberOfPlanDrawingPending > 0) {
          isPending = true;
        }

        // create vessel screening summary
        const preparedVesselScreeningSummary: VesselScreeningSummary[] =
          VesselScreeningSummaryBusiness.preparedDataCreateVesselScreeningSummary(
            vesselScreeningCreated.id,
            isPending,
          );
        await manager.insert(VesselScreeningSummary, preparedVesselScreeningSummary);

        const references = Object.values(VesselScreeningSummaryReferenceEnum);
        for (const reference of references) {
          if (reference === VesselScreeningSummaryReferenceEnum.INJURIES) {
            await manager
              .getCustomRepository(VesselScreeningSummaryRepository)
              .updateVesselScreeningSummaryByRef(
                manager,
                VesselScreeningSummaryReferenceEnum.INJURIES,
                vesselScreeningCreated.id,
              );
          }
          if (reference === VesselScreeningSummaryReferenceEnum.SURVEY_CLASS_INFO) {
            await manager
              .getCustomRepository(VesselScreeningSummaryRepository)
              .updateVesselScreeningSummaryByRef(
                manager,
                VesselScreeningSummaryReferenceEnum.SURVEY_CLASS_INFO,
                vesselScreeningCreated.id,
              );
          }
          if (reference === VesselScreeningSummaryReferenceEnum.CONDITION_OF_CLASS_DISPENSATIONS) {
            await manager
              .getCustomRepository(VesselScreeningSummaryRepository)
              .updateVesselScreeningSummaryByRef(
                manager,
                VesselScreeningSummaryReferenceEnum.CONDITION_OF_CLASS_DISPENSATIONS,
                vesselScreeningCreated.id,
              );
          }
          if (reference === VesselScreeningSummaryReferenceEnum.PORT_STATE_CONTROL) {
            await manager
              .getCustomRepository(VesselScreeningSummaryRepository)
              .updateVesselScreeningSummaryByRef(
                manager,
                VesselScreeningSummaryReferenceEnum.PORT_STATE_CONTROL,
                vesselScreeningCreated.id,
              );
          }
          if (reference === VesselScreeningSummaryReferenceEnum.CHARTERER_INSPECTION) {
            await manager
              .getCustomRepository(VesselScreeningSummaryRepository)
              .updateVesselScreeningSummaryByRef(
                manager,
                VesselScreeningSummaryReferenceEnum.CHARTERER_INSPECTION,
                vesselScreeningCreated?.id,
              );
          }
          if (reference === VesselScreeningSummaryReferenceEnum.INCIDENTS) {
            await manager
              .getCustomRepository(VesselScreeningSummaryRepository)
              .updateVesselScreeningSummaryByRef(
                manager,
                VesselScreeningSummaryReferenceEnum.INCIDENTS,
                null,
                body.vesselId,
                user.companyId,
              );
          }
          if (reference === VesselScreeningSummaryReferenceEnum.PILOT_TERMINAL_FEEDBACK) {
            await manager
              .getCustomRepository(VesselScreeningSummaryRepository)
              .updateVesselScreeningSummaryByRef(
                manager,
                VesselScreeningSummaryReferenceEnum.PILOT_TERMINAL_FEEDBACK,
                null,
                body.vesselId,
                user.companyId,
              );
          }
          if (reference === VesselScreeningSummaryReferenceEnum.VESSEL_GENERAL_RISK) {
            await manager
              .getCustomRepository(VesselScreeningSummaryRepository)
              .updateVesselScreeningSummaryByRef(
                manager,
                VesselScreeningSummaryReferenceEnum.VESSEL_GENERAL_RISK,
                vesselScreeningCreated.id,
              );
          }
          if (reference === VesselScreeningSummaryReferenceEnum.DOC_HOLDER) {
            await manager
              .getCustomRepository(VesselScreeningSummaryRepository)
              .updateVesselScreeningSummaryByRef(
                manager,
                VesselScreeningSummaryReferenceEnum.DOC_HOLDER,
                vesselScreeningCreated.id,
              );
          }
          if (reference === VesselScreeningSummaryReferenceEnum.CHARTERER) {
            await manager
              .getCustomRepository(VesselScreeningSummaryRepository)
              .updateVesselScreeningSummaryByRef(
                manager,
                VesselScreeningSummaryReferenceEnum.CHARTERER,
                vesselScreeningCreated.id,
              );
          }
          if (reference === VesselScreeningSummaryReferenceEnum.VESSEL_OWNER) {
            await manager
              .getCustomRepository(VesselScreeningSummaryRepository)
              .updateVesselScreeningSummaryByRef(
                manager,
                VesselScreeningSummaryReferenceEnum.VESSEL_OWNER,
                vesselScreeningCreated.id,
              );
          }
        }
      });
    } catch (ex) {
      LoggerCommon.error(
        '[VesselScreeningRepository] createVesselScreening error ',
        ex.message || ex,
      );
      throw ex;
    }
  }

  async listVesselScreening(
    token: TokenPayloadModel,
    query?: ListVesselScreeningDto,
    body?: PayloadAGGridDto,
  ) {
    const queryBuilder = this.createQueryBuilder('vesselScreening')
      .leftJoin('vesselScreening.createdUser', 'createdUser')
      .leftJoin('vesselScreening.vessel', 'vessel')
      .leftJoin('vesselScreening.company', 'company')
      .leftJoin('vesselScreening.companyRequest', 'companyRequest')
      .leftJoin('vesselScreening.transferType', 'transferType')
      .leftJoin('vesselScreening.cargoType', 'cargoType')
      .leftJoin('vesselScreening.cargos', 'cargo')
      .leftJoin('vesselScreening.voyageType', 'voyageType')
      .where(
        `(vesselScreening.companyId = '${token.companyId}' OR company.parentId = '${token.companyId}') and vesselScreening.deleted = false`,
      )
      .select();

    const fieldSelects = [
      'company.name',
      'company.companyIMO',
      'companyRequest.name',
      'createdUser.username',
      'transferType.name',
      'transferType.code',
      'cargoType.name',
      'cargoType.code',
      'cargo.id',
      'cargo.name',
      'cargo.code',
      'vessel.name',
      'vessel.code',
      'vessel.imoNumber',
      'voyageType.name',
    ];

    if (query.content) {
      queryBuilder.andWhere(
        `(vesselScreening.requestNo LIKE '%${query.content}%' OR companyRequest.name LIKE '%${query.content}%' OR createdUser.username LIKE '%${query.content}%' OR company.name LIKE '%${query.content}%' OR vessel.name LIKE '%${query.content}%'  ` +
        `OR vessel.code LIKE '%${query.content}%' OR vessel.imoNumber LIKE '%${query.content}%' OR cargoType.code LIKE '%${query.content}%' OR cargoType.name LIKE '%${query.content}%' OR cargo.name LIKE '%${query.content}%' OR cargo.code LIKE '%${query.content}%')`,
      );
    }

    if (query.ids?.length) {
      queryBuilder.andWhere('vesselScreening.id IN (:...ids)', {
        ids: query.ids,
      });
    }

    if (query.dateRequestFrom) {
      queryBuilder.andWhere(
        `vesselScreening.dateRequest >= '${new Date(query.dateRequestFrom).toISOString()}'`,
      );
    }

    if (query.dateRequestTo) {
      queryBuilder.andWhere(
        `vesselScreening.dateRequest <= '${new Date(query.dateRequestTo).toISOString()}'`,
      );
    }

    if (!RoleScopeCheck.isAdmin(token)) {
      const { whereForExternal, whereForMainAndInternal } = await _supportWhereDOCChartererOwner(
        this.manager,
        token.explicitCompanyId,
        'vesselScreening',
      );
      queryBuilder
        .leftJoin('vessel.divisionMapping', 'divisionMapping')
        .leftJoin('divisionMapping.division', 'division')
        .leftJoin('division.users', 'users')
        .leftJoin(
          'vessel.vesselDocHolders',
          'vesselDocHolders',
          'vesselDocHolders.vesselId = vessel.id',
        )
        .leftJoin('vesselDocHolders.company', 'companyVesselDocHolders')
        .leftJoin('vessel.vesselOwners', 'vesselOwners')
        .leftJoin('vesselOwners.company', 'companyVesselOwnersPlans')
        .leftJoin('vessel.vesselCharterers', 'vesselCharterers')
        .leftJoin('vesselCharterers.company', 'companyVesselCharterers');

      if (
        token.companyLevel === CompanyLevelEnum.MAIN_COMPANY ||
        token.companyLevel === CompanyLevelEnum.INTERNAL_COMPANY
      ) {
        queryBuilder.andWhere(`( users.id = '${token.id}' ` + whereForMainAndInternal + ')');
      } else {
        queryBuilder.andWhere(`( users.id = '${token.id}' ` + whereForExternal + ')');
      }
    }

    //AG-Grid
    const connection = getConnection();
    const subQueryBuilder = connection.createQueryBuilder();
    queryBuilder.addSelect(fieldSelects);

    if (body) {
      convertFilterField(body, VESSEL_SCREENING_FILTER_FIELDS);

      const querySummary = this.connection
        .getCustomRepository(VesselScreeningSummaryRepository)
        .createQueryBuilder('vesselScreeningSummary')
        .select([
          `"vesselScreeningSummary"."vesselScreeningId"`,
          `SUM(vesselScreeningSummary.observedScore) AS "sumObservedScore"`,
          `COUNT(*) AS "count"`,
        ])
        .where(`vesselScreeningSummary.reviewStatus IN ('Reject', 'Accept')`)
        .groupBy(`vesselScreeningSummary.vesselScreeningId, vesselScreeningSummary.tabName`);

      const queryRiskScore = this.connection
        .createQueryBuilder()
        .select(`"vesselScreeningId"`)
        .addSelect([
          `CASE 
            WHEN COUNT(*) FILTER (WHERE "sumObservedScore" IS NOT NULL) = 0 THEN NULL
            ELSE ROUND(CAST(SUM("sumObservedScore" / "count") / COUNT(*) AS NUMERIC), 2)
           END AS "riskRating"`,
        ])
        .from(`(${querySummary.getQuery()})`, 'summary')
        .groupBy(`"vesselScreeningId"`);

      queryBuilder
        .leftJoin(
          `(${queryRiskScore.getQuery()})`,
          'riskScore',
          `"riskScore"."vesselScreeningId" = "vesselScreening"."id"`,
        )
        .addSelect(`"riskScore"."riskRating"`)
        .groupBy(
          `
          vesselScreening.id, 
          "riskScore"."riskRating",
          ${fieldSelects.join(', ')}
      `,
        )
        .andWhere(`vesselScreening.deleted = false`);
      queryBuilder.distinctOn(['vesselScreening.id']);
      subQueryBuilder
        .select(`DISTINCT "vesselScreening_id" AS id`)
        .from(`(${queryBuilder.getQuery()})`, 'subquery');

      createWhereSql(body, subQueryBuilder);
      createSelectSql(body, subQueryBuilder, null, '"vesselScreening_id"');

      queryBuilder.groupBy();
    }

    //update after build sql ag-grid
    queryBuilder
      .leftJoinAndSelect('vesselScreening.ports', 'vsPort')
      .leftJoin('vsPort.port', 'port')
      .leftJoin('vsPort.terminal', 'terminal')
      .leftJoin('terminal.portMaster', 'portMaster')
      .addSelect([
        'port.code',
        'port.name',
        'port.country',
        'terminal.code',
        'terminal.name',
        'portMaster.code',
        'portMaster.name',
        'portMaster.country',
      ]);

    return await handleGetDataForAGGrid(
      this,
      queryBuilder,
      query,
      body,
      subQueryBuilder,
      queryBuilder,
      'vesselScreening',
      [`"riskRating"`],
    );
  }

  async updateVesselScreening(
    vesselScreeningId: string,
    user: TokenPayloadModel,
    body: UpdateVesselScreeningDto,
  ) {
    try {
      const prepareVesselScreening = {
        id: vesselScreeningId,
        ...body,
        pics: body.picIds ? body.picIds.map((userId) => ({ id: userId } as User)) : undefined,
        updatedUserId: user.id,
        cargos: body.cargoIds.map((cargoId) => ({ id: cargoId } as Cargo)),
      };
      Reflect.deleteProperty(prepareVesselScreening, 'picIds');
      Reflect.deleteProperty(prepareVesselScreening, 'ports');

      return await this.connection.transaction(async (manager) => {
        if (body.status && body.status === VesselScreeningStatusEnum.IN_PROGRESS && body.vesselId) {
          const vesselScreeningExistedStatusInProgress = await manager.findOne(VesselScreening, {
            where: {
              status: VesselScreeningStatusEnum.IN_PROGRESS,
              deleted: false,
              companyId: user.companyId,
              id: Not(vesselScreeningId),
              vesselId: body.vesselId,
            },
          });
          if (vesselScreeningExistedStatusInProgress) {
            throw new BaseError({ message: 'vesselScreening.EXISTED_STATUS_IN_PROGRESS' });
          }
        }
        const vesselScreeningFound = await manager.findOne(VesselScreening, {
          where: { id: vesselScreeningId, companyId: user.companyId, deleted: false },
          relations: ['ports', 'cargos'],
        });

        if (!vesselScreeningFound) {
          throw new BaseError({ status: 404, message: 'vesselScreening.NOT_FOUND' });
        }
        //Validate Update Vessel Screening
        await this._validateUpdateVesselScreeningDto(body, user, vesselScreeningFound);

        await manager.save(
          VesselScreening,
          Object.assign(vesselScreeningFound, prepareVesselScreening),
        );

        // extract month and year
        const monthList = Object.keys(SelfAssessmentMonthEnum).map(
          (key) => SelfAssessmentMonthEnum[key],
        );
        const createdAtMonth = vesselScreeningFound.createdAt.getMonth();
        const finalCreatedDateMonth = monthList[createdAtMonth];
        const createdAtYear = vesselScreeningFound.updatedAt.getFullYear();
        const vesselScreeningUpdated = await manager.save(VesselScreening, {
          id: vesselScreeningFound.id,
          createdAt_Year: createdAtYear,
          createdAt_Month: finalCreatedDateMonth,
        });
        // saving the vessel screening status history
        await manager.insert(VesselScreeningHistory, {
          vesselScreeningId: prepareVesselScreening.id,
          status: prepareVesselScreening.status,
          remark: prepareVesselScreening.remark,
          createdUserId: prepareVesselScreening.updatedUserId,
        });
        //#region Prepare data
        const rawVSPort: VesselScreeningPort[] = [];
        for (let i = 0; i < body.ports.length; i++) {
          const vsport = body.ports[i];
          rawVSPort.push({
            id: vsport.id ? vsport.id : Utils.strings.generateUUID(),
            berth: vsport.berth,
            portId: vsport.portId,
            layCanDate: new Date(vsport.layCanDate),
            terminalId: vsport.terminalId ? vsport.terminalId : null,
            vesselScreeningId,
          } as VesselScreeningPort);
        }

        const listCurrentVSPort = vesselScreeningFound.ports;
        const listCurrentVSPortIds: string[] = [];
        const listNewVSPortIds: string[] = [];

        if (listCurrentVSPort && listCurrentVSPort.length > 0) {
          listCurrentVSPort.forEach((item: VesselScreeningPort) => {
            listCurrentVSPortIds.push(item.id);
          });
        }
        // console.log('current ids: ', listCurrentVSPortIds);
        if (rawVSPort && rawVSPort.length > 0) {
          rawVSPort.forEach((item: VesselScreeningPort) => {
            listNewVSPortIds.push(item.id);
          });
        }
        // console.log('new ids: ', listNewVSPortIds);

        const listVSPortUpdateIds = MySet.intersect(
          new Set(listCurrentVSPortIds),
          new Set(listNewVSPortIds),
        );
        // console.log('ids update: ', Array.from(listVSPortUpdateIds));

        const listVSPortCreateIds = MySet.difference(
          new Set(listNewVSPortIds),
          new Set(listCurrentVSPortIds),
        );
        // console.log('ids create: ', Array.from(listVSPortCreateIds));

        const listVSPortDeleteIds = MySet.difference(
          new Set(listCurrentVSPortIds),
          new Set(listNewVSPortIds),
        );
        // console.log('ids delete: ', Array.from(listVSPortDeleteIds));

        const vsPortCreate = rawVSPort.filter((item) => listVSPortCreateIds.has(item.id));

        const vsPortUpdate = rawVSPort.filter((item) => listVSPortUpdateIds.has(item.id));

        if (vsPortCreate.length > 0) {
          const preparedPorts: VesselScreeningPort[] = [];
          for (const [index, item] of vsPortCreate.entries()) {
            preparedPorts.push(
              Object.assign(item, {
                createdAt: new Date(moment().unix() * 1000 - index),
              }),
            );
          }

          await manager.save(VesselScreeningPort, preparedPorts);
        }
        if (vsPortUpdate.length > 0) {
          await manager.save(VesselScreeningPort, vsPortUpdate);
        }
        if (Array.from(listVSPortDeleteIds).length > 0) {
          await manager.delete(VesselScreeningPort, { id: In(Array.from(listVSPortDeleteIds)) });
        }
        //#endregion
        return 1;

        // const resultUpdate = await manager.update(
        //   VesselScreening,
        //   {
        //     id: vesselScreeningId,
        //     companyId: user.companyId,
        //     deleted: false,
        //   },
        //   prepareVesselScreening,
        // );
        // console.log('resultUpdate: ', resultUpdate);

        // if (resultUpdate.affected === 0) {
        //   throw new BaseError({ status: 404, message: 'vesselScreening.NOT_FOUND' });
        // }
      });
    } catch (ex) {
      LoggerCommon.error(
        '[VesselScreeningRepository] updateVesselScreening error ',
        ex.message || ex,
      );
      throw ex;
    }
  }

  async detailByVesselScreeningId(vesselScreeningId: string, user: TokenPayloadModel) {
    const vesselScreening = await this.getOneQB(
      this.createQueryBuilder('vesselScreening')
        .leftJoin('vesselScreening.createdUser', 'createdUser')
        .leftJoin('vesselScreening.vessel', 'vessel')
        .leftJoin('vesselScreening.company', 'company')
        .leftJoin('company.parent', 'parent')
        .leftJoin('vesselScreening.companyRequest', 'companyRequest')
        .leftJoin('vesselScreening.transferType', 'transferType')
        .leftJoin('vesselScreening.cargoType', 'cargoType')
        .leftJoin('vesselScreening.cargos', 'cargo')
        // .leftJoin('vesselScreening.port', 'port')
        // .leftJoin('vesselScreening.terminal', 'terminal')
        .leftJoin('vesselScreening.shipParticular', 'shipParticular')
        .leftJoin('vesselScreening.pics', 'pics')
        .leftJoinAndMapOne(
          'vesselScreening.rightShip',
          RightShip,
          'rightShip',
          'rightShip.vesselId = vessel.id',
        )

        // .leftJoinAndMapMany(
        //   'vesselScreening.incidentInvestigation',
        //   IncidentInvestigation,
        //   'incidentInvestigation',
        //   'incidentInvestigation.vesselId = vessel.id',
        // )

        // .leftJoinAndMapMany(
        //   'vesselScreening.planningRequest',
        //   PlanningRequest,
        //   'planningRequest',
        //   'planningRequest.vesselId = vessel.id',
        // )
        .leftJoinAndSelect('vesselScreening.ports', 'vsPort')
        .leftJoin('vesselScreening.voyageType', 'voyageType')
        .leftJoin('vsPort.port', 'port')
        .leftJoin('vsPort.terminal', 'terminal')
        .leftJoin('terminal.portMaster', 'portMaster')
        .select([
          'vesselScreening',
          'vsPort',
          // 'incidentInvestigation',
          // 'planningRequest',
          'company.id',
          'company.name',
          'company.companyIMO',
          'parent.id',
          'parent.name',
          'companyRequest.name',
          'createdUser.username',
          'transferType.name',
          'transferType.code',
          'cargoType.name',
          'cargoType.code',
          'cargo.id',
          'cargo.name',
          'cargo.code',
          'vessel.id',
          'vessel.name',
          'vessel.code',
          'vessel.imoNumber',
          'vessel.customerRestricted',
          'vessel.blacklistOnMOUWebsite',
          'vessel.isVesselRestricted',
          'vessel.isCompanyRestricted',
          'port.code',
          'port.name',
          'port.country',
          'terminal.code',
          'terminal.name',
          'portMaster.code',
          'portMaster.name',
          'portMaster.country',
          'shipParticular',
          'pics.id',
          'pics.username',
          'pics.jobTitle',
          'rightShip',
          'voyageType.id',
          'voyageType.name',
        ])
        .where(
          '(vesselScreening.companyId = :companyId OR company.parentId = :companyId) and vesselScreening.deleted = false and vesselScreening.id = :vesselScreeningId',
          {
            companyId: user.companyId,
            vesselScreeningId,
          },
        )
        .orderBy('vsPort.createdAt', 'DESC'),
      // .addOrderBy('planningRequest.createdAt', 'DESC'),
    );
    if (!vesselScreening) {
      throw new BaseError({ status: 404, message: 'vesselScreening.NOT_FOUND' });
    }
    const [incidents, planningRequests] = await Promise.all([
      // this.getCargos(vesselScreeningId),
      // this.getPorts(vesselScreeningId),
      // this.getPics(vesselScreeningId),
      // this.getRightShip(vesselScreening.vessel.id),
      this.getIncidentInvestigations(vesselScreening?.vessel?.id),
      this.getPlanningRequests(vesselScreening?.vessel?.id),
    ]);
    const recordsFound = await this.manager
      .createQueryBuilder(
        VesselScreeningSummaryAttachmentRemark,
        'vesselScreeningSummaryAttachmentRemark',
      )
      .select()
      .where(
        'vesselScreeningSummaryAttachmentRemark.vesselScreeningId = :vesselScreeningId AND vesselScreeningSummaryAttachmentRemark.tabName IN (:...tabNames)',
        {
          vesselScreeningId,
          tabNames: [
            VesselScreeningSummaryTabNameEnum.SHIP_PARTICULARS,
            VesselScreeningSummaryTabNameEnum.TECHNICAL,
            VesselScreeningSummaryTabNameEnum.SAFETY_MANAGEMENT,
            VesselScreeningSummaryTabNameEnum.INSPECTIONS,
            VesselScreeningSummaryTabNameEnum.PILOT_TERMINAL_FEEDBACK,
          ],
        },
      )
      .orderBy('vesselScreeningSummaryAttachmentRemark.createdAt', 'DESC')
      .getMany();
    let attachmentIds: string[] = [];
    for (const item of recordsFound) {
      attachmentIds = attachmentIds.concat(item.attachments);
    }

    // const { incidentInvestigation, planningRequest, ...other } = vesselScreening;
    const hasOpenIncident = incidents?.some(
      (incident) => incident?.reviewStatus !== IncidentInvestigationReviewStatus.CLEARED,
    );

    const hasOpenInspection = planningRequests?.some(
      (planning) => planning?.globalStatus === 'Opening schedule',
    );

    const mainCompany = await this.connection.getCustomRepository(CompanyRepository).findOne({
      where: {
        id: user.companyId,
      },
      select: ['id', 'isInspection', 'isQA'],
    });

    let displaySailSafetyInspection = false;
    let sailSafetyInspection: Date;
    const foundCompletedPr = planningRequests.find(
      (pr) => pr.status === PlanningRequestStatus.COMPLETED,
    );

    if (foundCompletedPr) {
      const prHistory = await this.manager
        .getCustomRepository(PlanningRequestHistoryRepository)
        .findOne({
          planningRequestId: foundCompletedPr.id,
        });

      if (mainCompany.isInspection && mainCompany.isQA) {
        displaySailSafetyInspection = true;
        sailSafetyInspection = prHistory.createdAt;
      }
    }

    return {
      ...vesselScreening,
      hasOpenIncident: hasOpenIncident ? 'Yes' : 'No',
      hasOpenInspection: hasOpenInspection ? 'Yes' : 'No',
      sailSafetyInspection: { display: displaySailSafetyInspection, value: sailSafetyInspection },
      attachmentIds,
    };
  }

  async updateRemarkVesselScreening(
    vesselScreeningId: string,
    remarkId: string,
    user: TokenPayloadModel,
    body: CreateVesselScreeningRemarkDto,
  ) {
    try {
      return await this.connection.transaction(async (manager) => {
        const vesselScreening = await manager.findOne(VesselScreening, {
          id: vesselScreeningId,
          companyId: user.companyId,
          deleted: false,
        });
        if (!vesselScreening) {
          throw new BaseError({ status: 404, message: 'vesselScreening.NOT_FOUND' });
        }
        const userHistory = await this.manager
          .getCustomRepository(UserRepository)
          ._getUserInfoForHistory(user.id);
        const preparedRemark = {
          vesselScreeningId: vesselScreeningId,
          remark: body.remark,
          updatedUser: userHistory,
        };
        const result = await manager.update(
          VesselScreeningRemark,
          {
            id: remarkId,
            vesselScreeningId: vesselScreeningId,
          },
          preparedRemark,
        );
        if (result.affected === 0) {
          throw new BaseError({ status: 404, message: 'vesselScreening.REMARK_NOT_FOUND' });
        }
      });
    } catch (ex) {
      LoggerCommon.error(
        '[VesselScreeningRepository] updateRemarkVesselScreening error ',
        ex.message || ex,
      );
      throw ex;
    }
  }

  async createRemarkVesselScreening(
    vesselScreeningId: string,
    user: TokenPayloadModel,
    body: CreateVesselScreeningRemarkDto,
  ) {
    try {
      return await this.connection.transaction(async (manager) => {
        const vesselScreening = await manager.findOne(VesselScreening, {
          id: vesselScreeningId,
          companyId: user.companyId,
          deleted: false,
        });
        if (!vesselScreening) {
          throw new BaseError({ status: 404, message: 'vesselScreening.NOT_FOUND' });
        }
        const userHistory = await this.manager
          .getCustomRepository(UserRepository)
          ._getUserInfoForHistory(user.id);
        const preparedRemark = {
          vesselScreeningId: vesselScreeningId,
          remark: body.remark,
          createdUser: userHistory,
        };
        await manager.insert(VesselScreeningRemark, preparedRemark);
      });
    } catch (ex) {
      LoggerCommon.error(
        '[VesselScreeningRepository] createRemarkVesselScreening error ',
        ex.message || ex,
      );
      throw ex;
    }
  }

  async createOrUpdateVesselScreeningSummary(
    vesselScreeningId: string,
    user: TokenPayloadModel,
    body: CreateVesselScreeningSummaryDto,
    isRestricted: boolean,
    isPilot: boolean,
  ) {
    try {
      return await this.connection.transaction(async (manager) => {
        const vesselScreening = await manager.findOne(VesselScreening, {
          id: vesselScreeningId,
          companyId: user.companyId,
          deleted: false,
        });

        const vesselScreeningSummary = await manager.findOne(VesselScreeningSummary, {
          vesselScreeningId,
          reference: body.reference,
        });

        if (!vesselScreening) {
          throw new BaseError({ status: 404, message: 'vesselScreening.NOT_FOUND' });
        }

        const numberOfRecords = await this.supportCountNumberRecordsByTab(
          body.reference,
          user,
          vesselScreeningId,
          isRestricted,
          isPilot,
        );

        if (
          body?.status === VesselScreeningSummaryStatusEnum.COMPLETED &&
          numberOfRecords === 0 &&
          !vesselScreeningSummary?.potentialRisk &&
          !vesselScreeningSummary?.observedRisk
        ) {
          Object.assign(body, {
            potentialRisk: VesselScreeningPotentialRiskEnum.NEGLIGIBLE,
            potentialScore: 0,
            observedRisk: VesselScreeningPotentialRiskEnum.NEGLIGIBLE,
            observedScore: 0,
          });
        }

        //START updateReviewStatus: if current review status = Open and have risk change -> change review status(in section summary and in tab)  to INPROGRESS
        const isChangeRiskValue =
          body?.potentialRisk !== undefined || body?.observedRisk !== undefined;
        if (!vesselScreeningSummary?.reviewStatus && isChangeRiskValue) {
          // update section summary
          await manager.update(
            VesselScreeningSummary,
            {
              vesselScreeningId,
              tabName: body.tabName,
            },
            { reviewStatus: VesselScreeningSummaryReviewStatusEnum.IN_PROGRESS },
          );
        }
        if (
          isChangeRiskValue &&
          (vesselScreeningSummary?.status === VesselScreeningSummaryStatusEnum.OPEN ||
            (body?.reference === VesselScreeningSummaryReferenceEnum.PLAN_AND_DRAWINGS &&
              vesselScreeningSummary?.status === VesselScreeningSummaryStatusEnum.PENDING_INFO))
        ) {
          Object.assign(body, {
            status: VesselScreeningSummaryStatusEnum.IN_PROGRESS,
          });
        }
        //END updateReviewStatus

        const resultUpdate = await manager.update(
          VesselScreeningSummary,
          {
            vesselScreeningId,
            reference: body.reference,
          },
          body,
        );

        if (resultUpdate?.affected === 0) {
          await manager.insert(VesselScreeningSummary, Object.assign(body, { vesselScreeningId }));
        }
        // update the average observed risk in vessel scheduler (voyageMasterDetails)
        const rawQuery = `SELECT
                        sum(vss."observedScore") as "sumObservedScore",
                        count(*) as "count"
                      FROM
                        vessel_screening_summary vss
                      WHERE
                        "vesselScreeningId" = $1 AND
                        ("reviewStatus" = 'Reject' or "reviewStatus" = 'Accept')
                      GROUP BY "tabName" ;`;
        const data = await this.connection.query(rawQuery, [vesselScreeningId]);
        let totalRiskRating = 0;
        let t_flag = false; // to check all tab is null or contain some data
        for (let i = 0; i < data?.length; i++) {
          let averageRiskByTab = 0;
          if (data[i].sumObservedScore !== null) {
            t_flag = true;
            averageRiskByTab = Number(data[i].sumObservedScore) / Number(data[i].count);
          }
          if (averageRiskByTab) {
            totalRiskRating += Number(averageRiskByTab);
          }
        }
        let riskRating = totalRiskRating / data.length;

        // const riskRating = flag ? (totalRiskRating / data.length).toFixed(2) : null;
        if (t_flag && !Number.isInteger(riskRating)) {
          riskRating = Number((totalRiskRating / data.length).toFixed(2));
        }
        if (!t_flag) {
          riskRating = null;
        }
        // update the risk rating into voyage master details(vessel scheduler)
        const voyageMasterDetails = manager.connection.getRepository(VoyageMasterDetails);
        if (riskRating) {
          await voyageMasterDetails.update(
            { vesselId: vesselScreening.vesselId },
            { observedRiskLastScreening: riskRating, lastScreeningDate: vesselScreening.updatedAt },
          );
        }
      });
    } catch (ex) {
      LoggerCommon.error(
        '[VesselScreeningRepository] createOrUpdateVesselScreeningSummary error ',
        ex.message || ex,
      );
      throw ex;
    }
  }
  async detailVesselScreeningSummary(
    vesselScreeningId: string,
    user: TokenPayloadModel,
    query: DetailVesselScreeningSummaryDTO,
  ) {
    return await this.connection.transaction(async (manager) => {
      const vesselScreening = await manager.findOne(VesselScreening, {
        id: vesselScreeningId,
        companyId: user.companyId,
        deleted: false,
      });
      if (!vesselScreening) {
        throw new BaseError({ status: 404, message: 'vesselScreening.NOT_FOUND' });
      }
      return manager.findOne(VesselScreeningSummary, {
        vesselScreeningId,
        reference: query.reference,
      });
    });
  }
  async detailRiskAnalysisVesselScreeningSummary(
    vesselScreeningId: string,
    user: TokenPayloadModel,
    body: vesselScreeningSummaryReferenceDTO,
  ) {
    return await this.connection.transaction(async (manager) => {
      const vesselScreening = await manager.findOne(VesselScreening, {
        id: vesselScreeningId,
        companyId: user.companyId,
        deleted: false,
      });
      if (!vesselScreening) {
        throw new BaseError({ status: 404, message: 'vesselScreening.NOT_FOUND' });
      }
      // const listVesselScreeningSummary = await manager.find(VesselScreeningSummary, {
      //   where: {
      //     vesselScreeningId,
      //     reference: Not(In([VesselScreeningSummaryReferenceEnum.INJURIES])),
      //   },
      //   order: {
      //     tabName: 'ASC',
      //   },
      // });
      const listVesselScreeningSummary = await manager
        .createQueryBuilder(VesselScreeningSummary, 'vss')
        .where('vss.vesselScreeningId = :vesselScreeningId', { vesselScreeningId })
        .andWhere('vss.reference NOT IN (:...excludedReferences)', {
          excludedReferences: [VesselScreeningSummaryReferenceEnum.INJURIES],
        })
        .andWhere('vss.reference IN (:...allowedReferences)', {
          allowedReferences: body.vesselScreeningSummaryReferences,
        })
        .orderBy('vss.tabName', 'ASC')
        .getMany();

      return chain(listVesselScreeningSummary)
        .groupBy('tabName')
        .map((value, key) => ({ tabName: key, data: value }))
        .value();
    });
  }

  async updateReviewStatusVesselScreeningSummary(
    vesselScreeningId: string,
    user: TokenPayloadModel,
    body: UpdateReViewStatusVesselScreeningSummaryDTO,
  ) {
    try {
      return await this.connection.transaction(async (manager) => {
        const vesselScreening = await manager.findOne(VesselScreening, {
          id: vesselScreeningId,
          companyId: user.companyId,
          deleted: false,
        });
        if (!vesselScreening) {
          throw new BaseError({ status: 404, message: 'vesselScreening.NOT_FOUND' });
        }
        const resultUpdate = await manager.update(
          VesselScreeningSummary,
          {
            vesselScreeningId,
            tabName: body.tabName,
          },
          body,
        );
        if (resultUpdate.affected === 0) {
          await manager.insert(VesselScreeningSummary, Object.assign(body, { vesselScreeningId }));
        }
        const flag =
          body.reviewStatus === VesselScreeningSummaryReviewStatusEnum.IN_PROGRESS ? true : false;
        await this._autoUpdateStatusVesselScreening(flag, vesselScreening, user);
        const rawQuery = `SELECT
                        sum(vss."observedScore") as "sumObservedScore",
                        count(*) as "count"
                      FROM
                        vessel_screening_summary vss
                      WHERE
                        "vesselScreeningId" = $1 AND
                        ("reviewStatus" = 'Reject' or "reviewStatus" = 'Accept')
                      GROUP BY "tabName" ;`;
        const data = await this.connection.query(rawQuery, [vesselScreeningId]);
        let totalRiskRating = 0;
        let t_flag = false; // to check all tab is null or contain some data
        for (let i = 0; i < data.length; i++) {
          let averageRiskByTab = 0;
          if (data[i].sumObservedScore !== null) {
            t_flag = true;
            averageRiskByTab = Number(data[i].sumObservedScore) / Number(data[i].count);
          }
          if (averageRiskByTab) {
            totalRiskRating += Number(averageRiskByTab);
          }
        }
        let riskRating = totalRiskRating / data.length;

        // const riskRating = flag ? (totalRiskRating / data.length).toFixed(2) : null;
        if (t_flag && !Number.isInteger(riskRating)) {
          riskRating = Number((totalRiskRating / data.length).toFixed(2));
        }
        if (!t_flag) {
          riskRating = null;
        }
        // update the risk rating into voyage master details(vessel scheduler)
        const voyageMasterDetails = manager.connection.getRepository(VoyageMasterDetails);
        if (riskRating) {
          await voyageMasterDetails.update(
            { vesselId: vesselScreening.vesselId },
            { observedRiskLastScreening: riskRating, lastScreeningDate: vesselScreening.updatedAt },
          );
        }
      });
    } catch (ex) {
      LoggerCommon.error(
        '[VesselScreeningRepository] updateReviewStatusVesselScreeningSummary error ',
        ex.message || ex,
      );
      throw ex;
    }
  }

  async supportCountNumberRecordsByTab(
    reference: VesselScreeningSummaryReferenceEnum,
    token: TokenPayloadModel,
    vesselScreeningId: string,
    isRestricted = false,
    isPilot = false,
  ) {
    const query = { page: 1, pageSize: -1 };
    const vesselScreening = await this.connection
      .getCustomRepository(VesselScreeningRepository)
      .createQueryBuilder('vesselScreening')
      .leftJoin('vesselScreening.vessel', 'vessel')
      .leftJoin('vessel.vesselType', 'vesselType')
      .select(['vesselScreening.id'])
      .addSelect(['vessel.id', 'vesselType.id', 'vesselType.name'])
      .where(
        'vesselScreening.companyId = :companyId and vesselScreening.deleted = false and vesselScreening.id = :vesselScreeningId',
        {
          companyId: token.companyId,
          vesselScreeningId,
        },
      )
      .getOne();

    let number = 0;
    switch (reference) {
      case VesselScreeningSummaryReferenceEnum.PLAN_AND_DRAWINGS:
        const planDrawings = await this.connection
          .getCustomRepository(PlansDrawingsMasterRepository)
          .listPlansDrawingsMaster(token, {
            status: CommonStatus.ACTIVE,
            vesselId: vesselScreening?.vessel.id || null,
            vesselTypeName: vesselScreening?.vessel?.vesselType.name || null,
          });
        number = planDrawings.data.length;
        break;
      case VesselScreeningSummaryReferenceEnum.CONDITION_OF_CLASS_DISPENSATIONS:
        const classDispensations = await this.connection
          .getCustomRepository(ClassDispensationsRepository)
          .listClassDispensationsByVesselScreeningId(
            vesselScreeningId,
            {
              ...query,
              filterRisk: VesselScreeningFilterRiskEnum.POTENTIAL,
              vesselId: vesselScreening.vesselId,
            },
            token,
          );
        number = classDispensations.list.data.length;
        break;
      case VesselScreeningSummaryReferenceEnum.SURVEY_CLASS_INFO:
        const surveyClass = await this.connection
          .getCustomRepository(SurveyClassInfoRepository)
          .listSurveyClassInfoByVesselScreeningId(
            vesselScreeningId,
            { ...query, vesselId: vesselScreening.vessel.id },
            token,
          );
        number = surveyClass.list.data.length;
        break;

      case VesselScreeningSummaryReferenceEnum.MAINTENANCE_PERFORMANCE:
        const maintenances = await this.connection
          .getCustomRepository(MaintenancePerformanceRepository)
          .listAndAggregateMaintenancePerformance(
            {
              ...query,
              filterRisk: VesselScreeningFilterRiskEnum.POTENTIAL,
            },
            token,
            vesselScreeningId,
          );
        number = maintenances.list.data.length;
        break;

      case VesselScreeningSummaryReferenceEnum.OTHER_TECHNICAL_RECORDS:
        const otherTechRecords = await this.connection
          .getCustomRepository(OtherTechRecordsRepository)
          .listAndAggregateOtherTechniqueRecords(
            {
              ...query,
              filterRisk: VesselScreeningFilterRiskEnum.POTENTIAL,
            },
            token,
            vesselScreeningId,
          );
        number = otherTechRecords.list.data.length;
        break;
      case VesselScreeningSummaryReferenceEnum.DRY_DOCKING:
        const dryDockings = await this.connection
          .getCustomRepository(DryDockingRepository)
          .listAndAggregateDryDocking(
            {
              ...query,
              filterRisk: VesselScreeningFilterRiskEnum.POTENTIAL,
            },
            token,
            vesselScreeningId,
          );
        number = dryDockings.list.data.length;
        break;
      case VesselScreeningSummaryReferenceEnum.INCIDENTS:
        const incidents = await this.connection
          .getCustomRepository(IncidentInvestigationRepository)
          .listIncidentInvestigation(
            {
              ...query,
              filterRisk: VesselScreeningFilterRiskEnum.POTENTIAL,
              vesselId: vesselScreening.vessel.id,
            },
            token,
            isRestricted,
          );
        number = incidents.data.length;
        break;
      case VesselScreeningSummaryReferenceEnum.INJURIES:
        const injuries = await this.connection
          .getCustomRepository(InjuryRepository)
          .listAndAggregateInjury(
            {
              ...query,
              filterRisk: VesselScreeningFilterRiskEnum.POTENTIAL,
            },
            token,
            vesselScreeningId,
          );
        number = injuries.list.data.length;
        break;
      case VesselScreeningSummaryReferenceEnum.OTHER_SMS_RECORDS:
        const otherSmsRecords = await this.connection
          .getCustomRepository(OtherSmsRecordsRepository)
          .listAndAggregateOtherSmsRecords(
            {
              ...query,
              filterRisk: VesselScreeningFilterRiskEnum.POTENTIAL,
            },
            token,
            vesselScreeningId,
          );
        number = otherSmsRecords.list.data.length;
        break;

      case VesselScreeningSummaryReferenceEnum.PORT_STATE_CONTROL:
        const portStateControls = await this.connection
          .getCustomRepository(PortStateControlRepository)
          .listAndAggregatePortStateControl(
            {
              ...query,
              filterRisk: VesselScreeningFilterRiskEnum.POTENTIAL,
            },
            token,
            vesselScreeningId,
          );
        number = portStateControls.list.data.length;
        break;

      case VesselScreeningSummaryReferenceEnum.INTERNAL_INSPECTIONS_AUDITS:
        const internalInspections = await this.connection
          .getCustomRepository(InternalInspectionsRepository)
          .listAndAggregateInternalInspections(
            {
              ...query,
              filterRisk: VesselScreeningFilterRiskEnum.POTENTIAL,
            },
            token,
            vesselScreeningId,
          );
        number = internalInspections.list.data.length;
        break;

      case VesselScreeningSummaryReferenceEnum.EXTERNAL_INSPECTIONS:
        const externalInspections = await this.connection
          .getCustomRepository(ExternalInspectionsRepository)
          .listAndAggregateExternalInspections(
            {
              ...query,
              filterRisk: VesselScreeningFilterRiskEnum.POTENTIAL,
            },
            token,
            vesselScreeningId,
          );
        number = externalInspections.list.data.length;
        break;

      case VesselScreeningSummaryReferenceEnum.PILOT_TERMINAL_FEEDBACK:
        const pilotTerminalFeedbacks = await this.connection
          .getCustomRepository(PilotTerminalFeedbackRepository)
          .listPilotTerminalFeedback(
            token,
            {
              ...query,
              vesselId: vesselScreening.vessel.id,
            },
            isRestricted,
            isPilot,
          );
        number = pilotTerminalFeedbacks.data.length;
        break;

      case VesselScreeningSummaryReferenceEnum.VESSEL_COMPANY_FEEDBACK:
        const vesselCompanyFeedbacks = await this.connection
          .getCustomRepository(VesselCompanyFeedbackRepository)
          .listVesselCompanyFeedback(
            token,
            {
              ...query,
              vesselId: vesselScreening.vessel.id,
              companyId: token.companyId,
            },
            isRestricted,
          );
        number = vesselCompanyFeedbacks.data.length;
        break;
      // case VesselScreeningSummaryReferenceEnum.VESSEL_GENERAL_RISK:
      //   const vesselRiskRequests = await this.connection
      //     .getCustomRepository(VesselRiskRequestRepository)
      //     .listVesselRiskRequest(token, { ...query, vesselId: vesselScreening.vessel.id });
      //   number = vesselRiskRequests.data.length;
      //   break;
      // case VesselScreeningSummaryReferenceEnum.DOC_HOLDER:
      //   const docHolder = await this.connection
      //     .getRepository(VesselRepository)
      //     .listDocHolder(token, { ...query, vesselId: vesselScreening.vessel.id });
      //   number = docHolder.data.length;
      //   break;
      // case VesselScreeningSummaryReferenceEnum.CHARTERER:
      //   const charterer = await this.connection
      //     .getCustomRepository(VesselRepository)
      //     .listCharterer(token, { ...query, vesselId: vesselScreening.vessel.id });
      //   number = charterer.data.length;
      //   break;
      // case VesselScreeningSummaryReferenceEnum.VESSEL_OWNER:
      //   const vesselOwner = await this.connection
      //     .getCustomRepository(VesselRepository)
      //     .listVesselOwner(token, { ...query, vesselId: vesselScreening.vessel.id });
      //   number = vesselOwner.data.length;
      //   break;
    }
    return number;
  }

  async _autoUpdateStatusVesselScreening(
    flag: boolean,
    vesselScreening: VesselScreening,
    user: TokenPayloadModel,
  ) {
    const vesselScreeningExistedStatusInProgress = await this.findOne({
      where: {
        status: VesselScreeningStatusEnum.IN_PROGRESS,
        deleted: false,
        companyId: user.companyId,
        vesselId: vesselScreening.vesselId,
      },
    });

    if (
      flag &&
      !vesselScreeningExistedStatusInProgress &&
      vesselScreening.status === VesselScreeningStatusEnum.OPEN
    ) {
      await this.update(
        { id: vesselScreening.id },
        { status: VesselScreeningStatusEnum.IN_PROGRESS },
      );
    }

    if (flag && !vesselScreeningExistedStatusInProgress && !vesselScreening.reviewStatus) {
      await this.update(
        { id: vesselScreening.id },
        { reviewStatus: VesselScreeningReviewStatusEnum.IN_PROGRESS },
      );
    }
    return 1;
  }

  async getByCargoId(cargoId: string, token: TokenPayloadModel) {
    const queryBuilder = this.createQueryBuilder('vesselScrenning')
      .leftJoin('vesselScrenning.cargos', 'cargo')
      .select()
      .where('cargo.id = :cargoId AND vesselScrenning.companyId = :companyId ', {
        cargoId: cargoId,
        companyId: token.companyId,
      })
      .andWhere('vesselScrenning.deleted = false and cargo.deleted = false ');
    return await queryBuilder.getOne();
  }

  async _validateCreateVesselScreeningDto(body: CreateVesselScreeningDto, user: TokenPayloadModel) {
    //check cargotype status
    await this.manager
      .getCustomRepository(CargoTypeRepository)
      .getCargoTypeById(body.cargoTypeId, user, CommonStatus.ACTIVE);
    //check inactive cargo
    await this.manager.getCustomRepository(CargoRepository).checkInactiveCargo(body.cargoIds, user);
    //Port
    if (body.ports?.length) {
      const portIds = body.ports.map((port) => port.portId);
      await this.manager
        .getCustomRepository(PortMasterRepository)
        ._checkActivePort(portIds, user.companyId);
    }
    //Vessel
    if (body.vesselId) {
      await this.manager
        .getCustomRepository(VesselRepository)
        ._checkActiveVessel([body.vesselId], user);
    }
    //check active transfer type
    if (body.transferTypeId) {
      await this.manager
        .getCustomRepository(TransferTypeRepository)
        ._checkActiveTransferType([body.transferTypeId], user.companyId);
    }
  }

  async _validateUpdateVesselScreeningDto(
    body: UpdateVesselScreeningDto,
    user: TokenPayloadModel,
    vesselScreeningFound: VesselScreening,
  ) {
    //check cargotype status
    const cargoType = await this.manager
      .getCustomRepository(CargoTypeRepository)
      .getCargoTypeById(body.cargoTypeId, user);
    if (
      body.cargoTypeId !== vesselScreeningFound.cargoTypeId &&
      cargoType?.status === CommonStatus.IN_ACTIVE
    ) {
      throw new BaseError({ status: 404, message: 'cargoType.CARGO_TYPE_IN_ACTIVE' });
    }
    //check inactive cargo
    const cagoIds = vesselScreeningFound.cargos?.map((cargo) => cargo.id);
    const checkCargoIds = body.cargoIds?.filter((cagoId) => !cagoIds?.includes(cagoId));
    if (cagoIds?.length !== body.cargoIds?.length || checkCargoIds?.length > 0) {
      await this.manager
        .getCustomRepository(CargoRepository)
        .checkInactiveCargo(body.cargoIds, user);
    }
    const activePortIds = await this.manager
      .getCustomRepository(PortMasterRepository)
      ._getPortByStatus(user.companyId, CommonStatus.ACTIVE);
    //Vessel screening port
    const currentPortMap = new Map<string, VesselScreeningPort>();
    vesselScreeningFound.ports?.forEach((port) => currentPortMap.set(port.id, port));
    const portIds = body.ports.filter((SCPort) => !SCPort.id).map((port) => port.portId);
    const portUpdate = body.ports.filter((SCPort) => SCPort.id !== null);
    if (portIds.length) {
      await this.manager
        .getCustomRepository(PortMasterRepository)
        ._checkActivePort(portIds, user.companyId);
    }
    if (portUpdate.length && currentPortMap.size) {
      for (const port of portUpdate) {
        const vesselScreeningPort = currentPortMap.get(port.id);
        if (
          vesselScreeningPort &&
          vesselScreeningPort.portId !== port.portId &&
          !activePortIds.includes(port.id)
        ) {
          throw new BaseError({ status: 400, message: 'portMaster.PORT_INACTIVE' });
        }
      }
    }
    //Vessel
    await this.manager
      .getCustomRepository(VesselRepository)
      ._compareAndCheckActiveVessel(vesselScreeningFound.vesselId, body.vesselId, user);
    //check active transfer type
    if (body.transferTypeId && body.transferTypeId !== vesselScreeningFound.transferTypeId) {
      await this.manager
        .getCustomRepository(TransferTypeRepository)
        ._checkActiveTransferType([body.transferTypeId], user.companyId);
    }
  }

  async gatVesselscreeningDashboard(user: TokenPayloadModel, query: DateRangeDto) {
    try {
      let queryDate = '';
      if (query?.fromDate && query?.toDate) {
        queryDate = `
      AND vs."createdAt" >= '${query?.fromDate}'
      AND vs."createdAt" <= '${query?.toDate}'`;
      }
      const rawQuery = `SELECT
    vs."status" as "status",
    COUNT(distinct vs."id") AS  "total",
    json_agg(json_build_object(
      'id', vs.id,
      'requestNo', vs."requestNo",
      'status', vs.status,
      'vesselName', v."name",
      'imoNumber', v."imoNumber")) AS list
  FROM
  vessel_screening vs
  LEFT JOIN vessel v on vs."vesselId" = v.id
  WHERE
  vs."companyId" = $1
  ${queryDate} AND
  vs."deleted" = false 
  GROUP BY vs."status"`;
      return await this.connection.query(rawQuery, [user.companyId]);
    } catch (ex) {
      LoggerCommon.error(
        '[SelfAssessmentRepository] getSelfassessmentDashboard error ',
        ex.message || ex,
      );
      throw ex;
    }
  }

  private async getIncidentInvestigations(vesselId: string) {
    return this.manager.getRepository(IncidentInvestigation).find({ where: { vesselId } });
  }

  private async getPlanningRequests(vesselId: string) {
    return this.manager.getRepository(PlanningRequest).find({
      where: { vesselId },
      order: { createdAt: 'DESC' },
    });
  }
}
