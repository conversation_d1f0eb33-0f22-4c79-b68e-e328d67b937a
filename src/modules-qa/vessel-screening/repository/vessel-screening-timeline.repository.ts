import { Injectable } from '@nestjs/common';
import { Connection } from 'typeorm';
import { TokenPayloadModel } from 'svm-nest-lib-v3';
import moment from 'moment';
import {
  VesselScreeningTimelineQueryDto,
  TimelineEventTypeEnum,
  TimelineColorEnum,
  TimelineZoomLevelEnum,
} from '../dto/vessel-screening-timeline.dto';
import {
  IncidentInvestigation,
  IncidentInvestigationStatus,
} from '../../incident-investigation/entity/incident-investigation.entity';
import { PortStateControl } from '../../port-state-control/entity/port-state-control.entity';
import { Injury } from '../../injury/entity/injury.entity';
import { VesselCompanyFeedback } from '../../vessel-company-feedback/entity/vessel-company-feedback.entity';
import { PilotTerminalFeedback } from '../../pilot-terminal-feedback/entity/pilot-terminal-feedback.entity';
import { RightShip } from '../../right-ship/right-ship.entity';
import { VesselScreening } from '../entity/vessel-screening.entity';
import { ClassDispensations } from '../../class-dispensations/entity/class-dispensations.entity';
import { RightShipRestrictions } from '../../right-ship/rightship-restrictions.entity';
import { Vessel } from '../../../modules/vessel/entity/vessel.entity';
import { VesselBlacklistMOU } from '../../../modules/vessel/entity/vessel-blacklist-mou.entity';
import { FollowUp } from '../../../modules/corrective-action-request/entities/follow-up.entity';
import { AnalyticalReportInspectionPerformanceRepository } from '../../../modules/analytical-report/repositories/analytical-report-inspection-performance.repository';
import {
  VesselScreeningObservedRiskEnum,
  VesselScreeningFinalRiskEnum,
  PortStateControlDetentionEnum,
  LostTimeInjuryEnum,
  ClassDispensationsStatusEnum,
} from '../../../commons/enums';

@Injectable()
export class VesselScreeningTimelineRepository {
  constructor(private readonly connection: Connection) { }

  async getVesselScreeningTimeline(
    vesselId: string,
    query: VesselScreeningTimelineQueryDto,
    user: TokenPayloadModel,
  ) {
    // Set default date range to past 2 years if not provided
    // Use user's timezone or default timezone to get proper local time
    const defaultFromDate = moment().subtract(2, 'years').toISOString();
    const defaultToDate = moment().toISOString();

    const fromDate = query.fromDate || defaultFromDate;
    const toDate = query.toDate || defaultToDate;
    const zoomLevel = query.zoomLevel || TimelineZoomLevelEnum.MONTH;

    // Get vessel information
    const vessel = await this.connection.getRepository(Vessel).findOne({
      where: { id: vesselId, companyId: user.companyId, deleted: false },
      select: ['id', 'name', 'imoNumber'],
    });

    if (!vessel) {
      throw new Error('Vessel not found');
    }

    // Get all event categories
    const categories = await this.getAllTimelineCategories(
      vesselId,
      user.companyId,
      fromDate,
      toDate,
      query.categories,
      user,
    );

    const totalEvents = categories.reduce((sum, category) => sum + category.eventCount, 0);

    return {
      // vesselId: vessel.id,
      // vesselName: vessel.name,
      // vesselImo: vessel.imoNumber,
      // fromDate,
      // toDate,
      // zoomLevel,
      totalEvents,
      categories,
    };
  }

  private async getAllTimelineCategories(
    vesselId: string,
    companyId: string,
    fromDate: string,
    toDate: string,
    requestedCategories?: TimelineEventTypeEnum[],
    user?: TokenPayloadModel,
  ) {
    const categories = [];
    const categoryMap = new Map<TimelineEventTypeEnum, string>([
      [TimelineEventTypeEnum.INCIDENTS, 'Incidents'],
      [TimelineEventTypeEnum.PSC, 'Port State Control'],
      [TimelineEventTypeEnum.INJURIES, 'Injuries'],
      [TimelineEventTypeEnum.INTERNAL_INSPECTIONS, 'Charterer Inspection'],
      [TimelineEventTypeEnum.VESSEL_FEEDBACK, 'Vessel Feedback'],
      [TimelineEventTypeEnum.PILOT_FEEDBACK, 'Pilot/Terminal Feedback'],
      [TimelineEventTypeEnum.CONDITION_OF_CLASS, 'Condition of Class'],
      [TimelineEventTypeEnum.SAFETY_SCORE, 'Safety Score'],
      [TimelineEventTypeEnum.DOC_SCORE, 'DOC Score'],
      [TimelineEventTypeEnum.GHG, 'GHG Rating'],
      [TimelineEventTypeEnum.SCREENING, 'Vessel Screening'],
      [TimelineEventTypeEnum.RESTRICTIONS, 'Restrictions'],
      [TimelineEventTypeEnum.BLACKLISTED, 'Blacklisted'],
      [TimelineEventTypeEnum.CHARTERER_INSPECTION, 'Follow Up'],
    ]);

    // If specific categories are requested, use only those
    const categoriesToProcess = requestedCategories || Array.from(categoryMap.keys());

    // Process each category
    for (const categoryType of categoriesToProcess) {
      const events = await this.getCategoryEvents(
        categoryType,
        vesselId,
        companyId,
        fromDate,
        toDate,
        user,
      );

      categories.push({
        category: categoryType,
        displayName: categoryMap.get(categoryType),
        eventCount: events.length,
        events,
      });
    }

    return categories;
  }

  private async getCategoryEvents(
    category: TimelineEventTypeEnum,
    vesselId: string,
    companyId: string,
    fromDate: string,
    toDate: string,
    user?: TokenPayloadModel,
  ) {
    switch (category) {
      case TimelineEventTypeEnum.INCIDENTS:
        return this.getIncidentEvents(vesselId, companyId, fromDate, toDate);
      case TimelineEventTypeEnum.PSC:
        return this.getPscEvents(vesselId, companyId, fromDate, toDate);
      case TimelineEventTypeEnum.INJURIES:
        return this.getInjuryEvents(vesselId, companyId, fromDate, toDate);
      case TimelineEventTypeEnum.CONDITION_OF_CLASS:
        return this.getConditionOfClassEvents(vesselId, companyId, fromDate, toDate);
      case TimelineEventTypeEnum.VESSEL_FEEDBACK:
        return this.getVesselFeedbackEvents(vesselId, companyId, fromDate, toDate);
      case TimelineEventTypeEnum.PILOT_FEEDBACK:
        return this.getPilotFeedbackEvents(vesselId, companyId, fromDate, toDate);
      case TimelineEventTypeEnum.SAFETY_SCORE:
        return this.getSafetyScoreEvents(vesselId, companyId, fromDate, toDate);
      case TimelineEventTypeEnum.DOC_SCORE:
        return this.getDocScoreEvents(vesselId, companyId, fromDate, toDate);
      case TimelineEventTypeEnum.GHG:
        return this.getGhgEvents(vesselId, companyId, fromDate, toDate);
      case TimelineEventTypeEnum.SCREENING:
        return this.getScreeningEvents(vesselId, companyId, fromDate, toDate);
      case TimelineEventTypeEnum.RESTRICTIONS:
        return this.getRestrictionEvents(vesselId, companyId, fromDate, toDate);
      case TimelineEventTypeEnum.BLACKLISTED:
        return this.getBlacklistedEvents(vesselId, companyId, fromDate, toDate);
      case TimelineEventTypeEnum.CHARTERER_INSPECTION:
        return this.getFollowUpEvents(vesselId, companyId, fromDate, toDate, user);
      default:
        return [];
    }
  }

  private async getIncidentEvents(
    vesselId: string,
    companyId: string,
    fromDate: string,
    toDate: string,
  ) {
    const incidents = await this.connection
      .getRepository(IncidentInvestigation)
      .createQueryBuilder('incident')
      .leftJoin('incident.vessel', 'vessel')
      .leftJoin('incident.typeIncidents', 'typeIncidents')
      .leftJoin('incident.secondaryIncidents', 'secondaryIncidents')
      .select([
        'incident.id',
        'incident.vesselId',
        'incident.dateTimeOfIncident',
        'incident.observedRisk',
        'incident.observedScore',
        'incident.finalRisk',
        'incident.finalScore',
        'typeIncidents.name',
        'secondaryIncidents.name',
      ])
      .where('incident.vesselId = :vesselId', { vesselId })
      .andWhere('incident.companyId = :companyId', { companyId })
      .andWhere('incident.status != :status', { status: IncidentInvestigationStatus.DRAFT })
      .andWhere('incident.dateTimeOfIncident >= :fromDate', { fromDate })
      .andWhere('incident.dateTimeOfIncident <= :toDate', { toDate })
      .andWhere('incident.deleted = false')
      .orderBy('incident.dateTimeOfIncident', 'ASC')
      .getMany();

    return incidents.map((incident) => ({
      ...incident,
      category: TimelineEventTypeEnum.INCIDENTS,
      color: this.getIncidentColor(incident?.observedRisk, incident?.finalRisk)
    }));
  }

  private async getPscEvents(
    vesselId: string,
    companyId: string,
    fromDate: string,
    toDate: string,
  ) {
    const pscEvents = await this.connection
      .getRepository(PortStateControl)
      .createQueryBuilder('psc')
      .leftJoin('psc.portStateInspectionReports', 'portStateInspectionReports')
      .leftJoin('psc.vessel', 'vessel')
      .leftJoin('psc.port', 'port')
      .leftJoin('psc.authority', 'authority')
      .select([
        'psc.id',
        'psc.comment',
        'psc.dateOfInspection',
        'psc.detention',
        'psc.noFindings',
        'psc.inspectorName',
        'vessel.name',
        'port.name',
        'authority.name',
        'portStateInspectionReports.id',
      ])
      .where('psc.vesselId = :vesselId', { vesselId })
      .andWhere('psc.companyId = :companyId', { companyId })
      .andWhere('psc.dateOfInspection >= :fromDate', { fromDate })
      .andWhere('psc.dateOfInspection <= :toDate', { toDate })
      .andWhere('psc.deleted = false')
      .orderBy('psc.dateOfInspection', 'ASC')
      .getMany();

    return pscEvents.map((psc) => ({
      ...psc,
      totalFindings: psc?.portStateInspectionReports?.length || 0,
      category: TimelineEventTypeEnum.PSC,
      color: this.getPscColor(psc.detention, psc.noFindings),
    }));
  }

  private async getInjuryEvents(
    vesselId: string,
    companyId: string,
    fromDate: string,
    toDate: string,
  ) {
    const injuries = await this.connection
      .getRepository(Injury)
      .createQueryBuilder('injury')
      .leftJoin('injury.vessel', 'vessel')
      .leftJoin('injury.injuryMaster', 'injuryMaster')
      .leftJoin('injury.location', 'location')
      .select([
        'injury.id',
        'injury.vesselId',
        'injury.injuryDate',
        'injury.lostTime',
        'injuryMaster.name',
        'location.name',
      ])
      .where('injury.vesselId = :vesselId', { vesselId })
      .andWhere('injury.companyId = :companyId', { companyId })
      .andWhere('injury.injuryDate >= :fromDate', { fromDate })
      .andWhere('injury.injuryDate <= :toDate', { toDate })
      .andWhere('injury.deleted = false')
      .orderBy('injury.injuryDate', 'ASC')
      .getMany();

    return injuries.map((injury) => ({
      ...injury,
      category: TimelineEventTypeEnum.INJURIES,
      color: this.getInjuryColor(injury.lostTime),
    }));
  }


  private async getVesselFeedbackEvents(
    vesselId: string,
    companyId: string,
    fromDate: string,
    toDate: string,
  ) {
    const feedbacks = await this.connection
      .getRepository(VesselCompanyFeedback)
      .createQueryBuilder('feedback')
      .leftJoin('feedback.createdUser', 'user')
      .select([
        'feedback.id',
        'feedback.vesselId',
        'feedback.companyId',
        'feedback.feedbackDate',
        'feedback.feedbackType',
        'user.username',
      ])
      .where('feedback.vesselId = :vesselId', { vesselId })
      .andWhere('feedback.companyId = :companyId', { companyId })
      .andWhere('feedback.feedbackDate >= :fromDate', { fromDate })
      .andWhere('feedback.feedbackDate <= :toDate', { toDate })
      .andWhere('feedback.deleted = false')
      .orderBy('feedback.feedbackDate', 'ASC')
      .getMany();

    return feedbacks.map((feedback) => ({
      ...feedback,
      category: TimelineEventTypeEnum.VESSEL_FEEDBACK,
      color: TimelineColorEnum.BLUE,
    }));
  }

  private async getPilotFeedbackEvents(
    vesselId: string,
    companyId: string,
    fromDate: string,
    toDate: string,
  ) {
    const feedbacks = await this.connection
      .getRepository(PilotTerminalFeedback)
      .createQueryBuilder('feedback')
      .leftJoin('feedback.createdUser', 'user')
      .leftJoin('feedback.port', 'port')
      .select([
        'feedback.id',
        'feedback.vesselId',
        'feedback.companyId',
        'feedback.feedbackType',
        'feedback.dateOfInteraction',
        'port.name',
        'user.username',
      ])
      .where('feedback.vesselId = :vesselId', { vesselId })
      .andWhere('feedback.companyId = :companyId', { companyId })
      .andWhere('feedback.dateOfInteraction >= :fromDate', { fromDate })
      .andWhere('feedback.dateOfInteraction <= :toDate', { toDate })
      .andWhere('feedback.deleted = false')
      .orderBy('feedback.dateOfInteraction', 'ASC')
      .getMany();

    return feedbacks.map((feedback) => ({
      ...feedback,
      category: TimelineEventTypeEnum.PILOT_FEEDBACK,
      color: this.getPilotFeedbackColor(feedback?.score),
    }));
  }

  private async getConditionOfClassEvents(
    vesselId: string,
    companyId: string,
    fromDate: string,
    toDate: string,
  ) {
    // Query ClassDispensations for Condition of Class events based on Issue Date
    const conditionOfClassEvents = await this.connection
      .getRepository(ClassDispensations)
      .createQueryBuilder('classDispensation')
      .leftJoin('classDispensation.authority', 'authority')
      .select([
        'classDispensation.id',
        'classDispensation.vesselId',
        'classDispensation.expiryDate',
        'classDispensation.issueDate',
        'classDispensation.status',
        'classDispensation.remarks',
        'authority.name',
      ])
      .where('classDispensation.vesselId = :vesselId', { vesselId })
      .andWhere('classDispensation.companyId = :companyId', { companyId })
      .andWhere('classDispensation.issueDate >= :fromDate', { fromDate })
      .andWhere('classDispensation.issueDate <= :toDate', { toDate })
      .andWhere('classDispensation.issueDate IS NOT NULL')
      .andWhere('classDispensation.deleted = false')
      .orderBy('classDispensation.issueDate', 'ASC')
      .getMany();

    return conditionOfClassEvents.map((event) => ({
      ...event,
      category: TimelineEventTypeEnum.CONDITION_OF_CLASS,
      color: this.getConditionOfClassColor(event.status),
    }));
  }

  private async getSafetyScoreEvents(
    vesselId: string,
    companyId: string,
    fromDate: string,
    toDate: string,
  ) {
    const safetyScoreEvents = await this.connection
      .getRepository(RightShip)
      .createQueryBuilder('rightShip')
      .leftJoin('rightShip.vessel', 'vessel')
      .select([
        'rightShip.id',
        'rightShip.safetyScore',
        'rightShip.safetyScoreDate',
        'rightShip.verified',
        'vessel.name',
      ])
      .where('rightShip.vesselId = :vesselId', { vesselId })
      .andWhere('rightShip.companyId = :companyId', { companyId })
      .andWhere('rightShip.createdAt >= :fromDate', { fromDate })
      .andWhere('rightShip.createdAt <= :toDate', { toDate })
      .andWhere('rightShip.deleted = false')
      .orderBy('rightShip.createdAt', 'ASC')
      .getMany();

    return safetyScoreEvents.map((event) => ({
      ...event,
      category: TimelineEventTypeEnum.SAFETY_SCORE,
      color: this.getSafetyScoreColor(event.safetyScore),
    }));
  }

  private async getDocScoreEvents(
    vesselId: string,
    companyId: string,
    fromDate: string,
    toDate: string,
  ) {
    const docScoreEvents = await this.connection
      .getRepository(RightShip)
      .createQueryBuilder('rightShip')
      .leftJoin('rightShip.vessel', 'vessel')
      .select([
        'rightShip.id',
        'rightShip.docSafetyScore',
        'rightShip.safetyScoreDate',
        'rightShip.verified',
        'vessel.name',
      ])
      .where('rightShip.vesselId = :vesselId', { vesselId })
      .andWhere('rightShip.companyId = :companyId', { companyId })
      .andWhere('rightShip.createdAt >= :fromDate', { fromDate })
      .andWhere('rightShip.createdAt <= :toDate', { toDate })
      .andWhere('rightShip.deleted = false')
      .orderBy('rightShip.createdAt', 'ASC')
      .getMany();

    return docScoreEvents.map((event) => ({
      ...event,
      category: TimelineEventTypeEnum.DOC_SCORE,
      color: this.getDocScoreColor(event.docSafetyScore),
    }));
  }

  private async getGhgEvents(
    vesselId: string,
    companyId: string,
    fromDate: string,
    toDate: string,
  ) {
    const ghgData = await this.connection
      .getRepository(RightShip)
      .createQueryBuilder('rightShip')
      .leftJoin('rightShip.vessel', 'vessel')
      .select([
        'rightShip.id',
        'rightShip.ghgRating',
        'rightShip.ghgRatingDate',
        'rightShip.safetyScore',
        'rightShip.safetyScoreDate',
        'rightShip.evdi',
        'rightShip.verified',
        'vessel.name',
      ])
      .where('rightShip.vesselId = :vesselId', { vesselId })
      .andWhere('rightShip.companyId = :companyId', { companyId })
      .andWhere('rightShip.createdAt >= :fromDate', { fromDate })
      .andWhere('rightShip.createdAt <= :toDate', { toDate })
      .andWhere('rightShip.deleted = false')
      .orderBy('rightShip.createdAt', 'ASC')
      .getMany();

    return ghgData.map((ghg) => ({
      ...ghg,
      category: TimelineEventTypeEnum.GHG,
      color: this.getGhgColor(ghg.ghgRating),
    }));
  }

  private async getScreeningEvents(
    vesselId: string,
    companyId: string,
    fromDate: string,
    toDate: string,
  ) {
    const screenings = await this.connection
      .getRepository(VesselScreening)
      .createQueryBuilder('screening')
      .leftJoin('screening.vessel', 'vessel')
      .leftJoin('screening.company', 'company')
      .leftJoin('screening.companyRequest', 'companyRequest')
      .select([
        'screening.id',
        'screening.requestNo',
        'screening.dateRequest',
        'screening.nameRequest',
        'screening.status',
        'screening.reviewStatus',
        'screening.remark',
        'vessel.name',
        'company.name',
        'companyRequest.name',
      ])
      .where('screening.vesselId = :vesselId', { vesselId })
      .andWhere('screening.companyId = :companyId', { companyId })
      .andWhere('screening.dateRequest >= :fromDate', { fromDate })
      .andWhere('screening.dateRequest <= :toDate', { toDate })
      .andWhere('screening.deleted = false')
      .orderBy('screening.dateRequest', 'ASC')
      .getMany();

    // Calculate risk rating for each screening
    const screeningsWithRiskRating = await Promise.all(
      screenings.map(async (screening) => {
        const riskRating = await this.calculateScreeningRiskRating(screening?.id);
        return {
          ...screening,
          riskRating,
        };
      }),
    );

    return screeningsWithRiskRating.map((screening) => ({
      ...screening,
      category: TimelineEventTypeEnum.SCREENING,
      color: this.getScreeningColor(screening.riskRating),
    }));
  }

  /**
   * Calculate risk rating for a vessel screening based on vessel screening summary data
   * Similar to the averageRiskScore method logic
   */
  private async calculateScreeningRiskRating(vesselScreeningId: string): Promise<number | null> {
    try {
      const rawQuery = `SELECT
                          sum(vss."observedScore") as "sumObservedScore",
                          count(*) as "count"
                        FROM
                          vessel_screening_summary vss
                        WHERE
                          "vesselScreeningId" = $1 AND
                          reference NOT IN ('Injuries')
                          AND
                          ("reviewStatus" = 'Reject' or "reviewStatus" = 'Accept')
                        GROUP BY "tabName"`;

      const data = await this.connection.query(rawQuery, [vesselScreeningId]);

      if (!data || data.length === 0) {
        return null;
      }

      let totalRiskRating = 0;
      let flag = false; // to check all tab is null or contain some data

      for (let i = 0; i < data.length; i++) {
        let averageRiskByTab = 0;
        if (data[i].sumObservedScore !== null) {
          flag = true;
          averageRiskByTab = Number(data[i].sumObservedScore) / Number(data[i].count);
        }
        if (averageRiskByTab) {
          totalRiskRating += Number(averageRiskByTab);
        }
      }

      if (!flag) {
        return null;
      }

      let riskRating = totalRiskRating / data.length;

      if (flag && !Number.isInteger(riskRating)) {
        riskRating = Number((totalRiskRating / data.length).toFixed(2));
      }

      return riskRating;
    } catch (error) {
      // Log error but don't fail the entire request
      console.error('Error calculating risk rating for screening:', error);
      return null;
    }
  }

  private async getRestrictionEvents(
    vesselId: string,
    companyId: string,
    fromDate: string,
    toDate: string,
  ) {
    const restrictions = await this.connection
      .getRepository(RightShipRestrictions)
      .createQueryBuilder('restriction')
      .leftJoin('restriction.vessel', 'vessel')
      .leftJoin('restriction.rightShip', 'rightShip')
      .select([
        'restriction.id',
        'restriction.name',
        'restriction.description',
        'restriction.restrictionType',
        'restriction.effectiveFrom',
        'restriction.effectiveTo',
        'restriction.comment',
        'restriction.status',
        'vessel.name',
      ])
      .where('restriction.vesselId = :vesselId', { vesselId })
      .andWhere('restriction.effectiveFrom >= :fromDate', { fromDate })
      .andWhere('restriction.effectiveFrom <= :toDate', { toDate })
      .andWhere('restriction.deleted = false')
      .andWhere('restriction.status = :status', { status: 'active' })
      .andWhere('vessel.companyId = :companyId', { companyId })
      .orderBy('restriction.effectiveFrom', 'ASC')
      .getMany();

    return restrictions.map((restriction) => ({
      ...restriction,
      category: TimelineEventTypeEnum.RESTRICTIONS,
      color: this.getRestrictionColor(restriction.status),
    }));
  }

  private async getFollowUpEvents(
    vesselId: string,
    companyId: string,
    fromDate: string,
    toDate: string,
    user?: TokenPayloadModel,
  ) {
    const followUpEvents = await this.connection
      .getRepository(FollowUp)
      .createQueryBuilder('followUp')
      .leftJoin('followUp.planningRequest', 'planningRequest')
      .leftJoin('planningRequest.auditTimeTable', 'auditTimeTable')
      .leftJoin('planningRequest.vessel', 'vessel')
      .leftJoin('planningRequest.auditWorkspace', 'auditWorkspace')
      .select([
        'followUp.id',
        'followUp.status',
        'followUp.refId',
        'followUp.createdAt',
        'planningRequest.id',
        'planningRequest.refId',
        'planningRequest.typeOfAudit',
        'auditTimeTable.id',
        'auditTimeTable.actualTo',
        'auditTimeTable.actualFrom',
        'vessel.name',
        'auditWorkspace.id',
      ])
      .where('planningRequest.vesselId = :vesselId', { vesselId })
      .andWhere('planningRequest.companyId = :companyId', { companyId })
      .andWhere('auditTimeTable.actualTo IS NOT NULL')
      .andWhere('auditTimeTable.actualTo >= :fromDate', { fromDate })
      .andWhere('auditTimeTable.actualTo <= :toDate', { toDate })
      .andWhere('followUp.deleted = false')
      .andWhere('planningRequest.deleted = false')
      .orderBy('followUp.createdAt', 'ASC')
      .getMany();

    // Calculate inspection performance scores for each follow-up
    const followUpWithScores = await Promise.all(
      followUpEvents.map(async (followUp) => {
        let inspectionScore = 0;
        if (followUp.planningRequest?.auditWorkspace?.id) {
          try {
            const scoreResult = await this.calculateInspectionPerformanceScore(
              followUp.planningRequest.auditWorkspace.id,
              companyId,
              user,
            );
            inspectionScore =
              scoreResult &&
                'maxValueVesselObservedRisk' in scoreResult &&
                'totalObservedRisk' in scoreResult
                ? ((scoreResult as any).maxValueVesselObservedRisk /
                  (scoreResult as any).totalObservedRisk) *
                100
                : 0;
          } catch (error) {
            console.error('Error calculating inspection performance score:', error);
          }
        }
        return {
          ...followUp,
          inspectionScore,
        };
      }),
    );

    return followUpWithScores.map((followUp) => ({
      ...followUp,
      category: TimelineEventTypeEnum.CHARTERER_INSPECTION,
      color: this.getFollowUpColor(followUp.inspectionScore),
    }));
  }

  /**
   * Calculate inspection performance score using the actual getInfoAnalyticalReportInspectionPerformance function
   */
  private async calculateInspectionPerformanceScore(
    workspaceId: string,
    companyId: string,
    user?: TokenPayloadModel,
  ) {
    try {
      // Create a mock user token with the company ID for the analytical report function

      // Use the actual getInfoAnalyticalReportInspectionPerformance function
      const analyticalReportRepo = this.connection.getCustomRepository(
        AnalyticalReportInspectionPerformanceRepository,
      );

      const result = await analyticalReportRepo.getInfoAnalyticalReportInspectionPerformance(
        workspaceId,
        user,
      );

      return result;
    } catch (error) {
      console.error('Error calculating inspection performance score:', error);
      return { inspectionPerformance: 0 };
    }
  }

  private async getBlacklistedEvents(
    vesselId: string,
    companyId: string,
    fromDate: string,
    toDate: string,
  ) {
    const currentDate = new Date().toISOString();

    const blacklistedEvents = await this.connection
      .getRepository(VesselBlacklistMOU)
      .createQueryBuilder('blacklistMOU')
      .leftJoin('blacklistMOU.vessel', 'vessel')
      .leftJoin('blacklistMOU.authorityMaster', 'authorityMaster')
      .select([
        'blacklistMOU.id',
        'blacklistMOU.description',
        'blacklistMOU.effectiveFrom',
        'blacklistMOU.effectiveTo',
        'blacklistMOU.blacklistType',
        'vessel.name',
        'vessel.blacklistOnMOUWebsite',
        'authorityMaster.name',
      ])
      .where('blacklistMOU.vesselId = :vesselId', { vesselId })
      .andWhere('blacklistMOU.effectiveFrom >= :fromDate', { fromDate })
      .andWhere('blacklistMOU.effectiveFrom <= :toDate', { toDate })
      .andWhere('blacklistMOU.deleted = false')
      .andWhere('vessel.blacklistOnMOUWebsite = true')
      .andWhere('vessel.companyId = :companyId', { companyId })
      .orderBy('blacklistMOU.effectiveFrom', 'ASC')
      .getMany();

    return blacklistedEvents?.map((blacklist) => {
      return {
        ...blacklist,
        category: TimelineEventTypeEnum.BLACKLISTED,
        color: this.getBlacklistedColor(blacklist.vessel?.blacklistOnMOUWebsite),
      };
    });
  }

  // Color and severity mapping functions
  private getIncidentColor(observedRisk: any, finalRisk: any): TimelineColorEnum {
    const risk = finalRisk || observedRisk;

    if (
      risk === VesselScreeningObservedRiskEnum.HIGH ||
      risk === VesselScreeningFinalRiskEnum.HIGH
    ) {
      return TimelineColorEnum.RED;
    }
    if (
      risk === VesselScreeningObservedRiskEnum.MEDIUM ||
      risk === VesselScreeningFinalRiskEnum.MEDIUM
    ) {
      return TimelineColorEnum.YELLOW;
    }
    if (risk === VesselScreeningObservedRiskEnum.LOW || risk === VesselScreeningFinalRiskEnum.LOW) {
      return TimelineColorEnum.GREEN;
    }
    if (
      risk === VesselScreeningObservedRiskEnum.NEGLIGIBLE ||
      risk === VesselScreeningFinalRiskEnum.NEGLIGIBLE
    ) {
      return TimelineColorEnum.BLUE;
    }
    return TimelineColorEnum.GRAY;
  }

  private getPscColor(detention: string, noFindings: boolean): TimelineColorEnum {
    if (detention === PortStateControlDetentionEnum.YES) {
      return TimelineColorEnum.RED;
    }
    if (noFindings) {
      return TimelineColorEnum.GREEN;
    }
    if (detention === PortStateControlDetentionEnum.No) {
      return TimelineColorEnum.YELLOW;
    }
  }

  private getInjuryColor(lostTime: string): TimelineColorEnum {
    return lostTime === LostTimeInjuryEnum.YES ? TimelineColorEnum.RED : TimelineColorEnum.YELLOW;
  }

  // private getInspectionColor(status: string): TimelineColorEnum {
  //   return status === 'Close' ? TimelineColorEnum.GREEN : TimelineColorEnum.BLUE;
  // }

  // private getExternalInspectionColor(noFindings: boolean): TimelineColorEnum {
  //   return noFindings ? TimelineColorEnum.GREEN : TimelineColorEnum.YELLOW;
  // }

  // private getFeedbackColor(feedbackType: string): TimelineColorEnum {
  //   // BRD: Vessel/Company Feedback - Green = Positive, Red = Negative
  //   if (feedbackType === 'Positive') {
  //     return TimelineColorEnum.GREEN;
  //   }
  //   if (feedbackType === 'Negative') {
  //     return TimelineColorEnum.RED;
  //   }
  //   return TimelineColorEnum.BLUE;
  // }

  private getPilotFeedbackColor(score: number): TimelineColorEnum {
    if (score && score > 5) {
      return TimelineColorEnum.RED;
    }
    if (score > 2 && score < 5) {
      return TimelineColorEnum.YELLOW;
    }
    if (score > 0.1 && score <= 2) {
      return TimelineColorEnum.GREEN;
    }
    if (score == 0) {
      return TimelineColorEnum.BLUE;
    }
  }

  private getConditionOfClassColor(status: string): TimelineColorEnum {
    // BRD: Red = Open, Green = Closed (based on Issue Date)
    if (status === ClassDispensationsStatusEnum.OPEN) {
      return TimelineColorEnum.RED;
    }
    if (status === ClassDispensationsStatusEnum.CLOSED) {
      return TimelineColorEnum.GREEN;
    }
    // return TimelineColorEnum.BLUE;
  }

  private getSafetyScoreColor(score: string): TimelineColorEnum {
    const scoreNum = parseInt(score) || 0;
    if (scoreNum >= 4) {
      return TimelineColorEnum.GREEN;
    }
    if (scoreNum <= 2) {
      return TimelineColorEnum.RED;
    }
    return TimelineColorEnum.YELLOW;
  }

  private getDocScoreColor(score: string): TimelineColorEnum {
    const scoreNum = parseInt(score) || 0;
    if (scoreNum >= 4) {
      return TimelineColorEnum.GREEN;
    }
    if (scoreNum <= 2) {
      return TimelineColorEnum.RED;
    }
    return TimelineColorEnum.YELLOW;
  }

  private getGhgColor(ghgRating: string): TimelineColorEnum {
    if (['A', 'B'].includes(ghgRating)) {
      return TimelineColorEnum.GREEN;
    }
    if (['C', 'D'].includes(ghgRating)) {
      return TimelineColorEnum.YELLOW;
    }
    return TimelineColorEnum.RED;
  }

  private getScreeningColor(score: any): TimelineColorEnum {
    if (score && score > 5) {
      return TimelineColorEnum.RED;
    }
    if (score > 2 && score < 5) {
      return TimelineColorEnum.YELLOW;
    }
    if (score > 0.1 && score <= 2) {
      return TimelineColorEnum.GREEN;
    }
    if (score == 0) {
      return TimelineColorEnum.BLUE;
    }
  }

  private getRestrictionColor(restrictionType: string): TimelineColorEnum {
    if (restrictionType === 'active') {
      return TimelineColorEnum.RED;
    }
  }

  private getVetRequestColor(verified: boolean, safetyScore: string): TimelineColorEnum {
    // BRD: Green=Accepted/Passed, Red=Failed/Escalated, Yellow=Review Required, Blue=Cancelled
    const score = parseInt(safetyScore) || 0;

    if (verified && score >= 4) {
      return TimelineColorEnum.GREEN; // Accepted/Passed
    }
    if (!verified || score <= 2) {
      return TimelineColorEnum.RED; // Failed/Escalated
    }
    if (score === 3) {
      return TimelineColorEnum.YELLOW; // Review Required
    }
    return TimelineColorEnum.BLUE; // Cancelled/Neutral
  }

  private getBlacklistedColor(blacklistStatus: boolean): TimelineColorEnum {
    // Red = Active blacklist on MOU website, Green = Inactive/Expired blacklist
    if (blacklistStatus) {
      return TimelineColorEnum.RED;
    }
    // return TimelineColorEnum.GREEN;
  }

  private getFollowUpColor(inspectionScore: number): TimelineColorEnum {
    // Color based on inspection performance score
    if (inspectionScore > 85) {
      return TimelineColorEnum.GREEN; // > 85
    }
    if (inspectionScore >= 71 && inspectionScore <= 85) {
      return TimelineColorEnum.YELLOW; // 71-85
    }
    if (inspectionScore >= 51 && inspectionScore <= 70) {
      return TimelineColorEnum.ORANGE; // 51-70
    }
    if (inspectionScore >= 0 && inspectionScore <= 50) {
      return TimelineColorEnum.RED; // 0-50
    }
    return TimelineColorEnum.BLUE; // No score available
  }
}
