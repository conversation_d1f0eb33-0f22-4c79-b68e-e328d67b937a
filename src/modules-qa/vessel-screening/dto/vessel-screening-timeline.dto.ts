import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsUUID,
  IsArray,
  IsString,
  IsNumber,
} from 'class-validator';

export enum TimelineEventTypeEnum {
  INCIDENTS = 'Incidents',
  PSC = 'PSC',
  INJURIES = 'Injuries',
  INTERNAL_INSPECTIONS = 'Charterer Inspection',
  EXTERNAL_INSPECTIONS = 'External Inspections',
  VESSEL_FEEDBACK = 'Vessel Feedback',
  PILOT_FEEDBACK = 'Pilot Feedback',
  CONDITION_OF_CLASS = 'Condition of Class',
  SAFETY_SCORE = 'Safety Score',
  DOC_SCORE = 'DOC Score',
  GHG = 'GHG',
  SCREENING = 'Screening',
  RESTRICTIONS = 'Restrictions',
  VET_REQUEST = 'Vet Request',
  BLACKLISTED = 'Blacklisted',
  CHARTERER_INSPECTION = 'Charterer Inspection',
}

export enum TimelineColorEnum {
  RED = 'red',
  ORANGE = 'orange',
  YELLOW = 'yellow',
  GREEN = 'green',
  BLUE = 'blue',
  GRAY = 'gray',
}

export enum TimelineZoomLevelEnum {
  MONTH = 'month',
  YEAR = 'year',
}

export class VesselScreeningTimelineQueryDto {
  // @ApiProperty({
  //   type: 'string',
  //   description: 'Vessel UUID',
  //   example: 'f58723d0-0471-4c72-822c-5ef6de720249',
  //   required: false,
  // })
  // // @IsOptional()
  // @IsUUID('all', { message: 'vesselId must be a valid UUID' })
  // vesselId: string;

  @ApiProperty({
    type: 'string',
    description: 'ISO string',
    example: '2022-01-01T00:00:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsDateString({ strict: true })
  fromDate?: string;

  @ApiProperty({
    type: 'string',
    description: 'ISO string',
    example: '2024-01-01T00:00:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsDateString({ strict: true })
  toDate?: string;

  @ApiProperty({
    type: [String],
    enum: TimelineEventTypeEnum,
    required: false,
    description: 'Event categories to include',
  })
  @IsOptional()
  @IsArray()
  @IsEnum(TimelineEventTypeEnum, { each: true })
  categories?: TimelineEventTypeEnum[];

  @ApiProperty({
    type: 'string',
    enum: TimelineZoomLevelEnum,
    required: false,
    default: TimelineZoomLevelEnum.MONTH,
  })
  @IsOptional()
  @IsEnum(TimelineZoomLevelEnum)
  zoomLevel?: TimelineZoomLevelEnum;
}

export class TimelineEventDto {
  @ApiProperty({ type: 'string' })
  id: string;

  @ApiProperty({ type: 'string', enum: TimelineEventTypeEnum })
  category: TimelineEventTypeEnum;

  @ApiProperty({ type: 'string' })
  title: string;

  @ApiProperty({ type: 'string' })
  description: string;

  @ApiProperty({ type: 'string' })
  eventDate: string;

  @ApiProperty({ type: 'string', enum: TimelineColorEnum })
  color: TimelineColorEnum;

  @ApiProperty({ type: 'number' })
  severity: number; // 0-10 scale

  @ApiProperty({ type: 'object' })
  metadata: any; // Additional event-specific data

  @ApiProperty({ type: 'string' })
  location?: string;

  @ApiProperty({ type: 'string' })
  authority?: string;

  @ApiProperty({ type: 'string' })
  status?: string;

  @ApiProperty({ type: 'number' })
  score?: number;

  @ApiProperty({ type: 'string' })
  findings?: string;

  @ApiProperty({ type: 'number', required: false })
  riskRating?: number; // Risk rating calculated from vessel screening summary

  @ApiProperty({ type: 'string' })
  source: string; // 'internal' or 'rightship'
}

export class TimelineCategoryDto {
  @ApiProperty({ type: 'string', enum: TimelineEventTypeEnum })
  category: TimelineEventTypeEnum;

  @ApiProperty({ type: 'string' })
  displayName: string;

  @ApiProperty({ type: 'number' })
  eventCount: number;

  @ApiProperty({ type: [TimelineEventDto] })
  events: TimelineEventDto[];
}

export class VesselScreeningTimelineResponseDto {
  @ApiProperty({ type: 'string' })
  vesselId: string;

  @ApiProperty({ type: 'string' })
  vesselName: string;

  @ApiProperty({ type: 'string' })
  vesselImo: string;

  @ApiProperty({ type: 'string' })
  fromDate: string;

  @ApiProperty({ type: 'string' })
  toDate: string;

  @ApiProperty({ type: 'string', enum: TimelineZoomLevelEnum })
  zoomLevel: TimelineZoomLevelEnum;

  @ApiProperty({ type: 'number' })
  totalEvents: number;

  @ApiProperty({ type: [TimelineCategoryDto] })
  categories: TimelineCategoryDto[];
}

export class TimelineEventDetailDto {
  @ApiProperty({ type: 'string' })
  id: string;

  @ApiProperty({ type: 'string', enum: TimelineEventTypeEnum })
  category: TimelineEventTypeEnum;

  @ApiProperty({ type: 'string' })
  title: string;

  @ApiProperty({ type: 'string' })
  description: string;

  @ApiProperty({ type: 'string' })
  eventDate: string;

  @ApiProperty({ type: 'object' })
  fullDetails: any; // Complete event data for popup

  @ApiProperty({ type: 'string' })
  moduleUrl: string; // URL to open in popup
}
