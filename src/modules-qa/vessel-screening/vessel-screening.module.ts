import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { VesselScreeningController } from './vessel-screening.controller';
import { VesselScreeningRepository } from './repository/vessel-screening.repository';
import { VesselScreeningService } from './vessel-screening.service';
import { VesselScreeningRemarkRepository } from './repository/vessel-screening-remark.repository';
import { VesselScreeningPortRepository } from './repository/vessel-screening-port.repository';
import { VesselScreeningSummaryRepository } from './repository/vessel-screening-summary.repository';
import { VesselScreeningSummaryWebServiceRepository } from './repository/vessel-screeing-summary-web-service.repository';
import { VesselScreeningSummaryAttachmentRemarkRepository } from './repository/vessel-screening-summary-attachment-remark.repository';
import { SummaryVesselScreeningRepository } from '../summary/repository/summary-vessel-screening.repository';
import { SVMSupportService } from 'src/micro-services/sync/svm-support.service';
import { MicroservicesAsyncModule } from '../../micro-services/async';
import { MicroservicesSyncModule } from '../../micro-services/sync';
import { SireInspectinReportRepository } from '../sire-viq/sire-inspection-report.repository';
import { VesselScreeningTimelineRepository } from './repository/vessel-screening-timeline.repository';
@Module({
  imports: [
    TypeOrmModule.forFeature([
      VesselScreeningRepository,
      VesselScreeningPortRepository,
      VesselScreeningRemarkRepository,
      VesselScreeningSummaryRepository,
      VesselScreeningSummaryWebServiceRepository,
      VesselScreeningSummaryAttachmentRemarkRepository,
      SummaryVesselScreeningRepository,
      SireInspectinReportRepository,
    ]),
    MicroservicesAsyncModule,
    MicroservicesSyncModule,
  ],
  controllers: [VesselScreeningController],
  providers: [VesselScreeningService, SVMSupportService, VesselScreeningTimelineRepository],
  exports: [VesselScreeningService],
})
export class VesselScreeningModule {}
