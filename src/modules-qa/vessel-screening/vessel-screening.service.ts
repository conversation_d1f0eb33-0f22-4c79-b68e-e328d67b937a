import { Injectable } from '@nestjs/common';
import { TokenPayloadModel } from 'svm-nest-lib-v3';
import {
  CreateVesselScreeningDto,
  CreateVesselScreeningSummaryDto,
  CreateVesselScreeningRemarkDto,
  DetailVesselScreeningSummaryDTO,
  ListVesselScreeningDto,
  ListVesselScreeningRemarkDto,
  UpdateVesselScreeningDto,
  CreateVSSummaryAttachmentRemarkDto,
  CreateVSSummaryWebServiceDto,
  ListVSSummaryWebServiceDto,
  ListVSSummaryAttachmentRemarkDto,
  UpdateReViewStatusVesselScreeningSummaryDTO,
  TitleVesselScreening,
  VesselScreeningTimelineQueryDto,
  TimelineEventDetailDto,
  TimelineEventTypeEnum,
} from './dto';
import { VesselScreeningRepository } from './repository/vessel-screening.repository';
import { VesselScreeningRemarkRepository } from './repository/vessel-screening-remark.repository';
import { VesselScreeningSummaryRepository } from './repository/vessel-screening-summary.repository';
import { VesselScreeningSummaryAttachmentRemarkRepository } from './repository/vessel-screening-summary-attachment-remark.repository';
import { VesselScreeningSummaryWebServiceRepository } from './repository/vessel-screeing-summary-web-service.repository';
import { UpdateVesselScreeningSummaryDto } from './dto/update-vessel-screening-summary.dto';
import { SummaryVesselScreeningRepository } from '../summary/repository/summary-vessel-screening.repository';
import { SVMSupportService } from '../../micro-services/sync/svm-support.service';
import { SVMIAMService } from '../../micro-services/sync/svm-iam.service';
import { ActionEnum, FeatureEnum, ROLE_PILOT, SubFeatureEnum } from '../../commons/enums';
import { VesselScreeningSummaryReferenceEnum } from './entity/vessel-screening-summary.entity';
import { SireInspectinReportRepository } from '../sire-viq/sire-inspection-report.repository';
import { downloadResource, getTextData, isDoingGrouping, PayloadAGGridDto } from '../../utils';
import { Response } from 'express';
import { vesselScreeningSummaryReferenceDTO } from './dto/vessel-screening-summary-reference.dto';
import { VesselScreeningTimelineRepository } from './repository/vessel-screening-timeline.repository';

@Injectable()
export class VesselScreeningService {
  constructor(
    private readonly vesselScreeningRepository: VesselScreeningRepository,
    private readonly svmSupportService: SVMSupportService,
    private readonly svmIAMService: SVMIAMService,
    private readonly vesselScreeningRemarkRepository: VesselScreeningRemarkRepository,
    private readonly vsSummaryAttachmentRemarkRepository: VesselScreeningSummaryAttachmentRemarkRepository,
    private readonly vesselScreeningSummaryRepository: VesselScreeningSummaryRepository,
    private readonly vsWebServiceRepository: VesselScreeningSummaryWebServiceRepository,
    private readonly summaryVesselScreeningRepository: SummaryVesselScreeningRepository,
    private readonly sireInspectinReportRepository: SireInspectinReportRepository,
    private readonly vesselScreeningTimelineRepository: VesselScreeningTimelineRepository,
  ) {
    this.vesselScreeningRepository._migrateData();
  }

  //#region Vessel screening
  async createVesselScreening(user: TokenPayloadModel, body: CreateVesselScreeningDto) {
    return this.vesselScreeningRepository.createVesselScreening(user, body);
  }

  async listVesselScreening(
    user: TokenPayloadModel,
    query: ListVesselScreeningDto,
    body?: PayloadAGGridDto,
  ) {
    const vesselScreenings: any = await this.vesselScreeningRepository.listVesselScreening(
      user,
      query,
      body,
    );

    // for (let i = 0; i < vesselScreenings.data.length; i++) {
    //   const riskRating = await this.summaryVesselScreeningRepository.averageRiskScore(
    //     vesselScreenings.data[i].id,
    //   );
    //   vesselScreenings.data[i].riskRating = riskRating.riskRating;
    // }
    return vesselScreenings;
  }

  async exportVesselScreening(
    user: TokenPayloadModel,
    query: ListVesselScreeningDto,
    body: PayloadAGGridDto,
    res: Response,
  ) {
    const vesselScreenings: any = await this.vesselScreeningRepository.listVesselScreening(
      user,
      query,
      body,
    );

    const data = [];
    vesselScreenings?.data?.forEach((item) => {
      data.push({
        [TitleVesselScreening.VESSEL]: getTextData(item.vessel?.name),
        [TitleVesselScreening.STATUS]: getTextData(item.status),
        [TitleVesselScreening.REQUEST_NO]: getTextData(item.requestNo),
        [TitleVesselScreening.REQUESTER_DATE]: getTextData(item.dateRequest),
        [TitleVesselScreening.REQUESTER]: getTextData(item.nameRequest),
        [TitleVesselScreening.REQUESTER_COMPANY]: getTextData(item.company?.name),
        [TitleVesselScreening.RISK_RATING]: getTextData(item.riskRating),
        [TitleVesselScreening.IMO_NUMBER]: getTextData(item.vessel?.imoNumber),
        [TitleVesselScreening.CARGO_TYPE]: getTextData(item.cargoType?.name),
        [TitleVesselScreening.CREATED_DATE]: getTextData(item.createdAt),
        [TitleVesselScreening.CREATED_MONTH]: getTextData(item.createdAt_Month),
        [TitleVesselScreening.CREATED_YEAR]: getTextData(item.createdAt_Year),
      });
    });

    return downloadResource(
      query,
      res,
      'Vessel-Screening',
      Object.values(TitleVesselScreening),
      data,
    );
  }

  async updateVesselScreening(
    vesselScreeningId: string,
    user: TokenPayloadModel,
    body: UpdateVesselScreeningDto,
  ) {
    return this.vesselScreeningRepository.updateVesselScreening(vesselScreeningId, user, body);
  }

  async detailByVesselScreeningId(vesselScreeningId: string, user: TokenPayloadModel) {
    const {
      attachmentIds,
      ...vesselScreening
    } = await this.vesselScreeningRepository.detailByVesselScreeningId(vesselScreeningId, user);
    let attachments;
    if (attachmentIds.length) {
      attachments = await this.svmSupportService.getDetailImage(attachmentIds, 3);
    }

    const latestDocuments = attachments?.map((latestDocument) => {
      const { providedInspections, ...other } = latestDocument.uploadByUser;
      return { ...latestDocument, uploadByUser: other };
    });
    // To find totalSireFindings
    const isSireFindings = await this.sireInspectinReportRepository.totalFindings(
      vesselScreeningId,
      user,
    );
    return {
      ...vesselScreening,
      latestDocuments: latestDocuments ? latestDocuments : null,
      isSireFindings,
    };
  }

  //#endregion Vessel screening

  //#region Vessel Screening Remark
  async createVesselScreeningRemark(
    vesselScreeningId: string,
    user: TokenPayloadModel,
    body: CreateVesselScreeningRemarkDto,
  ) {
    return this.vesselScreeningRepository.createRemarkVesselScreening(
      vesselScreeningId,
      user,
      body,
    );
  }

  async updateVesselScreeningRemark(
    vesselScreeningId: string,
    remarkId: string,
    user: TokenPayloadModel,
    body: CreateVesselScreeningRemarkDto,
  ) {
    return this.vesselScreeningRepository.updateRemarkVesselScreening(
      vesselScreeningId,
      remarkId,
      user,
      body,
    );
  }

  async listVesselScreeningRemarkByVesselScreeningId(
    vesselScreeningId: string,
    query: ListVesselScreeningRemarkDto,
  ) {
    return this.vesselScreeningRemarkRepository.getListRemarkByVesselScreeningId(
      vesselScreeningId,
      query,
    );
  }

  async detailRemarkById(vesselScreeningId: string, remarkId: string, user: TokenPayloadModel) {
    return this.vesselScreeningRemarkRepository.detailRemarkById(vesselScreeningId, remarkId, user);
  }
  async deleteRemarkById(vesselScreeningId: string, remarkId: string, user: TokenPayloadModel) {
    return this.vesselScreeningRemarkRepository.deleteRemarkById(vesselScreeningId, remarkId, user);
  }
  //#endregion Vessel Screening Remark

  //#region Summary
  async createOrUpdateVesselScreeningSummary(
    vesselScreeningId: string,
    user: TokenPayloadModel,
    body: CreateVesselScreeningSummaryDto,
  ) {
    const { rolePermissions } = await this.svmIAMService.listRolePermissionsByUser(user.id, {
      companyId: user.companyId,
      parentCompanyId: user.parentCompanyId,
    });

    // for check number records for each reference
    let isRestricted = false;
    let isPilot = false;
    if (body.reference === VesselScreeningSummaryReferenceEnum.INCIDENTS) {
      isRestricted = rolePermissions.some(
        (permission) =>
          permission ===
          `${FeatureEnum.QUALITY_ASSURANCE_INCIDENTS}::${SubFeatureEnum.INCIDENTS}::${ActionEnum.RESTRICTED}`,
      );
    }

    if (body.reference === VesselScreeningSummaryReferenceEnum.PILOT_TERMINAL_FEEDBACK) {
      const listRoleByUser: any = await this.svmIAMService.listRoleByUser(user.id);
      isPilot = listRoleByUser.some(
        (item) => item.name === ROLE_PILOT.PILOT || item.name === ROLE_PILOT.PILOT_TERMINAL,
      );

      isRestricted = rolePermissions.some(
        (permission) =>
          permission ===
          `${FeatureEnum.QUALITY_ASSURANCE_PILOT_TERMINAL_FEEDBACK}::${SubFeatureEnum.PILOT_TERMINAL_FEEDBACK}::${ActionEnum.RESTRICTED}`,
      );
    }

    if (body.reference === VesselScreeningSummaryReferenceEnum.VESSEL_COMPANY_FEEDBACK) {
      isRestricted = rolePermissions.some(
        (permission) =>
          permission ===
          `${FeatureEnum.QUALITY_ASSURANCE_PILOT_TERMINAL_FEEDBACK}::${SubFeatureEnum.VESSEL_COMPANY_FEEDBACK}::${ActionEnum.RESTRICTED}`,
      );
    }

    return this.vesselScreeningRepository.createOrUpdateVesselScreeningSummary(
      vesselScreeningId,
      user,
      body,
      isRestricted,
      isPilot,
    );
  }

  async detailVesselScreeningSummary(
    vesselScreeningId: string,
    user: TokenPayloadModel,
    query: DetailVesselScreeningSummaryDTO,
  ) {
    return this.vesselScreeningRepository.detailVesselScreeningSummary(
      vesselScreeningId,
      user,
      query,
    );
  }

  async detailRiskAnalysisVesselScreeningSummary(
    vesselScreeningId: string,
    user: TokenPayloadModel,
    body: vesselScreeningSummaryReferenceDTO,
  ) {
    return this.vesselScreeningRepository.detailRiskAnalysisVesselScreeningSummary(
      vesselScreeningId,
      user,
      body,
    );
  }

  async updateReviewStatusVesselScreeningSummary(
    vesselScreeningId: string,
    user: TokenPayloadModel,
    body: UpdateReViewStatusVesselScreeningSummaryDTO,
  ) {
    return this.vesselScreeningRepository.updateReviewStatusVesselScreeningSummary(
      vesselScreeningId,
      user,
      body,
    );
  }

  async updateVSSummary(
    vesselScreeningSummaryId: string,
    user: TokenPayloadModel,
    body: UpdateVesselScreeningSummaryDto,
  ) {
    return this.vesselScreeningSummaryRepository.updateVSSummary(
      vesselScreeningSummaryId,
      user,
      body,
    );
  }

  //#endregion Summary

  //#region Summary Attachment Remark
  async createVSSummaryAttachmentRemark(
    vesselScreeningId: string,
    user: TokenPayloadModel,
    body: CreateVSSummaryAttachmentRemarkDto,
  ) {
    return this.vsSummaryAttachmentRemarkRepository.createVSSummaryAttachmentRemark(
      vesselScreeningId,
      user,
      body,
    );
  }

  async updateVSSummaryAttachmentRemark(
    vesselScreeningId: string,
    recordId: string,
    user: TokenPayloadModel,
    body: CreateVSSummaryAttachmentRemarkDto,
  ) {
    return this.vsSummaryAttachmentRemarkRepository.updateVSSummaryAttachmentRemark(
      vesselScreeningId,
      recordId,
      user,
      body,
    );
  }

  async listVSSummaryAttachmentRemarkByVesselScreeningId(
    vesselScreeningId: string,
    query: ListVSSummaryAttachmentRemarkDto,
  ) {
    return this.vsSummaryAttachmentRemarkRepository.listSummaryAttachmentRemarkByVesselScreeningId(
      vesselScreeningId,
      query,
    );
  }

  async detailSummaryAttachmentRemarkById(
    vesselScreeningId: string,
    recordId: string,
    user: TokenPayloadModel,
  ) {
    return this.vsSummaryAttachmentRemarkRepository.detailSummaryAttachmentRemarkById(
      vesselScreeningId,
      recordId,
      user,
    );
  }
  async deleteSummaryAttachmentRemarkById(
    vesselScreeningId: string,
    recordId: string,
    user: TokenPayloadModel,
  ) {
    return this.vsSummaryAttachmentRemarkRepository.deleteSummaryAttachmentRemarkById(
      vesselScreeningId,
      recordId,
      user,
    );
  }
  //#endregion Summary Remark

  //#region Summary WebService
  async createVSSummaryWebService(
    vesselScreeningId: string,
    user: TokenPayloadModel,
    body: CreateVSSummaryWebServiceDto,
  ) {
    return this.vsWebServiceRepository.createVSSummaryWebService(vesselScreeningId, user, body);
  }

  async updateVSSummaryWebService(
    vesselScreeningId: string,
    recordId: string,
    user: TokenPayloadModel,
    body: CreateVSSummaryWebServiceDto,
  ) {
    return this.vsWebServiceRepository.updateVSSummaryWebService(
      vesselScreeningId,
      recordId,
      user,
      body,
    );
  }

  async listVSSummaryWebServiceByVesselScreeningId(
    vesselScreeningId: string,
    query: ListVSSummaryWebServiceDto,
  ) {
    return this.vsWebServiceRepository.listSummaryWebServiceByVesselScreeningId(
      vesselScreeningId,
      query,
    );
  }

  async detailSummaryWebServiceById(
    vesselScreeningId: string,
    recordId: string,
    user: TokenPayloadModel,
  ) {
    return this.vsWebServiceRepository.detailSummaryWebServiceById(
      vesselScreeningId,
      recordId,
      user,
    );
  }

  async deleteSummaryWebServiceById(
    vesselScreeningId: string,
    recordId: string,
    user: TokenPayloadModel,
  ) {
    return this.vsWebServiceRepository.deleteSummaryWebServiceById(
      vesselScreeningId,
      recordId,
      user,
    );
  }
  //#endregion Summary Web Service

  //#region Timeline
  async getVesselScreeningTimeline(
    vesselId: string,
    query: VesselScreeningTimelineQueryDto,
    user: TokenPayloadModel,
  ) {
    try {
      // Validate required vesselId parameter
      if (!vesselId) {
        throw new Error('vesselId is required');
      }

      // Use the timeline repository to get comprehensive timeline data
      const timelineData = await this.vesselScreeningTimelineRepository.getVesselScreeningTimeline(
        vesselId,
        query,
        user,
      );

      return timelineData;
    } catch (error) {
      // Log the error for debugging
      console.error('Error fetching vessel screening timeline:', error);

      // Re-throw with more context
      throw new Error(`Failed to fetch vessel screening timeline: ${error.message}`);
    }
  }
  //#endregion Timeline
}
