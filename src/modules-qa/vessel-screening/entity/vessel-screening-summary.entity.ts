import { Column, Entity } from 'typeorm';
import { VesselScreeningRisk } from './vessel-screening-risk.entity';

export enum VesselScreeningSummaryStatusEnum {
  OPEN = 'Open',
  IN_PROGRESS = 'In progress',
  COMPLETED = 'Completed',
  PENDING_INFO = 'Pending Info',
  DISAPPROVED = 'Disapproved',
}

export enum VesselScreeningSummaryReviewStatusEnum {
  ACCEPT = 'Accept',
  IN_PROGRESS = 'In progress',
  REJECT = 'Reject',
}

export enum VesselScreeningSummaryReferenceEnum {
  CONDITION_OF_CLASS_DISPENSATIONS = 'Condition of Class/Dispensations',
  SURVEY_CLASS_INFO = 'Survey/Class Info',
  MAINTENANCE_PERFORMANCE = 'Maintenance Performance',
  OTHER_TECHNICAL_RECORDS = 'Other Technical Records',
  DRY_DOCKING = 'Dry Docking',
  INCIDENTS = 'Incidents',
  INJURIES = 'Injuries',
  OTHER_SMS_RECORDS = 'Other SMS Records',
  PORT_STATE_CONTROL = 'Port State Control',
  EXTERNAL_INSPECTIONS = 'External Inspections',
  INTERNAL_INSPECTIONS_AUDITS = 'Internal Inspections/Audits',
  SAFETY_ENGAGEMENT = 'Safety Engagement',
  PLAN_AND_DRAWINGS = 'Plans and Drawings',
  PILOT_TERMINAL_FEEDBACK = 'Pilot/Terminal Feedback',
  CHARTERER_INSPECTION = 'Charterer Inspection',
  VESSEL_COMPANY_FEEDBACK = 'Vessel/Company Feedback',
  RIGHT_SHIP = 'Right Ship',
  VESSEL_GENERAL_RISK = 'Vessel General Risk',
  DOC_HOLDER = 'Doc Holder',
  CHARTERER = 'Charterer',
  VESSEL_OWNER = 'Vessel Owner',
}

export enum VesselScreeningSummaryTabNameEnum {
  TECHNICAL = 'Technical',
  SAFETY_MANAGEMENT = 'Safety Management',
  INSPECTIONS = 'Inspections',
  SAFETY_ENGAGEMENT = 'Safety Engagement',
  SHIP_PARTICULARS = 'Ship Particulars',
  PILOT_TERMINAL_FEEDBACK = 'Pilot/Terminal Feedback',
  BASIC_INFO = 'Basic Info',
  RIGHT_SHIP = 'Right Ship',
}

@Entity()
export class VesselScreeningSummary extends VesselScreeningRisk {
  @Column({
    type: 'enum',
    enum: VesselScreeningSummaryStatusEnum,
    default: VesselScreeningSummaryStatusEnum.OPEN,
    nullable: true,
  })
  public status: VesselScreeningSummaryStatusEnum;

  @Column({
    type: 'enum',
    enum: VesselScreeningSummaryReferenceEnum,
    nullable: true,
  })
  public reference: VesselScreeningSummaryReferenceEnum;

  @Column({
    type: 'enum',
    enum: VesselScreeningSummaryTabNameEnum,
    nullable: true,
  })
  public tabName: VesselScreeningSummaryTabNameEnum;

  @Column({
    type: 'enum',
    enum: VesselScreeningSummaryReviewStatusEnum,
    nullable: true,
  })
  public reviewStatus: VesselScreeningSummaryReviewStatusEnum;
}
