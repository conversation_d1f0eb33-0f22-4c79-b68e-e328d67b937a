import {
  Get,
  Controller,
  Query,
  HttpStatus,
  UseGuards,
  Post,
  Body,
  Param,
  ParseUUIDPipe,
  Put,
  Delete,
  Patch,
  Res,
} from '@nestjs/common';
import {
  Roles,
  RoleScope,
  AuthGuard,
  RolesGuard,
  TokenDecorator,
  TokenPayloadModel,
  RequiredPermissions,
} from 'svm-nest-lib-v3';
import {
  ApiTags,
  ApiBody,
  ApiQuery,
  ApiParam,
  ApiResponse,
  ApiOperation,
  ApiBearerAuth,
} from '@nestjs/swagger';

import { I18n, I18nContext, I18nLang } from 'nestjs-i18n';
import { VesselScreeningService } from './vessel-screening.service';
import {
  ListVesselScreeningDto,
  CreateVesselScreeningDto,
  UpdateVesselScreeningDto,
  ListVSSummaryWebServiceDto,
  ListVesselScreeningRemarkDto,
  CreateVSSummaryWebServiceDto,
  CreateVesselScreeningRemarkDto,
  CreateVesselScreeningSummaryDto,
  DetailVesselScreeningSummaryDTO,
  ListVSSummaryAttachmentRemarkDto,
  CreateVSSummaryAttachmentRemarkDto,
  UpdateReViewStatusVesselScreeningSummaryDTO,
  VesselScreeningTimelineQueryDto,
  TimelineEventDetailDto,
  TimelineEventTypeEnum,
  VesselScreeningTimelineResponseDto,
} from './dto';
import { ActionEnum, FeatureEnum, SubFeatureEnum } from '../../commons/enums';
import { UpdateVesselScreeningSummaryDto } from './dto/update-vessel-screening-summary.dto';
import { PayloadAGGridDto } from '../../utils';
import { Response } from 'express';
import { vesselScreeningSummaryReferenceDTO } from './dto/vessel-screening-summary-reference.dto';

@ApiTags('Vessel Screening')
@Controller('/vessel-screening')
@ApiBearerAuth()
@UseGuards(AuthGuard, RolesGuard)
export class VesselScreeningController {
  constructor(private readonly vesselScreeningService: VesselScreeningService) {}

  //#region Vessel Screening

  @ApiResponse({ description: 'List vessel screening success', status: HttpStatus.OK })
  @ApiResponse({ description: 'List vessel screening error', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({ summary: 'List vessel screening', operationId: 'listVesselScreening' })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @Get('')
  async listVesselScreening(
    @Query() query: ListVesselScreeningDto,
    @TokenDecorator() token: TokenPayloadModel,
  ) {
    return this.vesselScreeningService.listVesselScreening(token, query);
  }

  @ApiResponse({ description: 'List vessel screening success', status: HttpStatus.OK })
  @ApiResponse({ description: 'List vessel screening error', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({ summary: 'List vessel screening', operationId: 'listVesselScreening' })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @Post('/listVesselScreening')
  async optimizedListVesselScreening(
    @Query() query: ListVesselScreeningDto,
    @TokenDecorator() token: TokenPayloadModel,
    @Body() body: PayloadAGGridDto,
  ) {
    return this.vesselScreeningService.listVesselScreening(token, query, body);
  }

  @ApiResponse({ description: 'Export vessel screening success', status: HttpStatus.OK })
  @ApiResponse({ description: 'Export vessel screening error', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({ summary: 'Export vessel screening', operationId: 'exportVesselScreening' })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @Post('/exportVesselScreening')
  async exportVesselScreening(
    @Query() query: ListVesselScreeningDto,
    @TokenDecorator() token: TokenPayloadModel,
    @Body() body: PayloadAGGridDto,
    @Res() res: Response,
  ) {
    return this.vesselScreeningService.exportVesselScreening(token, query, body, res);
  }

  @ApiResponse({ description: 'Create vessel screening success', status: HttpStatus.OK })
  @ApiResponse({ description: 'Create vessel screening error', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({ summary: 'Create vessel screening', operationId: 'createVesselScreening' })
  @ApiBody({ type: CreateVesselScreeningDto })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature:
      FeatureEnum.QUALITY_ASSURANCE_VESSEL_SCREENING + '::' + SubFeatureEnum.VESSEL_SCREENING,
    action: ActionEnum.CREATE,
  })
  @Post('')
  async createVesselScreening(
    @TokenDecorator() user: TokenPayloadModel,
    @Body() body: CreateVesselScreeningDto,
    @I18n() i18n: I18nContext,
  ) {
    await this.vesselScreeningService.createVesselScreening(user, body);
    return {
      message: await i18n.t('common.CREATE_SUCCESS'),
    };
  }

  @ApiResponse({ description: 'Update vessel screening success', status: HttpStatus.OK })
  @ApiResponse({
    description: 'update vessel screening error',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiOperation({
    summary: 'update vessel screening',
    operationId: 'updateVesselScreening',
  })
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: 'vessel screening id',
  })
  @ApiBody({ type: UpdateVesselScreeningDto, required: true })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature:
      FeatureEnum.QUALITY_ASSURANCE_VESSEL_SCREENING + '::' + SubFeatureEnum.VESSEL_SCREENING,
    action: ActionEnum.UPDATE,
  })
  @Put('/:id')
  async updateVesselScreening(
    @TokenDecorator() user: TokenPayloadModel,
    @Param('id', ParseUUIDPipe) id: string,
    @I18nLang() lang: string,
    @Body() body: UpdateVesselScreeningDto,
    @I18n() i18n: I18nContext,
  ) {
    await this.vesselScreeningService.updateVesselScreening(id, user, body);
    return {
      message: await i18n.t('common.UPDATE_SUCCESS'),
    };
  }

  @ApiParam({ name: 'id', type: 'string', required: true })
  @ApiResponse({
    description: 'Get detail vessel screening success',
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Get detail vessel screening' })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature:
      FeatureEnum.QUALITY_ASSURANCE_VESSEL_SCREENING + '::' + SubFeatureEnum.VESSEL_SCREENING,
    action: ActionEnum.VIEW,
  })
  @Get('/:id')
  async getDetailVesselScreening(
    @TokenDecorator() user: TokenPayloadModel,
    @Param('id', ParseUUIDPipe) vesselScreeningId: string,
  ) {
    return this.vesselScreeningService.detailByVesselScreeningId(vesselScreeningId, user);
  }
  // #region Timeline
  @ApiResponse({
    description: 'Get vessel screening timeline success',
    status: HttpStatus.OK,
    type: VesselScreeningTimelineResponseDto,
  })
  @ApiResponse({
    description: 'Get vessel screening timeline error',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiOperation({
    summary: 'Get vessel screening timeline',
    operationId: 'getVesselScreeningTimeline',
  })
  @ApiQuery({
    description: 'Timeline query parameters',
    type: VesselScreeningTimelineQueryDto,
    required: false,
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature:
      FeatureEnum.QUALITY_ASSURANCE_VESSEL_SCREENING + '::' + SubFeatureEnum.VESSEL_SCREENING,
    action: ActionEnum.VIEW,
  })
  @Get(':vesselId/timeline')
  async getVesselScreeningTimeline(
    @Query() query: VesselScreeningTimelineQueryDto,
    @TokenDecorator() user: TokenPayloadModel,
    @Param('vesselId', ParseUUIDPipe) vesselId: string,
  ) {
    return this.vesselScreeningService.getVesselScreeningTimeline(vesselId, query, user);
  }

  //#endregion Vessel Screening

  //#region VS Remark
  @ApiResponse({
    description: 'Create vessel screening remark success',
    status: HttpStatus.OK,
  })
  @ApiResponse({
    description: 'Create vessel screening remark error',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiOperation({
    summary: 'Create vessel screening remark',
    operationId: 'createVesselScreeningRemark',
  })
  @ApiParam({
    name: 'vesselScreeningId',
    type: 'string',
    required: true,
    description: 'vessel screening id',
  })
  @ApiBody({ type: CreateVesselScreeningRemarkDto, required: true })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature:
      FeatureEnum.QUALITY_ASSURANCE_VESSEL_SCREENING + '::' + SubFeatureEnum.VESSEL_SCREENING,
    action: ActionEnum.CREATE,
  })
  @Post('/:vesselScreeningId/remark')
  async createVesselScreeningRemark(
    @TokenDecorator() user: TokenPayloadModel,
    @Param('vesselScreeningId', ParseUUIDPipe) vesselScreeningId: string,
    @I18nLang() lang: string,
    @Body() body: CreateVesselScreeningRemarkDto,
    @I18n() i18n: I18nContext,
  ) {
    await this.vesselScreeningService.createVesselScreeningRemark(vesselScreeningId, user, body);
    return {
      message: await i18n.t('common.CREATE_SUCCESS'),
    };
  }

  @ApiResponse({
    description: 'Update vessel screening remark success',
    status: HttpStatus.OK,
  })
  @ApiResponse({
    description: 'Update vessel screening remark error',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiOperation({
    summary: 'Update vessel screening remark',
    operationId: 'updateVesselScreeningRemark',
  })
  @ApiParam({
    name: 'vesselScreeningId',
    type: 'string',
    required: true,
    description: 'vessel screening id',
  })
  @ApiParam({
    name: 'remarkId',
    type: 'string',
    required: true,
    description: 'remark id',
  })
  @ApiBody({ type: CreateVesselScreeningRemarkDto, required: true })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature:
      FeatureEnum.QUALITY_ASSURANCE_VESSEL_SCREENING + '::' + SubFeatureEnum.VESSEL_SCREENING,
    action: ActionEnum.UPDATE,
  })
  @Put('/:vesselScreeningId/remark/:remarkId')
  async updateVesselScreeningRemark(
    @TokenDecorator() user: TokenPayloadModel,
    @Param('vesselScreeningId', ParseUUIDPipe) vesselScreeningId: string,
    @Param('remarkId', ParseUUIDPipe) remarkId: string,
    @I18nLang() lang: string,
    @Body() body: CreateVesselScreeningRemarkDto,
    @I18n() i18n: I18nContext,
  ) {
    await this.vesselScreeningService.updateVesselScreeningRemark(
      vesselScreeningId,
      remarkId,
      user,
      body,
    );
    return {
      message: await i18n.t('common.UPDATE_SUCCESS'),
    };
  }

  @ApiResponse({
    description: 'Get list vessel screening remark success',
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Get list vessel screening remark' })
  @ApiQuery({
    description: 'Paginate params',
    type: ListVesselScreeningRemarkDto,
    required: false,
  })
  @ApiParam({
    name: 'vesselScreeningId',
    type: 'string',
    required: true,
    description: 'vessel screening id',
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature:
      FeatureEnum.QUALITY_ASSURANCE_VESSEL_SCREENING + '::' + SubFeatureEnum.VESSEL_SCREENING,
    action: ActionEnum.VIEW,
  })
  @Get('/:vesselScreeningId/list-remark')
  async listVesselScreeningRemarkByVesselScreeningId(
    @Query() query: ListVesselScreeningRemarkDto,
    @Param('vesselScreeningId', ParseUUIDPipe) vesselScreeningId: string,
  ) {
    return this.vesselScreeningService.listVesselScreeningRemarkByVesselScreeningId(
      vesselScreeningId,
      query,
    );
  }

  @ApiResponse({
    description: 'Get detail vessel screening remark success',
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Get detail vessel screening remark' })
  @ApiParam({
    name: 'vesselScreeningId',
    type: 'string',
    required: true,
    description: 'vessel screening id',
  })
  @ApiParam({
    name: 'remarkId',
    type: 'string',
    required: true,
    description: 'remark id',
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature:
      FeatureEnum.QUALITY_ASSURANCE_VESSEL_SCREENING + '::' + SubFeatureEnum.VESSEL_SCREENING,
    action: ActionEnum.VIEW,
  })
  @Get('/:vesselScreeningId/remark/:remarkId')
  async detailRemarkById(
    @TokenDecorator() user: TokenPayloadModel,
    @Param('vesselScreeningId', ParseUUIDPipe) vesselScreeningId: string,
    @Param('remarkId', ParseUUIDPipe) remarkId: string,
  ) {
    return this.vesselScreeningService.detailRemarkById(vesselScreeningId, remarkId, user);
  }

  @ApiResponse({
    description: 'Delete vessel screening remark success',
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Delete vessel screening remark' })
  @ApiParam({
    name: 'vesselScreeningId',
    type: 'string',
    required: true,
    description: 'vessel screening id',
  })
  @ApiParam({
    name: 'remarkId',
    type: 'string',
    required: true,
    description: 'remark id',
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature:
      FeatureEnum.QUALITY_ASSURANCE_VESSEL_SCREENING + '::' + SubFeatureEnum.VESSEL_SCREENING,
    action: ActionEnum.DELETE,
  })
  @Delete('/:vesselScreeningId/remark/:remarkId')
  async deleteRemarkById(
    @TokenDecorator() user: TokenPayloadModel,
    @Param('vesselScreeningId', ParseUUIDPipe) vesselScreeningId: string,
    @Param('remarkId', ParseUUIDPipe) remarkId: string,
    @I18n() i18n: I18nContext,
  ) {
    await this.vesselScreeningService.deleteRemarkById(vesselScreeningId, remarkId, user);
    return {
      message: await i18n.t('common.DELETE_SUCCESS'),
    };
  }
  //#endregion VS Remark

  //#region VS Summary
  @ApiResponse({
    description: 'Create or update vessel screening summary success',
    status: HttpStatus.OK,
  })
  @ApiResponse({
    description: 'Create or update vessel screening summary error',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiOperation({
    summary: 'Create or update vessel screening summary',
    operationId: 'createOrUpdateVesselScreeningSummary',
  })
  @ApiParam({
    name: 'vesselScreeningId',
    type: 'string',
    required: true,
    description: 'vessel screening id',
  })
  @ApiBody({ type: CreateVesselScreeningSummaryDto, required: true })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature:
      FeatureEnum.QUALITY_ASSURANCE_VESSEL_SCREENING + '::' + SubFeatureEnum.VESSEL_SCREENING,
    action: ActionEnum.UPDATE,
  })
  @Post('/:vesselScreeningId/summary')
  async createOrUpdateVesselScreeningSummary(
    @TokenDecorator() user: TokenPayloadModel,
    @Param('vesselScreeningId', ParseUUIDPipe) vesselScreeningId: string,
    @I18nLang() lang: string,
    @Body() body: CreateVesselScreeningSummaryDto,
    @I18n() i18n: I18nContext,
  ) {
    await this.vesselScreeningService.createOrUpdateVesselScreeningSummary(
      vesselScreeningId,
      user,
      body,
    );
    return {
      message: await i18n.t('common.UPDATE_SUCCESS'),
    };
  }

  @ApiResponse({
    description: 'Detail vessel screening summary success',
    status: HttpStatus.OK,
  })
  @ApiResponse({
    description: 'Detail vessel screening summary error',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiOperation({
    summary: 'Detail vessel screening summary',
    operationId: 'detailVesselScreeningSummary',
  })
  @ApiParam({
    name: 'vesselScreeningId',
    type: 'string',
    required: true,
    description: 'vessel screening id',
  })
  @ApiQuery({ type: DetailVesselScreeningSummaryDTO, required: true })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature:
      FeatureEnum.QUALITY_ASSURANCE_VESSEL_SCREENING + '::' + SubFeatureEnum.VESSEL_SCREENING,
    action: ActionEnum.VIEW,
  })
  @Get('/:vesselScreeningId/summary')
  async detailVesselScreeningSummary(
    @TokenDecorator() user: TokenPayloadModel,
    @Param('vesselScreeningId', ParseUUIDPipe) vesselScreeningId: string,
    @I18nLang() lang: string,
    @Query() query: DetailVesselScreeningSummaryDTO,
    @I18n() i18n: I18nContext,
  ) {
    return this.vesselScreeningService.detailVesselScreeningSummary(vesselScreeningId, user, query);
  }

  @ApiResponse({
    description: 'Detail risk analysis vessel screening summary success',
    status: HttpStatus.OK,
  })
  @ApiResponse({
    description: 'Detail risk analysis vessel screening summary error',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiOperation({
    summary: 'Detail risk analysis vessel screening summary',
    operationId: 'detailRiskAnalysisVesselScreeningSummary',
  })
  @ApiParam({
    name: 'vesselScreeningId',
    type: 'string',
    required: true,
    description: 'vessel screening id',
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature:
      FeatureEnum.QUALITY_ASSURANCE_VESSEL_SCREENING + '::' + SubFeatureEnum.VESSEL_SCREENING,
    action: ActionEnum.VIEW,
  })
  @Patch('/:vesselScreeningId/summary/risk-analysis')
  async detailRiskAnalysisVesselScreeningSummary(
    @TokenDecorator() user: TokenPayloadModel,
    @Param('vesselScreeningId', ParseUUIDPipe) vesselScreeningId: string,
    @Body() body: vesselScreeningSummaryReferenceDTO,
  ) {
    return this.vesselScreeningService.detailRiskAnalysisVesselScreeningSummary(
      vesselScreeningId,
      user,
      body,
    );
  }

  @ApiResponse({
    description: 'Update vessel screening summary success',
    status: HttpStatus.OK,
  })
  @ApiResponse({
    description: 'Update vessel screening error',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiOperation({
    summary: 'Update vessel screening summary',
    operationId: 'updateVesselScreeningSummary',
  })
  @ApiParam({
    name: 'vesselScreeningId',
    type: 'string',
    required: true,
    description: 'vessel screening id',
  })
  @ApiParam({
    name: 'vesselScreeningSummaryId',
    type: 'string',
    required: true,
    description: 'vessel screening summary id',
  })
  @ApiBody({ type: UpdateVesselScreeningSummaryDto, required: true })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature:
      FeatureEnum.QUALITY_ASSURANCE_VESSEL_SCREENING + '::' + SubFeatureEnum.VESSEL_SCREENING,
    action: ActionEnum.UPDATE,
  })
  @Put('/:vesselScreeningId/summary/:vesselScreeningSummaryId')
  async updateVSSummary(
    @TokenDecorator() user: TokenPayloadModel,
    @Body() body: UpdateVesselScreeningSummaryDto,
    @Param('vesselScreeningId', ParseUUIDPipe) vesselScreeningId: string,
    @Param('vesselScreeningSummaryId', ParseUUIDPipe) vesselScreeningSummaryId: string,
    @I18n() i18n: I18nContext,
  ) {
    await this.vesselScreeningService.updateVSSummary(vesselScreeningSummaryId, user, body);
    return {
      message: await i18n.t('common.UPDATE_SUCCESS'),
    };
  }

  @ApiResponse({
    description: 'Update review status vessel screening summary success',
    status: HttpStatus.OK,
  })
  @ApiResponse({
    description: 'Update review status vessel screening summary error',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiOperation({
    summary: 'Update review status vessel screening summary',
    operationId: 'updateReviewStatusVesselScreeningSummary',
  })
  @ApiParam({
    name: 'vesselScreeningId',
    type: 'string',
    required: true,
    description: 'vessel screening id',
  })
  @ApiBody({ type: UpdateReViewStatusVesselScreeningSummaryDTO, required: true })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature:
      FeatureEnum.QUALITY_ASSURANCE_VESSEL_SCREENING + '::' + SubFeatureEnum.VESSEL_SCREENING,
    action: ActionEnum.UPDATE,
  })
  @Post('/:vesselScreeningId/summary/review-status')
  async updateReviewStatusVesselScreeningSummary(
    @TokenDecorator() user: TokenPayloadModel,
    @Param('vesselScreeningId', ParseUUIDPipe) vesselScreeningId: string,
    @I18nLang() lang: string,
    @Body() body: UpdateReViewStatusVesselScreeningSummaryDTO,
    @I18n() i18n: I18nContext,
  ) {
    await this.vesselScreeningService.updateReviewStatusVesselScreeningSummary(
      vesselScreeningId,
      user,
      body,
    );
    return {
      message: await i18n.t('common.UPDATE_SUCCESS'),
    };
  }
  //#endregion VS Summary

  //#region Summary Attachment Remark
  @ApiResponse({
    description: 'Create vessel screening summary attachment remark success',
    status: HttpStatus.OK,
  })
  @ApiResponse({
    description: 'Create vessel screening summary remark error',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiOperation({
    summary: 'Create vessel screening summary attachment remark',
    operationId: 'createVesselScreeningSummaryAttachmentRemark',
  })
  @ApiParam({
    name: 'vesselScreeningId',
    type: 'string',
    required: true,
    description: 'vessel screening id',
  })
  @ApiBody({ type: CreateVSSummaryAttachmentRemarkDto, required: true })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature:
      FeatureEnum.QUALITY_ASSURANCE_VESSEL_SCREENING + '::' + SubFeatureEnum.VESSEL_SCREENING,
    action: ActionEnum.CREATE,
  })
  @Post('/:vesselScreeningId/summary-attachment-remark')
  async createVSSummaryAttachmentRemark(
    @TokenDecorator() user: TokenPayloadModel,
    @Param('vesselScreeningId', ParseUUIDPipe) vesselScreeningId: string,
    @I18nLang() lang: string,
    @Body() body: CreateVSSummaryAttachmentRemarkDto,
    @I18n() i18n: I18nContext,
  ) {
    await this.vesselScreeningService.createVSSummaryAttachmentRemark(
      vesselScreeningId,
      user,
      body,
    );
    return {
      message: await i18n.t('common.CREATE_SUCCESS'),
    };
  }

  @ApiResponse({
    description: 'Update vessel screening summary attachment remark success',
    status: HttpStatus.OK,
  })
  @ApiResponse({
    description: 'Update vessel screening summary attachment remark error',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiOperation({
    summary: 'Update vessel screening summary attachment remark',
    operationId: 'updateVesselScreeningSummaryAttachmentRemark',
  })
  @ApiParam({
    name: 'vesselScreeningId',
    type: 'string',
    required: true,
    description: 'vessel screening id',
  })
  @ApiParam({
    name: 'recordId',
    type: 'string',
    required: true,
    description: 'Record id',
  })
  @ApiBody({ type: CreateVSSummaryAttachmentRemarkDto, required: true })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature:
      FeatureEnum.QUALITY_ASSURANCE_VESSEL_SCREENING + '::' + SubFeatureEnum.VESSEL_SCREENING,
    action: ActionEnum.UPDATE,
  })
  @Put('/:vesselScreeningId/summary-attachment-remark/:recordId')
  async updateVSSummaryAttachmentRemark(
    @TokenDecorator() user: TokenPayloadModel,
    @Param('vesselScreeningId', ParseUUIDPipe) vesselScreeningId: string,
    @Param('recordId', ParseUUIDPipe) recordId: string,
    @I18nLang() lang: string,
    @Body() body: CreateVSSummaryAttachmentRemarkDto,
    @I18n() i18n: I18nContext,
  ) {
    await this.vesselScreeningService.updateVSSummaryAttachmentRemark(
      vesselScreeningId,
      recordId,
      user,
      body,
    );
    return {
      message: await i18n.t('common.UPDATE_SUCCESS'),
    };
  }

  @ApiResponse({
    description: 'Get list vessel screening summary attachment remark success',
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Get list vessel screening summary attachment remark' })
  @ApiQuery({
    description: 'Paginate params',
    type: ListVSSummaryAttachmentRemarkDto,
    required: false,
  })
  @ApiParam({
    name: 'vesselScreeningId',
    type: 'string',
    required: true,
    description: 'vessel screening id',
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature:
      FeatureEnum.QUALITY_ASSURANCE_VESSEL_SCREENING + '::' + SubFeatureEnum.VESSEL_SCREENING,
    action: ActionEnum.VIEW,
  })
  @Get('/:vesselScreeningId/list-summary-attachment-remark')
  async listVSSummaryAttachmentRemarkByVesselScreeningId(
    @Query() query: ListVSSummaryAttachmentRemarkDto,
    @Param('vesselScreeningId', ParseUUIDPipe) vesselScreeningId: string,
  ) {
    return this.vesselScreeningService.listVSSummaryAttachmentRemarkByVesselScreeningId(
      vesselScreeningId,
      query,
    );
  }

  @ApiResponse({
    description: 'Get detail vessel screening summary attachment remark success',
    status: HttpStatus.OK,
  })
  @ApiOperation({
    summary: 'Get detail vessel screening summary attachment remark',
    operationId: 'detailVSSummaryAttachmentRemark',
  })
  @ApiParam({
    name: 'vesselScreeningId',
    type: 'string',
    required: true,
    description: 'vessel screening id',
  })
  @ApiParam({
    name: 'recordId',
    type: 'string',
    required: true,
    description: 'Record id',
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature:
      FeatureEnum.QUALITY_ASSURANCE_VESSEL_SCREENING + '::' + SubFeatureEnum.VESSEL_SCREENING,
    action: ActionEnum.VIEW,
  })
  @Get('/:vesselScreeningId/summary-attachment-remark/:recordId')
  async detailSummaryAttachmentRemarkById(
    @TokenDecorator() user: TokenPayloadModel,
    @Param('vesselScreeningId', ParseUUIDPipe) vesselScreeningId: string,
    @Param('recordId', ParseUUIDPipe) recordId: string,
  ) {
    return this.vesselScreeningService.detailSummaryAttachmentRemarkById(
      vesselScreeningId,
      recordId,
      user,
    );
  }

  @ApiResponse({
    description: 'Delete vessel screening summary attachment remark success',
    status: HttpStatus.OK,
  })
  @ApiOperation({
    summary: 'Delete vessel screening summary attachment remark',
    operationId: 'deleteVSSummaryAttachmentRemark',
  })
  @ApiParam({
    name: 'vesselScreeningId',
    type: 'string',
    required: true,
    description: 'vessel screening id',
  })
  @ApiParam({
    name: 'recordId',
    type: 'string',
    required: true,
    description: 'record id',
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature:
      FeatureEnum.QUALITY_ASSURANCE_VESSEL_SCREENING + '::' + SubFeatureEnum.VESSEL_SCREENING,
    action: ActionEnum.DELETE,
  })
  @Delete('/:vesselScreeningId/summary-attachment-remark/:recordId')
  async deleteSummaryAttachmentRemarkById(
    @TokenDecorator() user: TokenPayloadModel,
    @Param('vesselScreeningId', ParseUUIDPipe) vesselScreeningId: string,
    @Param('recordId', ParseUUIDPipe) recordId: string,
    @I18n() i18n: I18nContext,
  ) {
    await this.vesselScreeningService.deleteSummaryAttachmentRemarkById(
      vesselScreeningId,
      recordId,
      user,
    );
    return {
      message: await i18n.t('common.DELETE_SUCCESS'),
    };
  }
  //#endregion Summary Remark

  //#region Web Service
  @ApiResponse({
    description: 'Create vessel screening summary web service success',
    status: HttpStatus.OK,
  })
  @ApiResponse({
    description: 'Create vessel screening web service error',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiOperation({
    summary: 'Create vessel screening summary web service',
    operationId: 'createVesselScreeningSummaryWebService',
  })
  @ApiParam({
    name: 'vesselScreeningId',
    type: 'string',
    required: true,
    description: 'vessel screening id',
  })
  @ApiBody({ type: CreateVSSummaryWebServiceDto, required: true })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature:
      FeatureEnum.QUALITY_ASSURANCE_VESSEL_SCREENING + '::' + SubFeatureEnum.VESSEL_SCREENING,
    action: ActionEnum.CREATE,
  })
  @Post('/:vesselScreeningId/summary-web-service')
  async createVSSummaryWebService(
    @TokenDecorator() user: TokenPayloadModel,
    @Param('vesselScreeningId', ParseUUIDPipe) vesselScreeningId: string,
    @I18nLang() lang: string,
    @Body() body: CreateVSSummaryWebServiceDto,
    @I18n() i18n: I18nContext,
  ) {
    await this.vesselScreeningService.createVSSummaryWebService(vesselScreeningId, user, body);
    return {
      message: await i18n.t('common.CREATE_SUCCESS'),
    };
  }

  @ApiResponse({
    description: 'Update vessel screening summary web service success',
    status: HttpStatus.OK,
  })
  @ApiResponse({
    description: 'Update vessel screening summary web service error',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiOperation({
    summary: 'Update vessel screening summary web service',
    operationId: 'updateVesselScreeningSummaryWebService',
  })
  @ApiParam({
    name: 'vesselScreeningId',
    type: 'string',
    required: true,
    description: 'vessel screening id',
  })
  @ApiParam({
    name: 'webServiceId',
    type: 'string',
    required: true,
    description: 'web service id',
  })
  @ApiBody({ type: CreateVSSummaryWebServiceDto, required: true })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature:
      FeatureEnum.QUALITY_ASSURANCE_VESSEL_SCREENING + '::' + SubFeatureEnum.VESSEL_SCREENING,
    action: ActionEnum.UPDATE,
  })
  @Put('/:vesselScreeningId/summary-web-service/:webServiceId')
  async updateVSSummaryWebService(
    @TokenDecorator() user: TokenPayloadModel,
    @Param('vesselScreeningId', ParseUUIDPipe) vesselScreeningId: string,
    @Param('webServiceId', ParseUUIDPipe) webServiceId: string,
    @I18nLang() lang: string,
    @Body() body: CreateVSSummaryWebServiceDto,
    @I18n() i18n: I18nContext,
  ) {
    await this.vesselScreeningService.updateVSSummaryWebService(
      vesselScreeningId,
      webServiceId,
      user,
      body,
    );
    return {
      message: await i18n.t('common.UPDATE_SUCCESS'),
    };
  }

  @ApiResponse({
    description: 'Get list vessel screening summary web service success',
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Get list vessel screening summary web service' })
  @ApiQuery({
    description: 'Paginate params',
    type: ListVSSummaryWebServiceDto,
    required: false,
  })
  @ApiParam({
    name: 'vesselScreeningId',
    type: 'string',
    required: true,
    description: 'vessel screening id',
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature:
      FeatureEnum.QUALITY_ASSURANCE_VESSEL_SCREENING + '::' + SubFeatureEnum.VESSEL_SCREENING,
    action: ActionEnum.VIEW,
  })
  @Get('/:vesselScreeningId/list-summary-web-service')
  async listVSSummaryWebServiceByVesselScreeningId(
    @Query() query: ListVSSummaryWebServiceDto,
    @Param('vesselScreeningId', ParseUUIDPipe) vesselScreeningId: string,
  ) {
    return this.vesselScreeningService.listVSSummaryWebServiceByVesselScreeningId(
      vesselScreeningId,
      query,
    );
  }

  @ApiResponse({
    description: 'Get detail vessel screening summary web service success',
    status: HttpStatus.OK,
  })
  @ApiOperation({
    summary: 'Get detail vessel screening summary web service',
    operationId: 'detailVSSummaryWebService',
  })
  @ApiParam({
    name: 'vesselScreeningId',
    type: 'string',
    required: true,
    description: 'vessel screening id',
  })
  @ApiParam({
    name: 'webServiceId',
    type: 'string',
    required: true,
    description: 'web service id',
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature:
      FeatureEnum.QUALITY_ASSURANCE_VESSEL_SCREENING + '::' + SubFeatureEnum.VESSEL_SCREENING,
    action: ActionEnum.VIEW,
  })
  @Get('/:vesselScreeningId/summary-web-service/:webServiceId')
  async detailSummaryWebServiceById(
    @TokenDecorator() user: TokenPayloadModel,
    @Param('vesselScreeningId', ParseUUIDPipe) vesselScreeningId: string,
    @Param('webServiceId', ParseUUIDPipe) webServiceId: string,
  ) {
    return this.vesselScreeningService.detailSummaryWebServiceById(
      vesselScreeningId,
      webServiceId,
      user,
    );
  }

  @ApiResponse({
    description: 'Delete vessel screening summary web service success',
    status: HttpStatus.OK,
  })
  @ApiOperation({
    summary: 'Delete vessel screening summary web service',
    operationId: 'deleteVSSummaryWebService',
  })
  @ApiParam({
    name: 'vesselScreeningId',
    type: 'string',
    required: true,
    description: 'vessel screening id',
  })
  @ApiParam({
    name: 'webServiceId',
    type: 'string',
    required: true,
    description: 'web service id',
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature:
      FeatureEnum.QUALITY_ASSURANCE_VESSEL_SCREENING + '::' + SubFeatureEnum.VESSEL_SCREENING,
    action: ActionEnum.DELETE,
  })
  @Delete('/:vesselScreeningId/summary-web-service/:webServiceId')
  async deleteSummaryWebServiceById(
    @TokenDecorator() user: TokenPayloadModel,
    @Param('vesselScreeningId', ParseUUIDPipe) vesselScreeningId: string,
    @Param('webServiceId', ParseUUIDPipe) webServiceId: string,
    @I18n() i18n: I18nContext,
  ) {
    await this.vesselScreeningService.deleteSummaryWebServiceById(
      vesselScreeningId,
      webServiceId,
      user,
    );
    return {
      message: await i18n.t('common.DELETE_SUCCESS'),
    };
  }
  //#endregion Web Service
}
