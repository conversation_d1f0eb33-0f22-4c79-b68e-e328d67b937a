import { AfterLoad, Column, <PERSON><PERSON><PERSON>, JoinTable, ManyToMany, ManyToOne, OneToMany } from 'typeorm';
import { Company } from '../../../modules/company/company.entity';
import { User } from '../../../modules/user/user.entity';
import { Vessel } from '../../../modules/vessel/entity/vessel.entity';
import { IdentifyEntity } from 'svm-nest-lib-v3';
import {
  FeedbackTypeEnum,
  VesselCompanyFeedbackStatusEnum,
  VesselScreeningFinalRiskEnum,
  VesselScreeningObservedRiskEnum,
  VesselScreeningPotentialRiskEnum,
} from '../../../commons/enums';
import { hashAttachmentValues } from '../../../commons/functions';
import { VesselCompanyFeedbackHistory } from './vessel-company-feedback-history.entity';
import { CategorizationMaster } from '../../categorization-master/entities/categorization-master.entity';
import { VoyageType } from '../../../modules/voyage-type/entities/voyage-type.entity';

@Entity()
export class VesselCompanyFeedback extends IdentifyEntity {
  @Column()
  public refId: string;

  @Column({
    nullable: false,
    enum: FeedbackTypeEnum,
    type: 'enum',
  })
  public feedbackType: string;

  @Column({ type: 'uuid', nullable: true })
  public vesselId: string;

  @Column({
    nullable: true,
    enum: VesselCompanyFeedbackStatusEnum,
    default: VesselCompanyFeedbackStatusEnum.DRAFT,
    type: 'enum',
  })
  public status: VesselCompanyFeedbackStatusEnum;

  @Column({ type: 'uuid' })
  public companyId: string;

  @Column({ nullable: true })
  public title: string;

  @Column({ nullable: true })
  public description: string;

  @Column({
    type: 'uuid',
    array: true,
    default: [],
  })
  public attachments: string[];

  @Column({ type: 'timestamp', nullable: false })
  public feedbackDate: Date;

  @Column({ type: 'uuid', nullable: true })
  public createdUserId: string;

  @Column({ type: 'uuid', nullable: true })
  public updatedUserId?: string;

  @Column({ type: 'uuid', nullable: true })
  public voyageTypeId: string;

  @Column({ type: 'varchar', nullable: true })
  public categorizationNames?: string;

  @Column({
    type: 'smallint',
    enum: VesselScreeningPotentialRiskEnum,
    // default: VesselScreeningPotentialRiskEnum.LOW,
    nullable: true,
  })
  public potentialRisk: VesselScreeningPotentialRiskEnum;

  @Column({
    type: 'float4',
    nullable: true,
  })
  public potentialScore: number;

  @Column({
    type: 'smallint',
    enum: VesselScreeningObservedRiskEnum,
    // default: VesselScreeningObservedRiskEnum.LOW,
    nullable: true,
  })
  public observedRisk: VesselScreeningObservedRiskEnum;

  @Column({
    type: 'float4',
    nullable: true,
  })
  public observedScore: number;

  @Column({
    type: 'smallint',
    enum: VesselScreeningFinalRiskEnum,
    // default: VesselScreeningFinalRiskEnum.LOW,
    nullable: true,
  })
  public finalRisk: VesselScreeningFinalRiskEnum;

  @Column({
    type: 'float4',
    nullable: true,
  })
  public finalScore: number;

  @Column({ type: 'varchar', length: 3, nullable: true })
  public doos?: string;

  //////
  @ManyToOne(() => Company, { onDelete: 'CASCADE' })
  company: Company;

  @ManyToOne(() => Vessel, {
    onDelete: 'NO ACTION',
  })
  vessel: Vessel;

  @ManyToOne(() => User, { onDelete: 'NO ACTION' })
  createdUser: User;

  @ManyToOne(() => User, { onDelete: 'NO ACTION' })
  updatedUser: User;

  @OneToMany(
    () => VesselCompanyFeedbackHistory,
    (vesselCompanyFeedbackHistory) => vesselCompanyFeedbackHistory.vesselCompanyFeedback,
  )
  vesselCompanyFeedbackHistories: VesselCompanyFeedbackHistory[];

  @ManyToMany(
    () => CategorizationMaster,
    (categorizationMaster) => categorizationMaster.vesselCompanyFeedback,
  )
  @JoinTable({ name: 'vessel_company_feedback_categorization_master' })
  categorizationMaster: CategorizationMaster[];

  @ManyToOne(() => VoyageType, { onDelete: 'NO ACTION' })
  voyageType: VoyageType;

  @AfterLoad()
  async transformAttachment() {
    this.attachments = await hashAttachmentValues(this.attachments);
  }
}
