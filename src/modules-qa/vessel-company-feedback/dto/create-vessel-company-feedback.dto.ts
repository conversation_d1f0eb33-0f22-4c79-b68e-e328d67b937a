import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayMinSize,
  ArrayUnique,
  IsArray,
  IsDateString,
  IsEnum,
  IsIn,
  IsNotEmpty,
  IsOptional,
  IsUUID,
  <PERSON><PERSON>ength,
} from 'class-validator';

import { Transform } from 'class-transformer';
import { decryptAttachmentValues } from '../../../commons/functions';
import { FeedbackTypeEnum, VesselCompanyFeedbackStatusEnum } from 'src/commons/enums';

export class CreateVesselCompanyFeedbackDto {
  @ApiProperty({ type: 'string' })
  @IsOptional()
  @IsUUID()
  vesselId: string;

  @ApiProperty({ type: 'string' })
  @IsOptional()
  // @IsUUID()
  companyId: string;

  @ApiProperty({ type: 'string', enum: FeedbackTypeEnum })
  @IsNotEmpty({ message: 'common.REQUIRED_FIELD' })
  @IsEnum(FeedbackTypeEnum)
  feedbackType: string;

  @ApiProperty({ type: 'string' })
  @IsNotEmpty({ message: 'common.REQUIRED_FIELD' })
  @Transform((value: string) => value.trim())
  @MaxLength(256)
  title: string;

  @ApiProperty({ type: 'string' })
  @IsNotEmpty({ message: 'common.REQUIRED_FIELD' })
  @Transform((value: string) => value.trim())
  description: string;

  @ApiProperty({
    type: 'string',
    description: 'ISO 8601 date only',
    example: '2011-10-05T14:48:00.000Z',
    required: false,
  })
  @IsNotEmpty({ message: 'common.REQUIRED_FIELD' })
  @IsDateString({ strict: true })
  feedbackDate: string;

  @ApiProperty({ type: [String], required: false })
  @IsOptional()
  @IsArray()
  @Transform((value: string[]) => {
    const newValue = decryptAttachmentValues(value);
    return newValue;
  })
  attachments: string[] = [];
  @ApiProperty({ type: 'string', enum: VesselCompanyFeedbackStatusEnum })
  @IsOptional()
  @IsEnum(VesselCompanyFeedbackStatusEnum)
  status?: VesselCompanyFeedbackStatusEnum;

  @ApiProperty({ type: [String] })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @ArrayUnique()
  @IsUUID('all', { each: true })
  categorizationIds: string[];

  @ApiProperty({ type: 'string', required: true })
  @IsNotEmpty()
  categorizationNames?: string;

  @ApiProperty({ type: 'string' })
  @IsOptional()
  @IsUUID()
  voyageTypeId: string;

  @ApiProperty({ type: 'string', required: false, enum: ['Yes', 'No'] })
  @IsOptional()
  @IsIn(['Yes', 'No'])
  doos?: string;
}
