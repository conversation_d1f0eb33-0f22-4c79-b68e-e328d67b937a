import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsOptional, IsUUID } from 'class-validator';
import { ListQueryDto } from '../../../commons/dtos';
import { IsGreaterThanOrEqual } from 'svm-nest-lib-v3';
import { DataType, FilterField } from '../../../utils';
export class ListVesselCompanyFeedbackDto extends ListQueryDto {
  @ApiProperty({ type: String, required: false })
  @IsOptional()
  @IsUUID('all')
  vesselId: string;

  @ApiProperty({ type: String, required: false })
  @IsOptional()
  feedbackType?: string;

  @ApiProperty({ type: String, required: false })
  @IsOptional()
  @IsUUID('all')
  companyId: string;

  @ApiProperty({
    type: 'string',
    description: 'ISO 8601 date time',
    example: '2021-11-21T12:09:32.142z',
    required: false,
  })
  @IsOptional()
  @IsDateString({ strict: true })
  createdAtFrom?: string;

  @ApiProperty({
    type: 'string',
    description: 'ISO 8601 date time',
    example: '2021-11-28T12:09:32.142z',
    required: false,
  })
  @IsOptional()
  @IsDateString({ strict: true })
  @IsGreaterThanOrEqual('createdAtFrom', { message: 'common.INVALID_DATE_RANGE' })
  createdAtTo?: string;
}

export const VESSEL_COMPANY_FEEDBACK_FILTER_FIELDS: FilterField[] = [
  {
    field: 'vesselCompanyName',
    column: '"vesselCompanyName"',
    type: DataType.TEXT,
  },
  {
    field: 'refId',
    column: '"vesselCompanyFeedback_refId"',
    type: DataType.TEXT,
  },
  {
    field: 'imo',
    column: '"imo"',
    type: DataType.TEXT,
  },
  {
    field: 'title',
    column: '"vesselCompanyFeedback_title"',
    type: DataType.TEXT,
  },
  {
    field: 'feedbackType',
    column: '"vesselCompanyFeedback_feedbackType"',
    type: DataType.TEXT,
  },
  {
    field: 'feedbackByUser',
    column: '"createdUser_username"',
    type: DataType.TEXT,
  },
  {
    field: 'status',
    column: '"vesselCompanyFeedback_status"',
    type: DataType.TEXT,
  },
  {
    field: 'dateOfFeedback',
    column: '"vesselCompanyFeedback_feedbackDate"',
    type: DataType.DATE,
  },
  {
    field: 'country',
    column: '"country_name"',
    type: DataType.TEXT,
  },
  {
    field: 'comment',
    column: '"comment"',
    type: DataType.TEXT,
  },
  {
    field: 'createdByUser',
    column: '"createdUser_username"',
    type: DataType.TEXT,
  },
  {
    field: 'createdDate',
    column: '"vesselCompanyFeedback_createdAt"',
    type: DataType.DATE,
  },
  {
    field: 'updatedByUser',
    column: '"updatedUser_username"',
    type: DataType.TEXT,
  },
  {
    field: 'updatedDate',
    column: '"vesselCompanyFeedback_updatedAt"',
    type: DataType.DATE,
  },
  {
    field: 'voyageType',
    column: '"voyageType_name"',
    type: DataType.TEXT,
  },
  {
    field: 'categorizationNames',
    column: '"vesselCompanyFeedback_categorizationNames"',
    type: DataType.TEXT,
  },
  {
    field: 'doos',
    column: '"vesselCompanyFeedback_doos"',
    type: DataType.TEXT,
  },
];

export enum TitleVesselCompanyFeedback {
  VESSEL_COMPANY_NAME = 'Vessel/Company Name',
  IMO = 'IMO',
  TITLE = 'Title',
  COMMENT = 'Comment',
  FEEDBACK_TYPE = 'Feedback Type',
  FEEDBACK_BY_USER = 'Feedback by User',
  STATUS = 'Status',
  DATE_OF_FEEDBACK = 'Date of Feedback',
  CREATED_DATE = 'Created Date',
  UPDATED_DATE = 'Updated Date',
  UPDATED_BY_USER = 'Updated by User',
  REF_ID = 'Ref.ID',
}
