import { IdentifyEntity } from 'svm-nest-lib-v3';
import { Column, Entity, Index, ManyToOne, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { StandardMaster } from '../standard-master/entity/standard-master.entity';
import { SelfDeclaration } from '../self-assessment/entity/self-declaration.entity';
import { FillSAChecklistQuestion } from '../../modules/audit-workspace/entities/fill-sa-checklist-question.entity';

@Entity()
export class ElementMaster extends IdentifyEntity {
  @PrimaryGeneratedColumn('uuid')
  public id: string;

  @Column()
  public code: string;

  @Column()
  public name: string;

  @Column()
  public number: string;

  @Column({ type: 'float4', nullable: true })
  public questionNumber: number;

  @Column()
  public stage: string;

  @Column()
  public elementStageQ: string;

  @Column({ type: 'text', nullable: false, default: '' })
  public group: string;

  @Column({ type: 'text', nullable: true })
  public keyPerformanceIndicator: string;

  @Column({ type: 'text', nullable: true })
  public bestPracticeGuidance: string;

  @Column({ type: 'uuid' })
  public standardMasterId: string;

  @Column({ type: 'uuid', nullable: true })
  public createdUserId: string;

  @Column({ type: 'uuid', nullable: true })
  public updatedUserId: string;

  @Column({ type: 'uuid', nullable: true })
  public companyId: string;

  @Column({ type: 'uuid', nullable: true })
  public auditCompanyId: string;

  @Column({ type: 'jsonb', nullable: true })
  public others: any;

  @Column({ nullable: true })
  public otherInfoGuidance: string;

  @Column({ nullable: true })
  public aimPrinciple: string;

  //Relation
  @ManyToOne(() => StandardMaster, (standardMaster) => standardMaster.elementMasters, {
    onDelete: 'CASCADE',
  })
  standardMaster: StandardMaster;

  @OneToMany(() => SelfDeclaration, (selfDeclaration) => selfDeclaration.elementMaster)
  selfDeclarations: SelfDeclaration[];

  @OneToMany(
    () => FillSAChecklistQuestion,
    (fillSAChecklistQuestion) => fillSAChecklistQuestion.SAQuestions,
  )
  fillSAChecklistQuestion: FillSAChecklistQuestion[];
}
