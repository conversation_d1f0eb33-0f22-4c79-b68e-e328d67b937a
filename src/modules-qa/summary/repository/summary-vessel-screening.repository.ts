import { omit } from 'lodash';
import { CommonStatus, TokenPayloadModel, TypeORMRepository } from 'svm-nest-lib-v3';
import { Connection, EntityRepository, In, MoreThanOrEqual } from 'typeorm';
import {
  StatusCommon,
  VesselScreeningObservedRiskEnum,
  VesselScreeningPotentialRiskEnum,
} from '../../../commons/enums';
import { VesselRepository } from '../../../modules/vessel/vessel.repository';
import { VesselScreeningSummaryAttachmentRemark } from '../../vessel-screening/entity/vessel-screening-summary-attachment-remark.entity';
import {
  VesselScreeningSummaryReferenceEnum,
  VesselScreeningSummaryTabNameEnum,
} from '../../vessel-screening/entity/vessel-screening-summary.entity';
import { VesselScreeningSummaryRepository } from '../../vessel-screening/repository/vessel-screening-summary.repository';
import { VesselScreeningRepository } from '../../vessel-screening/repository/vessel-screening.repository';
import { VesselScreeningSummaryByTabQueryDto } from '../dto';
import { vesselScreeningSummaryReferenceDTO } from 'src/modules-qa/vessel-screening/dto/vessel-screening-summary-reference.dto';
import { VesselScreening } from '../../vessel-screening/entity/vessel-screening.entity';
import { Vessel } from '../../../modules/vessel/entity/vessel.entity';
import { RightShip } from '../../right-ship/right-ship.entity';
import { PortStateControl } from '../../port-state-control/entity/port-state-control.entity';
import { ClassificationSociety } from '../../classification-society/classification-society.entity';

// @Injectable()
@EntityRepository(VesselScreeningSummaryAttachmentRemark)
export class SummaryVesselScreeningRepository extends TypeORMRepository<VesselScreeningSummaryAttachmentRemark> {
  constructor(
    private readonly connection: Connection,
    private readonly vesselScreeningRepository: VesselScreeningRepository,
  ) {
    super();
  }

  async getVesselScreeningSummaryByTab(
    token: TokenPayloadModel,
    vesselScreeningId: string,
    query: VesselScreeningSummaryByTabQueryDto,
    body: vesselScreeningSummaryReferenceDTO,
  ) {
    // List Reference by tab name and vessel screening
    const listRef = await this.manager.getCustomRepository(VesselScreeningSummaryRepository).find({
      vesselScreeningId,
      tabName: query.tabName,
      reference: In(body?.vesselScreeningSummaryReferences),
    });

    // list Attachment and Remark by tab name and vessel screening
    const recordsFound = await this.manager
      .createQueryBuilder(
        VesselScreeningSummaryAttachmentRemark,
        'vesselScreeningSummaryAttachmentRemark',
      )
      .select()
      .where(
        'vesselScreeningSummaryAttachmentRemark.vesselScreeningId = :vesselScreeningId AND vesselScreeningSummaryAttachmentRemark.tabName = :tabName',
        { vesselScreeningId, tabName: query.tabName },
      )
      .orderBy('vesselScreeningSummaryAttachmentRemark.createdAt', 'DESC')
      .getMany();
    let attachment: VesselScreeningSummaryAttachmentRemark;
    const remarks: VesselScreeningSummaryAttachmentRemark[] = [];
    for (const item of recordsFound) {
      if (item.attachments.length > 0 && item.remark == null) {
        attachment = item;
      }

      if (item.attachments.length == 0 && item.remark) {
        remarks.push(item);
      }
    }

    return {
      riskAndReview: listRef,
      attachment: attachment ? attachment : null,
      remarks,
    };
  }

  async averageRiskScore(vesselScreeningId: string, body?: vesselScreeningSummaryReferenceDTO) {
    // First, get vessel information for override checks
    const vesselScreening = await this.connection.getRepository(VesselScreening).findOne({
      where: { id: vesselScreeningId },
      relations: ['vessel', 'vessel.classificationSociety'],
    });

    if (!vesselScreening) {
      return { riskRating: null };
    }

    const vesselId = vesselScreening.vessel?.id;

    let rawQuery = `SELECT
                        sum(vss."observedScore") as "sumObservedScore",
                        count(*) as "count"
                      FROM
                        vessel_screening_summary vss
                      WHERE
                        "vesselScreeningId" = $1 AND
                        reference NOT IN ('Injuries')
                        AND
                        ("reviewStatus" = 'Reject' or "reviewStatus" = 'Accept')`;

    const queryParams: any[] = [vesselScreeningId];

    if (
      body?.vesselScreeningSummaryReferences &&
      body.vesselScreeningSummaryReferences.length > 0
    ) {
      rawQuery += ` AND reference = ANY($2)`;
      queryParams.push(body.vesselScreeningSummaryReferences);
    }

    rawQuery += ` GROUP BY "tabName" ;`;

    const data = await this.connection.query(rawQuery, queryParams);
    let totalRiskRating = 0;
    let flag = false; // to check all tab is null or contain some data
    for (let i = 0; i < data.length; i++) {
      let averageRiskByTab = 0;
      if (data[i].sumObservedScore !== null) {
        flag = true;
        averageRiskByTab = Number(data[i].sumObservedScore) / Number(data[i].count);
      }
      if (averageRiskByTab) {
        totalRiskRating += Number(averageRiskByTab);
      }
    }
    let riskRating = totalRiskRating / data.length;

    // const riskRating = flag ? (totalRiskRating / data.length).toFixed(2) : null;
    if (flag && !Number.isInteger(riskRating)) {
      riskRating = Number((totalRiskRating / data.length).toFixed(2));
    }
    if (!flag) {
      riskRating = null;
    }

    // Apply override rules
    try {
      const overrideBy = [];

      // 1. Check if vessel is on MOC Black list
      const blacklistStatus = await this.connection.getRepository(Vessel).findOne({
        where: { id: vesselId, blacklistOnMOUWebsite: true },
      });

      if (blacklistStatus) {
        overrideBy.push('MOC Black list');
      }

      // 2. Check Safety Score (1)
      const rightShipData = await this.connection.getRepository(RightShip).findOne({
        where: { vesselId: vesselId },
      });

      if (rightShipData && rightShipData.safetyScore === '1') {
        overrideBy.push('Safety Score 1');
      }

      // 3. Check DOC Safety Score (1 or 2)
      if (
        rightShipData &&
        (rightShipData.docSafetyScore === '1' || rightShipData.docSafetyScore === '2')
      ) {
        overrideBy.push('DOC Safety Score 1 or 2');
      }

      // 4. Check PSC - More than 1 detention in last 12 months
      const oneYearAgo = new Date();
      oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);

      const detentions = await this.connection.getRepository(PortStateControl).count({
        where: {
          vesselId: vesselId,
          detention: 'Yes',
          dateOfInspection: MoreThanOrEqual(oneYearAgo),
          deleted: false,
        },
      });

      if (detentions > 1) {
        overrideBy.push('PSC - Multiple detentions in last 12 months');
      }

      // 5. Check Class - "No" = Non IACS Members
      if (
        vesselScreening.vessel?.classificationSociety &&
        !vesselScreening.vessel?.classificationSociety.isIACSMember
      ) {
        overrideBy.push('Non IACS Member classification society');
      }

      // Return risk rating with any override items found
      return { riskRating, overrideBy };
    } catch (error) {
      console.error('Error applying override rules:', error);
      // If there's an error in the override logic, return the calculated risk rating
    }

    // No override condition met, return the calculated risk rating
    return { riskRating, overrideItems: [] };
  }

  async listVesselIdsForRiskRating(user: TokenPayloadModel) {
    const queryBuilder = this.connection
      .getCustomRepository(VesselRepository)
      .createQueryBuilder('vessel')
      .leftJoin('vessel.vesselType', 'vesselType')
      .leftJoin('vessel.vesselDocHolders', 'vesselDocHolders')
      .leftJoin('vesselDocHolders.company', 'companyVesselDocHolders')
      .leftJoin('vessel.country', 'country')
      .select([
        'vessel.id',
        'vessel.imoNumber',
        'vessel.name',
        'vessel.code',
        // 'vessel.countryFlag',
        'vessel.countryId',
        'vessel.country',
      ])
      .addSelect([
        'vesselDocHolders.id',
        'vesselType.id',
        'vesselType.name',
        'vesselDocHolders.status',
        'companyVesselDocHolders.id',
        'companyVesselDocHolders.name',
        'country.id',
        'country.name',
      ])
      .where(
        'vessel.companyId = :companyId AND vessel.deleted = false AND vessel.status = :status',
        {
          companyId: user.companyId,
          status: StatusCommon.ACTIVE,
        },
      );

    const dataVessels = await queryBuilder.getMany();

    const rs = dataVessels.map((vessel) => {
      return {
        id: vessel.id,
        imoNumber: vessel.imoNumber,
        name: vessel.name,
        code: vessel.code,
        type: vessel?.vesselType.name || null,
        // countryFlag: vessel.countryFlag,
        countryId: vessel.countryId,
        nameDocHolder:
          vessel.vesselDocHolders.find((vdh) => {
            if (vdh.status === CommonStatus.ACTIVE) {
              return true;
            }
          })?.company.name || null,
      };
    });

    return rs;
  }

  async listVesselScreeningIdsForRiskRating(user: TokenPayloadModel) {
    const queryBuilder = this.connection
      .getCustomRepository(VesselScreeningRepository)
      .createQueryBuilder('vesselScreening')
      .leftJoin('vesselScreening.vessel', 'vessel')
      .leftJoin('vessel.vesselType', 'vesselType')
      .leftJoin('vessel.vesselDocHolders', 'vesselDocHolders')
      .leftJoin('vesselDocHolders.company', 'companyVesselDocHolders')
      .leftJoin('vessel.country', 'country')
      .select([
        'vesselScreening.id',
        'vesselScreening.requestNo',
        'vessel.id',
        'vessel.name',
        'vessel.imoNumber',
        'vessel.code',
        // 'vessel.countryFlag',
        'vessel.countryId',
        'vessel.country',
        'country.id',
        'country.name',
        'vesselType.id',
        'vesselType.name',
        'vesselDocHolders.id',
        'vesselDocHolders.status',
        'companyVesselDocHolders.id',
        'companyVesselDocHolders.name',
      ])
      .where('vesselScreening.companyId = :companyId AND vessel.deleted = false', {
        companyId: user.companyId,
      })
      .orderBy('vesselScreening.createdAt', 'DESC');

    const listVesselScreenings = await queryBuilder.getMany();
    const rs = listVesselScreenings.map((vesselScreening) => {
      return {
        id: vesselScreening.id,
        requestNo: vesselScreening?.requestNo || null,
        vessel: {
          id: vesselScreening.vessel.id,
          imoNumber: vesselScreening.vessel.imoNumber,
          name: vesselScreening.vessel.name,
          code: vesselScreening.vessel.code,
          // countryFlag: vesselScreening.vessel?.countryFlag || null,
          countryId: vesselScreening.vessel.countryId || null,
          type: vesselScreening.vessel.vesselType.name,
          nameDocHolder:
            vesselScreening.vessel.vesselDocHolders.find((vdh) => {
              if (vdh.status === CommonStatus.ACTIVE) {
                return true;
              }
            })?.company.name || null,
        },
      };
    });
    return rs;
  }

  async totalNumberOfRisks(vesselScreenings, user: TokenPayloadModel) {
    let totalPotentialRiskNA = 0;
    let totalPotentialRiskNegligible = 0;
    let totalPotentialRiskLow = 0;
    let totalPotentialRiskMedium = 0;
    let totalPotentialRiskHigh = 0;
    const dataPotentialRisk = {
      potentialRiskNA: [],
      potentialRiskNegligible: [],
      potentialRiskLow: [],
      potentialRiskMedium: [],
      potentialRiskHigh: [],
    };
    let totalObservedRiskNA = 0;
    let totalObservedRiskNegligible = 0;
    let totalObservedRiskLow = 0;
    let totalObservedRiskMedium = 0;
    let totalObservedRiskHigh = 0;

    const dataObservedRisk = {
      observedRiskNA: [],
      observedRiskNegligible: [],
      observedRiskLow: [],
      observedRiskMedium: [],
      observedRiskHigh: [],
    };

    let totalRisks = 0;

    for (let i = 0; i < vesselScreenings.length; i++) {
      // const rawQuery = `
      // SELECT "potentialRisk" , "potentialScore" , "observedRisk" , "observedScore", "tabName" as 'module' FROM vessel_screening_summary vss WHERE "vesselScreeningId"  = $1
      // AND ( reference = $4 OR reference = $5 OR reference = $6 OR reference = $7 OR reference = $8 OR reference = $9 OR reference = $10)
      // UNION ALL
      // SELECT "potentialRisk" , "potentialScore" , "observedRisk" , "observedScore", ''  FROM class_dispensations_request cdr
      // LEFT JOIN class_dispensations cd ON cdr."classDispensationsId" = cd.id  WHERE cdr."vesselScreeningId"  = $1 AND cd.deleted = false
      // UNION ALL
      // SELECT "potentialRisk" , "potentialScore" , "observedRisk" , "observedScore" FROM survey_class_info_request scir
      // LEFT JOIN survey_class_info sci ON scir."surveyClassInfoId" = sci.id WHERE scir."vesselScreeningId"  = $1 and sci.deleted = false
      // UNION ALL
      // SELECT "potentialRisk" , "potentialScore" , "observedRisk" , "observedScore" FROM injury_request ir
      // LEFT JOIN injury i ON ir."injuryId" = i.id  WHERE ir."vesselScreeningId"  = $1 AND i.deleted = false
      // UNION ALL
      // SELECT "potentialRisk" , "potentialScore" , "observedRisk" , "observedScore" FROM port_state_control_request pscr
      // LEFT JOIN port_state_control psc ON pscr."portStateControlId" = psc.id WHERE pscr."vesselScreeningId"  = $1 AND psc.deleted = false
      // UNION ALL
      // SELECT "potentialRisk" , "potentialScore" , "observedRisk" , "observedScore" FROM incident_investigation ii WHERE "vesselId"  = $2 AND deleted = false AND "companyId" = $3
      // UNION ALL
      // SELECT "potentialRisk" , "potentialScore" , "observedRisk" , "observedScore" FROM pilot_terminal_feedback ptf WHERE "vesselId"  = $2 AND deleted = false AND "companyId" = $3 AND status = 'Submitted';
      // `;

      // const risks = await this.query(rawQuery, [
      //   vesselScreenings[i].id,
      //   vesselScreenings[i].vessel.id,
      //   user.companyId,
      //   VesselScreeningSummaryReferenceEnum.PLAN_AND_DRAWINGS,
      //   VesselScreeningSummaryReferenceEnum.MAINTENANCE_PERFORMANCE,
      //   VesselScreeningSummaryReferenceEnum.OTHER_TECHNICAL_RECORDS,
      //   VesselScreeningSummaryReferenceEnum.DRY_DOCKING,
      //   VesselScreeningSummaryReferenceEnum.OTHER_SMS_RECORDS,
      //   VesselScreeningSummaryReferenceEnum.EXTERNAL_INSPECTIONS,
      //   VesselScreeningSummaryReferenceEnum.INTERNAL_INSPECTIONS_AUDITS,
      // ]);

      const rawQuery = `SELECT "potentialRisk" , "potentialScore" , "observedRisk" , "observedScore", "reference", "tabName" as "module" FROM vessel_screening_summary vss WHERE "vesselScreeningId"  = $1 AND "tabName" <> $2 
  AND "tabName" IS NOT NULL AND "reference" <> $3`;

      const risks = await this.query(rawQuery, [
        vesselScreenings[i].id,
        VesselScreeningSummaryTabNameEnum.SAFETY_ENGAGEMENT,
        VesselScreeningSummaryReferenceEnum.CHARTERER_INSPECTION,
      ]);

      for (let j = 0; j < risks.length; j++) {
        // count potential
        if (risks[j].potentialRisk === null) {
          totalPotentialRiskNA++;
          dataPotentialRisk.potentialRiskNA.push({
            ...vesselScreenings[i],
            module: risks[j].module,
            reference: risks[j].reference,
          });
        }
        if (risks[j].potentialRisk === VesselScreeningPotentialRiskEnum.NEGLIGIBLE) {
          totalPotentialRiskNegligible++;
          dataPotentialRisk.potentialRiskNegligible.push({
            ...vesselScreenings[i],
            module: risks[j].module,
            reference: risks[j].reference,
          });
        }
        if (risks[j].potentialRisk === VesselScreeningPotentialRiskEnum.LOW) {
          totalPotentialRiskLow++;
          dataPotentialRisk.potentialRiskLow.push({
            ...vesselScreenings[i],
            module: risks[j].module,
            reference: risks[j].reference,
          });
        }
        if (risks[j].potentialRisk === VesselScreeningPotentialRiskEnum.MEDIUM) {
          totalPotentialRiskMedium++;
          dataPotentialRisk.potentialRiskMedium.push({
            ...vesselScreenings[i],
            module: risks[j].module,
            reference: risks[j].reference,
          });
        }
        if (risks[j].potentialRisk === VesselScreeningPotentialRiskEnum.HIGH) {
          totalPotentialRiskHigh++;
          dataPotentialRisk.potentialRiskHigh.push({
            ...vesselScreenings[i],
            module: risks[j].module,
            reference: risks[j].reference,
          });
        }
        // count observed
        if (risks[j].observedRisk === null) {
          totalObservedRiskNA++;
          dataObservedRisk.observedRiskNA.push({
            ...vesselScreenings[i],
            module: risks[j].module,
            reference: risks[j].reference,
          });
        }
        if (risks[j].observedRisk === VesselScreeningObservedRiskEnum.NEGLIGIBLE) {
          totalObservedRiskNegligible++;
          dataObservedRisk.observedRiskNegligible.push({
            ...vesselScreenings[i],
            module: risks[j].module,
            reference: risks[j].reference,
          });
        }
        if (risks[j].observedRisk === VesselScreeningObservedRiskEnum.LOW) {
          totalObservedRiskLow++;
          dataObservedRisk.observedRiskLow.push({
            ...vesselScreenings[i],
            module: risks[j].module,
            reference: risks[j].reference,
          });
        }
        if (risks[j].observedRisk === VesselScreeningObservedRiskEnum.MEDIUM) {
          totalObservedRiskMedium++;
          dataObservedRisk.observedRiskMedium.push({
            ...vesselScreenings[i],
            module: risks[j].module,
            reference: risks[j].reference,
          });
        }
        if (risks[j].observedRisk === VesselScreeningObservedRiskEnum.HIGH) {
          totalObservedRiskHigh++;
          dataObservedRisk.observedRiskHigh.push({
            ...vesselScreenings[i],
            module: risks[j].module,
            reference: risks[j].reference,
          });
        }
      }
      totalRisks += risks.length;
    }

    return {
      potentialRisk: {
        totalPotentialRiskNA,
        totalPotentialRiskNegligible,
        totalPotentialRiskLow,
        totalPotentialRiskMedium,
        totalPotentialRiskHigh,
        totalPotentialRisk: totalRisks,
      },
      dataPotentialRisk,
      observedRisk: {
        totalObservedRiskNA,
        totalObservedRiskNegligible,
        totalObservedRiskLow,
        totalObservedRiskMedium,
        totalObservedRiskHigh,
        totalObservedRisk: totalRisks,
      },
      dataObservedRisk,
    };
  }

  async listVesselRiskRating(vesselScreeningIds, vessels) {
    const vesselRiskRatings = [];
    // list vessel duplicate risk rating
    for (let i = 0; i < vesselScreeningIds.length; i++) {
      const vsRiskRating = await this.averageRiskScore(vesselScreeningIds[i].id);
      if (vsRiskRating.riskRating !== null) {
        vesselRiskRatings.push({
          id: vesselScreeningIds[i].vessel.id,
          riskRating: vsRiskRating.riskRating,
          overrideItems: vsRiskRating.overrideItems || [],
        });
      }
    }

    // list vessel no duplicate
    for (let i = 0; i < vessels.length; i++) {
      let count = 0;
      let total = 0;
      const allOverrideItems = [];

      for (let j = 0; j < vesselRiskRatings.length; j++) {
        if (vesselRiskRatings[j].id === vessels[i].id) {
          total = total + vesselRiskRatings[j].riskRating;
          count = count + 1;

          // Collect all override items from vessel screenings
          if (vesselRiskRatings[j].overrideItems && vesselRiskRatings[j].overrideItems.length > 0) {
            allOverrideItems.push(...vesselRiskRatings[j].overrideItems);
          }
        }
      }

      // Store unique override items
      vessels[i].overrideItems = [...new Set(allOverrideItems)];
      vessels[i].total = total;
      vessels[i].count = count;
    }

    // vessel average score
    const vesselAverageScores = [];
    for (let i = 0; i < vessels.length; i++) {
      const objVessel = omit(vessels[i], ['total', 'count']);
      if (vessels[i].count > 0) {
        vesselAverageScores.push({
          ...objVessel,
          riskRating:
            vessels[i].total !== 0 ? Number((vessels[i].total / vessels[i].count).toFixed(2)) : 0,
          // Include any override items found
          overrideItems: vessels[i].overrideItems || [],
        });
      } else {
        vesselAverageScores.push({
          ...objVessel,
          riskRating: null,
          overrideItems: [],
        });
      }
    }

    return vesselAverageScores;
  }

  // FUNC SUPPORT RISK CHART
  checkExistedVS(data, vesselScreening) {
    if (data.length < 1) {
      return true;
    }
    return data.every((d) => d.requestNo !== vesselScreening.requestNo);
  }
}
