import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  IsNotEmpty,
  IsOptional,
  ValidateNested,
  IsArray,
  IsBoolean,
  IsEnum,
} from 'class-validator';
import { StandardMaterScoringMethod, StatusCommon } from '../../../commons/enums';

import { CreateCompAnswersDto } from './create-comp-answer.dto';

export class CreateStandardMasterDto {
  @ApiProperty({ type: 'string' })
  @IsNotEmpty()
  code: string;

  @ApiProperty({ type: 'string' })
  @IsNotEmpty()
  name: string;

  @ApiProperty({ type: 'string' })
  @IsNotEmpty()
  fieldName: string;

  @ApiProperty({ enum: StatusCommon })
  @IsNotEmpty()
  @IsEnum(StatusCommon)
  status: string;

  @ApiProperty({ enum: StandardMaterScoringMethod })
  @IsOptional()
  @IsEnum(StandardMaterScoringMethod)
  scoringMethod: string;

  @ApiProperty({ type: Boolean })
  @IsNotEmpty()
  @IsBoolean()
  scoreApplicable: boolean;

  @ApiProperty({ type: [String], required: false })
  @IsOptional()
  @IsArray()
  levels?: string[] = [];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateCompAnswersDto)
  @ApiProperty({
    type: [CreateCompAnswersDto],
    description: 'compliance answer',
    required: false,
  })
  complianceAnswers: CreateCompAnswersDto[] = [];

  @ApiProperty({ type: 'object', required: false })
  @IsNotEmpty({ message: 'common.REQUIRED_FIELD' })
  others: any;

  @ApiProperty({ type: Boolean })
  @IsNotEmpty()
  @IsBoolean()
  isInternal: boolean;
}
