import { IdentifyEntity } from 'svm-nest-lib-v3';

import { StandardMaterScoringMethod, StatusCommon } from '../../../commons/enums';
import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  Index,
  ManyToOne,
  OneToMany,
  OneToOne,
  ManyToMany,
} from 'typeorm';
import { Company } from '../../../modules/company/company.entity';
import { User } from '../../../modules/user/user.entity';
import { ComplianceAnswer } from './compliance-answer.entity';
import { ElementMaster } from './../../element-master/element-master.entity';
import { DBIndexes } from '../../../commons/consts/db.const';
import { StandardMasterElements } from './standard-master-elements.entity';
import { SelfAssessment } from '../../self-assessment/entity/self-assessment.entity';
import { StandardModuleLabel } from './standard-module-label.entity';
import { InspectionMapping } from '../../../modules/inspection-mapping/entities/inspection-mapping.entity';
import { FillAuditChecklist } from '../../../modules/audit-workspace/entities/fill-audit-checklist.entity';
@Entity()
export class StandardMaster extends IdentifyEntity {
  @PrimaryGeneratedColumn('uuid')
  public id: string;

  @Column('citext')
  public code: string;

  @Column()
  public name: string;

  @Column({ type: 'enum', enum: StatusCommon, default: StatusCommon.ACTIVE })
  public status: string;

  @Column({ type: 'uuid' })
  public companyId: string;

  @Column({ type: 'uuid', nullable: true })
  public auditCompanyId: string;

  @Column({ nullable: true })
  public fieldName: string;

  @Column({ nullable: true, default: false })
  public scoreApplicable: boolean;

  @Column({ type: 'enum', enum: StandardMaterScoringMethod, nullable: true })
  public scoringMethod: string;

  @Column({ type: 'jsonb' })
  public others: any;

  @Column({ type: 'text', array: true, default: [] })
  public levels: string[];

  @Column({ type: 'uuid', nullable: true })
  public createdUserId: string;

  @Column({ type: 'uuid', nullable: true })
  public updatedUserId?: string;

  @Column({ type: 'boolean', default: false, nullable: true })
  public isInternal?: boolean;

  // Relationship
  @ManyToOne(() => Company, { onDelete: 'CASCADE' })
  company: Company;

  @ManyToOne(() => Company, { onDelete: 'CASCADE' })
  auditCompany: Company;

  @ManyToOne(() => User, { onDelete: 'NO ACTION' })
  createdUser: User;

  @ManyToOne(() => User, { onDelete: 'NO ACTION' })
  updatedUser: User;

  @OneToMany(() => ComplianceAnswer, (complianceAnswer) => complianceAnswer.standardMaster)
  complianceAnswers: ComplianceAnswer[];

  @OneToMany(() => ElementMaster, (elementMaster) => elementMaster.standardMaster)
  elementMasters: ElementMaster[];

  @OneToMany(() => SelfAssessment, (selfAssessment) => selfAssessment.standardMaster)
  selfAssessments: SelfAssessment[];

  @OneToOne(
    () => StandardMasterElements,
    (standardMasterElements) => standardMasterElements.standardMaster,
  )
  standardMasterElements: StandardMasterElements;

  @OneToMany(() => StandardModuleLabel, (standardModuleLabel) => standardModuleLabel.standardMaster)
  standardModuleLabels: StandardModuleLabel[];

  @ManyToMany(() => InspectionMapping, (inspectionMapping) => inspectionMapping.standardMasters)
  inspectionMapping: InspectionMapping[];

}
