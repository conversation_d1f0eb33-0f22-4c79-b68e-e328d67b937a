import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsUUID, IsString, IsDateString } from 'class-validator';
import { ListQueryDto } from '../../../commons/dtos';

export class ListRightShipIncidentsDto extends ListQueryDto {
  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @IsUUID('all')
  vesselId?: string;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @IsString()
  imo?: string;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @IsUUID('all')
  incidentMasterId?: string;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @IsString()
  status?: string;

  @ApiProperty({
    type: 'string',
    description: 'ISO date string for filtering from date',
    example: '2023-01-01',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  fromDate?: string;

  @ApiProperty({
    type: 'string',
    description: 'ISO date string for filtering to date',
    example: '2023-12-31',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  toDate?: string;
} 