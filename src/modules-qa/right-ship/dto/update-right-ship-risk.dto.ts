import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsEnum, IsOptional, IsUUID } from 'class-validator';
import { RiskTypeEnum } from 'src/commons/enums';

export class UpdateRightShipRiskDto {
  @ApiProperty({
    description: 'Potential risk level',
    enum: RiskTypeEnum,
    required: false,
  })
  @IsOptional()
  @IsEnum(RiskTypeEnum)
  potentialRisk?: RiskTypeEnum;

  @ApiProperty({
    description: 'Observed risk level',
    enum: RiskTypeEnum,
    required: false,
  })
  @IsOptional()
  @IsEnum(RiskTypeEnum)
  observedRisk?: RiskTypeEnum;

  @ApiProperty({
    description: 'Time loss indicator',
    type: 'boolean',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  timeloss?: boolean;
} 