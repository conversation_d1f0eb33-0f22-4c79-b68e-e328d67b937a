import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  ParseUUI<PERSON>ipe,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiBearerAuth,
  ApiResponse,
  ApiOperation,
  ApiBody,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';
import { I18n, I18nContext, I18nLang } from 'nestjs-i18n';
import {
  AuthGuard,
  RolesGuard,
  Roles,
  RoleScope,
  TokenDecorator,
  TokenPayloadModel,
  RequiredPermissions,
} from 'svm-nest-lib-v3';
import { FeatureEnum, SubFeatureEnum, ActionEnum } from '../../commons/enums';
import { RightShipService } from './right-ship.service';
import {
  CreateRightShipDto,
  ListRightShipDto,
  UpdateRightShipDto,
  UpdateRightShipRiskDto,
  SyncRightShipDTO,
  ListRightShipIncidentsDto,
} from './dto';

@ApiTags('Right Ship')
@Controller('right-ship')
export class RightShipController {
  constructor(private readonly rightShipService: RightShipService) {}

  @ApiResponse({ description: 'Sync right ship and vessel success', status: HttpStatus.OK })
  @ApiResponse({ description: 'Sync right ship and vessel error', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({ summary: 'Sync right ship and vessel', operationId: 'syncRightShipAndVessel' })
  @ApiBody({ type: SyncRightShipDTO })
  @ApiBearerAuth()
  @UseGuards(AuthGuard, RolesGuard)
  @Roles(RoleScope.ADMIN, RoleScope.SUPER_ADMIN)
  @Post('sync')
  async syncRightShip(@Body() body: SyncRightShipDTO, @I18n() i18n: I18nContext) {
    await this.rightShipService.syncVesselAndRightShip(body);
    return {
      message: await i18n.t('common.CREATE_SUCCESS'),
    };
  }

  @ApiResponse({ description: 'Correct incorrect sync success', status: HttpStatus.OK })
  @ApiResponse({ description: 'Correct incorrect sync error', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({ summary: 'Correct incorrect sync', operationId: 'correctIncorrectSync' })
  @ApiBody({ type: SyncRightShipDTO })
  @Post('correct-sync')
  async correctSyncRightShip(@Body() body: SyncRightShipDTO, @I18n() i18n: I18nContext) {
    return await this.rightShipService.correctSyncRightShip(body.companyId);
  }

  // @ApiResponse({ description: 'Create right ship success', status: HttpStatus.OK })
  // @ApiResponse({ description: 'Create right ship error', status: HttpStatus.BAD_REQUEST })
  // @ApiOperation({ summary: 'Create right ship ', operationId: 'createRightShip' })
  // @ApiBody({ type: CreateRightShipDto })
  // @Roles(RoleScope.ADMIN, RoleScope.USER)
  // @RequiredPermissions({
  //   feature:
  //     FeatureEnum.QUALITY_ASSURANCE_SAILING_REPORT + '::' + SubFeatureEnum.SAILING_GENERAL_REPORT,

  //   action: ActionEnum.CREATE,
  // })
  // @Post('')
  // async createRightShip(
  //   @TokenDecorator() token: TokenPayloadModel,
  //   @Body() body: CreateRightShipDto,
  //   @I18n() i18n: I18nContext,
  // ) {
  //   await this.rightShipService.createRightShip(body, token);
  //   return {
  //     message: await i18n.t('common.CREATE_SUCCESS'),
  //   };
  // }

  @ApiResponse({ description: 'List right ship success', status: HttpStatus.OK })
  @ApiResponse({ description: 'List right ship error', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({ summary: 'List incident', operationId: 'listRightShip' })
  @ApiQuery({
    description: 'Paginate params',
    type: ListRightShipDto,
  })
  @ApiBearerAuth()
  @UseGuards(AuthGuard, RolesGuard)
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @Get('')
  async listRightShip(
    @TokenDecorator() token: TokenPayloadModel,
    @Query() query: ListRightShipDto,
  ) {
    return this.rightShipService.listRightShip(query, token);
  }

  @ApiResponse({ description: 'List right ship incidents success', status: HttpStatus.OK })
  @ApiResponse({ description: 'List right ship incidents error', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({ summary: 'List RightShip incidents', operationId: 'listRightShipIncidents' })
  @ApiQuery({
    description: 'Paginate and filter params',
    type: ListRightShipIncidentsDto,
  })
  @ApiBearerAuth()
  @UseGuards(AuthGuard, RolesGuard)
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @Get('incidents')
  async listRightShipIncidents(
    @TokenDecorator() token: TokenPayloadModel,
    @Query() query: ListRightShipIncidentsDto,
  ) {
    return this.rightShipService.listRightShipIncidents(query, token);
  }

  @ApiParam({ name: 'id', type: 'string', required: true })
  @ApiResponse({
    description: 'Get RightShip incident detail success',
    status: HttpStatus.OK,
  })
  @ApiOperation({
    summary: 'Get RightShip incident detail',
    operationId: 'getRightShipIncidentById',
  })
  @ApiBearerAuth()
  @UseGuards(AuthGuard, RolesGuard)
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @Get('incidents/:id')
  async getRightShipIncidentById(
    @TokenDecorator() token: TokenPayloadModel,
    @Param('id', ParseUUIDPipe) id: string,
  ) {
    return this.rightShipService.getRightShipIncidentById(id, token);
  }

  @ApiResponse({ description: 'Update right ship risks success', status: HttpStatus.OK })
  @ApiResponse({ description: 'Update right ship risks error', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({
    summary: 'Update right ship risks by vessel ID',
    operationId: 'updateRightShipRiskByVesselId',
  })
  @ApiBody({
    type: UpdateRightShipRiskDto,
    required: true,
    description: 'Update right ship risk data',
  })
  @ApiBearerAuth()
  @UseGuards(AuthGuard, RolesGuard)
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature:
      FeatureEnum.QUALITY_ASSURANCE_SAILING_REPORT + '::' + SubFeatureEnum.SAILING_GENERAL_REPORT,
    action: ActionEnum.UPDATE,
  })
  @Put('/risks/:vesselId')
  async updateRightShipRiskByVesselId(
    @TokenDecorator() token: TokenPayloadModel,
    @I18nLang() lang: string,
    @Body() body: UpdateRightShipRiskDto,
    @I18n() i18n: I18nContext,
    @Param('vesselId', ParseUUIDPipe) vesselId: string,
  ) {
    const result = await this.rightShipService.updateRightShipRiskByVesselId(body, vesselId, token);
    return {
      message: await i18n.t('common.UPDATE_SUCCESS'),
      data: result,
    };
  }

  // @ApiParam({ name: 'id', type: 'string', required: true })
  // @ApiResponse({
  //   description: 'Get detail right ship success',
  //   status: HttpStatus.OK,
  // })
  // @ApiOperation({ summary: 'Get detail right ship' })
  // @Roles(RoleScope.ADMIN, RoleScope.USER)
  // @RequiredPermissions({
  //   feature:
  //     FeatureEnum.QUALITY_ASSURANCE_SAILING_REPORT + '::' + SubFeatureEnum.SAILING_GENERAL_REPORT,

  //   action: ActionEnum.VIEW,
  // })
  // @Get('/:id')
  // async getDetailRightShip(
  //   @TokenDecorator() token: TokenPayloadModel,
  //   @Param('id', ParseUUIDPipe) id: string,
  // ) {
  //   return this.rightShipService.getDetailRightShip(id, token);
  // }

  // @ApiResponse({ description: 'Update right ship success', status: HttpStatus.OK })
  // @ApiResponse({
  //   description: 'update right ship error',
  //   status: HttpStatus.BAD_REQUEST,
  // })
  // @ApiOperation({
  //   summary: 'update right ship',
  //   operationId: 'updateRightShip',
  // })
  // @ApiParam({
  //   name: 'id',
  //   type: 'string',
  //   required: true,
  //   description: 'Right ship id',
  // })
  // @ApiBody({ type: UpdateRightShipDto, required: true })
  // @Roles(RoleScope.ADMIN, RoleScope.USER)
  // @RequiredPermissions({
  //   feature:
  //     FeatureEnum.QUALITY_ASSURANCE_SAILING_REPORT + '::' + SubFeatureEnum.SAILING_GENERAL_REPORT,

  //   action: ActionEnum.UPDATE,
  // })
  // @Put('/:id')
  // async updateRightShip(
  //   @TokenDecorator() token: TokenPayloadModel,
  //   @Param('id', ParseUUIDPipe) id: string,
  //   @I18nLang() lang: string,
  //   @Body() body: UpdateRightShipDto,
  //   @I18n() i18n: I18nContext,
  // ) {
  //   await this.rightShipService.updateRightShip(id, body, token);
  //   return {
  //     message: await i18n.t('common.UPDATE_SUCCESS'),
  //   };
  // }

  // @ApiParam({ name: 'id', type: 'string', required: true })
  // @ApiResponse({
  //   description: 'Delete right ship success',
  //   status: HttpStatus.OK,
  // })
  // @ApiOperation({ summary: 'Delete right ship' })
  // @Roles(RoleScope.ADMIN, RoleScope.USER)
  // @RequiredPermissions({
  //   feature:
  //     FeatureEnum.QUALITY_ASSURANCE_SAILING_REPORT + '::' + SubFeatureEnum.SAILING_GENERAL_REPORT,
  //   action: ActionEnum.DELETE,
  // })
  // @Delete('/:id')
  // async deleteRightShip(
  //   @TokenDecorator() token: TokenPayloadModel,
  //   @Param('id', ParseUUIDPipe) RightShipId: string,
  //   @I18n() i18n: I18nContext,
  // ) {
  //   await this.rightShipService.deleteRightShip(RightShipId, token);
  //   return {
  //     message: await i18n.t('common.DELETE_SUCCESS'),
  //   };
  // }
}
