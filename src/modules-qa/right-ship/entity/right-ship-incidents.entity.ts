import { IdentifyEntity } from 'svm-nest-lib-v3';
import { Column, Entity, ManyToOne } from 'typeorm';
import { Company } from '../../../modules/company/company.entity';
import { Vessel } from '../../../modules/vessel/entity/vessel.entity';
import { RightShip } from '../right-ship.entity';
import { IncidentMaster } from '../../incident-master/incident-master.entity';

@Entity()
export class RightShipIncidents extends IdentifyEntity {
  @Column({ type: 'integer', unique: true })
  public incidentId: number;

  @Column({ type: 'date' })
  public incidentDate: Date;

  @Column({ type: 'integer' })
  public imo: number;

  @Column({ nullable: true })
  public status: string;

  @Column({ nullable: true })
  public detailedStatus: string;

  @Column({ nullable: true })
  public incidentSource: string;

  @Column({ nullable: true })
  public flagAtTimeOfIncident: string;

  @Column({ type: 'integer', nullable: true })
  public docAtTimeOfIncident: number;

  @Column({ nullable: true })
  public severity: string;

  @Column({ type: 'boolean', default: false })
  public activeInSafetyScore: boolean;

  @Column({ type: 'uuid', nullable: true })
  public incidentMasterId: string;

  @Column({ nullable: true })
  public assistanceGiven: string;

  @Column({ nullable: true })
  public casualtyOrDemolition: string;

  @Column({ type: 'boolean', default: false })
  public totalLoss: boolean;

  @Column({ type: 'integer', default: 0 })
  public numberKilled: number;

  @Column({ type: 'integer', default: 0 })
  public numberMissing: number;

  @Column({ type: 'boolean', default: false })
  public sustainedSignificantDamage: boolean;

  @Column({ type: 'boolean', default: false })
  public pollutionOccured: boolean;

  @Column({ nullable: true })
  public pollutionDetails: string;

  @Column({ type: 'integer', default: 0 })
  public numberSeriousInjuries: number;

  @Column({ type: 'text', nullable: true })
  public preciseText: string;

  @Column({ type: 'text', nullable: true })
  public complimentaryText: string;

  @Column({ type: 'uuid' })
  public companyId: string;

  @Column({ type: 'uuid', nullable: true })
  public vesselId: string;

  @Column({ type: 'uuid', nullable: true })
  public rightShipId: string;

  /** Mapping foreign keys */
  @ManyToOne(() => Company, { onDelete: 'CASCADE' })
  company: Company;

  @ManyToOne(() => Vessel, { onDelete: 'CASCADE' })
  vessel: Vessel;

  @ManyToOne(() => RightShip, { onDelete: 'CASCADE' })
  rightShip: RightShip;

  @ManyToOne(() => IncidentMaster, { onDelete: 'SET NULL' })
  incidentMaster: IncidentMaster;
}