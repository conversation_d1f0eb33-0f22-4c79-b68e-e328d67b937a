import { Injectable } from '@nestjs/common';
import { Logger<PERSON><PERSON><PERSON>, TokenPayloadModel } from 'svm-nest-lib-v3';
import { RightShipRepository } from './right-ship.repository';
import {
  ListRightShipDto,
  CreateRightShipDto,
  UpdateRightShipDto,
  UpdateRightShipRiskDto,
  SyncRightShipDTO,
  ListRightShipIncidentsDto,
} from './dto';
import { SyncDataVesselService } from '../../micro-services/sync/sync-data-vessel.service';
import { VoyageMasterDetails } from '../vessel-scheduler-module/entity/vessel-scheduler.entity';

@Injectable()
export class RightShipService {
  constructor(private readonly rightShipRepository: RightShipRepository) {
    // this._syncVesselAndRightShip();
  }

  async listRightShip(query: ListRightShipDto, token: TokenPayloadModel) {
    return this.rightShipRepository.listRightShip(query, token);
  }

  async getDetailRightShip(RightShipId: string, token: TokenPayloadModel) {
    return this.rightShipRepository.getDetailRightShip(RightShipId, token);
  }

  async deleteRightShip(RightShipId: string, token: TokenPayloadModel) {
    return this.rightShipRepository.deleteRightShip(RightShipId, token);
  }

  async createRightShip(body: CreateRightShipDto, token: TokenPayloadModel) {
    return this.rightShipRepository.createRightShip(body, token);
  }

  async updateRightShip(RightShipId: string, body: UpdateRightShipDto, token: TokenPayloadModel) {
    const prepareBody = {
      ...body,
      updatedUserId: token.id,
    } as UpdateRightShipDto;
    return this.rightShipRepository.updateRightShip(RightShipId, prepareBody, token);
  }

  async updateRightShipRiskByVesselId(
    body: UpdateRightShipRiskDto,
    vesselId: string,
    token: TokenPayloadModel,
  ) {
    return this.rightShipRepository.updateRightShipRiskByVesselId(body, vesselId, token);
  }

  async listRightShipIncidents(query: ListRightShipIncidentsDto, token: TokenPayloadModel) {
    return this.rightShipRepository.listRightShipIncidents(query, token);
  }

  async getRightShipIncidentById(incidentId: string, token: TokenPayloadModel) {
    return this.rightShipRepository.getRightShipIncidentById(incidentId, token);
  }

  async syncVesselAndRightShip(body: SyncRightShipDTO) {
    LoggerCommon.log('getDataVessel start');
    const login: any = await SyncDataVesselService.connectLoginService();
    console.log(login);
    if (login && login.status_code == 200) {
      console.log(login.result.token);
      const vessels: any = await SyncDataVesselService.getDataVesselMaster(
        login.result.token,
        body.fromDate,
        body.toDate,
      );
      const rightShips: any = await SyncDataVesselService.getDataRightShip(
        login.result.token,
        body.fromDate,
        body.toDate,
      );
      // console.log(rightShips);
      const voyages: any = await SyncDataVesselService.getDataVoyage(
        login.result.token,
        body.fromDate,
        body.toDate,
      );
      const voyageMasterDetails: any = await SyncDataVesselService.getDataVoyageMasterDetails(
        login.result.token,
        body.fromDate,
        body.toDate,
      );
      // console.log('getDataVoyageMasterDetails: ', voyageMasterDetails);
      // console.log('----', voyages);
      let vesselDatas = undefined;
      let rightShipDatas = undefined;
      let voyageDatas = undefined;
      let voyageMasterDetailsData = undefined;
      if (vessels && vessels.status_code == 200) {
        vesselDatas = vessels.data;
      }
      if (rightShips && rightShips.status_code == 200) {
        rightShipDatas = rightShips.data;
      }
      if (voyages && voyages.status_code == 200) {
        voyageDatas = voyages.data;
      }
      if (voyageMasterDetails && voyageMasterDetails.status_code == 200) {
        voyageMasterDetailsData = voyageMasterDetails.data;
      }
      await this.rightShipRepository.syncVesselAndRightShipUpdate(
        body.companyId,
        vesselDatas ? vesselDatas : [],
        rightShipDatas ? rightShipDatas : [],
        voyageDatas ? voyageDatas : [],
        voyageMasterDetails ? voyageMasterDetailsData : [],
      );
      await this.rightShipRepository.docHolderMasterUpdates(
        body.companyId,
        rightShipDatas ? rightShipDatas : [],
      );
      return this.rightShipRepository.vesselDocHolderIdUpdates(
        body.companyId,
        rightShipDatas ? rightShipDatas : [],
      );
    }
    return 1;
  }

  async correctSyncRightShip(companyId: string) {
    return this.rightShipRepository.correctSyncRightShip(companyId);
  }
}
