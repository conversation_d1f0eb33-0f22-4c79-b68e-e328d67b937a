import { IdentifyEntity } from 'svm-nest-lib-v3';
import { Column, Entity, ManyToOne, OneToMany, OneToOne } from 'typeorm';
import { Company } from '../../modules/company/company.entity';
import { Vessel } from '../../modules/vessel/entity/vessel.entity';
import { GHGRatingEnum, InspectionRequiredEnum, PlusEnum } from './enums';
import { RightShipRestrictions } from './rightship-restrictions.entity';


@Entity()
export class RightShip extends IdentifyEntity {
  @Column({ type: 'uuid', nullable: true })
  public vesselId: string;

  @Column({ type: 'timestamp', nullable: true })
  public buildDate: Date;

  @Column({ nullable: true })
  public docHolderName: string;

  @Column({ nullable: true })
  public docHolderCode: string;

  @Column({ type: 'timestamp', nullable: true })
  public ghgRatingDate: Date;

  @Column({ nullable: true })
  public ghgRating: string;

  @Column({ nullable: true })
  public evdi: string;

  @Column({ default: false, select: false })
  public verified: boolean;

  @Column({ nullable: true })
  public plus: string;

  @Column({ nullable: true })
  public safetyScore: string;

  @Column({ type: 'timestamp', nullable: true })
  public safetyScoreDate: Date;

  @Column({ default: false, select: false })
  public indicativeScore: boolean;

  @Column({ nullable: true })
  public inspectionRequired: string;

  @Column({ nullable: true })
  public docSafetyScore: string;

  @Column({ nullable: true })
  public latInspectionOutcome: string;

  @Column({ type: 'timestamp', nullable: true })
  public lastInspectionValidity: Date;

  @Column({ nullable: true })
  public inspectionAdditionalData: string;

  @Column({ nullable: true })
  public technicalManagerName: string;

  @Column({ nullable: true })
  public technicalManagerOwCode: string;

  @Column({ nullable: true })
  public platformLink: string;

  @Column({ type: 'uuid', nullable: true })
  public companyId: string;

  @Column({ nullable: true })
  public restrictedByRightShip: boolean;

  @Column({
    type: 'text',
    array: true,
    nullable: true,
  })
  public restrictions: string[];

  /** Mapping foreign keys */
  @ManyToOne(() => Company, (company) => company.rightShips, { onDelete: 'CASCADE' })
  company: Company;

  @ManyToOne(() => Vessel, (vessel) => vessel.rightShips, { onDelete: 'CASCADE' })
  vessel: Vessel;

  @OneToMany(
    () => RightShipRestrictions,
    (rightShipRestrictions) => rightShipRestrictions.rightShip,
  )
  rightShipRestrictions: RightShipRestrictions[];
}
