import {
  <PERSON><PERSON>rror,
  CommonStatus,
  <PERSON>gger<PERSON>ommon,
  TokenPayloadModel,
  TypeORMRepository,
  Utils,
  RoleScope,
} from 'svm-nest-lib-v3';
import {
  Connection,
  EntityManager,
  EntityRepository,
  getCustomRepository,
  getRepository,
} from 'typeorm';
import { VesselScreening } from '../vessel-screening/entity/vessel-screening.entity';
import {
  CreateRightShipDto,
  ListRightShipDto,
  UpdateRightShipDto,
  UpdateRightShipRiskDto,
} from './dto';
import { FilterForEnum } from './enums';
import { RightShip } from './right-ship.entity';
import RIGHT_SHIP_JSON from './data/right-ship-sample.json';

import VESSEL_JSON from './data/vessel-data.json';
import VOYAGE_JSON_NEW from './data/voyage-data.json';
import { VesselRepository } from '../../modules/vessel/vessel.repository';
import { Vessel } from '../../modules/vessel/entity/vessel.entity';
import { VesselTypeRepository } from '../../modules/vessel-type/vessel-type.repository';
import { VesselType } from '../../modules/vessel-type/vessel-type.entity';
import {
  ActorsEnum,
  AllowedSubscriptionsEnum,
  ApiOrManual,
  StatusCommon,
  VesselScreeningStatusEnum,
} from '../../commons/enums';
import { User } from '../../modules/user/user.entity';
import { UserRepository } from '../../modules/user/user.repository';
import { Voyage } from '../voyage/voyage.entity';
import { CompanyRepository } from '../../modules/company/company.repository';
import { ClassificationSocietyRepository } from '../classification-society/classification-society.repository';
import { Company } from '../../modules/company/company.entity';
import { VesselDocHolderRepository } from '../../modules/vessel/repository/vessel-doc-holder.repository';
import { VesselDocHolder } from '../../modules/vessel/entity/vessel-doc-holder.entity';
import moment from 'moment';
import { VoyageMasterDetails } from '../vessel-scheduler-module/entity/vessel-scheduler.entity';
import { VesselGeneralHistory } from '../../modules/vessel/entity/vessel-history.entity';
import { PortMaster } from '../../modules/port-master/port-master.entity';
import { VoyageStatus } from '../voyage-status/entity/voyage-status.entity';
import { Cargo } from '../cargo/cargo.entity';
import { CompanyType } from 'src/modules/company-type/company-type.entity';
import { RightShipRestrictions } from './rightship-restrictions.entity';
import { RightShipIncidents } from './entity/right-ship-incidents.entity';
import { IncidentMaster } from '../incident-master/incident-master.entity';
import APP_CONFIG from '../../configs/app.config';
import { RoleScopeEnum } from 'src/modules/user/enums';

@EntityRepository(RightShip)
export class RightShipRepository extends TypeORMRepository<RightShip> {
  constructor(private readonly connection: Connection) {
    super();
  }
  async listRightShip(query: ListRightShipDto, token: TokenPayloadModel) {
    try {
      const queryBuilder = this.createQueryBuilder('rightShip')
        // .leftJoin('rightShip.vessel', 'vessel')
        .select([
          'rightShip.id',
          'rightShip.updatedAt',
        ])
        .where('rightShip.vesselId = :vesselId AND rightShip.companyId = :companyId', {
          vesselId: query.vesselId,
          companyId: token.companyId,
        });
      if (query.filterFor) {
        switch (query.filterFor) {
          case FilterForEnum.SAFETY_SCORE: {
            queryBuilder.addSelect([`rightShip.${query.filterFor}`, 'rightShip.safetyScoreDate']);
            break;
          }
          case FilterForEnum.DOC_SAFETY_SCORE: {
            queryBuilder.addSelect([`rightShip.${query.filterFor}`, 'rightShip.docHolderName']);
            break;
          }
          case FilterForEnum.GHG_RATING: {
            queryBuilder.addSelect([`rightShip.${query.filterFor}`, 'rightShip.ghgRatingDate']);
            break;
          }
          case FilterForEnum.LAST_INSPECTION_VALIDITY: {
            queryBuilder.addSelect(`rightShip.${query.filterFor}`);
            break;
          }
          default:
        }

        const dataList = await this.list(
          { page: query.page, limit: query.pageSize },
          {
            queryBuilder,
            sort: query.sort || 'rightShip.createdAt:-1',
          },
        );

        return dataList;
      } else {
        queryBuilder
          .addSelect([
            'rightShip.id',
            'rightShip.vesselId',
            'rightShip.buildDate',
            'rightShip.docHolderName',
            'rightShip.docHolderCode',
            'rightShip.ghgRating',
            'rightShip.ghgRatingDate',
            'rightShip.evdi',
            'rightShip.verified',
            'rightShip.plus',
            'rightShip.safetyScore',
            'rightShip.safetyScoreDate',
            'rightShip.docSafetyScore',
            'rightShip.indicativeScore',
            'rightShip.inspectionRequired',
            'rightShip.latInspectionOutcome',
            'rightShip.lastInspectionValidity',
            'rightShip.technicalManagerName',
            'rightShip.technicalManagerOwCode',
            'rightShip.createdAt',
            'rightShip.updatedAt',
          ])
          .orderBy('rightShip.updatedAt', 'DESC');
        const dataList = await this.list(
          { page: query.page, limit: query.pageSize },
          {
            queryBuilder,
            // sort: query.sort || 'rightShip.createdAt:-1',
          },
        );

        return dataList;
      }
    } catch (ex) {
      LoggerCommon.error('[RightShipRepository] listRightShip  error ', ex.message || ex);
      throw ex;
    }
  }

  async getDetailRightShip(rightShipId: string, token: TokenPayloadModel) {
    try {
      const rightShip = await this.getOneQB(
        this.createQueryBuilder('rightShip')
          .leftJoin('rightShip.vessel', 'vessel')
          .select()
          .where('rightShip.id = :rightShipId', {
            rightShipId,
          }),
      );

      if (rightShip) {
        return rightShip;
      } else {
        throw new BaseError({ status: 404, message: 'rightShip.NOT_FOUND' });
      }
    } catch (ex) {
      LoggerCommon.error('[RightShipRepository] create Right Ship  error ', ex.message || ex);
      throw ex;
    }
  }

  async deleteRightShip(rightShipId: string, token: TokenPayloadModel) {
    const hasRef = await this.hasRefInGivenTables(rightShipId, 'rightShipId', [
      { entity: VesselScreening },
    ]);

    if (hasRef) {
      throw new BaseError({ status: 400, message: 'common.CANNOT_DELETE_DUE_TO_REF' });
    }

    // soft delete
    const updateResult = await this.softDelete({
      id: rightShipId,
    });
    if (updateResult.affected === 0) {
      throw new BaseError({ status: 404, message: 'rightShip.NOT_FOUND' });
    } else {
      return 1;
    }
  }

  async createRightShip(body: CreateRightShipDto, token: TokenPayloadModel) {
    try {
      // const rs = await this.insert({
      //   ...body,
      //   safetyScoreDate: body.safetyScoreDate && new Date(body.safetyScoreDate),
      //   ghgRatingDate: body.ghgRatingDate && new Date(body.ghgRatingDate),
      //   lastInspectionValidity:
      //     body.lastInspectionValidity && new Date(body.lastInspectionValidity),
      // } as RightShip);
      // return rs.identifiers[0];
    } catch (ex) {
      LoggerCommon.error('[RightShipRepository] createRightShip error ', ex.message || ex);
      throw ex;
    }
  }

  async updateRightShip(id: string, body: UpdateRightShipDto, token: TokenPayloadModel) {
    try {
      // const updateResult = await this.update(
      //   {
      //     id,
      //     deleted: false,
      //   },
      //   body,
      // );
      // if (updateResult.affected) {
      //   return updateResult.affected;
      // } else {
      //   throw new BaseError({ status: 404, message: 'rightShip.NOT_FOUND' });
      // }
    } catch (ex) {
      LoggerCommon.error('[RightShipRepository] update right ship  error ', ex.message || ex);

      throw ex;
    }
  }

  async updateRightShipRiskByVesselId(
    body: UpdateRightShipRiskDto,
    vesselId: string,
    token: TokenPayloadModel,
  ) {
    try {
      // Check if right ship record exists for the vessel
      const rightShip = await this.findOne({
        vesselId: vesselId,
        deleted: false,
      });

      if (!rightShip) {
        throw new BaseError({
          status: 404,
          message: 'Right ship record not found for this vessel',
        });
      }

      // Prepare update data - only include fields that are provided
      const updateData: any = {
        updatedAt: new Date(),
      };

      if (body.potentialRisk !== undefined) {
        updateData.potentialRisk = body.potentialRisk;
      }

      if (body.observedRisk !== undefined) {
        updateData.observedRisk = body.observedRisk;
      }

      if (body.timeloss !== undefined) {
        updateData.timeloss = body.timeloss;
      }

      // Update the record
      const updateResult = await this.update(
        {
          vesselId: vesselId,
          deleted: false,
        },
        updateData,
      );

      if (updateResult.affected) {
        return {
          success: true,
          message: 'Right ship risks updated successfully',
          affected: updateResult.affected,
        };
      } else {
        throw new BaseError({ status: 404, message: 'Failed to update right ship risks' });
      }
    } catch (ex) {
      LoggerCommon.error('[RightShipRepository] update right ship risks error ', ex.message || ex);
      throw ex;
    }
  }

  async _syncVesselAndRightShip(companyId) {
    try {
      return await this.connection.transaction(async (manager) => {
        // const companyId = '';
        return this._syncVessel(manager, companyId);
      });
    } catch (ex) {
      LoggerCommon.error('[RightShipRepository] syncRightShipAndVessel error ', ex.message || ex);
      throw ex;
    }
  }

  async _syncVesselAndRightShipAPIUpdate(
    companyId,
    vesselRaws: any,
    rightShipRaws: any,
    voyageRaws: any,
    voyageMasterDetailsRaws: any,
  ) {
    try {
      if (!companyId) {
        return;
      }
      return await this.connection.transaction(async (manager) => {
        // const companyId = '';
        return this._syncVesselUpdate(
          manager,
          companyId,
          vesselRaws,
          rightShipRaws,
          voyageRaws,
          voyageMasterDetailsRaws,
        );
      });
    } catch (ex) {
      LoggerCommon.error(
        '[RightShipRepository] syncVesselAndRightShipUpdate error ',
        ex.message || ex,
      );
      throw ex;
    }
  }

  async syncVesselAndRightShipUpdate(
    companyId,
    vesselRaws: any,
    rightShipRaws: any,
    voyageRaws: any,
    voyageMasterDetailsRaw: any,
  ) {
    try {
      if (!companyId) {
        return;
      }
      const company = await this.manager
        .getCustomRepository(CompanyRepository)
        .getDetailCompanyByIdNotThrowError(companyId);
      if (!company) {
        return;
      }
      // return await this.connection.transaction(async (manager) => {
      // const companyId = '';
      return this._syncVesselUpdate(
        this.manager,
        companyId,
        vesselRaws,
        rightShipRaws,
        voyageRaws,
        voyageMasterDetailsRaw,
      );
      // });
    } catch (ex) {
      LoggerCommon.error('[RightShipRepository] syncVesselAndRightShipUpdate error ', ex.log);
      throw ex;
    }
  }
  async docHolderMasterUpdates(companyId: string, rightShipRaws: any) {
    try {
      if (!companyId) {
        return;
      }
      const company = await this.manager
        .getCustomRepository(CompanyRepository)
        .getDetailCompanyByIdNotThrowError(companyId);
      if (!company) {
        return;
      }
      return await this.connection.transaction(async (manager) => {
        return this._syncDocHolderMasterData(manager, companyId, rightShipRaws);
      });
    } catch (ex) {
      LoggerCommon.error('[DocRepository] error ', ex.message || ex);
      throw ex;
    }
  }
  async vesselDocHolderIdUpdates(companyId: string, rightShipRaws: any) {
    try {
      if (!companyId) {
        return;
      }
      const company = await this.manager
        .getCustomRepository(CompanyRepository)
        .getDetailCompanyByIdNotThrowError(companyId);
      if (!company) {
        return;
      }
      return await this.connection.transaction(async (manager) => {
        // const companyId = '';
        return this._vesselMasterDocIdUpdate(manager, companyId, rightShipRaws);
      });
    } catch (ex) {
      LoggerCommon.error('[VesselDocHolderIdUpdates] error', ex.message || ex);
      throw ex;
    }
  }
  async _syncVesselUpdate(
    managerTrans: EntityManager,
    companyId: string,
    vesselRaws: any,
    rightShipRaws: any,
    voyageRaws: any,
    voyageMasterDetailsRaws: any,
  ) {
    try {
      // Initialize result counters
      let noOfVesselInserted = 0;
      let noOfVesselUpdated = 0;
      let noOfRightShipInserted = 0;
      let noOfVoyageMasterInserted = 0;
      let counter = 0;
      let counterUpdate = 0;
      let listCurrentVessel = [];

      try {
        // Get current vessels
        listCurrentVessel = await managerTrans.getCustomRepository(VesselRepository).find({
          where: {
            deleted: false,
            companyId,
          },
          select: ['id', 'imoNumber'],
        });
        this._logData('Retrieved current vessels', { count: listCurrentVessel.length });
      } catch (error) {
        this._logError('[_syncVesselUpdate] Error retrieving current vessels', error);
        throw error; // Critical error, can't proceed without vessel list
      }

      // Get classification society
      let classificationSocietyUnknownId = undefined;
      try {
        const classificationSocietyUnknown = await managerTrans
          .getCustomRepository(ClassificationSocietyRepository)
          .findOne({
            code: 'Unknown',
            deleted: false,
          });
        if (classificationSocietyUnknown) {
          classificationSocietyUnknownId = classificationSocietyUnknown.id;
        }
      } catch (error) {
        this._logError('[_syncVesselUpdate] Error retrieving classification society', error);
        // Continue without classification society ID
      }

      // Get country data
      let allCountry = [];
      try {
        allCountry = await this.connection.query(`select id, code, code3, name from country`);
      } catch (error) {
        this._logError('[_syncVesselUpdate] Error retrieving country data', error);
        // Continue without country data
      }

      // Process each vessel
      for (const vesselRaw of vesselRaws) {
        try {
          let vesselId, vesselImo;
          let country = [];

          if (vesselRaw.ISOKey) {
            country = allCountry.filter((item) => item.code3 === vesselRaw.ISOKey);
          }

          // Find vessel by IMO and code
          let vesselFoundByimo, vesselFoundByCode;
          try {
            vesselFoundByimo = await managerTrans
              .getCustomRepository(VesselRepository)
              .findOne({ imoNumber: vesselRaw.imo, companyId });

            vesselFoundByCode = await managerTrans
              .getCustomRepository(VesselRepository)
              .findOne({ code: vesselRaw.vessel_code, companyId });
          } catch (error) {
            this._logError('[_syncVesselUpdate] Error finding vessel', error);
            continue; // Skip this vessel and continue with the next one
          }

          // Get or create vessel type
          let vesselTypeId;
          try {
            const vesselTypeFoundByName = await managerTrans
              .getCustomRepository(VesselTypeRepository)
              .findOne({ name: vesselRaw.vessel_type, companyId });

            if (!vesselTypeFoundByName) {
              const vesselTypeCreated = await managerTrans.save(VesselType, {
                name: vesselRaw.vessel_type,
                code:
                  vesselRaw.vessel_type.length > 20
                    ? vesselRaw.vessel_type.substring(0, 20)
                    : vesselRaw.vessel_type,
                vettingRiskScore: 0,
                status: StatusCommon.ACTIVE,
                companyId,
              } as VesselType);
              vesselTypeId = vesselTypeCreated.id;
            } else {
              vesselTypeId = vesselTypeFoundByName.id;
            }
          } catch (error) {
            this._logError('[_syncVesselUpdate] Error processing vessel type', error);
            continue; // Skip this vessel and continue with the next one
          }

          // Create new vessel if not found by code
          if (!vesselFoundByCode) {
            try {
              this._logData('Creating new vessel', {
                imo: vesselRaw.imo,
                name: vesselRaw.vessel_name,
              });
              counter = counter + 1;

              // Find company admins
              const listCompanyAdmins = await managerTrans
                .getCustomRepository(UserRepository)
                .listAdminsByCompany(companyId);

              // Create vessel
              const vesselParams = {
                imoNumber: vesselRaw.imo,
                name: vesselRaw.vessel_name,
                code: vesselRaw.vessel_code,
                countryId: country.length > 0 ? country[0].id : null,
                vesselTypeId,
                callSign: vesselRaw.call_letters,
                buildDate: new Date(vesselRaw.year_built).toISOString(),
                status:
                  vesselRaw.status == 'True' || 'TRUE'
                    ? CommonStatus.ACTIVE
                    : CommonStatus.IN_ACTIVE,
                deadWeightTonnage: vesselRaw.dwt ? Number(vesselRaw.dwt) : 0,
                grt: vesselRaw.grt ? Number(vesselRaw.grt) : 0,
                nrt: vesselRaw.nrt ? Number(vesselRaw.nrt) : 0,
                maxDraft: Number(vesselRaw.vessel_summer_draft),
                loa: Number(vesselRaw.loa),
                depth: Number(vesselRaw.depth),
                companyId,
                owners: listCompanyAdmins.map((admin) => ({ id: admin.id } as User)),
                isImported: false,
                classificationSocietyId: classificationSocietyUnknownId,
              };

              const vesselCreated = await managerTrans.save(Vessel, vesselParams as Vessel);
              noOfVesselInserted = noOfVesselInserted + 1;
              LoggerCommon.log(`Insert vessel success: ${counter}/${vesselRaws.length}`);

              vesselId = vesselCreated.id;
              vesselImo = vesselCreated.imoNumber;
              listCurrentVessel.push({
                id: vesselId,
                imoNumber: vesselImo,
              });
            } catch (error) {
              this._logError('[_syncVesselUpdate] Error creating vessel', error);
              continue; // Skip this vessel and continue with the next one
            }
          } else {
            // Update existing vessel
            try {
              // Handle vessel name history if name has changed
              try {
                const existingVesselName = vesselFoundByimo.name.trim().replace('/s+/g', ' ');
                const currentVesselName = vesselRaw.vessel_name.trim().replace('/s+/g', ' ');

                if (
                  existingVesselName.toLocaleLowerCase() !== currentVesselName.toLocaleLowerCase()
                ) {
                  // Get vessel history
                  const vesselGeneralHistory = await getRepository(VesselGeneralHistory)
                    .createQueryBuilder('vesselGeneralHistory')
                    .select([
                      'vesselGeneralHistory.id',
                      'vesselGeneralHistory.fromDate',
                      'vesselGeneralHistory.toDate',
                      'vesselGeneralHistory.name',
                      'vesselGeneralHistory.source',
                    ])
                    .where('vesselGeneralHistory.vesselId = :vesselId', {
                      vesselId: vesselFoundByimo.id,
                    })
                    .getMany();

                  const dateObj = new Date();

                  if (!vesselGeneralHistory.length) {
                    // Create first history entry
                    const exVesselPreparedData = {
                      companyId: companyId,
                      vesselId: vesselFoundByimo.id,
                      name: vesselFoundByimo.name,
                      fromDate: new Date('2020-01-01'),
                      toDate: new Date(),
                      source: ApiOrManual.API,
                    };
                    await managerTrans.save(
                      VesselGeneralHistory,
                      exVesselPreparedData as VesselGeneralHistory,
                    );
                  } else {
                    // Update with latest history
                    const latestVesselGeneralHistory = vesselGeneralHistory.reduce(function (
                      prev,
                      current,
                    ) {
                      const prevDate = new Date(prev.toDate);
                      const currentDate = new Date(current.toDate);
                      return currentDate > prevDate ? current : prev;
                    });

                    const exVesselPreparedData = {
                      companyId: companyId,
                      vesselId: vesselFoundByimo.id,
                      name: vesselRaw.vessel_name,
                      fromDate: latestVesselGeneralHistory.toDate,
                      toDate: new Date(),
                      source: ApiOrManual.API,
                    };
                    await managerTrans.save(
                      VesselGeneralHistory,
                      exVesselPreparedData as VesselGeneralHistory,
                    );
                  }
                }
              } catch (historyError) {
                this._logError('[_syncVesselUpdate] Error updating vessel history', historyError);
                // Continue with vessel update even if history update fails
              }

              // Update vessel data
              const listCompanyAdmins = await managerTrans
                .getCustomRepository(UserRepository)
                .listAdminsByCompany(companyId);

              counterUpdate += 1;
              const vesselParamsUpdate = {
                id: vesselFoundByimo.id,
                imoNumber: vesselRaw.imo,
                name: vesselRaw.vessel_name,
                // code: vesselRaw.vessel_code, // Removed since code should stay same on update
                countryId: country.length > 0 ? country[0].id : null,
                vesselTypeId,
                callSign: vesselRaw.call_letters,
                buildDate: new Date(vesselRaw.year_built).toISOString(),
                status:
                  vesselRaw.status == 'True' || 'TRUE'
                    ? CommonStatus.ACTIVE
                    : CommonStatus.IN_ACTIVE,
                deadWeightTonnage: vesselRaw.dwt ? Number(vesselRaw.dwt) : 0,
                grt: vesselRaw.grt ? Number(vesselRaw.grt) : 0,
                nrt: vesselRaw.nrt ? Number(vesselRaw.nrt) : 0,
                maxDraft: Number(vesselRaw.vessel_summer_draft),
                loa: Number(vesselRaw.loa),
                depth: Number(vesselRaw.depth),
                owners: listCompanyAdmins.map((admin) => ({ id: admin.id } as User)),
                isImported: false,
                classificationSocietyId: vesselFoundByimo.classificationSocietyId
                  ? vesselFoundByimo.classificationSocietyId
                  : classificationSocietyUnknownId,
              };

              await managerTrans.save(Vessel, vesselParamsUpdate as Vessel);
              noOfVesselUpdated = noOfVesselUpdated + 1;
              LoggerCommon.log(
                `Update vessel success: ${counterUpdate}/${vesselRaws.length}, ${vesselRaw.imo}, ${vesselRaw.code}`,
              );

              vesselId = vesselFoundByimo.id;
              vesselImo = vesselRaw.imo;
            } catch (error) {
              this._logError('[_syncVesselUpdate] Error updating vessel', error);
              continue; // Skip this vessel and continue with the next one
            }
          }
        } catch (vesselError) {
          this._logError('[_syncVesselUpdate] Error processing vessel', vesselError);
          continue; // Skip this vessel and continue with the next one
        }
      }

      // Sync RightShip data
      try {
        noOfRightShipInserted = await this._syncRightShipImprove(
          managerTrans,
          listCurrentVessel,
          companyId,
          rightShipRaws,
        );
      } catch (error) {
        this._logError('[_syncVesselUpdate] Error syncing RightShip data', error);
        // Continue with other sync operations
      }

      // Sync company master data
      try {
        await this._syncCompanyMasterData(companyId, rightShipRaws);
      } catch (error) {
        this._logError('[_syncVesselUpdate] Error syncing company master data', error);
        // Continue with other sync operations
      }

      // Sync voyage data
      try {
        await this._syncVoyage(managerTrans, listCurrentVessel, companyId, voyageRaws);
      } catch (error) {
        this._logError('[_syncVesselUpdate] Error syncing voyage data', error);
        // Continue with other sync operations
      }

      // Sync voyage master details
      try {
        noOfVoyageMasterInserted = await this._syncVoyageMasterDetails(
          managerTrans,
          listCurrentVessel,
          companyId,
          voyageMasterDetailsRaws,
        );
      } catch (error) {
        this._logError('[_syncVesselUpdate] Error syncing voyage master details', error);
        // Continue and return results
      }

      // Return results
      return {
        noOfVesselInserted,
        noOfVesselUpdated,
        noOfRightShipInserted,
        noOfVoyageMasterInserted,
      };
    } catch (error) {
      this._logError('[_syncVesselUpdate] Main error in vessel update process', error);
      // Return partial results if available
      return {
        noOfVesselInserted: 0,
        noOfVesselUpdated: 0,
        noOfRightShipInserted: 0,
        noOfVoyageMasterInserted: 0,
        error: error.message || 'Unknown error occurred',
      };
    }
  }

  async _syncVessel(managerTrans: EntityManager, companyId: string) {
    const rightShipRaws = RIGHT_SHIP_JSON['rightShipData'];
    const voyageRaws = VOYAGE_JSON_NEW['dataVoyage'];
    const vesselRaws = VESSEL_JSON['vesselData'];
    const listCurrentVessel: any = await managerTrans.getCustomRepository(VesselRepository).find({
      where: {
        deleted: false,
        companyId,
      },
      select: ['id', 'imoNumber'],
    });
    let counter = 0;
    let counterUpdate = 0;
    const allCountry = await this.connection.query(`select code, code3, name from country`);
    for (const vesselRaw of vesselRaws) {
      let vesselId, vesselImo;
      let country = [];
      if (vesselRaw.flag_code) {
        country = allCountry.filter((item) => item.code3 === vesselRaw.flag_code);
      }
      const vesselFoundByimo = await managerTrans
        .getCustomRepository(VesselRepository)
        .findOne({ imoNumber: vesselRaw.imo, companyId });
      let vesselTypeId;
      // check if vessel type exists
      const vesselTypeFoundByName = await managerTrans
        .getCustomRepository(VesselTypeRepository)
        .findOne({ name: vesselRaw.vessel_type, companyId });
      if (!vesselTypeFoundByName) {
        const vesselTypeCreated = await managerTrans.save(VesselType, {
          name: vesselRaw.vessel_type,
          code:
            vesselRaw.vessel_type.length > 20
              ? vesselRaw.vessel_type.substring(0, 20)
              : vesselRaw.vessel_type,
          vettingRiskScore: 0,
          status: StatusCommon.ACTIVE,
          companyId,
        } as VesselType);
        vesselTypeId = vesselTypeCreated.id;
      } else {
        vesselTypeId = vesselTypeFoundByName.id;
      }

      if (!vesselFoundByimo) {
        counter = counter + 1;
        // Find company admin

        const listCompanyAdmins = await managerTrans
          .getCustomRepository(UserRepository)
          .listAdminsByCompany(companyId);
        // Create vessel
        const vesselParams = {
          imoNumber: vesselRaw.imo,
          name: vesselRaw.vessel_name,
          code: vesselRaw.vessel_code,
          // countryFlag: country.length > 0 ? country[0].name : null,
          countryId: country.length > 0 ? country[0].id : null,
          vesselTypeId,
          callSign: vesselRaw.call_letters,
          buildDate: new Date(vesselRaw.year_built).toISOString(),
          status: vesselRaw.status == 'True' ? CommonStatus.ACTIVE : CommonStatus.IN_ACTIVE,
          deadWeightTonnage: Number(vesselRaw.dwt),
          grt: Number(vesselRaw.grt),
          nrt: Number(vesselRaw.nrt),
          maxDraft: Number(vesselRaw.vessel_summer_draft),
          loa: Number(vesselRaw.loa),
          depth: Number(vesselRaw.depth),
          companyId,
          owners: listCompanyAdmins.map((admin) => ({ id: admin.id } as User)),
          isImported: true,
        };
        const vesselCreated = await managerTrans.save(Vessel, vesselParams as Vessel);
        LoggerCommon.log(`Insert vessel success: ${counter}/${vesselRaws.length}`);
        vesselId = vesselCreated.id;
        vesselImo = vesselCreated.imoNumber;
        listCurrentVessel.push({
          id: vesselId,
          imoNumber: vesselImo,
        });
        // continue;
      } else {
        // update data
        const listCompanyAdmins = await managerTrans
          .getCustomRepository(UserRepository)
          .listAdminsByCompany(companyId);
        counterUpdate += 1;
        const vesselParamsUpdate = {
          id: vesselFoundByimo.id,
          imoNumber: vesselRaw.imo,
          name: vesselRaw.vessel_name,
          code: vesselRaw.vessel_code,
          // countryFlag: country.length > 0 ? country[0].name : null,
          countryId: country.length > 0 ? country[0].id : null,
          vesselTypeId,
          callSign: vesselRaw.call_letters,
          buildDate: new Date(vesselRaw.year_built).toISOString(),
          status: vesselRaw.status == 'True' ? CommonStatus.ACTIVE : CommonStatus.IN_ACTIVE,
          deadWeightTonnage: Number(vesselRaw.dwt),
          grt: Number(vesselRaw.grt),
          nrt: Number(vesselRaw.nrt),
          maxDraft: Number(vesselRaw.vessel_summer_draft),
          loa: Number(vesselRaw.loa),
          depth: Number(vesselRaw.depth),
          owners: listCompanyAdmins.map((admin) => ({ id: admin.id } as User)),
          isImported: true,
        };
        await managerTrans.save(Vessel, vesselParamsUpdate as Vessel);
        LoggerCommon.log(`Update vessel success: ${counterUpdate}/${vesselRaws.length}`);
        vesselId = vesselFoundByimo.id;
        vesselImo = vesselRaw.imo;
      }
      // await this._syncRightShipImprove(managerTrans, vesselId, vesselImo, companyId, rightShipRaws);
      // await this._syncVoyage(managerTrans, vesselId, vesselImo, companyId, voyageRaws);
    }
    return 1;
  }

  async _syncCompanyMasterData(parentCompanyId: string, rightShipRaws: any) {
    console.log('SYNC COMPANY MASTER STARTS:');
    const companyPreparedData = [];
    for (const rightShipRaw of rightShipRaws) {
      const companyIMO = rightShipRaw?.docCompanyOwCode;
      let getCompanyDetails = undefined;
      if (companyIMO) {
        getCompanyDetails = await getRepository(Company)
          .createQueryBuilder('company')
          .select(['company.id', 'company.name'])
          .where('company.companyIMO = :companyIMO AND company.parentId = :parentCompanyId', {
            companyIMO: companyIMO,
            parentCompanyId: parentCompanyId,
          })
          .getMany();
      }
      if (!getCompanyDetails.length) {
        await this.createCompanyFromDocHolder(rightShipRaw, parentCompanyId);
      } else {
        continue;
      }
    }
    console.log('SYNC COMPANY MASTER SAVED...');
  }

  async _syncDocHolderMasterData(
    managerTrans: EntityManager,
    parentCompanyId: string,
    rightShipRaws: any,
  ) {
    const listCurrentVessel: any = await managerTrans.getCustomRepository(VesselRepository).find({
      where: {
        deleted: false,
        companyId: parentCompanyId,
      },
      select: ['id', 'imoNumber'],
    });
    console.log('SYNC DOC HOLDER STARTS: ');
    const vesselDocHolderPreparedData = [];
    for (const vessel of listCurrentVessel) {
      const rightShipRawsFilterIMO = rightShipRaws.filter(
        (item) => item.imo.toString() == vessel.imoNumber.toString(),
      );
      if (rightShipRawsFilterIMO.length > 0) {
        for (const rightShipRaw of rightShipRawsFilterIMO) {
          if (vessel.imoNumber.toString() == rightShipRaw.imo) {
            const companyIMO = rightShipRaw.docCompanyOwCode;
            const newDocHolder = await this.createDocHolder(
              vessel.id,
              companyIMO,
              parentCompanyId,
              managerTrans,
            );
            if (newDocHolder && newDocHolder.length === 1) {
              vesselDocHolderPreparedData.push(newDocHolder[0]);
            } else if (newDocHolder && newDocHolder.length > 1) {
              for (const i of newDocHolder) {
                vesselDocHolderPreparedData.push(i);
              }
            }
            // check the vessel having different docHolder
            console.log('companyIMOtoSearch: ', companyIMO);
            const companyDetails = await getRepository(Company)
              .createQueryBuilder('company')
              .select(['company.id', 'company.name'])
              .where('company.companyIMO = :companyIMO AND company.parentId = :parentCompanyId', {
                companyIMO: companyIMO,
                parentCompanyId: parentCompanyId,
              })
              .getOne();
            console.log('companyDetails: ', companyDetails);
            if (companyDetails) {
              const differentDocHolder = await getRepository(VesselDocHolder)
                .createQueryBuilder('vesselDocHolders')
                .select([
                  'vesselDocHolders.id',
                  'vesselDocHolders.fromDate',
                  'vesselDocHolders.toDate',
                ])
                .where(
                  'vesselDocHolders.vesselId = :vesselId AND vesselDocHolders.companyId != :companyId',
                  { vesselId: vessel.id, companyId: companyDetails.id },
                )
                .getMany();
              if (differentDocHolder.length) {
                const checkActiveDiffDocHolder = differentDocHolder.filter(
                  (obj) => obj.toDate === null,
                );
                if (checkActiveDiffDocHolder.length) {
                  for (let i = 0; i < checkActiveDiffDocHolder.length; i++) {
                    if (checkActiveDiffDocHolder[i].toDate == null) {
                      const updateVesselDoHolderStatusAndDate = {
                        id: checkActiveDiffDocHolder[i].id,
                        status: CommonStatus.IN_ACTIVE,
                        toDate: new Date(moment().unix() * 1000 - i),
                      };
                      managerTrans.save(VesselDocHolder, updateVesselDoHolderStatusAndDate);
                    } else {
                      continue;
                    }
                  }
                } else {
                  continue;
                }
              } else {
                continue;
              }
            } else {
              continue;
            }
          }
        }
      }
    }
    // console.log(vesselDocHolderPreparedData);
    await managerTrans.save(VesselDocHolder, vesselDocHolderPreparedData);
    console.log('SYNC DOC HOLDER SAVED...');
    return vesselDocHolderPreparedData;
  }
  async _vesselMasterDocIdUpdate(
    managerTrans: EntityManager,
    companyId: string,
    rightShipRaws: any,
  ) {
    LoggerCommon.log('Updating the vessel doc holder id in vessel master - starts...');
    const listCurrentVessel: any = await managerTrans.getCustomRepository(VesselRepository).find({
      where: {
        deleted: false,
        companyId: companyId,
      },
    });
    const preparedData = [];
    for (const vessel of listCurrentVessel) {
      const rightShipRawsFilterIMO = rightShipRaws.filter(
        (item) => item.imo.toString() == vessel.imoNumber.toString(),
      );
      if (rightShipRawsFilterIMO.length > 0) {
        for (const rightShipRaw of rightShipRawsFilterIMO) {
          if (vessel.imoNumber.toString() == rightShipRaw.imo) {
            // getting latest docHolder from vesselDocHolder Repository
            const docHolderDetails = await getCustomRepository(VesselDocHolderRepository).findOne({
              select: ['id', 'companyId', 'vesselId'],
              where: { vesselId: vessel.id },
            });
            //  getting vessel data
            const vesselMasterDocHolderId = await getRepository(Vessel)
              .createQueryBuilder('vessel')
              .select(['vessel.docHolderId', 'vessel.id'])
              .where('vessel.id = :vesselId', { vesselId: vessel.id })
              .getOne();
            if (
              vesselMasterDocHolderId?.id === null ||
              vesselMasterDocHolderId?.id !== docHolderDetails?.companyId
            ) {
              const updatedVesselDocId = docHolderDetails?.companyId;
              const vesselParamsUpdate = {
                id: vessel.id,
                docHolderId: updatedVesselDocId,
              };
              preparedData.push(vesselParamsUpdate);
              await managerTrans.save(Vessel, vesselParamsUpdate as Vessel);
            }
          }
        }
      }
    }
    LoggerCommon.log('Updating the vessel doc holder id in vessel master - ends...');
    // LoggerCommon.log(``)
    return 1;
  }
  async createCompanyFromDocHolder(rawRightShipData: any, parentCompanyId: string) {
    const companyName = rawRightShipData.docCompanyName;
    // get createdUser from company table (main user)
    const createdUserDetails = await getRepository(Company) // getting createdUser email from company table
      .find({
        select: ['email', 'code', 'id', 'countryId'],
        where: {
          id: parentCompanyId,
        },
      });
    const createdUserEmail = createdUserDetails[0].email;
    const createdUserId = await getRepository(User).find({
      select: ['id'],
      where: {
        email: createdUserDetails[0].email,
      },
    });
    const companyType: any = await getRepository(CompanyType).find({
      select: ['actors', 'id'],
      where: {
        companyType: 'Ship management (DOC holder)',
        deleted: false,
      },
    });

    const allowedSubscriptions = [];
    if (
      companyType[0]?.actors.includes(ActorsEnum.CONSUMER) ||
      companyType[0]?.actors.includes(ActorsEnum.PROVIDER)
    ) {
      allowedSubscriptions.push(AllowedSubscriptionsEnum.INSPECTION);
    }
    if (
      companyType[0]?.actors.includes(ActorsEnum.MAIN) ||
      companyType[0]?.actors.includes(ActorsEnum.EXTERNAL)
    ) {
      allowedSubscriptions.push(AllowedSubscriptionsEnum.QUALITY_ASSURANCE);
    }

    // Split the full name into an array of words
    const nameArray = companyName.split(' ');

    // The first word is the first name
    const firstName = nameArray[0];

    // The remaining words form the last name
    const lastNameArray = nameArray.slice(1);
    const lastName = lastNameArray.join(' ');
    // const status = 'active';
    const companyIMO = rawRightShipData.docCompanyOwCode;
    let code = companyName.slice(0, 3) + '_' + companyIMO;
    code = code.toUpperCase();
    const newCompanyData = {
      code: code,
      name: companyName,
      status: 'active',
      email: createdUserEmail,
      companyIMO: companyIMO,
      createdBy: createdUserId[0].id,
      parentId: parentCompanyId,
      companyTypeIds: [companyType[0].id],
      companyId: createdUserDetails[0].id,
      abbreviation: code,
      companyLevel: 'External Company',
      isInspection: allowedSubscriptions?.includes(AllowedSubscriptionsEnum.INSPECTION),
      isQA: allowedSubscriptions?.includes(AllowedSubscriptionsEnum.QUALITY_ASSURANCE),
      subscriptionAs: [
        'Service provider inspection',
        'Service consumer inspection',
        'External - QA',
      ],
      firstName,
      lastName,
      roleScope: RoleScope.ADMIN,
      countryId: createdUserDetails[0].countryId,
    };
    await this.manager
      .getCustomRepository(CompanyRepository)
      .docHoldercreateCompany(newCompanyData);
    return newCompanyData;
  }

  async createDocHolder(
    vesselId: string,
    companyIMO: string,
    parentCompanyId: string,
    managerTrans: EntityManager,
  ) {
    const companyDetails = await getRepository(Company)
      .createQueryBuilder('company')
      .select(['company.id', 'company.name'])
      .where('company.companyIMO = :companyIMO AND company.parentId = :parentCompanyId', {
        companyIMO: companyIMO,
        parentCompanyId: parentCompanyId,
      })
      .getOne();
    if (companyDetails) {
      const companyId = companyDetails.id;
      // check docHolders based on vessel and incoming companyId
      const checkActiveDocOnVesselAndIncomingCompnayId = await getRepository(VesselDocHolder)
        .createQueryBuilder('vesselDocHolders')
        .select(['vesselDocHolders.id'])
        .where(
          'vesselDocHolders.companyId = :companyId AND vesselDocHolders.vesselId = :vesselId AND vesselDocHolders.toDate is null',
          { companyId: companyId, vesselId: vesselId },
        )
        .getOne();
      if (!checkActiveDocOnVesselAndIncomingCompnayId) {
        const docHolderData = {
          companyId: companyId,
          vesselId: vesselId,
          fromDate: new Date('2020-01-01').toISOString(),
          status: CommonStatus.ACTIVE,
        };
        return [docHolderData];
      }
    }
  }

  async _syncRightShipImprove(
    managerTrans: EntityManager,
    listVessel: any,
    companyId: string,
    rightShipRaws: any,
  ) {
    let noOfRightShipInserted = 0;
    for (const vessel of listVessel) {
      const rightShipRawsFilterIMO = rightShipRaws.filter(
        (item) => item.imo.toString() === vessel.imoNumber.toString(),
      );
      if (rightShipRawsFilterIMO.length > 0) {
        const preparedData: RightShip[] = [];
        const preparedRestrictions: RightShipRestrictions[] = [];

        for (const rightShipRaw of rightShipRawsFilterIMO) {
          const ghgAdditionalData = this._normalizeghgData(rightShipRaw.ghg_additionalData);
          const safetyScoreData = this._normalizeSafetyScoreData(
            rightShipRaw.safetyScore_additionalData,
          );

          if (vessel.imoNumber.toString() === rightShipRaw.imo) {
            const newRightShip: RightShip = {
              vesselId: vessel.id,
              buildDate: rightShipRaw.dateOfBuild ? new Date(rightShipRaw.dateOfBuild) : null,
              docHolderName: rightShipRaw.docCompanyName,
              docHolderCode: rightShipRaw.docCompanyOwCode,
              ghgRatingDate: rightShipRaw.ghg_ratingDate
                ? new Date(rightShipRaw.ghg_ratingDate)
                : null,
              ghgRating: rightShipRaw.ghg_rating,
              evdi: ghgAdditionalData.evdi,
              verified: ghgAdditionalData.verified,
              plus: ghgAdditionalData.plus,
              safetyScore: rightShipRaw.safetyScore,
              safetyScoreDate: rightShipRaw.safetyScore_date
                ? new Date(rightShipRaw.safetyScore_date)
                : null,
              indicativeScore: safetyScoreData.indicativeScore,
              inspectionRequired: rightShipRaw.Inspection_required,
              docSafetyScore: safetyScoreData.docSafetyScore,
              latInspectionOutcome: rightShipRaw.lastInspectionOutcome,
              lastInspectionValidity: rightShipRaw.lastInspectionValidity
                ? new Date(rightShipRaw.lastInspectionValidity)
                : null,
              inspectionAdditionalData: rightShipRaw.Inspection_additionalData
                ? rightShipRaw.Inspection_additionalData
                : null,
              technicalManagerName: rightShipRaw.technicalManagerName,
              technicalManagerOwCode: rightShipRaw.technicalManagerOwCode,
              platformLink: rightShipRaw.platformLink,
              companyId,
              restrictedByRightShip: rightShipRaw.restrictedByRightShip,
              restrictions: rightShipRaw.restrictions ? [rightShipRaw.restrictions] : null,
            } as RightShip;

            preparedData.push(newRightShip);
          }
          if (rightShipRaw?.restrictions) {
            JSON.parse(rightShipRaw.restrictions.replace(/'/g, `"`)).map((data) => {
              if (data.listType === 'Vessel') {
                const newRestriction: RightShipRestrictions = {
                  rightShipId: '', // This will be filled in later after inserting RightShip
                  vesselId: vessel.id,
                  description: data.listName,
                  restrictionType: data.listType,
                  name: data.name,
                  code: data.imo,
                  comment: data.comment,
                  effectiveFrom: data.effectiveFrom,
                  effectiveTo: data.effectiveTo,
                } as RightShipRestrictions;
                preparedRestrictions.push(newRestriction);
              } else if (data.listType === 'Company') {
                if (rightShipRaw.docCompanyOwCode === data.owCode) {
                  const newRestriction: RightShipRestrictions = {
                    rightShipId: '', // This will be filled in later after inserting RightShip
                    vesselId: vessel.id,
                    description: data.listName,
                    restrictionType: data.listType,
                    name: data.name,
                    code: data.owCode,
                    comment: data.comment,
                    effectiveFrom: data.effectiveFrom,
                    effectiveTo: data.effectiveTo,
                  } as RightShipRestrictions;
                  preparedRestrictions.push(newRestriction);
                }
              }
            });
          }
        }

        if (preparedData.length > 0) {
          const savedRightShips = await managerTrans.save(RightShip, preparedData);
          console.log('has right ship: ', savedRightShips.length);

          if (preparedRestrictions.length > 0) {
            savedRightShips.forEach((savedShip, index) => {
              preparedRestrictions[index].rightShipId = savedShip.id; // Set the RightShip ID
            });
            await managerTrans.save(RightShipRestrictions, preparedRestrictions);
          }
        }

        noOfRightShipInserted += preparedData.length;
      }
    }

    // Other sync operations...

    return noOfRightShipInserted;
  }

  async _syncVoyage(
    managerTrans: EntityManager,
    listVessel: any,
    companyId: string,
    voyageRaws: any,
  ) {
    for (const vessel of listVessel) {
      const voyageRawsFilterIMO = voyageRaws.filter(
        (item) => item.imo.toString() == vessel.imoNumber.toString(),
      );
      const preparedData = [];
      if (voyageRawsFilterIMO.length > 0) {
        for (const voyageRaw of voyageRawsFilterIMO) {
          preparedData.push({
            vesselId: vessel.id,
            companyId: companyId,
            voyageStatus: voyageRaw.voyage_status,
            voyageNo: voyageRaw.voyage_no,
            opsCoordinator: voyageRaw.ops_coordinator,
            loadDate: voyageRaw.load_date ? new Date(voyageRaw.load_date) : null,
            consecutive: voyageRaw.consecutive_yn,
            firstTCI: voyageRaw.first_tci,
            lastTCI: voyageRaw.last_tci,
            totalLoadCargoVol: voyageRaw.total_load_cargo_vol,
            tradeAreaNo: voyageRaw.trade_area_no,
            firstLoadPortNo: voyageRaw.first_load_portno,
            firstLoadPort: voyageRaw.first_load_port,
            lastDischargePortNo: voyageRaw.last_discharge_portno,
            lastDischargePort: voyageRaw.last_discharge_port,
            fixtureNo: voyageRaw.fixture_no,
            estimateId: voyageRaw.estimate_id,
            cargoGradesList: voyageRaw.cargo_grades_list,
            cargoCounterpartyShortnames: voyageRaw.cargo_counterparty_shortnames,
            imosUrl: voyageRaw.imos_url,
            lastUpdateGMT: voyageRaw.last_update_gmt ? new Date(voyageRaw.last_update_gmt) : null,
            completeDateGMT: voyageRaw.complete_date_gmt
              ? new Date(voyageRaw.complete_date_gmt)
              : null,
            commenceDateGMT: voyageRaw.commence_date_gmt
              ? new Date(voyageRaw.commence_date_gmt)
              : null,
            oprType: voyageRaw.opr_type,
            commenceDateLocal: voyageRaw.commence_date_local
              ? new Date(voyageRaw.commence_date_local)
              : null,
            completeDateLocal: voyageRaw.complete_date_local
              ? new Date(voyageRaw.complete_date_local)
              : null,
            cargoNo: voyageRaw.cargo_list,
            cargoName: voyageRaw.cargo_short_names_list,
          } as Voyage);
          if (preparedData.length > 0) {
            await managerTrans.save(Voyage, preparedData);
          }
          console.log('has voyage: ', preparedData.length);
        }
      }
    }
  }

  async _syncVoyageMasterDetails(
    managerTrans: EntityManager,
    listVessel: any,
    companyId: string,
    voyageMasterDetailsRaws: any,
  ) {
    let noOfVoyageMastrerInserted = 0;
    for (const vessel of listVessel) {
      const voyageMasterDetailsRawsFilterIMO = voyageMasterDetailsRaws.filter(
        (item) => item.IMONo.toString() == vessel.imoNumber.toString(),
      );
      const preparedData = [];
      if (voyageMasterDetailsRawsFilterIMO.length > 0) {
        for (const voyageMasterDetailsRaw of voyageMasterDetailsRawsFilterIMO) {
          //  getting all port data from portMaster
          const getPortMasterDetails = await getRepository(PortMaster)
            .createQueryBuilder('portMaster')
            .select()
            .getMany();
          // getting unknown port data from portMaster
          const unknownPortNameId = getPortMasterDetails.find((obj) => obj.code === 'Unk').id;
          // generalizing incoming portname
          const incomingPortCode = voyageMasterDetailsRaw.PortCode;
          const generalizedIncomingPortCode = incomingPortCode
            ? incomingPortCode.trim().replace('/s+/g', ' ').toLocaleLowerCase()
            : incomingPortCode;
          // generalizing incoming first load portname
          const incomingFirstLoadPortCode = voyageMasterDetailsRaw.FirstLoadPortCode;
          const generalizedFirstLoadPortCode = incomingFirstLoadPortCode
            ? incomingFirstLoadPortCode.trim().replace('/s+/g', ' ').toLocaleLowerCase()
            : incomingFirstLoadPortCode;
          // generalizing incoming last discharge portname
          const incomingLastDischargePortCode = voyageMasterDetailsRaw.LastDischargePortCode;
          const generalizedLastDischargePortCode = incomingLastDischargePortCode
            ? incomingLastDischargePortCode.trim().replace('/s+/g', ' ').toLocaleLowerCase()
            : incomingLastDischargePortCode;

          const filterByString = (array, searchString) => {
            return array.find(
              (obj) => obj.code.trim().replace('/s+/g', ' ').toLocaleLowerCase() === searchString,
            );
          };
          let portNameId = undefined;
          let firstLoadPortNameId = undefined;
          let lastDischargePortNameId = undefined;
          const findPortName = filterByString(getPortMasterDetails, generalizedIncomingPortCode);
          const findFirstLoadPortName = filterByString(
            getPortMasterDetails,
            generalizedFirstLoadPortCode,
          );
          const findLastDischargePortName = filterByString(
            getPortMasterDetails,
            generalizedLastDischargePortCode,
          );
          if (findPortName) {
            portNameId = findPortName.id;
          } else {
            portNameId = unknownPortNameId;
          }
          if (findFirstLoadPortName) {
            firstLoadPortNameId = findFirstLoadPortName.id;
          } else {
            firstLoadPortNameId = unknownPortNameId;
          }
          if (findLastDischargePortName) {
            lastDischargePortNameId = findLastDischargePortName.id;
          } else {
            lastDischargePortNameId = unknownPortNameId;
          }

          // check voyage status is present in voyage status master
          let voyageStatusFound = undefined;
          let voyageStatusId = undefined;
          voyageMasterDetailsRaw.VoyageStatus = voyageMasterDetailsRaw.VoyageStatus
            ? voyageMasterDetailsRaw.VoyageStatus
            : 'Unknown';
          voyageStatusFound = await managerTrans.getRepository(VoyageStatus).findOne({
            name: voyageMasterDetailsRaw.VoyageStatus,
          });
          let voyageStatusCreated = undefined;
          if (!voyageStatusFound) {
            voyageStatusCreated = await managerTrans.save(VoyageStatus, {
              companyId: companyId,
              name: voyageMasterDetailsRaw.VoyageStatus
                ? voyageMasterDetailsRaw.VoyageStatus
                : 'Unknown',
              code: voyageMasterDetailsRaw.VoyageStatus
                ? voyageMasterDetailsRaw.VoyageStatus.length > 5
                  ? voyageMasterDetailsRaw.VoyageStatus.substring(0, 3)
                  : voyageMasterDetailsRaw.VoyageStatus
                : 'Unk',
              status: StatusCommon.ACTIVE,
            } as VoyageStatus);
          } else {
            voyageStatusId = voyageStatusFound.id;
          }
          // check cargo is present in cargo master
          const incomingCargoList = voyageMasterDetailsRaw.Cargo
            ? voyageMasterDetailsRaw.Cargo.split(',')
            : ['Unknown'];
          // check all incoming cargo list is in cargo master.
          const getCargoMasterData = await managerTrans
            .getRepository(Cargo)
            .createQueryBuilder('cargo')
            .select()
            .where('cargo.name IN (:...incomingCargoList) AND cargo.companyId = :companyId', {
              incomingCargoList: incomingCargoList,
              companyId: companyId,
            })
            .getMany();
          if (getCargoMasterData.length) {
            // check the cargo which are not in master
            const cargoToCreate = incomingCargoList.filter((name) => {
              return !getCargoMasterData.some((obj) => obj.name === name);
            });
            const preparedParamsCargoPartial = [];
            for (let i = 0; i < cargoToCreate.length; i++) {
              preparedParamsCargoPartial.push({
                companyId: companyId,
                name: cargoToCreate[i],
                code: cargoToCreate[i].substring(0, 3),
                status: CommonStatus.ACTIVE,
              });
            }
            await managerTrans.save(Cargo, preparedParamsCargoPartial);
          } else {
            const preparedParamsCargoFull = [];
            for (let i = 0; i < incomingCargoList.length; i++) {
              preparedParamsCargoFull.push({
                companyId: companyId,
                name: incomingCargoList[i],
                code: incomingCargoList[i].substring(0, 3),
                status: CommonStatus.ACTIVE,
              });
            }
            await managerTrans.save(Cargo, preparedParamsCargoFull);
          }
          const getCargoMasterDetails = await managerTrans
            .getRepository(Cargo)
            .createQueryBuilder('cargo')
            .select()
            .where('cargo.name IN (:...incomingCargoList) AND cargo.companyId = :companyId', {
              incomingCargoList: incomingCargoList,
              companyId: companyId,
            })
            .getMany();
          const cargoIds = getCargoMasterDetails.map((res) => res.id);

          // check for observed risk for a vessel
          const vesselScreeningRepository = this.manager.connection.getRepository(VesselScreening);
          const vesselScreeningData = await vesselScreeningRepository
            .createQueryBuilder('vesselScreening')
            .select(['vesselScreening.id', 'vesselScreening.updatedAt'])
            .where(
              'vesselScreening.vesselId = :vesselId AND vesselScreening.status in (:...status)',
              {
                vesselId: vessel.id,
                status: [VesselScreeningStatusEnum.CLEARED, VesselScreeningStatusEnum.DISAPPROVED],
              },
            )
            .orderBy({ 'vesselScreening.updatedAt': 'DESC' })
            .limit(1)
            .getOne();
          let flag = false; // to check all tab is null or contain some data
          let riskRating = undefined;
          if (vesselScreeningData) {
            const rawQuery = `SELECT
                        sum(vss."observedScore") as "sumObservedScore",
                        count(*) as "count"
                      FROM
                        vessel_screening_summary vss
                      WHERE
                        "vesselScreeningId" = $1 AND
                        ("reviewStatus" = 'Reject' or "reviewStatus" = 'Accept')
                      GROUP BY "tabName" ;`;
            const data = await this.connection.query(rawQuery, [vesselScreeningData.id]);
            let totalRiskRating = 0;
            for (let i = 0; i < data.length; i++) {
              let averageRiskByTab = 0;
              if (data[i].sumObservedScore !== null) {
                flag = true;
                averageRiskByTab = Number(data[i].sumObservedScore) / Number(data[i].count);
              }
              if (averageRiskByTab) {
                totalRiskRating += Number(averageRiskByTab);
              }
            }
            riskRating = totalRiskRating / data.length;

            // const riskRating = flag ? (totalRiskRating / data.length).toFixed(2) : null;
            if (flag && !Number.isInteger(riskRating)) {
              riskRating = Number((totalRiskRating / data.length).toFixed(2));
            }
            if (!flag) {
              riskRating = null;
            }
          }

          preparedData.push({
            vesselId: vessel.id,
            companyId: companyId,
            portMasterId: portNameId,
            voyageNo: voyageMasterDetailsRaw.VoyageNo,
            voyageStatusId: voyageStatusId ? voyageStatusId : voyageStatusCreated.id,
            purposeOfCall: voyageMasterDetailsRaw.PurposeOfCall,
            ETA_LT: voyageMasterDetailsRaw.ETA_LT,
            ETD_LT: voyageMasterDetailsRaw.ETD_LT,
            ETA_GMT: voyageMasterDetailsRaw.ETA_GMT,
            ETD_GMT: voyageMasterDetailsRaw.ETD_GMT,
            arrival_LT: voyageMasterDetailsRaw.Arrival_LT,
            departure_LT: voyageMasterDetailsRaw.Depature_LT,
            remarks: voyageMasterDetailsRaw.Remarks,
            firstLoadPort: firstLoadPortNameId,
            lastDischargePort: lastDischargePortNameId,
            fixtureNo: voyageMasterDetailsRaw.FixtureNO,
            completeDateAndTime_GMT: voyageMasterDetailsRaw.CompleteDateAndTime_GMT,
            commencedDateAndTime_GMT: voyageMasterDetailsRaw.CommencedDateAndTime_GMT,
            lastScreeningDate: vesselScreeningData ? vesselScreeningData.updatedAt : null,
            observedRiskLastScreening: riskRating ? riskRating : null,
            dischargeSeqNo: voyageMasterDetailsRaw.DischargeSeqNo,
            loadSeqNo: voyageMasterDetailsRaw.LoadSeqNo,
            cargos: cargoIds.map((cargoId) => ({ id: cargoId } as Cargo)),
          } as VoyageMasterDetails);

          if (preparedData.length > 0) {
            await managerTrans.save(VoyageMasterDetails, preparedData);
            noOfVoyageMastrerInserted = noOfVoyageMastrerInserted + 1;
          }
          console.log('has voyageMasterDetails: ', preparedData.length);
        }
      }
    }
    return noOfVoyageMastrerInserted;
  }

  async _syncRightShip(
    managerTrans: EntityManager,
    vesselId: string,
    vesselImo: string,
    companyId: string,
  ) {
    const preparedData: RightShip[] = [];
    for (let i = 0; i < RIGHT_SHIP_JSON['rightShipData'].length; i++) {
      // console.log(i);
      const rightShipRaw = RIGHT_SHIP_JSON['rightShipData'][i];
      const ghgAdditionalData = this._normalizeghgData(rightShipRaw.ghg_additionalData);
      const safetyScoreData = this._normalizeSafetyScoreData(
        rightShipRaw.safetyScore_additionalData,
      );
      if (vesselImo.toString() == rightShipRaw.imo) {
        preparedData.push({
          vesselId: vesselId,
          buildDate: rightShipRaw.dateOfBuild ? new Date(rightShipRaw.dateOfBuild) : null,
          docHolderName: rightShipRaw.docCompanyName,
          docHolderCode: rightShipRaw.docCompanyOwCode,
          ghgRatingDate: rightShipRaw.ghg_ratingDate ? new Date(rightShipRaw.ghg_ratingDate) : null,
          ghgRating: rightShipRaw.ghg_rating,
          evdi: ghgAdditionalData.evdi,
          verified: ghgAdditionalData.verified,
          plus: ghgAdditionalData.plus,
          safetyScore: rightShipRaw.safetyScore,
          safetyScoreDate: rightShipRaw.safetyScore_date
            ? new Date(rightShipRaw.safetyScore_date)
            : null,
          indicativeScore: safetyScoreData.indicativeScore,
          inspectionRequired: rightShipRaw.Inspection_required,
          docSafetyScore: safetyScoreData.docSafetyScore,
          latInspectionOutcome: rightShipRaw.lastInspectionOutcome,
          lastInspectionValidity: rightShipRaw.lastInspectionValidity
            ? new Date(rightShipRaw.lastInspectionValidity)
            : null,
          inspectionAdditionalData: rightShipRaw.Inspection_additionalData
            ? rightShipRaw.Inspection_additionalData
            : null,
          technicalManagerName: rightShipRaw.technicalManagerName,
          technicalManagerOwCode: rightShipRaw.technicalManagerOwCode,
          platformLink: rightShipRaw.platformLink,
          companyId,
        } as RightShip);
      }
    }
    console.log('has right ship: ', preparedData.length);
    // sync Right ship
    if (preparedData.length > 0) {
      await managerTrans.save(RightShip, preparedData);
    }
    return 1;
  }
  private _normalizeghgData(rawghgAdditional) {
    const resObj = {
      evdi: null,
      verified: null,
      plus: null,
    };
    const preparedghg = rawghgAdditional.substring(1, rawghgAdditional.length - 1);
    // console.log('preparedghg: ', preparedghg);
    const splitted = preparedghg.split('}, {');
    for (let i = 0; i < splitted.length; i++) {
      let substr = splitted[i];
      if (substr[0] !== '{') {
        substr = `{${substr}`;
      }
      if (substr[substr.length - 1] !== '}') {
        substr = `${substr}}`;
      }
      substr = substr.replace(/{'/g, '{"');
      substr = substr.replace(/': '/g, '": "');
      substr = substr.replace(/', '/g, '", "');
      substr = substr.replace(/': /g, '": "');
      substr = substr.replace(/}/g, '"}');
      // console.log('substr: ', substr);
      const obj = JSON.parse(substr);
      // console.log('obj: ', obj);
      if (obj.key == 'evdi') {
        resObj.evdi = obj.value;
      }
      if (obj.key == 'verified') {
        if (obj.value.includes('alse')) {
          resObj.verified = false;
        } else {
          resObj.verified = true;
        }
      }
      if (obj.key == 'plus') {
        resObj.plus = obj.value;
      }
    }
    // console.log('resObj: ', resObj);
    return resObj;
  }

  private _normalizeSafetyScoreData(rawSafetyScoreData) {
    const resObj = {
      indicativeScore: false,
      docSafetyScore: null,
    };
    const preparedSafetyScore = rawSafetyScoreData.substring(1, rawSafetyScoreData.length - 1);
    // console.log('preparedSafetyScore: ', preparedSafetyScore);
    const splitted = preparedSafetyScore.split('}, {');
    // console.log('splitted: ', splitted);

    for (let i = 0; i < splitted.length; i++) {
      let substr = splitted[i];
      if (substr[0] !== '{') {
        substr = `{${substr}`;
      }
      if (substr[substr.length - 1] !== '}') {
        substr = `${substr}}`;
      }
      substr = substr.replace(/{'/g, '{"');
      substr = substr.replace(/': '/g, '": "');
      substr = substr.replace(/', '/g, '", "');
      substr = substr.replace(/': /g, '": "');
      substr = substr.replace(/}/g, '"}');
      // console.log('substr: ', substr);
      const obj = JSON.parse(substr);
      // console.log('obj: ', obj);
      if (obj.key == 'indicativeScore') {
        if (obj.value.includes('alse')) {
          resObj.indicativeScore = false;
        } else {
          resObj.indicativeScore = true;
        }
      }
      if (obj.key == 'docSafetyScore') {
        // console.log(obj.value);

        resObj.docSafetyScore = obj.value;
      }
    }
    // console.log('resObj: ', resObj);
    return resObj;
  }

  async correctSyncRightShip(companyId: string) {
    return await this.connection.transaction(async (manager) => {
      for (let i = 0; i < RIGHT_SHIP_JSON['rightShipData'].length; i++) {
        const rightShipRaw = RIGHT_SHIP_JSON['rightShipData'][i];
        const ghgAdditionalData = this._normalizeghgData(rightShipRaw.ghg_additionalData);
        const safetyScoreData = this._normalizeSafetyScoreData(
          rightShipRaw.safetyScore_additionalData,
        );
        const rightShipFoundByLink = await manager.findOne(RightShip, {
          platformLink: rightShipRaw.platformLink,
          // companyId,
        });
        if (rightShipFoundByLink) {
          const preparedCorrectData = {
            indicativeScore: safetyScoreData.indicativeScore,
            docSafetyScore: safetyScoreData.docSafetyScore,
            buildDate: rightShipRaw.dateOfBuild,
            docHolderName: rightShipRaw.docCompanyName,
            docHolderCode: rightShipRaw.docCompanyOwCode,
            safetyScore: rightShipRaw.safetyScore,
            companyId,
          };
          await manager.save(RightShip, Object.assign(rightShipFoundByLink, preparedCorrectData));
        }
        // return rightShipFoundByLink;
      }
      return 1;
    });
  }

  async syncRestrictions(companyId: string, rightShipDatas: any[]) {
    try {
      await this.connection.transaction(async (manager) => {
        LoggerCommon.log('Sync the restrictions - starts...');

        const vesselFoundByImo = await this.manager.find(Vessel, {
          deleted: false,
          companyId: companyId,
        });

        for (const vesselRaw of vesselFoundByImo) {
          const rightShipRawsFilterIMO = rightShipDatas.filter((item) => {
            return item.imo.toString() === vesselRaw.imoNumber.toString();
          });

          for (const restrictionRaw of rightShipRawsFilterIMO) {
            const restrictions = JSON.parse(restrictionRaw?.restrictions.replace(/'/g, `"`));

            if (
              restrictions.find(
                (data) => data?.listType === 'Vessel' && data?.imo === vesselRaw.imoNumber,
              )
            ) {
              await manager.update(
                Vessel,
                { id: vesselRaw.id },
                { isVesselRestricted: true, customerRestricted: true },
              );
            } else if (
              restrictions.find(
                (data) =>
                  data?.listType === 'Company' && data?.owCode === restrictionRaw.docCompanyOwCode,
              )
            ) {
              const companyFoundByImo = await manager
                .getCustomRepository(CompanyRepository)
                .findOne({ companyIMO: restrictionRaw.docCompanyOwCode });

              if (companyFoundByImo) {
                await manager.update(
                  Company,
                  { id: companyFoundByImo.id },
                  { isCompanyRestricted: true },
                );

                await manager.update(
                  Vessel,
                  { docHolderId: companyFoundByImo.id },
                  {
                    isVesselRestricted: true,
                    isCompanyRestricted: true,
                    customerRestricted: true,
                  },
                );
              }
            }
          }
        }
        LoggerCommon.log('Sync the restrictions - ends...');
      });
    } catch (ex) {
      LoggerCommon.error('Sync the restrictions job error', ex.message || ex);
    }
  }

  async syncRightShipIncidents(companyId: string, rightShipIncidentsDatas: any[]) {
    try {
      return await this.connection.transaction(async (manager) => {
        LoggerCommon.log('Sync RightShip incidents - starts...');

        if (!rightShipIncidentsDatas || rightShipIncidentsDatas.length === 0) {
          LoggerCommon.log('No RightShip incidents data to process');
          return {
            noOfIncidentsInserted: 0,
            noOfIncidentsUpdated: 0,
            noOfIncidentMasterCreated: 0,
          };
        }

        // Find company admin user ID
        let companyAdminUserId = null;
        const companyAdmin = await manager.findOne(User, {
          where: {
            companyId: companyId,
            roleScope: RoleScopeEnum.ADMIN,
            deleted: false,
          },
          select: ['id'],
        });

        if (companyAdmin) {
          companyAdminUserId = companyAdmin.id;
        }

        let insertedCount = 0;
        let updatedCount = 0;
        let incidentMasterCreatedCount = 0;

        for (const incidentRaw of rightShipIncidentsDatas) {
          try {
            // Find vessel by IMO
            const vessel = await manager.findOne(Vessel, {
              where: { imoNumber: incidentRaw.imo, companyId: companyId, deleted: false },
            });

            if (!vessel) {
              LoggerCommon.log(
                `Vessel with IMO ${incidentRaw.imo} not found, skipping incident ${incidentRaw.incident_id}`,
              );
              continue;
            }

            // Find RightShip record for this vessel
            const rightShip = await manager.findOne(RightShip, {
              where: { vesselId: vessel.id, companyId: companyId, deleted: false },
            });

            // Handle incident_master mapping for incident_type
            let incidentMasterId = null;
            if (incidentRaw.incident_type) {
              // Check if incident_type exists in incident_master
              let incidentMaster = await manager.findOne(IncidentMaster, {
                where: {
                  name: incidentRaw.incident_type,
                  companyId: companyId,
                  deleted: false,
                },
              });

              if (!incidentMaster) {
                // Create new incident_master entry
                const newIncidentMaster = await manager.save(IncidentMaster, {
                  code: incidentRaw.incident_type.toUpperCase().replace(/[^A-Z0-9]/g, '_'),
                  name: incidentRaw.incident_type,
                  companyId: companyId,
                  status: StatusCommon.ACTIVE,
                  description: `Auto-created from RightShip incident type: ${incidentRaw.incident_type}`,
                  createdUserId: companyAdminUserId,
                  updatedUserId: companyAdminUserId,
                });
                incidentMaster = newIncidentMaster;
                incidentMasterCreatedCount++;
                LoggerCommon.log(
                  `Created new incident_master for type: ${incidentRaw.incident_type}`,
                );
              }
              incidentMasterId = incidentMaster.id;
            }

            // Check if incident already exists
            const existingIncident = await manager.findOne(RightShipIncidents, {
              where: { incidentId: incidentRaw.incident_id, companyId: companyId, deleted: false },
            });

            const incidentData = {
              incidentId: incidentRaw.incident_id,
              incidentDate: new Date(incidentRaw.incident_date),
              imo: incidentRaw.imo,
              status: incidentRaw.status || null,
              detailedStatus: incidentRaw.detailed_status || null,
              incidentSource: incidentRaw.incident_source || null,
              flagAtTimeOfIncident: incidentRaw.flag_at_time_of_incident || null,
              docAtTimeOfIncident: incidentRaw.doc_at_time_of_incident || null,
              severity: incidentRaw.severity || null,
              activeInSafetyScore: incidentRaw.active_in_safety_score || false,
              incidentMasterId: incidentMasterId,
              assistanceGiven: incidentRaw.assistance_given || null,
              casualtyOrDemolition: incidentRaw.casualty_or_demolition || null,
              totalLoss: incidentRaw.total_loss || false,
              numberKilled: incidentRaw.number_killed || 0,
              numberMissing: incidentRaw.number_missing || 0,
              sustainedSignificantDamage: incidentRaw.sustained_significant_damage || false,
              pollutionOccured: incidentRaw.pollution_occured || false,
              pollutionDetails: incidentRaw.pollution_details || null,
              numberSeriousInjuries: incidentRaw.number_serious_injuries || 0,
              preciseText: incidentRaw.precise_text || null,
              complimentaryText: incidentRaw.complimentary_text || null,
              companyId: companyId,
              vesselId: vessel.id,
              rightShipId: rightShip?.id || null,
            };

            if (existingIncident) {
              // Update existing incident
              await manager.update(RightShipIncidents, { id: existingIncident.id }, incidentData);
              updatedCount++;
            } else {
              // Insert new incident
              await manager.save(RightShipIncidents, incidentData);
              insertedCount++;
            }
          } catch (incidentError) {
            LoggerCommon.error(
              `Error processing incident ${incidentRaw.incident_id}:`,
              incidentError.message,
            );
          }
        }

        LoggerCommon.log(
          `Sync RightShip incidents - ends. Inserted: ${insertedCount}, Updated: ${updatedCount}, Incident Masters Created: ${incidentMasterCreatedCount}`,
        );

        return {
          noOfIncidentsInserted: insertedCount,
          noOfIncidentsUpdated: updatedCount,
          noOfIncidentMasterCreated: incidentMasterCreatedCount,
        };
      });
    } catch (ex) {
      LoggerCommon.error('Sync RightShip incidents job error', ex.message || ex);
      throw ex;
    }
  }

  /**
   * Updates vessel and company restrictions based on RightShip data
   * Checks for expired restrictions and updates vessel/company status accordingly
   */
  async updateRestrictions() {
    try {
      // First update any expired restrictions to inactive status
      this.updateRestrictionsAsActiveAndInactive();

      // Fetch companies that are currently restricted and check if restrictions are still valid
      const companyData = await getRepository(Company)
        .createQueryBuilder('company')
        .where(`company.isCompanyRestricted = TRUE`)
        .getMany();
      companyData.forEach(async (data) => {
        const restrictions = await this.fetchRestrictionsData(data?.companyIMO);
        if (restrictions.length === 0) {
          // If no active restrictions found, remove company restriction
          await this.manager.update(Company, { id: data?.id }, { isCompanyRestricted: false });

          // Get all active vessels associated with this company
          const doc = await getRepository(VesselDocHolder)
            .createQueryBuilder('vesselDocHolder')
            .innerJoinAndSelect('vesselDocHolder.vessel', 'vessel')
            .where(
              `vesselDocHolder.companyId = :companyId AND vesselDocHolder.status = 'active' AND vesselDocHolder.toDate IS NULL`,
              {
                companyId: data.id,
              },
            )
            .getMany();
          doc.forEach(async (doc) => {
            // Check if vessel has its own restrictions before removing company-based restrictions
            const restrictionsVessel = await this.fetchRestrictionsData(doc?.vessel?.imoNumber);
            if (restrictionsVessel.length === 0)
              await this.manager.update(
                Vessel,
                { id: doc?.vesselId },
                {
                  customerRestricted: false,
                  isVesselRestricted: false,
                  isCompanyRestricted: false,
                },
              );
          });
        }
      });

      // Fetch vessels that are currently restricted and check if restrictions are still valid
      const vesselData = await getRepository(Vessel)
        .createQueryBuilder('vessel')
        .where(`vessel.isVesselRestricted = TRUE AND vessel.isCompanyRestricted = FALSE`)
        .getMany();
      vesselData.forEach(async (data) => {
        const restrictions = await this.fetchRestrictionsData(data?.imoNumber);
        if (restrictions.length === 0) {
          // If no active restrictions found, remove vessel restrictions
          await this.manager.update(
            Vessel,
            { id: data?.id },
            { customerRestricted: false, isVesselRestricted: false },
          );
        }
      });
    } catch (ex) {
      LoggerCommon.error('updateRestrictions job error', ex.message || ex);
    }
  }

  /**
   * Fetches active restrictions for a given IMO number that haven't expired
   * @param imo - The IMO number to check for restrictions
   * @returns Array of active restrictions
   */
  async fetchRestrictionsData(imo: string) {
    const currentTime = new Date();
    const restrictions = await getRepository(RightShipRestrictions)
      .createQueryBuilder('rightShipRestrictions')
      .where(
        `rightShipRestrictions.code = :code AND (rightShipRestrictions.effectiveTo >= :timeQuery OR rightShipRestrictions.effectiveTo = '') AND status = :status`,
        {
          code: imo,
          timeQuery: currentTime,
          status: StatusCommon.ACTIVE,
        },
      )
      .getMany();
    return restrictions;
  }

  /**
   * Updates expired restrictions to inactive status
   * Checks all restrictions and marks those past their effectiveTo date as inactive
   */
  async updateRestrictionsAsActiveAndInactive() {
    try {
      const currentEffectiveToTime = new Date();
      const currectEffectiveFromTime = new Date(new Date().getTime() + 5.5 * 60 * 60 * 1000);
      const currentDate = currectEffectiveFromTime.toISOString().split('T')[0];

      const restrictions = await getRepository(RightShipRestrictions)
        .createQueryBuilder('rightShipRestrictions')
        .where(
          `(rightShipRestrictions.effectiveTo <= :currentEffectiveToTime AND rightShipRestrictions.effectiveTo != '' AND rightShipRestrictions.status = :activeStatus) OR (DATE(rightShipRestrictions.effectiveFrom) = DATE(:currectEffectiveFromTime) AND rightShipRestrictions.status = :inactiveStatus)`,
          {
            currentEffectiveToTime,
            currectEffectiveFromTime,
            activeStatus: StatusCommon.ACTIVE,
            inactiveStatus: StatusCommon.INACTIVE,
          },
        )
        .getMany();

      if (restrictions.length === 0) {
        return;
      }

      const vesselUpdates = [];
      const companyUpdates = [];
      const restrictionUpdates = [];

      restrictions.forEach((restriction) => {
        const isCurrentDate =
          new Date(restriction.effectiveFrom).toISOString().split('T')[0] === currentDate;
        const newStatus = isCurrentDate ? StatusCommon.ACTIVE : StatusCommon.INACTIVE;

        if (newStatus === StatusCommon.ACTIVE) {
          if (restriction.restrictionType === 'Vessel') {
            vesselUpdates.push({
              imoNumber: restriction.code,
              updates: { isVesselRestricted: true, customerRestricted: true },
            });
          } else if (restriction.restrictionType === 'Company') {
            companyUpdates.push({
              companyIMO: restriction.code,
              updates: { isCompanyRestricted: true },
            });
          }
        }

        restrictionUpdates.push({
          id: restriction.id,
          status: newStatus,
        });
      });

      try {
        await Promise.all([
          ...vesselUpdates.map((update) =>
            this.manager.update('vessel', { imoNumber: update.imoNumber }, update.updates),
          ),
          ...companyUpdates.map((update) =>
            this.manager.update('company', { companyIMO: update.companyIMO }, update.updates),
          ),
          ...restrictionUpdates.map((update) =>
            this.manager.update(
              RightShipRestrictions,
              { id: update.id },
              { status: update.status },
            ),
          ),
        ]);
      } catch (updateError) {
        this._logError('Error updating restrictions', updateError);
        throw updateError;
      }
    } catch (ex) {
      this._logError('updateRestrictionsAsActiveAndInactive error', ex);
      throw ex;
    }
  }

  private _logData(message: string, data?: any) {
    if (data) {
      LoggerCommon.log(
        JSON.stringify({
          message,
          data,
        }),
      );
    } else {
      LoggerCommon.log(message);
    }
  }

  private _logError(message: string, error: any) {
    LoggerCommon.error(message, error.message || error);
  }

  private _validateRequiredParams(params: Record<string, any>, requiredFields: string[]) {
    const missingFields = requiredFields.filter((field) => !params[field]);
    if (missingFields.length > 0) {
      throw new Error(`Missing required parameters: ${missingFields.join(', ')}`);
    }
    return true;
  }

  /**
   * List RightShip incidents with pagination and filtering
   */
  async listRightShipIncidents(query: any, token: TokenPayloadModel) {
    try {
      const queryBuilder = getRepository(RightShipIncidents)
        .createQueryBuilder('incidents')
        .leftJoin('incidents.vessel', 'vessel')
        .leftJoin('incidents.incidentMaster', 'incidentMaster')
        .leftJoin('incidents.company', 'company')
        .select(['incidents', 'vessel', 'incidentMaster', 'company.id', 'company.name'])
        .where('incidents.companyId = :companyId', { companyId: token.companyId });

      // Apply filters
      if (query.vesselId) {
        queryBuilder.andWhere('incidents.vesselId = :vesselId', { vesselId: query.vesselId });
      }

      if (query.imo) {
        queryBuilder.andWhere('incidents.imo = :imo', { imo: query.imo });
      }

      if (query.incidentMasterId) {
        queryBuilder.andWhere('incidents.incidentMasterId = :incidentMasterId', {
          incidentMasterId: query.incidentMasterId,
        });
      }

      if (query.status) {
        queryBuilder.andWhere('incidents.status = :status', { status: query.status });
      }

      if (query.fromDate) {
        queryBuilder.andWhere('incidents.incidentDate >= :fromDate', { fromDate: query.fromDate });
      }

      if (query.toDate) {
        queryBuilder.andWhere('incidents.incidentDate <= :toDate', { toDate: query.toDate });
      }

      // Apply sorting
      if (query.sort) {
        const [field, direction] = query.sort.split(':');
        queryBuilder.orderBy(`incidents.${field}`, direction.toUpperCase() as 'ASC' | 'DESC');
      } else {
        queryBuilder.orderBy('incidents.incidentDate', 'DESC');
      }

      // Apply pagination
      const page = query.page || 1;
      const pageSize = query.pageSize || 10;
      queryBuilder.skip((page - 1) * pageSize).take(pageSize);

      const [items, total] = await queryBuilder.getManyAndCount();

      return {
        items,
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize),
      };
    } catch (error) {
      this._logError('[RightShipRepository] listRightShipIncidents error', error);
      throw error;
    }
  }

  /**
   * Get RightShip incident by ID
   */
  async getRightShipIncidentById(incidentId: string, token: TokenPayloadModel) {
    try {
      const incident = await getRepository(RightShipIncidents)
        .createQueryBuilder('incidents')
        .leftJoin('incidents.vessel', 'vessel')
        .leftJoin('incidents.incidentMaster', 'incidentMaster')
        .leftJoin('incidents.company', 'company')
        .select(['incidents', 'vessel', 'incidentMaster', 'company.id', 'company.name'])
        .where('incidents.id = :incidentId AND incidents.companyId = :companyId', {
          incidentId,
          companyId: token.companyId,
        })
        .getOne();

      if (!incident) {
        throw new BaseError({ status: 404, message: 'rightShipIncidents.NOT_FOUND' });
      }

      return incident;
    } catch (error) {
      this._logError('[RightShipRepository] getRightShipIncidentById error', error);
      throw error;
    }
  }
}
