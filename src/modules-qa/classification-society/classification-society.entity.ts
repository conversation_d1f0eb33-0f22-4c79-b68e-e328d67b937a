import { IdentifyEntity } from 'svm-nest-lib-v3';
import { Column, Entity, Index, OneToMany } from 'typeorm';
import { DBIndexes } from '../../commons/consts/db.const';
import { StatusCommon } from '../../commons/enums/index';
import { Vessel } from '../../modules/vessel/entity/vessel.entity';

@Entity()
@Index(DBIndexes.IDX_CLASSIFICATION_SOCIETY_CODE, ['code'], {
  unique: true,
  where: 'deleted = false',
})
@Index(DBIndexes.IDX_CLASSIFICATION_SOCIETY_NAME, ['name'], {
  unique: true,
  where: 'deleted = false',
})
export class ClassificationSociety extends IdentifyEntity {
  @Column({ type: 'citext' })
  public code: string;

  @Column({ type: 'citext' })
  public name: string;

  @Column({ type: 'enum', enum: StatusCommon, default: StatusCommon.ACTIVE })
  public status: string;

  @Column({ nullable: true })
  public description: string;

  @Column({ type: 'boolean', default: false })
  public isIACSMember: boolean;

  @OneToMany(() => Vessel, (vessel) => vessel.classificationSociety)
  vessels: Vessel[];
}
