import { ListQueryDto } from '../../../commons/dtos';
import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { SelfDeclarationCommentType, SelfDeclarationStatusEnum } from '../../../commons/enums';

export class ListSelfDeclarationDto extends ListQueryDto {
  @ApiProperty({ type: String, enum: SelfDeclarationStatusEnum })
  @IsOptional()
  @IsEnum(SelfDeclarationStatusEnum)
  selfDeclarationStatus?: string;

  @ApiProperty({ type: String })
  @IsOptional()
  auditWorkspaceId?: string;

  @ApiProperty({
    type: String,
    required: false,
    description:
      'Search in Element Code, Element Number, Element Group, Best Practice Guidance, Key Performance Indicator',
  })
  @IsOptional()
  @IsString()
  search?: string;
}

export class ListLoopBackCommentDto extends ListQueryDto {
  @ApiProperty({
    enum: SelfDeclarationCommentType,
    required: false,
    description: 'type of comment self declaration',
  })
  @IsOptional()
  @IsEnum(SelfDeclarationCommentType)
  type?: string;
}

export enum TitleSelfDeclaration {
  LEVEL = 'Level',
  EXPECTATIONS = 'Expectations',
  TARGETS = 'Targets',
  SUGGESTED_OBJECTIVE_EVIDENCE = 'Suggested objective evidence',
  OPERATOR_ASSESSMENT = `Operator's Assessment`,
  OPERATOR_COMMENTS = `Operator's Comments`,
}
