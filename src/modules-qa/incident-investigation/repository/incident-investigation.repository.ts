import moment from 'moment';
import * as _ from 'lodash';
import { CatalogConst } from 'src/modules-qa/catalog/catalog-key.const';
import { MetaConfig } from 'src/modules-qa/catalog/entity/meta-config.entity';
import { IncidentMasterRepository } from 'src/modules-qa/incident-master/incident-master.repository';
import { ListIncidentsMapViewQueryDTO } from 'src/modules/map-view/dto/list-incident-map-view.dto';
import { VesselRepository } from 'src/modules/vessel/vessel.repository';
import { generatePointString } from 'src/utils/geo-location';
import {
  BaseError,
  LoggerCommon,
  RoleScopeCheck,
  TokenPayloadModel,
  TypeORMRepository,
  Utils,
} from 'svm-nest-lib-v3';
import { Connection, EntityRepository, getConnection, In } from 'typeorm';
import {
  CompanyLevelEnum,
  EmailTypeEnum,
  IncidentInvestigationReviewStatus,
  IncidentOriginEnum,
  MailTemplate,
  ModulePathEnum,
  PushTypeEnum,
  SelfAssessmentMonthEnum,
  VesselScreeningFilterRiskEnum,
} from '../../../commons/enums';
import {
  _supportWhereDOCChartererOwner,
  _supportWhereDOCChartererOwnerRawQuery,
  isChangeRiskValue,
} from '../../../commons/functions';
import APP_CONFIG from '../../../configs/app.config';
import { IEmailEventModel, IUserEmail } from '../../../micro-services/async/email.producer';
import {
  INotificationEventModel,
  IUser,
} from '../../../micro-services/async/notification.producer';
import { CompanyFeatureVersionRepository } from '../../../modules/commons/company-feature-version/company-feature-version.repository';
import { FeatureVersionConfig } from '../../../modules/commons/company-feature-version/feature-version.config';
import { Company } from '../../../modules/company/company.entity';
import { CreateUserAssignmentDTO } from '../../../modules/user-assignment/dto';
import {
  ModuleType,
  WorkflowPermission,
} from '../../../modules/user-assignment/user-assignment.enum';
import { UserAssignmentRepository } from '../../../modules/user-assignment/user-assignment.repository';
import { UserRepository } from '../../../modules/user/user.repository';
import {
  convertFilterField,
  createSelectSql,
  createWhereSql,
  handleGetDataForAGGrid,
  isDoingGrouping,
  leadingZero,
  MySet,
  PayloadAGGridDto,
} from '../../../utils';
import { IncidentMaster } from '../../incident-master/incident-master.entity';
import {
  VesselScreeningSummary,
  VesselScreeningSummaryReferenceEnum,
} from '../../vessel-screening/entity/vessel-screening-summary.entity';
import { VesselScreening } from '../../vessel-screening/entity/vessel-screening.entity';
import { VesselScreeningSummaryRepository } from '../../vessel-screening/repository/vessel-screening-summary.repository';
import {
  CreateIncidentInvestigationDto,
  INCIDENT_INVESTIGATION_FILTER_FIELDS,
  ListIncidentInvestigationDto,
} from '../dto';
import { ListIncidentInvestigationRequestDto } from '../dto/list-incident-investigation-request.dto';
import { UpdateIncidentInvestigationDto } from '../dto/update-incident-investigation.dto';
import { IncidentInvestigationComment } from '../entity/incident-investigation-comment.entity';
import { IncidentInvestigationHistory } from '../entity/incident-investigation-history.entity';
import { IncidentInvestigationRemark } from '../entity/incident-investigation-remark.entity';
import { IncidentInvestigationReview } from '../entity/incident-investigation-review.entity';
import {
  IncidentInvestigation,
  IncidentInvestigationStatus,
} from '../entity/incident-investigation.entity';
import { SubIncidentType } from 'src/modules-qa/sub-incident-type/sub-incident-type.entity';
import { IncidentInvestigationRemarkRepository } from './incident-investigation-remark.repository';
import { InjuryRepository } from 'src/modules-qa/injury/repository/injury.repository';
import { IncidentMainCategory } from '../entity/incident-main-category.entity';
import { IncidentSubCategory } from '../entity/incident-second-category.entity';
import { IncidentSecondSubCategory } from '../entity/incident-second-sub-category.entity';

@EntityRepository(IncidentInvestigation)
export class IncidentInvestigationRepository extends TypeORMRepository<IncidentInvestigation> {
  constructor(private readonly connection: Connection) {
    super();
  }
  async createIncidentInvestigation(
    user: TokenPayloadModel,
    body: CreateIncidentInvestigationDto,
    processedRiskMatrixData?: any,
  ) {
    try {
      // Prepare general info
      const {
        remarks,
        reviews,
        typeIds,
        subTypeIds,
        secondaryIncidents,
        secondarySubIncidents,
        ...incidentInvestigationObj
      } = body;
      incidentInvestigationObj.reviewStatus = IncidentInvestigationReviewStatus.PENDING;
      const currYear = moment().year();
      const dataNoti: INotificationEventModel[] = [];
      const dataSendMail: IEmailEventModel[] = [];
      return await this.connection.transaction(async (manager) => {
        //validate data for create
        await this._validateIncidentForCreate(body, user);

        // const incidentMasterLists = await manager.find(IncidentMaster, {
        //   where: { id: In(typeIds), deleted: false, companyId: user.companyId },
        //   select: ['id'],
        // });

        // const subIncidentMasterLists = await manager.find(SubIncidentType, {
        //   where: { id: In(subTypeIds), deleted: false, companyId: user.companyId },
        //   select: ['id'],
        // });
        if (typeIds?.length > 0) {
          const incidentMasterLists = await manager.find(IncidentMaster, {
            where: { id: In(typeIds), deleted: false, companyId: user.companyId },
            select: ['id'],
          });
          Object.assign(incidentInvestigationObj, {
            typeIncidents: incidentMasterLists as IncidentMaster[],
          });
        }

        if (subTypeIds?.length > 0) {
          const subIncidentMasterLists = await manager.find(SubIncidentType, {
            where: { id: In(subTypeIds), deleted: false, companyId: user.companyId },
            select: ['id'],
          });
          Object.assign(incidentInvestigationObj, {
            subIncidents: subIncidentMasterLists as SubIncidentType[],
          });
        }

        if (secondaryIncidents?.length > 0) {
          const secondaryIncidentMasterLists = await manager.find(IncidentMaster, {
            where: { id: In(secondaryIncidents), deleted: false, companyId: user.companyId },
            select: ['id'],
          });
          Object.assign(incidentInvestigationObj, {
            secondaryIncidents: secondaryIncidentMasterLists as IncidentMaster[],
          });
        }

        if (secondarySubIncidents?.length > 0) {
          const secondarySubIncidentMasterLists = await manager.find(SubIncidentType, {
            where: { id: In(secondarySubIncidents), deleted: false, companyId: user.companyId },
            select: ['id'],
          });
          Object.assign(incidentInvestigationObj, {
            secondarySubIncidents: secondarySubIncidentMasterLists as SubIncidentType[],
          });
        }

        const counter = await this.manager
          .getCustomRepository(CompanyFeatureVersionRepository)
          .getNextVersion({
            manager,
            companyId: user.companyId,
            feature: FeatureVersionConfig.INCIDENT_INVESTIGATION_COUNTER,
            year: Number(currYear),
          });
        const company = await this.manager.findOne(Company, {
          where: { id: user.companyId },
          select: ['code'],
        });

        const userHistory = await this.manager
          .getCustomRepository(UserRepository)
          ._getUserInfoForHistory(user.id);

        const serialNumber = leadingZero(counter, 3);
        const version = leadingZero(counter, 4);
        // extract month and year
        const monthList = Object.keys(SelfAssessmentMonthEnum).map(
          (key) => SelfAssessmentMonthEnum[key],
        );
        const dateTimeOfIncidentMonth = new Date(body.dateTimeOfIncident).getMonth();
        const finalDateTimeOfIncidentMonth = monthList[dateTimeOfIncidentMonth];
        const dateTimeOfIncidentYear = new Date(body.dateTimeOfIncident).getFullYear();
        const createIncidentInvestigation = await manager.save(IncidentInvestigation, {
          ...incidentInvestigationObj,
          companyId: user.companyId,
          sNo: `ICI${company.code}${currYear}${version}`,
          refId: `${company.code}/ICI/${serialNumber}/${currYear}`,
          createdUserId: user.id,
          createdUser: userHistory,
          dateTimeOfIncident_Month: finalDateTimeOfIncidentMonth,
          dateTimeOfIncident_Year: dateTimeOfIncidentYear,
          // Add rightShipIncidentId and set incidentOrigin if provided
          ...(body.rightShipIncidentId && { 
            rightShipIncidentId: body.rightShipIncidentId,
            incidentOrigin: IncidentOriginEnum.RIGHT_SHIP
          }),
          // Add risk matrix data if provided
          ...(processedRiskMatrixData && {
            riskMatrixId: processedRiskMatrixData.riskMatrixId,
            potentialRiskCellPosition: processedRiskMatrixData.potentialRiskCellPosition,
            potentialRiskValueMappingId: processedRiskMatrixData.potentialRiskValueMappingId,
            potentialPriorityMasterId: processedRiskMatrixData.potentialPriorityMasterId,
            observedRiskCellPosition: processedRiskMatrixData.observedRiskCellPosition,
            observedRiskValueMappingId: processedRiskMatrixData.observedRiskValueMappingId,
            observedPriorityMasterId: processedRiskMatrixData.observedPriorityMasterId,
            finalRiskCellPosition: processedRiskMatrixData.finalRiskCellPosition,
            finalRiskValueMappingId: processedRiskMatrixData.finalRiskValueMappingId,
            finalPriorityMasterId: processedRiskMatrixData.finalPriorityMasterId,
          }),
        });
        await manager.insert(IncidentInvestigationHistory, {
          incidentInvestigationId: createIncidentInvestigation.id,
          remark: null,
          status: IncidentInvestigationStatus.DRAFT,
          createdUser: userHistory,
        });

        await manager
          .getCustomRepository(VesselScreeningSummaryRepository)
          .updateVesselScreeningSummaryByRef(
            manager,
            VesselScreeningSummaryReferenceEnum.INCIDENTS,
            null,
            body.vesselId,
            user.companyId,
          );

        if (body.comments && body.comments.length > 0) {
          // create comment for incident
          const preparedComments: IncidentInvestigationComment[] = [];
          for (let i = 0; i < body.comments.length; i++) {
            const comment = body.comments[i];
            preparedComments.push({
              incidentInvestigationId: createIncidentInvestigation.id,
              comment: comment.comment,
              createdUser: userHistory,
              createdAt: new Date(moment().unix() * 1000 - i),
            } as IncidentInvestigationComment);
          }
          await manager.insert(IncidentInvestigationComment, preparedComments);
        }

        if (remarks && remarks.length > 0) {
          const prepareRemarks: IncidentInvestigationRemark[] = [];
          for (let i = 0; i < remarks.length; i++) {
            prepareRemarks.push({
              ...remarks[i],
              incidentInvestigationId: createIncidentInvestigation.id,
              createdUser: userHistory,
              createdAt: new Date(moment().unix() * 1000 - i),
            } as IncidentInvestigationRemark);
          }
          await manager.insert(IncidentInvestigationRemark, prepareRemarks);
        }
        if (reviews && reviews.length > 0) {
          const prepareReviews: IncidentInvestigationReview[] = [];
          for (let i = 0; i < reviews.length; i++) {
            prepareReviews.push({
              ...reviews[i],
              incidentInvestigationId: createIncidentInvestigation.id,
            } as IncidentInvestigationReview);
          }
          await manager.insert(IncidentInvestigationReview, prepareReviews);
        }
        if (createIncidentInvestigation.status === IncidentInvestigationStatus.DRAFT) {
          const params: CreateUserAssignmentDTO = {
            incidentInvestigationId: createIncidentInvestigation.id,
            usersPermissions: [
              {
                permission: WorkflowPermission.CREATOR,
                userIds: [user.id],
              },
            ],
          };

          await manager.getCustomRepository(UserAssignmentRepository).createUserAssignment(params);
        }
        if (createIncidentInvestigation.status === IncidentInvestigationStatus.SUBMITTED) {
          if (!body.userAssignment) {
            throw new BaseError({ message: 'userAssignment.REQUIRED' });
          }
          const preparedUserAssignment = {
            incidentInvestigationId: createIncidentInvestigation.id,
            usersPermissions: body.userAssignment.usersPermissions,
          } as CreateUserAssignmentDTO;

          // Create user assignment when submit
          await manager
            .getCustomRepository(UserAssignmentRepository)
            .createUserAssignment(preparedUserAssignment);
          await manager.insert(IncidentInvestigationHistory, {
            incidentInvestigationId: createIncidentInvestigation.id,
            remark: null,
            status: IncidentInvestigationStatus.SUBMITTED,
            createdUser: userHistory,
          });

          const listUserAssignment = await manager
            .getCustomRepository(UserAssignmentRepository)
            .listByModule(ModuleType.INCIDENTS, createIncidentInvestigation.id);
          const listReceiverNoti = listUserAssignment[WorkflowPermission.REVIEWER];
          const performer = await manager
            .getCustomRepository(UserRepository)
            .getUserDetailAndSelect(user.id, ['id', 'username', 'jobTitle']);
          dataNoti.push({
            receivers: listReceiverNoti as IUser[],
            module: ModuleType.INCIDENTS,
            recordId: createIncidentInvestigation.id,
            recordRef: createIncidentInvestigation.sNo,
            type: PushTypeEnum.UPDATE_RECORD,
            currentStatus: IncidentInvestigationStatus.SUBMITTED,
            previousStatus: IncidentInvestigationStatus.DRAFT,
            performer: performer,
            executedAt: new Date(),
          });
          for (const receiver of listReceiverNoti) {
            dataSendMail.push({
              receiver: receiver as IUserEmail,
              type: EmailTypeEnum.UPDATE_RECORD,
              templateKey: MailTemplate.CHANGE_RECORD_STATUS,
              subject: '[Notification] Change status in a record',
              data: {
                username: receiver.username,
                baseURL: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.ADMIN_PAGE,
                recordRef: createIncidentInvestigation.sNo,
                recordId: createIncidentInvestigation.id,
                path: ModulePathEnum.INCIDENTS,
                currentStatus: IncidentInvestigationStatus.SUBMITTED,
                previousStatus: IncidentInvestigationStatus.DRAFT,
                performer: performer,
                executedAt: new Date(),
              },
            });
          }
        }
        return { dataNoti, dataSendMail, createIncidentInvestigation };
      });
    } catch (ex) {
      LoggerCommon.error(
        '[IncidentInvestigation] createIncidentInvestigation error ',
        ex.message || ex,
      );
      throw ex;
    }
  }

  async detailIncidentInvestigation(incidentInvestigationId: string, user: TokenPayloadModel) {
    let whereVesselDocHolder = '(1=1)';
    if (user.companyLevel === CompanyLevelEnum.EXTERNAL_COMPANY) {
      whereVesselDocHolder = '(vesselDocHolders.responsiblePartyQA = true)';
    }
    let whereVesselCharterer = '(1=1)';
    if (user.companyLevel === CompanyLevelEnum.EXTERNAL_COMPANY) {
      whereVesselCharterer = '(vesselCharterers.responsiblePartyQA = true)';
    }
    let whereVesselOwner = '(1=1)';
    if (user.companyLevel === CompanyLevelEnum.EXTERNAL_COMPANY) {
      whereVesselOwner = '(vesselOwnersPlans.responsiblePartyQA = true)';
    }
    const IncidentInvestigation = await this.getOneQB(
      this.createQueryBuilder('incidentInvestigation')
        .leftJoin('incidentInvestigation.company', 'company')
        .leftJoin('incidentInvestigation.vessel', 'vessel')
        .leftJoin('vessel.country', 'country')
        .leftJoin('vessel.vesselType', 'vesselType')
        .leftJoin('incidentInvestigation.port', 'port')
        .leftJoin('incidentInvestigation.portTo', 'portTo')
        .leftJoin('incidentInvestigation.typeIncidents', 'typeIncidents')
        .leftJoin('incidentInvestigation.subIncidents', 'subIncidents')
        .leftJoin('incidentInvestigation.secondaryIncidents', 'secondaryIncidents')
        .leftJoin('incidentInvestigation.secondarySubIncidents', 'secondarySubIncidents')
        .leftJoinAndSelect('incidentInvestigation.injuries', 'injuries')
        .leftJoinAndSelect('injuries.injuryMaster', 'injuryMaster')
        .leftJoinAndSelect(
          'incidentInvestigation.incidentInvestigationRemarks',
          'incidentInvestigationRemarks',
        )
        .leftJoinAndSelect(
          'incidentInvestigation.incidentInvestigationReviews',
          'incidentInvestigationReviews',
        )
        .leftJoinAndSelect(
          'incidentInvestigation.incidentInvestigationHistories',
          'incidentInvestigationHistories',
        )
        .leftJoinAndSelect('incidentInvestigation.rightShipIncident', 'rightShipIncident')
        .leftJoin('incidentInvestigationReviews.riskFactor', 'riskFactor')
        .leftJoin('incidentInvestigation.voyageType', 'voyageType')
        .leftJoin('incidentInvestigation.riskMatrix', 'riskMatrix')
        .leftJoin('incidentInvestigation.potentialRiskValueMapping', 'potentialRiskValueMapping')
        .leftJoin('incidentInvestigation.potentialPriorityMaster', 'potentialPriorityMaster')
        .leftJoin('incidentInvestigation.observedRiskValueMapping', 'observedRiskValueMapping')
        .leftJoin('incidentInvestigation.observedPriorityMaster', 'observedPriorityMaster')
        .leftJoin('incidentInvestigation.finalRiskValueMapping', 'finalRiskValueMapping')
        .leftJoin('incidentInvestigation.finalPriorityMaster', 'finalPriorityMaster')
        .leftJoinAndSelect('incidentInvestigation.incidentMainCategory', 'incidentMainCategory')
        .leftJoinAndSelect('incidentMainCategory.incidentSubCategory', 'incidentSubCategory')
        .leftJoinAndSelect(
          'incidentSubCategory.incidentSecondSubCategory',
          'incidentSecondSubCategory',
        )
        .leftJoinAndSelect(
          'incidentInvestigation.incidentInvestigationComments',
          'incidentInvestigationComment',
        )
        .leftJoin('incidentInvestigation.userAssignments', 'userAssignments')
        .leftJoin('userAssignments.user', 'user')
        .leftJoin('user.company', 'userCompany')
        .leftJoin('user.divisions', 'userDivisions')
        .leftJoin('vessel.vesselDocHolders', 'vesselDocHolders', whereVesselDocHolder)
        .leftJoin('vesselDocHolders.company', 'vdhCompany')
        .leftJoin('vessel.vesselOwners', 'vesselOwnersPlans', whereVesselOwner)
        .leftJoin('vesselOwnersPlans.company', 'voCompany')
        .leftJoin('vessel.vesselCharterers', 'vesselCharterers', whereVesselCharterer)
        .leftJoin('vesselCharterers.company', 'vcCompany')
        .leftJoin('vessel.divisionMapping', 'divisionMapping')
        .leftJoin('divisionMapping.division', 'division')
        .select()
        .addSelect([
          'company.name',
          'riskFactor.id',
          'riskFactor.code',
          'riskFactor.name',
          'vessel.code',
          'vessel.name',
          'vessel.isVesselRestricted',
          'vessel.isCompanyRestricted',
          'country.id',
          'country.name',
          'vessel.buildDate',
          'vessel.imoNumber',
          'vesselType.code',
          'vesselType.name',
          'divisionMapping.id',
          'division.id',
          'division.name',
          'division.code',
          'port.id',
          'port.code',
          'port.name',
          'port.country',
          'portTo.id',
          'portTo.code',
          'portTo.name',
          'portTo.country',
          'typeIncidents.id',
          'typeIncidents.code',
          'typeIncidents.name',
          'subIncidents.id',
          'subIncidents.code',
          'subIncidents.name',
          'secondaryIncidents.id',
          'secondaryIncidents.code',
          'secondaryIncidents.name',
          'secondarySubIncidents.id',
          'secondarySubIncidents.code',
          'secondarySubIncidents.name',
          'vesselDocHolders.id',
          'vesselDocHolders.companyId',
          'vesselDocHolders.fromDate',
          'vesselDocHolders.toDate',
          'vesselDocHolders.responsiblePartyInspection',
          'vesselDocHolders.responsiblePartyQA',
          'vesselDocHolders.status',
          'vdhCompany.name',
          'vesselOwnersPlans.id',
          'vesselOwnersPlans.companyId',
          'vesselOwnersPlans.fromDate',
          'vesselOwnersPlans.toDate',
          'vesselOwnersPlans.responsiblePartyInspection',
          'vesselOwnersPlans.responsiblePartyQA',
          'vesselOwnersPlans.status',
          'voCompany.name',
          'vesselCharterers.id',
          'vesselCharterers.companyId',
          'vesselCharterers.fromDate',
          'vesselCharterers.toDate',
          'vesselCharterers.responsiblePartyInspection',
          'vesselCharterers.responsiblePartyQA',
          'vesselCharterers.status',
          'vcCompany.name',
          'userAssignments.id',
          'userAssignments.userId',
          'userAssignments.permission',
          'user.id',
          'user.username',
          'user.jobTitle',
          'user.roles',
          'userCompany.name',
          'userCompany.code',
          'userDivisions.name',
          'userDivisions.code',
          'voyageType.id',
          'voyageType.name',
          'riskMatrix.id',
          'riskMatrix.matrixCode',
          'riskMatrix.rows',
          'riskMatrix.columns',
          'riskMatrix.status',
          'potentialRiskValueMapping.id',
          'potentialRiskValueMapping.riskValue',
          'potentialRiskValueMapping.color',
          'potentialPriorityMaster.id',
          'potentialPriorityMaster.risk',
          'potentialPriorityMaster.order',
          'observedRiskValueMapping.id',
          'observedRiskValueMapping.riskValue',
          'observedRiskValueMapping.color',
          'observedPriorityMaster.id',
          'observedPriorityMaster.risk',
          'observedPriorityMaster.order',
          'finalRiskValueMapping.id',
          'finalRiskValueMapping.riskValue',
          'finalRiskValueMapping.color',
          'finalPriorityMaster.id',
          'finalPriorityMaster.risk',
          'finalPriorityMaster.order',
        ])
        .where(
          '(incidentInvestigation.id = :id AND incidentInvestigation.deleted = :deleted and incidentInvestigation.companyId =:companyId )',
          {
            id: incidentInvestigationId,
            deleted: false,
            companyId: user.companyId,
          },
        )
        .orderBy('incidentInvestigationComment.createdAt', 'DESC')
        .addOrderBy('incidentInvestigationRemarks.createdAt', 'DESC'),
    );
    //Map roles for User Asignment
    const userIds = IncidentInvestigation?.userAssignments?.map(
      (userAssignment) => userAssignment.user.id,
    );
    let mapRoles;
    if (userIds.length > 0) {
      mapRoles = await this.connection.getCustomRepository(UserRepository).getUserRoles(userIds);
    }
    IncidentInvestigation?.userAssignments?.forEach((userAssignment) => {
      if (mapRoles.has(userAssignment.user.id)) {
        userAssignment.user.roles = mapRoles.get(userAssignment.user.id);
      }
    });

    // Group incidentMainCategory by causeType using lodash - JIRA ticket INAT-4129
    if (IncidentInvestigation && IncidentInvestigation.incidentMainCategory?.length > 0) {
      (IncidentInvestigation as any).incidentMainCategory = _.groupBy(
        IncidentInvestigation.incidentMainCategory,
        'causeType',
      );
    }

    if (IncidentInvestigation) {
      return IncidentInvestigation;
    } else {
      throw new BaseError({ status: 404, message: 'incidentInvestigation.NOT_FOUND' });
    }
  }

  async getConditionForUserNotAdmin(
    user: TokenPayloadModel,
    aliasTable?: string,
    aliasVesselDocHolder?: string,
    aliasVesselCharterer?: string,
    aliasVesselOwner?: string,
    aliasUserDivision?: string,
  ) {
    if (!aliasTable) {
      aliasTable = 'ii';
    }
    if (!aliasVesselDocHolder) {
      aliasVesselDocHolder = 'vdh';
    }
    if (!aliasVesselCharterer) {
      aliasVesselCharterer = 'vc';
    }
    if (!aliasVesselOwner) {
      aliasVesselOwner = 'vo';
    }
    if (!aliasUserDivision) {
      aliasUserDivision = 'users';
    }
    let conditionNotAdmin = '';
    const {
      whereForExternal,
      whereForMainAndInternal,
    } = await _supportWhereDOCChartererOwnerRawQuery(
      this.manager,
      user.explicitCompanyId,
      aliasTable,
      aliasVesselDocHolder,
      aliasVesselCharterer,
      aliasVesselOwner,
    );
    if (
      user.companyLevel === CompanyLevelEnum.MAIN_COMPANY ||
      user.companyLevel === CompanyLevelEnum.INTERNAL_COMPANY
    ) {
      conditionNotAdmin = `AND ("${aliasUserDivision}".id = '${user.id}' ${whereForMainAndInternal})`;
    } else {
      conditionNotAdmin = `AND ("${aliasUserDivision}".id = '${user.id}' ${whereForExternal})`;
    }
    return conditionNotAdmin;
  }

  async listIncidentInvestigation(
    query: ListIncidentInvestigationDto,
    user: TokenPayloadModel,
    isRestricted: boolean,
    isVettingManager?: boolean,
    body?: PayloadAGGridDto,
  ) {
    const queryBuilder = this.createQueryBuilder('incidentInvestigation')
      // .leftJoin('incidentInvestigation.company', 'company')
      .leftJoin('incidentInvestigation.vessel', 'vessel')
      .leftJoin('vessel.country', 'vesselCountry')
      .leftJoin('vessel.vesselDocHolders', 'vesselDocHolders', `vesselDocHolders.status = 'active'`)
      .leftJoin('vesselDocHolders.company', 'vesselDocHolderCompany')
      .leftJoin('vessel.vesselType', 'vesselType')
      .leftJoin('incidentInvestigation.port', 'port')
      .leftJoin('incidentInvestigation.portTo', 'portTo')
      .leftJoin('incidentInvestigation.userAssignments', 'userAssignments')
      .leftJoin('incidentInvestigation.voyageType', 'voyageType')
      .leftJoin('userAssignments.user', 'user')
      .where(
        `(incidentInvestigation.deleted = false and incidentInvestigation.companyId = '${user.companyId}' )`,
      )
      .select();

    const fieldSelects = [
      // 'company.name',
      // 'company.companyIMO',
      'vessel.code',
      'vessel.name',
      'vessel.imoNumber',
      'vesselCountry.id',
      'vesselCountry.name',
      'vessel.docHolderId',
      'vesselType.code',
      'vesselType.name',
      'vesselDocHolders.id',
      'vesselDocHolders.status',
      'vesselDocHolderCompany.id',
      'vesselDocHolderCompany.name',
      'vesselDocHolderCompany.companyIMO',
      'port.id',
      'port.code',
      'port.name',
      'port.country',
      'portTo.id',
      'portTo.code',
      'portTo.name',
      'portTo.country',
      'voyageType.name',
    ];

    if (query.ids?.length) {
      queryBuilder.andWhere('incidentInvestigation.id IN (:...ids)', {
        ids: query.ids,
      });
    }
    if (isRestricted && !RoleScopeCheck.isAdmin(user)) {
      queryBuilder.andWhere(
        `(incidentInvestigation.createdUserId = '${user.id}' OR userAssignments.userId = '${user.id}')`,
      );
    }

    if (query.incidentDateFrom) {
      queryBuilder.andWhere(
        `incidentInvestigation.dateTimeOfIncident >= '${new Date(
          query.incidentDateFrom,
        ).toISOString()}'`,
      );
    }

    if (query.incidentDateTo) {
      queryBuilder.andWhere(
        `incidentInvestigation.dateTimeOfIncident <= '${new Date(
          query.incidentDateTo,
        ).toISOString()}'`,
      );
    }

    if (query.content) {
      queryBuilder.andWhere(
        `(incidentInvestigation.title LIKE '%${query.content}%' OR company.name LIKE '%${query.content}%' OR vessel.name LIKE '%${query.content}%' OR vessel.code LIKE '%${query.content}%' ` +
          `OR vesselType.code LIKE '%${query.content}%' OR vesselType.name LIKE '%${query.content}%')`,
      );
    }
    // query for getting records based on createdaUserId only for draftStatus if the user is not an admin
    if (!RoleScopeCheck.isAdmin(user)) {
      queryBuilder.andWhere(
        `(incidentInvestigation.status IN (
          '${IncidentInvestigationStatus.CLOSEOUT}',
          '${IncidentInvestigationStatus.REASSIGN}',
          '${IncidentInvestigationStatus.REVIEWED}',
          '${IncidentInvestigationStatus.SUBMITTED}'
        ) 
        OR (incidentInvestigation.status = '${IncidentInvestigationStatus.DRAFT}' AND incidentInvestigation.createdUserId = '${user.id}'))`,
      );
    } else if (RoleScopeCheck.isAdmin(user)) {
      queryBuilder.andWhere(`incidentInvestigation.status IN (
          '${IncidentInvestigationStatus.CLOSEOUT}',
          '${IncidentInvestigationStatus.REASSIGN}',
          '${IncidentInvestigationStatus.REVIEWED}',
          '${IncidentInvestigationStatus.SUBMITTED}',
          '${IncidentInvestigationStatus.DRAFT}'
        )`);
    }

    if (query.incidentStatus) {
      queryBuilder.andWhere(`incidentInvestigation.status = '${query.incidentStatus}'`);
    }

    // only for vessel screening module, because no need to show draft records
    if (query.module === ModulePathEnum.VESSEL_SCREENING) {
      queryBuilder.andWhere(`incidentInvestigation.status IN (
          '${IncidentInvestigationStatus.SUBMITTED}',
          '${IncidentInvestigationStatus.REVIEWED}',
          '${IncidentInvestigationStatus.CLOSEOUT}'
      )`);
    }
    // if (isVettingManager && !RoleScopeCheck.isAdmin(user)) {
    //   queryBuilder.andWhere(
    //     `incidentInvestigation.status != '${IncidentInvestigationStatus.DRAFT}'`,
    //   );
    // }
    if (query.vesselId) {
      queryBuilder.andWhere(`incidentInvestigation.vesselId = '${query.vesselId}'`);
    }

    let groupRisk;
    if (!RoleScopeCheck.isAdmin(user)) {
      const { whereForExternal, whereForMainAndInternal } = await _supportWhereDOCChartererOwner(
        this.manager,
        user.explicitCompanyId,
        'incidentInvestigation',
      );

      queryBuilder
        .leftJoin('vessel.divisionMapping', 'divisionMapping')
        .leftJoin('vessel.vesselCharterers', 'vesselCharterers')
        .leftJoin('vessel.vesselOwners', 'vesselOwners')
        .leftJoin('divisionMapping.division', 'division')
        .leftJoin('division.users', 'users');

      if (
        user.companyLevel === CompanyLevelEnum.MAIN_COMPANY ||
        user.companyLevel === CompanyLevelEnum.INTERNAL_COMPANY
      ) {
        queryBuilder.andWhere(`( users.id = '${user.id}' ` + whereForMainAndInternal + ')');
      } else {
        queryBuilder.andWhere(`( users.id = '${user.id}' ` + whereForExternal + ')');
      }

      if (query.filterRisk && query.filterRisk === VesselScreeningFilterRiskEnum.OBSERVED) {
        const rawQuery = `SELECT
                        ii."observedRisk" as "risk",
                        COUNT(*) AS  "count"
                      FROM
                        incident_investigation ii
                      LEFT JOIN vessel v on ii."vesselId" = v.id
                      LEFT JOIN vessel_charterer vc ON v.ID = vc."vesselId"
                      LEFT JOIN vesseL_owner vo ON v.ID = vo."vesselId"
                      LEFT JOIN division_mapping dm on v.id = dm."vesselId" 
                      LEFT JOIN division d on dm."divisionId" = d.id 
                      LEFT JOIN division_user du on du."divisionId"  = d.id 
                      LEFT JOIN "user" u on du."userId" = u.id
                      LEFT JOIN company c on v."docHolderId"  = c.id
                      WHERE
                        ii."companyId" = $1 AND
                        ii."deleted" = false AND
                        ii."observedRisk" is not null AND
                        (u.ID = $2 OR v."docHolderId" = $1 OR vo."companyId" = $1 OR vc."companyId" = $1)
                      GROUP BY "risk"`;
        groupRisk = await this.connection.query(rawQuery, [user.explicitCompanyId, user.id]);
      } else if (query.filterRisk && query.filterRisk === VesselScreeningFilterRiskEnum.FINAL) {
        const rawQuery = `SELECT
                        ii."finalRisk" as "risk",
                        COUNT(*) AS  "count"
                      FROM
                        incident_investigation ii
                      LEFT JOIN vessel v on ii."vesselId" = v.id
                      LEFT JOIN vessel_charterer vc ON v.ID = vc."vesselId"
                      LEFT JOIN vesseL_owner vo ON v.ID = vo."vesselId"
                      LEFT JOIN division_mapping dm on v.id = dm."vesselId" 
                      LEFT JOIN division d on dm."divisionId" = d.id 
                      LEFT JOIN division_user du on du."divisionId"  = d.id 
                      LEFT JOIN "user" u on du."userId" = u.id
                      LEFT JOIN company c on v."docHolderId"  = c.id
                      WHERE
                        ii."companyId" = $1 AND
                        ii."deleted" = false AND
                        ii."finalRisk" is not null AND
                        (u.ID = $2 OR v."docHolderId" = $1 OR vo."companyId" = $1 OR vc."companyId" = $1)
                      GROUP BY "risk"`;
        groupRisk = await this.connection.query(rawQuery, [user.explicitCompanyId, user.id]);
      } else {
        const rawQuery = `SELECT
                        ii."potentialRisk" as "risk",
                        COUNT(*) AS  "count"
                      FROM
                        incident_investigation ii
                      LEFT JOIN vessel v on ii."vesselId" = v.id 
                      LEFT JOIN vessel_charterer vc ON v.ID = vc."vesselId"
                      LEFT JOIN vesseL_owner vo ON v.ID = vo."vesselId"
                      LEFT JOIN division_mapping dm on v.id = dm."vesselId" 
                      LEFT JOIN division d on dm."divisionId" = d.id 
                      LEFT JOIN division_user du on du."divisionId"  = d.id 
                      LEFT JOIN "user" u on du."userId" = u.id
                      LEFT JOIN company c on v."docHolderId"  = c.id
                      WHERE
                        ii."companyId" = $1 AND
                        ii."deleted" = false AND
                        ii."potentialRisk" is not null AND
                      (u.ID = $2 OR v."docHolderId" = $1 OR vo."companyId" = $1 OR vc."companyId" = $1)
                      GROUP BY "risk"`;
        groupRisk = await this.connection.query(rawQuery, [user.explicitCompanyId, user.id]);
      }
    } else {
      if (query.filterRisk && query.filterRisk === VesselScreeningFilterRiskEnum.OBSERVED) {
        const rawQuery = `SELECT
                        ii."observedRisk" as "risk",
                        COUNT(*) AS  "count"
                      FROM
                        incident_investigation ii
                      WHERE
                        ii."companyId" = $1 AND
                        ii."deleted" = false AND
                        ii."observedRisk" is not null
                      GROUP BY "risk"`;
        groupRisk = await this.connection.query(rawQuery, [user.companyId]);
      } else if (query.filterRisk && query.filterRisk === VesselScreeningFilterRiskEnum.FINAL) {
        const rawQuery = `SELECT
                        ii."finalRisk" as "risk",
                        COUNT(*) AS  "count"
                      FROM
                        incident_investigation ii
                      WHERE
                        ii."companyId" = $1 AND
                        ii."deleted" = false AND
                        ii."finalRisk" is not null
                      GROUP BY "risk"`;
        groupRisk = await this.connection.query(rawQuery, [user.companyId]);
      } else {
        const rawQuery = `SELECT
                        ii."potentialRisk" as "risk",
                        COUNT(*) AS  "count"
                      FROM
                        incident_investigation ii
                      WHERE
                        ii."companyId" = $1 AND
                        ii."deleted" = false AND
                        ii."potentialRisk" is not null
                      GROUP BY "risk"`;
        groupRisk = await this.connection.query(rawQuery, [user.companyId]);
      }
    }

    //AG-Grid
    const connection = getConnection();
    const subQueryBuilder = connection.createQueryBuilder();
    queryBuilder.addSelect(fieldSelects);

    if (body) {
      convertFilterField(body, INCIDENT_INVESTIGATION_FILTER_FIELDS);

      const queryIncidentTypes = this.createQueryBuilder('incidentInvestigation')
        .leftJoin('incidentInvestigation.typeIncidents', 'typeIncidents')
        .select('incidentInvestigation.id')
        .addSelect(
          `COALESCE(STRING_AGG(DISTINCT typeIncidents.name, ', '), 'Other') AS "typeOfIncident"`,
        )
        .groupBy(`incidentInvestigation.id`);

      const queryIncidentRemarks = this.manager
        .getCustomRepository(IncidentInvestigationRemarkRepository)
        .createQueryBuilder('incidentInvestigationRemarks')
        .select(
          `
          DISTINCT ON ("incidentInvestigationId")
          "incidentInvestigationId", 
          "remark"
          `,
        )
        .addOrderBy(`"incidentInvestigationId", "createdAt"`, 'DESC');

      queryBuilder
        .leftJoin(
          `(${queryIncidentTypes.getQuery()})`,
          'incidentTypesGroup',
          `"incidentTypesGroup"."incidentInvestigation_id" = "incidentInvestigation"."id"`,
        )
        .leftJoin(
          `(${queryIncidentRemarks.getQuery()})`,
          'incidentRemarks',
          `"incidentRemarks"."incidentInvestigationId" = "incidentInvestigation"."id"`,
        )
        .addSelect([
          `CASE
            WHEN "incidentInvestigation"."potentialRisk" = 0 THEN 'NEGLIGIBLE'
            WHEN "incidentInvestigation"."potentialRisk" = 10 THEN 'LOW'
            WHEN "incidentInvestigation"."potentialRisk" = 20 THEN 'MEDIUM'
            WHEN "incidentInvestigation"."potentialRisk" = 30 THEN 'HIGH'
           ELSE NULL END AS "potentialRiskValue"`,
          `CASE
            WHEN "incidentInvestigation"."observedRisk" = 0 THEN 'NEGLIGIBLE'
            WHEN "incidentInvestigation"."observedRisk" = 10 THEN 'LOW'
            WHEN "incidentInvestigation"."observedRisk" = 20 THEN 'MEDIUM'
            WHEN "incidentInvestigation"."observedRisk" = 30 THEN 'HIGH'
           ELSE NULL END AS "observedRiskValue"`,
          `CASE 
            WHEN "incidentInvestigation"."timeLoss" = true THEN 'Yes'
            WHEN "incidentInvestigation"."timeLoss" = false THEN 'No'
           ELSE NULL END AS "timeLossValue"`,
          `"typeOfIncident"`,
          `"incidentRemarks"."remark"`,
        ])
        .groupBy(
          `
          incidentInvestigation.id, 
          "typeOfIncident", 
          "incidentRemarks"."remark", 
          ${fieldSelects.join(', ')}
      `,
        )
        .andWhere(`incidentInvestigation.deleted = false`)

      subQueryBuilder
        .select(`DISTINCT "incidentInvestigation_id" AS id`)
        .from(`(${queryBuilder.getQuery()})`, 'subquery');

      createWhereSql(body, subQueryBuilder);
      createSelectSql(body, subQueryBuilder, null, '"incidentInvestigation_id"');

      queryBuilder.groupBy();
    }

    //update after build sql ag-grid
    queryBuilder
      .leftJoin('incidentInvestigation.typeIncidents', 'typeIncidents')
      .leftJoin('incidentInvestigation.subIncidents', 'subIncidents')
      .leftJoinAndSelect(
        'incidentInvestigation.incidentInvestigationRemarks',
        'incidentInvestigationRemarks',
      )
      .leftJoinAndSelect(
        'incidentInvestigation.incidentInvestigationReviews',
        'incidentInvestigationReviews',
      )
      .leftJoin('incidentInvestigationReviews.riskFactor', 'riskFactor')
      .leftJoinAndSelect(
        'incidentInvestigation.incidentInvestigationComments',
        'incidentInvestigationComment',
      )
      .addSelect([
        'riskFactor.id',
        'riskFactor.code',
        'riskFactor.name',
        'typeIncidents.id',
        'typeIncidents.code',
        'typeIncidents.name',
        'subIncidents.id',
        'subIncidents.code',
        'subIncidents.name',
        'userAssignments.id',
        'userAssignments.userId',
        'userAssignments.permission',
        'userAssignments.incidentInvestigationId',
        'user.id',
        'user.username',
      ]);

    if (!RoleScopeCheck.isAdmin(user)) {
      queryBuilder.addSelect([
        'vesselCharterers.id',
        'vesselCharterers.companyId',
        'vesselCharterers.fromDate',
        'vesselCharterers.toDate',
        'vesselCharterers.responsiblePartyInspection',
        'vesselCharterers.responsiblePartyQA',
        'vesselCharterers.status',
        'vesselOwners.id',
        'vesselOwners.companyId',
        'vesselOwners.fromDate',
        'vesselOwners.toDate',
        'vesselOwners.responsiblePartyInspection',
        'vesselOwners.responsiblePartyQA',
        'vesselOwners.status',
      ]);
    }

    let list = await handleGetDataForAGGrid(
      this,
      queryBuilder,
      query,
      body,
      subQueryBuilder,
      queryBuilder,
      'incidentInvestigation',
      [
        `"typeOfIncident"`,
        `"potentialRiskValue"`,
        `"observedRiskValue"`,
        `"timeLossValue"`,
        `"remark"`,
      ],
    );
    if (body && isDoingGrouping(body)) {
      return list;
    }

    list.risk = groupRisk;
    // for (const element of list.data) {
    //   const activeDocHolderDetails = element.vessel.vesselDocHolders.filter((obj) => {
    //     return obj.status === CommonStatus.ACTIVE;
    //   });
    //   if (activeDocHolderDetails.length) {
    //     const docHoldercompany = activeDocHolderDetails[0].company;
    //     element.company = docHoldercompany;
    //   }
    // }
    return list;
  }
  async deleteIncidentInvestigation(incidentInvestigationId: string, companyId: string) {
    const incidentInvestigation = await this.findOne({
      where: { id: incidentInvestigationId },
      select: ['vesselId'],
    });

    // soft delete
    const updateResult = await this.softDelete({
      id: incidentInvestigationId,
      companyId,
    });
    await this.manager.getCustomRepository(InjuryRepository).delete({incidentInvestigationId: incidentInvestigationId});
    if (updateResult.affected === 0) {
      throw new BaseError({ status: 404, message: 'incidentInvestigation.NOT_FOUND' });
    } else {
      // trigger
      if (incidentInvestigation.vesselId) {
        await this.manager
          .getCustomRepository(VesselScreeningSummaryRepository)
          .updateVesselScreeningSummaryByRef(
            this.manager,
            VesselScreeningSummaryReferenceEnum.INCIDENTS,
            null,
            incidentInvestigation.vesselId,
            companyId,
          );
      }
      return 1;
    }
  }
  async updateIncidentInvestigation(
    user: TokenPayloadModel,
    incidentInvestigationId: string,
    body: UpdateIncidentInvestigationDto,
    processedRiskMatrixData?: any,
  ) {
    try {
      // Prepare general info
      if (body.status === IncidentInvestigationStatus.DRAFT) {
        body.reviewStatus = IncidentInvestigationReviewStatus.PENDING;
      } else if (body?.status === IncidentInvestigationStatus.CLOSEOUT) {
        body.reviewStatus = IncidentInvestigationReviewStatus.CLEARED;
      }
      // else {
      //   body.reviewStatus =
      //     body.status === IncidentInvestigationStatus.SUBMITTED
      //       ? IncidentInvestigationReviewStatus.IN_PROGRESS
      //       : IncidentInvestigationReviewStatus.CLEARED;
      // }
      const {
        remarks,
        typeIds,
        subTypeIds,
        secondaryIncidents,
        secondarySubIncidents,
        comments,
        incidentMainCategory,
        ...incidentInvestigationObjUpdate
      } = body;
      const incidentInvestigationFound = await this.findOne(incidentInvestigationId, {
        relations: [
          'incidentInvestigationComments',
          'incidentInvestigationRemarks',
          'typeIncidents',
        ],
      });
      //validate data for update
      await this._validateIncidentForUpdate(body, user, incidentInvestigationFound);
      // for comment
      const userHistory = await this.manager
        .getCustomRepository(UserRepository)
        ._getUserInfoForHistory(user.id);
      const dataNoti: INotificationEventModel[] = [];
      const dataSendMail: IEmailEventModel[] = [];
      await this.connection.transaction(async (manager) => {
        //#region Prepare data
        const rawComments: IncidentInvestigationComment[] = [];
        if (comments && comments.length > 0) {
          for (let i = 0; i < comments.length; i++) {
            const comment = comments[i];
            rawComments.push({
              id: comment.id ? comment.id : Utils.strings.generateUUID(),
              incidentInvestigationId: incidentInvestigationId,
              comment: comment.comment,
            } as IncidentInvestigationComment);
          }
        }

        const listCurrentIncidentComments =
          incidentInvestigationFound.incidentInvestigationComments;
        const listCurrentIncidentCommentIds: string[] = [];
        const listNewIncidentCommentIds: string[] = [];

        if (listCurrentIncidentComments && listCurrentIncidentComments.length > 0) {
          listCurrentIncidentComments.forEach((item: IncidentInvestigationComment) => {
            listCurrentIncidentCommentIds.push(item.id);
          });
        }
        // console.log('current ids: ', listCurrentIncidentCommentIds);

        if (rawComments && rawComments.length > 0) {
          rawComments.forEach((item) => {
            listNewIncidentCommentIds.push(item.id);
          });
        }
        // console.log('new ids: ', listNewIncidentCommentIds);

        const listIncidentCommentUpdateIds = MySet.intersect(
          new Set(listCurrentIncidentCommentIds),
          new Set(listNewIncidentCommentIds),
        );
        // console.log('ids update: ', Array.from(listIncidentCommentUpdateIds));

        const listIncidentCommentCreateIds = MySet.difference(
          new Set(listNewIncidentCommentIds),
          new Set(listCurrentIncidentCommentIds),
        );
        // console.log('ids create: ', Array.from(listIncidentCommentCreateIds));

        const listIncidentCommentDeleteIds = MySet.difference(
          new Set(listCurrentIncidentCommentIds),
          new Set(listNewIncidentCommentIds),
        );
        // console.log('ids delete: ', Array.from(listIncidentCommentDeleteIds));

        const IncidentCommentCreate = rawComments.filter((item) =>
          listIncidentCommentCreateIds.has(item.id),
        );

        const IncidentCommentUpdateTemp = rawComments.filter((item) =>
          listIncidentCommentUpdateIds.has(item.id),
        );

        const listFinalCommentUpdate: IncidentInvestigationComment[] = [];
        if (listCurrentIncidentComments) {
          for (const commentFound of listCurrentIncidentComments) {
            for (const newComment of IncidentCommentUpdateTemp) {
              if (commentFound.id == newComment.id && commentFound.comment !== newComment.comment) {
                listFinalCommentUpdate.push(
                  Object.assign(newComment, { updatedUser: userHistory }),
                );
              }
            }
          }
        }

        if (IncidentCommentCreate.length > 0) {
          const preparedIncidentCommentCreate: IncidentInvestigationComment[] = [];
          for (const [index, item] of IncidentCommentCreate.entries()) {
            preparedIncidentCommentCreate.push(
              Object.assign(item, {
                createdUser: userHistory,
                createdAt: new Date(moment().unix() * 1000 - index),
              }),
            );
          }
          await manager.save(IncidentInvestigationComment, IncidentCommentCreate);
        }
        if (listFinalCommentUpdate.length > 0) {
          await manager.save(IncidentInvestigationComment, listFinalCommentUpdate);
        }
        if (Array.from(listIncidentCommentDeleteIds).length > 0) {
          await manager.delete(IncidentInvestigationComment, {
            id: In(Array.from(listIncidentCommentDeleteIds)),
          });
        }

        // region remark
        const rawRemarks: IncidentInvestigationRemark[] = [];
        if (remarks && remarks.length > 0) {
          for (let i = 0; i < remarks.length; i++) {
            const remark = remarks[i];
            rawRemarks.push({
              id: remark.id ? remark.id : Utils.strings.generateUUID(),
              incidentInvestigationId: incidentInvestigationId,
              remark: remark.remark,
            } as IncidentInvestigationRemark);
          }
        }

        const listCurrentIncidentRemarks = incidentInvestigationFound.incidentInvestigationRemarks;
        const listCurrentIncidentRemarkIds: string[] = [];
        const listNewIncidentRemarkIds: string[] = [];

        if (listCurrentIncidentRemarks && listCurrentIncidentRemarks.length > 0) {
          listCurrentIncidentRemarks.forEach((item: IncidentInvestigationRemark) => {
            listCurrentIncidentRemarkIds.push(item.id);
          });
        }

        if (rawRemarks && rawRemarks.length > 0) {
          rawRemarks.forEach((item) => {
            listNewIncidentRemarkIds.push(item.id);
          });
        }

        const listIncidentRemarkUpdateIds = MySet.intersect(
          new Set(listCurrentIncidentRemarkIds),
          new Set(listNewIncidentRemarkIds),
        );

        const listIncidentRemarkCreateIds = MySet.difference(
          new Set(listNewIncidentRemarkIds),
          new Set(listCurrentIncidentRemarkIds),
        );

        const listIncidentRemarkDeleteIds = MySet.difference(
          new Set(listCurrentIncidentRemarkIds),
          new Set(listNewIncidentRemarkIds),
        );

        const incidentRemarkCreate = rawRemarks.filter((item) =>
          listIncidentRemarkCreateIds.has(item.id),
        );

        const incidentRemarkUpdateTemp = rawRemarks.filter((item) =>
          listIncidentRemarkUpdateIds.has(item.id),
        );

        const listFinalRemarkUpdate: IncidentInvestigationRemark[] = [];
        if (listCurrentIncidentRemarks) {
          for (const remarkFound of listCurrentIncidentRemarks) {
            for (const newRemark of incidentRemarkUpdateTemp) {
              if (remarkFound.id == newRemark.id && remarkFound.remark !== newRemark.remark) {
                listFinalRemarkUpdate.push(Object.assign(newRemark, { updatedUser: userHistory }));
              }
            }
          }
        }

        if (incidentRemarkCreate.length > 0) {
          const preparedIncidentRemarkCreate: IncidentInvestigationRemark[] = [];
          for (const [index, item] of incidentRemarkCreate.entries()) {
            preparedIncidentRemarkCreate.push(
              Object.assign(item, {
                createdUser: userHistory,
                createdAt: new Date(moment().unix() * 1000 - index),
              }),
            );
          }
          await manager.save(IncidentInvestigationRemark, incidentRemarkCreate);
        }
        if (listFinalRemarkUpdate.length > 0) {
          await manager.save(IncidentInvestigationRemark, listFinalRemarkUpdate);
        }
        if (Array.from(listIncidentRemarkDeleteIds).length > 0) {
          await manager.delete(IncidentInvestigationRemark, {
            id: In(Array.from(listIncidentRemarkDeleteIds)),
          });
        }
      });

      return await this.connection.transaction(async (manager) => {
        if (typeIds) {
          const incidentMasterLists = await manager.find(IncidentMaster, {
            where: { id: In(typeIds), deleted: false, companyId: user.companyId },
            select: ['id'],
          });
          Object.assign(incidentInvestigationObjUpdate, {
            typeIncidents: incidentMasterLists as IncidentMaster[],
          });
        }

        if (subTypeIds) {
          const subTypeLists = await manager.find(SubIncidentType, {
            where: { id: In(subTypeIds), deleted: false, companyId: user.companyId },
            select: ['id'],
          });
          Object.assign(incidentInvestigationObjUpdate, {
            subIncidents: subTypeLists as SubIncidentType[],
          });
        }

        if (secondaryIncidents) {
          const secondaryIncidentMasterLists = await manager.find(IncidentMaster, {
            where: { id: In(secondaryIncidents), deleted: false, companyId: user.companyId },
            select: ['id'],
          });
          Object.assign(incidentInvestigationObjUpdate, {
            secondaryIncidents: secondaryIncidentMasterLists as IncidentMaster[],
          });
        }

        if (secondarySubIncidents) {
          const secondarySubIncidentMasterLists = await manager.find(SubIncidentType, {
            where: { id: In(secondarySubIncidents), deleted: false, companyId: user.companyId },
            select: ['id'],
          });
          Object.assign(incidentInvestigationObjUpdate, {
            secondarySubIncidents: secondarySubIncidentMasterLists as SubIncidentType[],
          });
        }
        // extract month and year
        const monthList = Object.keys(SelfAssessmentMonthEnum).map(
          (key) => SelfAssessmentMonthEnum[key],
        );
        const dateTimeOfIncidentMonth = new Date(body.dateTimeOfIncident).getMonth();
        const finalDateTimeOfIncidentMonth = monthList[dateTimeOfIncidentMonth];
        const dateTimeOfIncidentYear = new Date(body.dateTimeOfIncident).getFullYear();

        const updateIncidentInvestigation = await manager.save(IncidentInvestigation, {
          ...incidentInvestigationObjUpdate,
          id: incidentInvestigationId,
          updatedUserId: user.id,
          updatedUser: userHistory,
          dateTimeOfIncident_Month: finalDateTimeOfIncidentMonth,
          dateTimeOfIncident_Year: dateTimeOfIncidentYear,
          // Add rightShipIncidentId and set incidentOrigin if provided
          ...(body.rightShipIncidentId !== undefined && { 
            rightShipIncidentId: body.rightShipIncidentId,
            ...(body.rightShipIncidentId && { incidentOrigin: IncidentOriginEnum.RIGHT_SHIP }) // Only set origin if rightShipIncidentId is not null/empty
          }),
          // Add risk matrix data if provided
          ...(processedRiskMatrixData && {
            riskMatrixId: processedRiskMatrixData.riskMatrixId,
            potentialRiskCellPosition: processedRiskMatrixData.potentialRiskCellPosition,
            potentialRiskValueMappingId: processedRiskMatrixData.potentialRiskValueMappingId,
            potentialPriorityMasterId: processedRiskMatrixData.potentialPriorityMasterId,
            observedRiskCellPosition: processedRiskMatrixData.observedRiskCellPosition,
            observedRiskValueMappingId: processedRiskMatrixData.observedRiskValueMappingId,
            observedPriorityMasterId: processedRiskMatrixData.observedPriorityMasterId,
            finalRiskCellPosition: processedRiskMatrixData.finalRiskCellPosition,
            finalRiskValueMappingId: processedRiskMatrixData.finalRiskValueMappingId,
            finalPriorityMasterId: processedRiskMatrixData.finalPriorityMasterId,
          }),
        });

        // trigger;
        //get vessel id
        const incidentInvestigation = await this.findOne({
          where: { id: incidentInvestigationId },
          select: ['vesselId', 'observedRisk'],
        });

        await manager
          .getCustomRepository(VesselScreeningSummaryRepository)
          .updateVesselScreeningSummaryByRef(
            manager,
            VesselScreeningSummaryReferenceEnum.INCIDENTS,
            null,
            incidentInvestigation.vesselId,
            user.companyId,
            isChangeRiskValue(incidentInvestigation.observedRisk, body.observedRisk, true),
          );
        // workflow
        if (body.userAssignment) {
          // Update user assignment
          await manager
            .getCustomRepository(UserAssignmentRepository)
            .updateUserAssignment(
              manager,
              ModuleType.INCIDENTS,
              incidentInvestigationId,
              body.userAssignment.usersPermissions,
            );
        }

        // Delete existing main categories, subcategories and second subcategories, then create new ones
        await manager.delete(IncidentMainCategory, {
          incidentInvestigationId: incidentInvestigationId,
        });
        // Handle causemainmapping storage - JIRA ticket INAT-4129
        if (incidentMainCategory && incidentMainCategory?.length > 0) {
          // Extract causeMappingId from the first main category to update the incident investigation
          const firstMainCategory = incidentMainCategory[0];
          if (firstMainCategory?.causeMappingId) {
            await manager.update(IncidentInvestigation, incidentInvestigationId, {
              causeMappingId: firstMainCategory.causeMappingId,
              updatedUserId: user.id,
              updatedUser: userHistory,
            });
          }

          // Process each main category
          for (const mainCategory of incidentMainCategory) {
            if (mainCategory?.mainCategoryId && mainCategory?.mainCategoryName) {
              // Save main category
              const savedMainCategory = await manager.save(IncidentMainCategory, {
                mainCategoryId: mainCategory?.mainCategoryId,
                mainCategoryName: mainCategory?.mainCategoryName,
                causeType: mainCategory?.causeType || '',
                causeMappingId: mainCategory?.causeMappingId, // Using same ID as mapping reference
                incidentInvestigationId: incidentInvestigationId,
                companyId: user?.companyId,
                createdUserId: user?.id,
                updatedUserId: user?.id,
              });

              // Process subcategories if they exist

              if (
                mainCategory?.incidentSubCategory &&
                mainCategory?.incidentSubCategory?.length > 0
              ) {
                for (const subCategory of mainCategory?.incidentSubCategory) {
                  if (subCategory?.subCategoryId && subCategory?.subCategoryName) {
                    const savedSubCategory = await manager.save(IncidentSubCategory, {
                      subCategoryId: subCategory?.subCategoryId,
                      subCategoryName: subCategory?.subCategoryName,
                      incidentMainCategoryId: savedMainCategory?.id,
                      companyId: user?.companyId,
                      createdUserId: user?.id,
                      updatedUserId: user?.id,
                    });
                    console.log('savedSubCategory', savedSubCategory);
                    // Process second subcategories nested under this subcategory
                    if (
                      subCategory?.incidentSecondSubCategory &&
                      subCategory?.incidentSecondSubCategory?.length > 0
                    ) {
                      for (const secondSubCategory of subCategory?.incidentSecondSubCategory) {
                        if (
                          secondSubCategory?.secSubCategoryId &&
                          secondSubCategory?.secSubCategoryName
                        ) {
                          await manager.save(IncidentSecondSubCategory, {
                            secSubCategoryId: secondSubCategory?.secSubCategoryId,
                            secSubCategoryName: secondSubCategory?.secSubCategoryName,
                            incidentSubCategoryId: savedSubCategory?.id,
                            companyId: user?.companyId,
                            createdUserId: user?.id,
                            updatedUserId: user?.id,
                          });
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }

        if (body.status) {
          if (updateIncidentInvestigation.status !== incidentInvestigationFound.status) {
            await manager.insert(IncidentInvestigationHistory, {
              incidentInvestigationId: incidentInvestigationId,
              remark: body.remark,
              status: updateIncidentInvestigation.status,
              createdUser: userHistory,
            });
          }
          // send notification and email
          if (
            incidentInvestigationFound.status === IncidentInvestigationStatus.DRAFT &&
            body.status === IncidentInvestigationStatus.SUBMITTED
          ) {
            let listReceiverNoti = [];
            const listUserAssignment = await manager
              .getCustomRepository(UserAssignmentRepository)
              .listByModule(ModuleType.INCIDENTS, incidentInvestigationFound.id);
            listReceiverNoti = listReceiverNoti.concat(
              listUserAssignment[WorkflowPermission.REVIEWER],
            );
            const performer = await manager
              .getCustomRepository(UserRepository)
              .getUserDetailAndSelect(user.id, ['id', 'username', 'jobTitle']);
            dataNoti.push({
              receivers: listReceiverNoti as IUser[],
              module: ModuleType.INCIDENTS,
              recordId: incidentInvestigationFound.id,
              recordRef: incidentInvestigationFound.sNo,
              type: PushTypeEnum.UPDATE_RECORD,
              currentStatus: IncidentInvestigationStatus.SUBMITTED,
              previousStatus: IncidentInvestigationStatus.DRAFT,
              performer: performer,
              executedAt: new Date(),
            });
            for (const receiver of listReceiverNoti) {
              dataSendMail.push({
                receiver: receiver as IUserEmail,
                type: EmailTypeEnum.UPDATE_RECORD,
                templateKey: MailTemplate.CHANGE_RECORD_STATUS,
                subject: '[Notification] Change status in a record',
                data: {
                  username: receiver.username,
                  baseURL: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.ADMIN_PAGE,
                  recordRef: incidentInvestigationFound.sNo,
                  recordId: incidentInvestigationFound.id,
                  path: ModulePathEnum.INCIDENTS,
                  currentStatus: IncidentInvestigationStatus.SUBMITTED,
                  previousStatus: IncidentInvestigationStatus.DRAFT,
                  performer: performer,
                  executedAt: new Date(),
                },
              });
            }
          }
          if (
            incidentInvestigationFound.status === IncidentInvestigationStatus.SUBMITTED &&
            body.status === IncidentInvestigationStatus.REVIEWED
          ) {
            const listReceiverNoti = await manager
              .getCustomRepository(UserRepository)
              .listByIds([incidentInvestigationFound.createdUserId]);

            // const listUserAssignment = await manager
            //   .getCustomRepository(UserAssignmentRepository)
            //   .listByModule(ModuleType.INCIDENTS, incidentInvestigationFound.id);

            // listReceiverNoti = listReceiverNoti.concat(
            //   listUserAssignment[WorkflowPermission.REVIEWER],
            // );
            const performer = await manager
              .getCustomRepository(UserRepository)
              .getUserDetailAndSelect(user.id, ['id', 'username', 'jobTitle']);
            dataNoti.push({
              receivers: listReceiverNoti as IUser[],
              module: ModuleType.INCIDENTS,
              recordId: incidentInvestigationFound.id,
              recordRef: incidentInvestigationFound.sNo,
              type: PushTypeEnum.UPDATE_RECORD,
              currentStatus: IncidentInvestigationStatus.REVIEWED,
              previousStatus: IncidentInvestigationStatus.SUBMITTED,
              performer: performer,
              executedAt: new Date(),
            });
            for (const receiver of listReceiverNoti) {
              dataSendMail.push({
                receiver: receiver as IUserEmail,
                type: EmailTypeEnum.UPDATE_RECORD,
                templateKey: MailTemplate.CHANGE_RECORD_STATUS,
                subject: '[Notification] Change status in a record',
                data: {
                  username: receiver.username,
                  baseURL: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.ADMIN_PAGE,
                  recordRef: incidentInvestigationFound.sNo,
                  recordId: incidentInvestigationFound.id,
                  path: ModulePathEnum.INCIDENTS,
                  currentStatus: IncidentInvestigationStatus.REVIEWED,
                  previousStatus: IncidentInvestigationStatus.SUBMITTED,
                  performer: performer,
                  executedAt: new Date(),
                },
              });
            }
          }
          if (
            incidentInvestigationFound.status === IncidentInvestigationStatus.SUBMITTED &&
            body.status === IncidentInvestigationStatus.REASSIGN
          ) {
            const listReceiverNoti = await manager
              .getCustomRepository(UserRepository)
              .listByIds([incidentInvestigationFound.createdUserId]);
            const performer = await manager
              .getCustomRepository(UserRepository)
              .getUserDetailAndSelect(user.id, ['id', 'username', 'jobTitle']);
            dataNoti.push({
              receivers: listReceiverNoti as IUser[],
              module: ModuleType.INCIDENTS,
              recordId: incidentInvestigationFound.id,
              recordRef: incidentInvestigationFound.sNo,
              type: PushTypeEnum.UPDATE_RECORD,
              currentStatus: IncidentInvestigationStatus.REASSIGN,
              previousStatus: IncidentInvestigationStatus.SUBMITTED,
              performer: performer,
              executedAt: new Date(),
            });
            for (const receiver of listReceiverNoti) {
              dataSendMail.push({
                receiver: receiver as IUserEmail,
                type: EmailTypeEnum.UPDATE_RECORD,
                templateKey: MailTemplate.CHANGE_RECORD_STATUS,
                subject: '[Notification] Change status in a record',
                data: {
                  username: receiver.username,
                  baseURL: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.ADMIN_PAGE,
                  recordRef: incidentInvestigationFound.sNo,
                  recordId: incidentInvestigationFound.id,
                  path: ModulePathEnum.INCIDENTS,
                  currentStatus: IncidentInvestigationStatus.REASSIGN,
                  previousStatus: IncidentInvestigationStatus.SUBMITTED,
                  performer: performer,
                  executedAt: new Date(),
                },
              });
            }
          }
          if (
            incidentInvestigationFound.status === IncidentInvestigationStatus.REASSIGN &&
            body.status === IncidentInvestigationStatus.SUBMITTED
          ) {
            let listReceiverNoti = [];
            const listUserAssignment = await manager
              .getCustomRepository(UserAssignmentRepository)
              .listByModule(ModuleType.INCIDENTS, incidentInvestigationFound.id);
            listReceiverNoti = listReceiverNoti.concat(
              listUserAssignment[WorkflowPermission.REVIEWER],
            );
            const performer = await manager
              .getCustomRepository(UserRepository)
              .getUserDetailAndSelect(user.id, ['id', 'username', 'jobTitle']);
            dataNoti.push({
              receivers: listReceiverNoti as IUser[],
              module: ModuleType.INCIDENTS,
              recordId: incidentInvestigationFound.id,
              recordRef: incidentInvestigationFound.sNo,
              type: PushTypeEnum.UPDATE_RECORD,
              currentStatus: IncidentInvestigationStatus.SUBMITTED,
              previousStatus: IncidentInvestigationStatus.REASSIGN,
              performer: performer,
              executedAt: new Date(),
            });
            for (const receiver of listReceiverNoti) {
              dataSendMail.push({
                receiver: receiver as IUserEmail,
                type: EmailTypeEnum.UPDATE_RECORD,
                templateKey: MailTemplate.CHANGE_RECORD_STATUS,
                subject: '[Notification] Change status in a record',
                data: {
                  username: receiver.username,
                  baseURL: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.ADMIN_PAGE,
                  recordRef: incidentInvestigationFound.sNo,
                  recordId: incidentInvestigationFound.id,
                  path: ModulePathEnum.INCIDENTS,
                  currentStatus: IncidentInvestigationStatus.SUBMITTED,
                  previousStatus: IncidentInvestigationStatus.REASSIGN,
                  performer: performer,
                  executedAt: new Date(),
                },
              });
            }
          }
          if (
            incidentInvestigationFound.status === IncidentInvestigationStatus.REVIEWED &&
            body.status === IncidentInvestigationStatus.CLOSEOUT
          ) {
            let listReceiverNoti = [];
            const listUserAssignment = await manager
              .getCustomRepository(UserAssignmentRepository)
              .listByModule(ModuleType.INCIDENTS, incidentInvestigationFound.id);
            listReceiverNoti = listReceiverNoti.concat(
              listUserAssignment[WorkflowPermission.CREATOR],
            );
            const performer = await manager
              .getCustomRepository(UserRepository)
              .getUserDetailAndSelect(user.id, ['id', 'username', 'jobTitle']);
            dataNoti.push({
              receivers: listReceiverNoti as IUser[],
              module: ModuleType.INCIDENTS,
              recordId: incidentInvestigationFound.id,
              recordRef: incidentInvestigationFound.sNo,
              type: PushTypeEnum.UPDATE_RECORD,
              currentStatus: IncidentInvestigationStatus.CLOSEOUT,
              previousStatus: IncidentInvestigationStatus.REVIEWED,
              performer: performer,
              executedAt: new Date(),
            });
            for (const receiver of listReceiverNoti) {
              dataSendMail.push({
                receiver: receiver as IUserEmail,
                type: EmailTypeEnum.UPDATE_RECORD,
                templateKey: MailTemplate.CHANGE_RECORD_STATUS,
                subject: '[Notification] Change status in a record',
                data: {
                  username: receiver.username,
                  baseURL: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.ADMIN_PAGE,
                  recordRef: incidentInvestigationFound.sNo,
                  recordId: incidentInvestigationFound.id,
                  path: ModulePathEnum.INCIDENTS,
                  currentStatus: IncidentInvestigationStatus.CLOSEOUT,
                  previousStatus: IncidentInvestigationStatus.REVIEWED,
                  performer: performer,
                  executedAt: new Date(),
                },
              });
            }
          }
        }

        return { dataNoti, dataSendMail, updateIncidentInvestigation };
      });
    } catch (ex) {
      LoggerCommon.error(
        '[IncidentInvestigation] updateIncidentInvestigation error ',
        ex.message || ex,
      );
      throw ex;
    }
  }

  async listAndAggregateIncidentInvestigation(
    query: ListIncidentInvestigationRequestDto,
    token: TokenPayloadModel,
    vesselScreeningId: string,
  ) {
    const vesselScreening = await this.manager.findOne(VesselScreening, {
      where: {
        id: vesselScreeningId,
        deleted: false,
      },
      select: ['id', 'vesselId', 'createdAt'],
    });

    if (!vesselScreening) {
      throw new BaseError({
        status: 404,
        message: 'incidentInvestigation.VESSEL_SCREENING_NOT_FOUND',
      });
    }
    const queryBuilder = this.createQueryBuilder('incidentInvestigation')
      .leftJoinAndSelect(
        'incidentInvestigation.incidentInvestigationRequests',
        'incidentInvestigationRequest',
        'incidentInvestigationRequest.vesselScreeningId = :vesselScreeningId',
        { vesselScreeningId },
      )
      .leftJoin('incidentInvestigation.typeIncidents', 'typeIncidents')
      .leftJoinAndSelect(
        'incidentInvestigationRequest.incidentRequestComments',
        'incidentRequestComment',
      )
      .where(
        'incidentInvestigation.companyId = :companyId and incidentInvestigation.vesselId = :vesselId',
        {
          companyId: token.companyId,
          vesselId: vesselScreening.vesselId,
        },
      )
      .select()
      .addSelect(['typeIncidents.id', 'typeIncidents.code', 'typeIncidents.name']);

    if (query.createdAtFrom) {
      queryBuilder.andWhere('incidentInvestigation.createdAt >= :createdAtFrom', {
        createdAtFrom: query.createdAtFrom,
      });
    }
    if (query.createdAtTo) {
      queryBuilder.andWhere('incidentInvestigation.createdAt <= :createdAtTo', {
        createdAtTo: query.createdAtTo,
      });
    }

    const queryBuilderCount = this.createQueryBuilder('incidentInvestigation')
      .leftJoin(
        'incidentInvestigation.incidentInvestigationRequests',
        'incidentInvestigationRequest',
        'incidentInvestigationRequest.vesselScreeningId = :vesselScreeningId',
        { vesselScreeningId },
      )
      .where(
        'incidentInvestigation.companyId = :companyId and incidentInvestigation.vesselId = :vesselId and incidentInvestigation.deleted = false',
        {
          companyId: token.companyId,
          vesselId: vesselScreening.vesselId,
        },
      );
    if (query.filterRisk && query.filterRisk === VesselScreeningFilterRiskEnum.OBSERVED) {
      queryBuilderCount
        .select(['incidentInvestigationRequest.observedRisk as risk', 'count(*) as count'])
        .groupBy('incidentInvestigationRequest.observedRisk');
    } else if (query.filterRisk && query.filterRisk === VesselScreeningFilterRiskEnum.FINAL) {
      queryBuilderCount
        .select(['incidentInvestigationRequest.finalRisk as risk', 'count(*) as count'])
        .groupBy('incidentInvestigationRequest.finalRisk');
    } else {
      queryBuilderCount
        .select(['incidentInvestigationRequest.potentialRisk as risk', 'count(*) as count'])
        .groupBy('incidentInvestigationRequest.potentialRisk');
    }

    const [incidentInvestigations, groupRisk] = await Promise.all([
      this.list(
        {
          page: query.page,
          limit: query.pageSize,
        },
        {
          queryBuilder,
          sort: query.sort || 'incidentInvestigation.createdAt:-1',
        },
      ),
      queryBuilderCount.getRawMany(),
    ]);
    const vesselScreeningSummary = await this.manager.findOne(VesselScreeningSummary, {
      where: {
        vesselScreeningId: vesselScreeningId,
        reference: VesselScreeningSummaryReferenceEnum.INCIDENTS,
      },
    });
    return { vesselScreeningSummary, list: incidentInvestigations, risk: groupRisk };
  }
  async checkUserCanUpdateRecord(userId: string, incidentInvestigationId: string) {
    const userAssignments = await this.connection
      .getCustomRepository(UserAssignmentRepository)
      .find({
        where: {
          incidentInvestigationId,
          userId,
        },
      });

    return userAssignments.length > 0 ? true : false;
  }

  async listIncidentsMapView(
    query: ListIncidentsMapViewQueryDTO,
    user: TokenPayloadModel,
    isRestricted: boolean,
  ) {
    const queryBuilder = this.createQueryBuilder('incidentInvestigation')
      .leftJoin('incidentInvestigation.vessel', 'vessel')
      .leftJoin('vessel.vesselType', 'vesselType')
      .leftJoin('vessel.vesselDocHolders', 'vesselDocHolders')
      .leftJoin('vesselDocHolders.company', 'companyDocHolder')
      .leftJoin('incidentInvestigation.port', 'port')
      .leftJoin('incidentInvestigation.portTo', 'portTo')
      .leftJoin('incidentInvestigation.typeIncidents', 'typeIncidents')
      .leftJoin('incidentInvestigation.userAssignments', 'userAssignments')
      .leftJoin('userAssignments.user', 'user')
      .where(
        '(incidentInvestigation.deleted = :deleted and incidentInvestigation.companyId = :companyId)',
        {
          deleted: false,
          companyId: user.companyId,
        },
      )
      .select()
      .addSelect([
        'vessel.id',
        'vessel.name',
        'vessel.image',
        'vesselType.name',
        'vesselType.icon',
        'vesselDocHolders.id',
        'companyDocHolder.name',
        'port.id',
        'port.code',
        'port.name',
        'port.longitude',
        'port.latitude',
        'port.gmtOffset',
        'port.geoLocation',
        'port.country',
        'portTo.id',
        'portTo.code',
        'portTo.name',
        'portTo.longitude',
        'portTo.latitude',
        'portTo.country',
        'portTo.geoLocation',
        'typeIncidents.id',
        'typeIncidents.name',
      ]);

    if (!RoleScopeCheck.isAdmin(user)) {
      const { whereForExternal, whereForMainAndInternal } = await _supportWhereDOCChartererOwner(
        this.manager,
        user.explicitCompanyId,
        'incidentInvestigation',
      );

      queryBuilder
        .leftJoin('vessel.divisionMapping', 'divisionMapping')
        .leftJoin('vessel.vesselCharterers', 'vesselCharterers')
        .leftJoin('vessel.vesselOwners', 'vesselOwners')
        .leftJoin('divisionMapping.division', 'division')
        .leftJoin('division.users', 'users')
        .addSelect([
          'vessel.id',
          'vessel.name',
          'vesselCharterers.id',
          'vesselCharterers.companyId',
          'vesselCharterers.fromDate',
          'vesselCharterers.toDate',
          'vesselCharterers.responsiblePartyInspection',
          'vesselCharterers.responsiblePartyQA',
          'vesselCharterers.status',
          'vesselOwners.id',
          'vesselOwners.companyId',
          'vesselOwners.fromDate',
          'vesselOwners.toDate',
          'vesselOwners.responsiblePartyInspection',
          'vesselOwners.responsiblePartyQA',
          'vesselOwners.status',
          'vesselDocHolders.id',
          'vesselDocHolders.companyId',
          'vesselDocHolders.fromDate',
          'vesselDocHolders.toDate',
          'vesselDocHolders.responsiblePartyInspection',
          'vesselDocHolders.responsiblePartyQA',
          'vesselDocHolders.status',
        ]);

      if (
        user.companyLevel === CompanyLevelEnum.MAIN_COMPANY ||
        user.companyLevel === CompanyLevelEnum.INTERNAL_COMPANY
      ) {
        queryBuilder.andWhere(`( users.id = :userId ` + whereForMainAndInternal + ')', {
          userId: user.id,
        });
      } else {
        queryBuilder.andWhere(`( users.id = :userId ` + whereForExternal + ')', {
          userId: user.id,
        });
      }
    }

    if (isRestricted && !RoleScopeCheck.isAdmin(user)) {
      queryBuilder.andWhere(
        '(incidentInvestigation.createdUserId = :createdUserId OR userAssignments.userId = :userAssignmentId)',
        {
          createdUserId: user.id,
          userAssignmentId: user.id,
        },
      );
    }

    if (query.vesselIds) {
      queryBuilder.andWhere('(vessel.id IN (:...vesselIds))', {
        vesselIds: query.vesselIds,
      });
    }

    if (query.atPort) {
      queryBuilder.andWhere('incidentInvestigation.atPort = :atPort', {
        atPort: query.atPort,
      });
    }

    if (query.portIds) {
      queryBuilder.andWhere('(port.id IN (:...portIds))', {
        portIds: query.portIds,
      });
    }

    if (query.circleRange) {
      const centerPointStr = generatePointString(
        query.circleRange.longitude,
        query.circleRange.latitude,
      );
      const radius = query.circleRange?.radius || 13589000; // 13589000 is like earth diameter
      queryBuilder.andWhere(
        `(
          ST_DWithin("port"."geoLocation", '${centerPointStr}'::geography,  ${radius}) OR
          ST_DWithin("portTo"."geoLocation", '${centerPointStr}'::geography, ${radius})
        )`,
      );
    }

    if (query.fromDate) {
      queryBuilder.andWhere('incidentInvestigation.dateTimeOfIncident >= :fromDate', {
        fromDate: new Date(query.fromDate),
      });
    }

    if (query.toDate) {
      queryBuilder.andWhere('incidentInvestigation.dateTimeOfIncident <= :toDate', {
        toDate: new Date(query.toDate),
      });
    }

    if (query.typeIncidentIds) {
      queryBuilder.andWhere('typeIncidents.id IN (:...typeIncidentIds)', {
        typeIncidentIds: query.typeIncidentIds,
      });
    }

    const list: any = await this.list(
      {
        page: query.page,
        limit: query.pageSize,
      },
      {
        queryBuilder,
        sort: query.sort || 'incidentInvestigation.createdAt:-1',
        advanceConditions: {
          createdAtFrom: query.createdAtFrom,
          createdAtTo: query.createdAtTo,
        },
      },
    );

    return list;
  }

  async _migrateIncidentAddCreatorForUserAssignment() {
    const metaConfig = await this.connection
      .getRepository(MetaConfig)
      .createQueryBuilder('meta_config')
      .where('meta_config.key = :key', {
        key: CatalogConst.MIGRATE_INCIDENT_USER_ASSIGNMENT,
      })
      .getOne();
    if (!metaConfig) {
      return await this.connection.transaction(async (managerTrans) => {
        //get incident have creator in userasignment
        const incidentHaveCreator = await managerTrans
          .getCustomRepository(UserAssignmentRepository)
          ._getInciedentHaveCreator();

        //get incident dont have creator in userasignment
        const queryBuilder = managerTrans
          .createQueryBuilder(IncidentInvestigation, 'incident')
          .where('incident.deleted = FALSE AND incident.createdUserId IS NOT NULL ');
        if (incidentHaveCreator.length) {
          queryBuilder.andWhere('incident.id NOT IN (:...incidentIds)', {
            incidentIds: incidentHaveCreator,
          });
        }
        const incidentMissCreator = await queryBuilder.getMany();

        //insert for missing

        if (incidentMissCreator.length) {
          const preparedData = [];
          for (const incident of incidentMissCreator) {
            const userAssignment = {
              incidentInvestigationId: incident.id,
              userId: incident.createdUserId,
              permission: WorkflowPermission.CREATOR,
              isView: true,
            };

            preparedData.push(userAssignment);
          }
          await managerTrans.getCustomRepository(UserAssignmentRepository).save(preparedData);
        }
        await managerTrans.save(MetaConfig, {
          key: CatalogConst.MIGRATE_INCIDENT_USER_ASSIGNMENT,
          lastTimeSync: '2023-10-26T16:20:00.000z',
        });
      });
    }
    return 1;
  }

  async _validateIncidentForUpdate(
    body: UpdateIncidentInvestigationDto,
    user: TokenPayloadModel,
    entity: IncidentInvestigation,
  ) {
    //check active vessel
    await this.manager
      .getCustomRepository(VesselRepository)
      ._compareAndCheckActiveVessel(entity.vesselId, body.vesselId, user);

    // check active incident type
    const currentTypeIds = entity.typeIncidents.map((type) => type.id);
    const newTypeIds = body.typeIds.filter((typeId) => !currentTypeIds.includes(typeId));
    await this.manager
      .getCustomRepository(IncidentMasterRepository)
      .checkActiveIncident(newTypeIds, user);
  }

  async _validateIncidentForCreate(body: CreateIncidentInvestigationDto, user: TokenPayloadModel) {
    //check active vessel
    await this.manager
      .getCustomRepository(VesselRepository)
      ._checkActiveVessel([body.vesselId], user);
    //check active incident type
    await this.manager
      .getCustomRepository(IncidentMasterRepository)
      .checkActiveIncident(body.typeIds, user);
  }
}
