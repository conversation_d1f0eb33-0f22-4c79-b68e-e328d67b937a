import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  IsNotEmpty,
  IsOptional,
  ValidateNested,
  IsArray,
  IsBoolean,
  IsEnum,
  IsUUID,
  IsDateString,
  MinLength,
  MaxLength,
  IsInt,
  ArrayMinSize,
  IsIn,
  Min,
  IsNumber,
  ValidateIf,
} from 'class-validator';
import { IsLatitudeDMS, IsLongitudeDMS } from 'svm-nest-lib-v3';
import {
  ExternalInspectionReportStatusEnum,
  IncidentInvestigationIncidentStatusEnum,
  IncidentInvestigationReviewStatus,
  IncidentInvestigationVesselAcceptableEnum,
  VesselScreeningObservedRiskEnum,
  VesselScreeningPotentialRiskEnum,
  VesselScreeningFinalRiskEnum,
} from '../../../commons/enums';
import { IncidentInvestigationCommentDTO } from './incident-investigation-comment.dto';
import { CreateUserAssignmentDTO } from '../../../modules/user-assignment/dto';
import {
  IncidentInvestigationStatus,
  ScoreStateEnum,
} from '../entity/incident-investigation.entity';
import { decryptAttachmentValues } from '../../../commons/functions';
import { CreateInjuryDto } from 'src/modules-qa/injury/dto/create-injury.dto';
import { RiskMatrixDataDto } from './risk-matrix-data.dto';

export class CreateIncidentInvestigationReviewDto {
  @ApiProperty()
  @IsOptional()
  @IsUUID('all')
  id: string;

  @ApiProperty({ type: 'string' })
  @IsNotEmpty()
  @MaxLength(2000)
  remark: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsUUID('all')
  riskFactorId: string;

  @ApiProperty({ type: 'string', enum: IncidentInvestigationVesselAcceptableEnum })
  @IsNotEmpty()
  @IsEnum(IncidentInvestigationVesselAcceptableEnum)
  vesselAcceptable: string;

  @ApiProperty({ type: 'string', enum: IncidentInvestigationIncidentStatusEnum })
  @IsNotEmpty()
  @IsEnum(IncidentInvestigationIncidentStatusEnum)
  incidentStatus: string;

  @ApiProperty({ type: [String], required: false })
  @IsOptional()
  @IsArray()
  @Transform((value: string[]) => {
    const newValue = decryptAttachmentValues(value);
    return newValue;
  })
  attachments: string[] = [];
}

export class CreateIncidentInvestigationRemarkDto {
  @ApiProperty()
  @IsOptional()
  @IsUUID('all')
  id: string;
  @ApiProperty({ type: 'string' })
  @IsNotEmpty()
  remark: string;
}

export class CreateIncidentInvestigationDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsUUID('all')
  vesselId: string;

  @ApiProperty({ type: 'string' })
  @IsNotEmpty()
  @MaxLength(5000)
  description: string;

  @ApiProperty({ type: 'string' })
  @IsNotEmpty()
  @MaxLength(128)
  title: string;

  @ApiProperty({ type: 'string' })
  @IsOptional()
  @MaxLength(128)
  voyageNo: string;

  @ApiProperty({
    type: 'number',
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  totalNumberOfCrew: number;

  @ApiProperty({
    type: 'string',
    description: 'ISO string',
    example: '2011-10-05T14:48:00.000Z',
    required: true,
  })
  @IsNotEmpty()
  // @Transform((value: string) => value.substr(0, 10))
  @IsDateString({ strict: true })
  // @IsAfterCurrentDayTZ('timezone')
  dateTimeOfIncident: string;

  @ApiProperty({ type: [String] })
  @IsOptional()
  @ArrayMinSize(0)
  @IsUUID('all', { each: true })
  typeIds: string[];

  @ApiProperty({ type: [String] })
  @IsOptional()
  @ArrayMinSize(0)
  @IsUUID('all', { each: true })
  subTypeIds: string[];

  @ApiProperty({ type: [String] })
  @IsOptional()
  @ArrayMinSize(0)
  @IsUUID('all', { each: true })
  secondaryIncidents: string[];

  @ApiProperty({ type: [String] })
  @IsOptional()
  @ArrayMinSize(0)
  @IsUUID('all', { each: true })
  secondarySubIncidents: string[];

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @MaxLength(2000)
  otherType?: string;

  @ApiProperty({ type: Boolean, required: true })
  @IsNotEmpty()
  @Type(() => Boolean)
  @IsBoolean()
  atPort: boolean;

  @ApiProperty()
  @IsNotEmpty()
  @IsUUID('all')
  portId: string;

  @ApiProperty()
  @IsOptional()
  @IsUUID('all')
  portToId?: string;

  @ApiProperty({ type: 'string', example: `30°16'23.88''N`, required: false })
  @IsOptional()
  @IsLatitudeDMS()
  latitude?: string;

  @ApiProperty({ type: 'string', example: `130°16'23.88''E`, required: false })
  @IsOptional()
  @IsLongitudeDMS()
  longitude?: string;

  @ApiProperty({ type: 'string', required: false })
  @MaxLength(2000)
  @IsOptional()
  typeOfLoss?: string;

  @ApiProperty({ type: 'string', required: false })
  @MaxLength(2000)
  @IsOptional()
  immediateDirectCause?: string;

  @ApiProperty({ type: 'string', required: false })
  @MaxLength(2000)
  @IsOptional()
  basicUnderlyingCauses: string;

  @ApiProperty({ type: 'string', required: false })
  @MaxLength(2000)
  @IsOptional()
  rootCause: string;

  @ApiProperty({ type: 'string', required: false })
  @MaxLength(2000)
  @IsOptional()
  contributionFactor: string;

  @ApiProperty({ type: 'string', required: false })
  @MaxLength(2000)
  @IsOptional()
  nonContributionFactor: string;

  @ApiProperty({ type: 'string', required: false })
  @MaxLength(2000)
  @IsOptional()
  immediateAction: string;

  @ApiProperty({ type: 'string', required: false })
  @MaxLength(2000)
  @IsOptional()
  preventiveAction: string;

  @ApiProperty({ type: 'string', required: false })
  @MaxLength(2000)
  @IsOptional()
  correctionAction: string;

  @ApiProperty({ type: 'string', required: false })
  @MaxLength(2000)
  @IsOptional()
  actionControlNeeds: string;

  @Type(() => CreateIncidentInvestigationRemarkDto)
  @ApiProperty({
    type: [CreateIncidentInvestigationRemarkDto],
    description: 'Incident investigation remark info',
  })
  @ValidateNested({ each: true })
  @IsOptional()
  remarks?: CreateIncidentInvestigationRemarkDto[];

  @Type(() => CreateIncidentInvestigationReviewDto)
  @ApiProperty({
    type: [CreateIncidentInvestigationReviewDto],
    description: 'Incident investigation review info',
  })
  @ValidateNested({ each: true })
  @IsOptional()
  reviews?: CreateIncidentInvestigationReviewDto[];

  @ApiProperty({ type: 'string', enum: IncidentInvestigationReviewStatus })
  @IsEnum(IncidentInvestigationReviewStatus)
  @IsOptional()
  reviewStatus?: string;

  @ApiProperty({ type: 'number', required: false })
  @IsOptional()
  @IsInt()
  @IsIn(Object.values(VesselScreeningPotentialRiskEnum))
  public potentialRisk?: VesselScreeningPotentialRiskEnum;

  @ApiProperty({
    type: 'number',
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  potentialScore?: number;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @IsEnum(ScoreStateEnum)
  potentialRiskState?: ScoreStateEnum;

  @ApiProperty({ type: 'number' })
  @IsOptional()
  @IsInt()
  @IsIn(Object.values(VesselScreeningObservedRiskEnum))
  public observedRisk?: VesselScreeningObservedRiskEnum;

  @ApiProperty({
    type: 'number',
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  observedScore?: number;

  @ApiProperty({ type: 'string', required: false }) 
  @IsOptional()
  @IsEnum(ScoreStateEnum)
  observedRiskState?: ScoreStateEnum;

  @ApiProperty({ type: 'number', required: false })
  @IsOptional()
  @IsInt()
  @IsIn(Object.values(VesselScreeningFinalRiskEnum))
  public finalRisk?: VesselScreeningFinalRiskEnum;

  @ApiProperty({
    type: 'number',
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  finalScore?: number;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @IsEnum(ScoreStateEnum)
  finalRiskState?: ScoreStateEnum;

  @ApiProperty({ type: Boolean, required: false })
  @IsOptional()
  @Type(() => Boolean)
  @IsBoolean()
  timeLoss?: boolean;

  @ApiProperty({ type: [IncidentInvestigationCommentDTO], required: false })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => IncidentInvestigationCommentDTO)
  @IsArray()
  @ArrayMinSize(1)
  comments?: IncidentInvestigationCommentDTO[];

  @ApiProperty({ type: [String], required: false })
  @IsOptional()
  @IsArray()
  @Transform((value: string[]) => {
    const newValue = decryptAttachmentValues(value);
    return newValue;
  })
  attachments: string[] = [];

  @ApiProperty({ type: CreateUserAssignmentDTO, required: false })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateUserAssignmentDTO)
  userAssignment?: CreateUserAssignmentDTO;

  @ApiProperty({ type: 'string', required: false })
  @MaxLength(128)
  @IsOptional()
  remark?: string;

  @ApiProperty({ type: 'string', enum: IncidentInvestigationStatus })
  @IsEnum(IncidentInvestigationStatus)
  status?: string;

  @ApiProperty({ type: CreateInjuryDto, required: false })
  @IsOptional()
  @Type(() => CreateInjuryDto)
  injuries?: CreateInjuryDto[];

  @ApiProperty({ type: 'string' })
  @IsOptional()
  @IsUUID()
  voyageTypeId: string;

  @ApiProperty({ 
    type: Number, 
    description: 'Number of fatalities (only applicable for injury incidents)', 
    required: false 
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0, { message: 'Fatalities cannot be negative' })
  @ValidateIf((o) => {
    // Only validate if the field is provided
    return o.fatalities !== undefined && o.fatalities !== null;
  })
  fatalities?: number;

  @ApiProperty({
    type: Number,
    description: 'Amount of pollution in cubic meters (m³)',
    required: false
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0, { message: 'Pollution amount cannot be negative' })
  @ValidateIf((o) => {
    // Only validate if the field is provided
    return o.pollution !== undefined && o.pollution !== null;
  })
  pollution?: number;

  @ApiProperty({
    type: RiskMatrixDataDto,
    required: false,
    description: 'Risk matrix assessment data for the incident'
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => RiskMatrixDataDto)
  riskMatrixData?: RiskMatrixDataDto;

  @ApiProperty({ type: 'string', required: false, enum: ['Yes', 'No'] })
  @IsOptional()
  @IsIn(['Yes', 'No'])
  doos?: string;

  @ApiProperty({
    type: String,
    description: 'Right Ship Incident ID for linking to external incident. When provided, incidentOrigin will be automatically set to "Right ship"',
    required: false
  })
  @IsOptional()
  @IsUUID('all')
  rightShipIncidentId?: string;
}
