import { ApiProperty } from '@nestjs/swagger';
import { ArrayUnique, IsArray, IsDateString, IsEnum, IsOptional, IsUUID } from 'class-validator';
import { IsGreaterThanOrEqual } from 'svm-nest-lib-v3';
import { ListQueryDto } from '../../../commons/dtos';
import { ModulePathEnum, VesselScreeningFilterRiskEnum } from '../../../commons/enums';
import { IncidentInvestigationStatus } from '../entity/incident-investigation.entity';
import { DataType, FilterField } from '../../../utils';
export class ListIncidentInvestigationDto extends ListQueryDto {
  @ApiProperty({ type: String, required: false })
  @IsOptional()
  @IsUUID('all')
  vesselId?: string;

  @ApiProperty({
    enum: VesselScreeningFilterRiskEnum,
    required: false,
    description: 'Status in common',
  })
  @IsOptional()
  @IsEnum(VesselScreeningFilterRiskEnum)
  filterRisk?: string; // common status

  @ApiProperty({
    enum: IncidentInvestigationStatus,
    required: false,
    description: 'Incident status',
  })
  @IsOptional()
  @IsEnum(IncidentInvestigationStatus)
  incidentStatus?: IncidentInvestigationStatus; // common status

  @ApiProperty({
    enum: ModulePathEnum,
    required: false,
    description: 'Status in common',
  })
  @IsOptional()
  @IsEnum(ModulePathEnum)
  module?: string;

  @IsOptional()
  @IsUUID('all', { each: true })
  @IsArray()
  @ArrayUnique()
  ids?: string[];

  @ApiProperty({
    type: 'string',
    description: 'ISO string',
    example: '2020-10-05T14:48:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsDateString({ strict: true })
  incidentDateFrom?: string;

  @ApiProperty({
    type: 'string',
    description: 'ISO string',
    example: '2023-10-05T14:48:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsDateString({ strict: true })
  @IsGreaterThanOrEqual('incidentDateFrom', { message: 'common.INVALID_DATE_RANGE' })
  incidentDateTo?: string;
}

export const INCIDENT_INVESTIGATION_FILTER_FIELDS: FilterField[] = [
  {
    field: 'vessel',
    column: '"vessel_name"',
    type: DataType.TEXT,
  },
  {
    field: 'company',
    column: '"vesselDocHolderCompany_name"',
    type: DataType.TEXT,
  },
  {
    field: 'typeOfIncident',
    column: '"typeOfIncident"',
    type: DataType.TEXT,
  },
  {
    field: 'description',
    column: '"incidentInvestigation_description"',
    type: DataType.TEXT,
  },
  {
    field: 'incidentDate',
    column: '"incidentInvestigation_dateTimeOfIncident"',
    type: DataType.DATE,
  },
  {
    field: 'status',
    column: '"incidentInvestigation_status"',
    type: DataType.TEXT,
  },
  {
    field: 'potentialRisk',
    column: '"potentialRiskValue"',
    type: DataType.TEXT,
  },
  {
    field: 'observedRisk',
    column: '"observedRiskValue"',
    type: DataType.TEXT,
  },
  {
    field: 'finalRisk',
    column: '"incidentInvestigation_finalRisk"',
    type: DataType.TEXT,
  },
  {
    field: 'finalScore',
    column: '"incidentInvestigation_finalScore"',
    type: DataType.TEXT,
  },
  {
    field: 'imoNumber',
    column: '"vessel_imoNumber"',
    type: DataType.TEXT,
  },
  {
    field: 'cimo',
    column: '"vesselDocHolderCompany_companyIMO"',
    type: DataType.TEXT,
  },
  {
    field: 'voyageNo',
    column: '"incidentInvestigation_voyageNo"',
    type: DataType.TEXT,
  },
  {
    field: 'timeLoss',
    column: '"timeLossValue"',
    type: DataType.TEXT,
  },
  {
    field: 'remark',
    column: '"remark"',
    type: DataType.TEXT,
  },
  {
    field: 'reviewStatus',
    column: '"incidentInvestigation_reviewStatus"',
    type: DataType.TEXT,
  },
  {
    field: 'createdDate',
    column: '"incidentInvestigation_createdAt"',
    type: DataType.DATE,
  },
  {
    field: 'createdByUser',
    column: `"incidentInvestigation_createdUser"->>'username'`,
    type: DataType.TEXT,
  },
  {
    field: 'updatedDate',
    column: '"incidentInvestigation_updatedAt"',
    type: DataType.DATE,
  },
  {
    field: 'updatedByUser',
    column: `"incidentInvestigation_updatedUser"->>'username'`,
    type: DataType.TEXT,
  },
  {
    field: 'refId',
    column: '"incidentInvestigation_refId"',
    type: DataType.TEXT,
  },
  {
    field: 'dateTimeOfIncident_Month',
    column: '"incidentInvestigation_dateTimeOfIncident_Month"',
    type: DataType.TEXT,
  },
  {
    field: 'dateTimeOfIncident_Year',
    column: '"incidentInvestigation_dateTimeOfIncident_Year"',
    type: DataType.TEXT,
  },
  {
    field: 'incidentOrigin',
    column: '"incidentInvestigation_incidentOrigin"',
    type: DataType.TEXT,
  },
  {
    field: 'voyageType',
    column: '"voyageType_name"',
    type: DataType.TEXT,
  },
  {
    field: 'doos',
    column: '"incidentInvestigation_doos"',
    type: DataType.TEXT,
  },
];

export enum TitleIncidentInvestigation {
  VESSEL = 'Vessel',
  COMPANY = 'Company',
  TYPE_OF_INCIDENT = 'Type of incident',
  DESCRIPTION = 'Description',
  INCIDENT_DATE = 'Incident Date',
  STATUS = 'Status',
  POTENTIAL_RISK = 'Potential Risk',
  OBSERVED_RISK = 'Observed Risk',
  FINAL_RISK = 'Final Risk',
  FINAL_SCORE = 'Final Score',
  IMO_NUMBER = 'IMO Number',
  CIMO = 'CIMO',
  VOYAGE_NO = 'Voyage no',
  TIME_LOSS = 'Time Loss',
  REMARK = 'Remark',
  REVIEW_STATUS = 'Review status',
  CREATED_DATE = 'Created Date',
  CREATED_BY_USER = 'Created by User',
  UPDATED_DATE = 'Updated Date',
  UPDATED_BY_USER = 'Updated by User',
  REF_ID = 'Ref.Id',
  INCIDENT_MONTH = 'Incident Month',
  INCIDENT_YEAR = 'Incident Year',
}
