import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsOptional, IsString, IsUUID, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class IncidentSecondSubCategoryDto {
  @ApiProperty({ type: String, description: 'Second subcategory ID', required: false })
  @IsOptional()
  @IsUUID('all')
  secSubCategoryId?: string;

  @ApiProperty({ type: String, description: 'Second subcategory name', required: false })
  @IsOptional()
  @IsString()
  secSubCategoryName?: string;
}

export class IncidentSubCategoryDto {
  @ApiProperty({ type: String, description: 'Subcategory ID', required: false })
  @IsOptional()
  @IsUUID('all')
  subCategoryId?: string;

  @ApiProperty({ type: String, description: 'Subcategory name', required: false })
  @IsOptional()
  @IsString()
  subCategoryName?: string;

  @ApiProperty({
    type: [IncidentSecondSubCategoryDto],
    description: 'Array of second subcategories nested under this subcategory',
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => IncidentSecondSubCategoryDto)
  incidentSecondSubCategory?: IncidentSecondSubCategoryDto[];
}

export class IncidentMainCategoryDto {
  @ApiProperty({ type: String, description: 'Main category ID', required: false })
  @IsOptional()
  @IsUUID('all')
  mainCategoryId?: string;

  @ApiProperty({ type: String, description: 'Main category name', required: false })
  @IsOptional()
  @IsString()
  mainCategoryName?: string;

  @ApiProperty({
    type: String,
    description:
      'Cause type (e.g., "Type of Loss", "Immediate Cause", "Basic", "Control Action Needs")',
    required: false,
  })
  @IsOptional()
  @IsString()
  causeType?: string;

  @ApiProperty({ type: String, description: 'Cause mapping ID reference', required: false })
  @IsOptional()
  @IsUUID('all')
  causeMappingId?: string;

  @ApiProperty({
    type: [IncidentSubCategoryDto],
    description: 'Array of subcategories with nested second subcategories',
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => IncidentSubCategoryDto)
  incidentSubCategory?: IncidentSubCategoryDto[];
}