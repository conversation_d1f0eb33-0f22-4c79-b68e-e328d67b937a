import { CreateIncidentInvestigationDto } from './create-incident-investigation.dto';
import { PartialType } from '@nestjs/swagger';
import { IncidentMainCategoryDto } from './incident-cause-mapping.dto';
import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsInt, IsOptional, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class UpdateIncidentInvestigationDto extends PartialType(CreateIncidentInvestigationDto) {
  @ApiProperty({
    type: [IncidentMainCategoryDto],
    description:
      'Array of main categories with their subcategories and second subcategories for cause mapping',
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => IncidentMainCategoryDto)
  incidentMainCategory?: IncidentMainCategoryDto[];

  @ApiProperty({
    type: 'number',
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  hours?: number;
}
