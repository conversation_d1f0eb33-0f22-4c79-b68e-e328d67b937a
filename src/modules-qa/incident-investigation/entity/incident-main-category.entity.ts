import { <PERSON>tity, Column, <PERSON>ToOne, OneToMany } from 'typeorm';
import { CommonStatus, IdentifyEntity } from 'svm-nest-lib-v3';
import { User } from '../../../modules/user/user.entity';
import { Company } from '../../../modules/company/company.entity';
import { IncidentSubCategory } from './incident-second-category.entity';
import { CauseMapping } from '../../../modules/cause-mapping/entities/cause-mapping.entity';
import { IncidentInvestigation } from './incident-investigation.entity';

@Entity()
export class IncidentMainCategory extends IdentifyEntity {
  @Column()
  causeType: string;

  @Column({ type: 'uuid' })
  mainCategoryId: string;

  @Column()
  mainCategoryName: string;

  @Column({ type: 'uuid' })
  causeMappingId: string;

  @Column({ type: 'uuid' })
  incidentInvestigationId: string;

  @Column({ type: 'uuid' })
  companyId: string;

  @Column({ type: 'uuid' })
  createdUserId: string;

  @Column({ type: 'uuid', nullable: true })
  updatedUserId?: string;

  @ManyToOne(() => Company, { onDelete: 'CASCADE' })
  company: Company;

  @ManyToOne(() => User, { onDelete: 'NO ACTION' })
  createdUser: User;

  @ManyToOne(() => User, { onDelete: 'NO ACTION' })
  updatedUser?: User;

  @ManyToOne(() => CauseMapping, { onDelete: 'CASCADE' })
  causeMapping: CauseMapping;

  @ManyToOne(() => IncidentInvestigation, { onDelete: 'CASCADE' })
  incidentInvestigation: IncidentInvestigation;

  @OneToMany(
    () => IncidentSubCategory,
    (incidentSubCategory) => incidentSubCategory.incidentMainCategory,
  )
  incidentSubCategory: IncidentSubCategory[];
} 