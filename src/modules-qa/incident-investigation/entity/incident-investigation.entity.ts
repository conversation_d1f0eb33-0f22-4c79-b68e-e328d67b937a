import { IdentifyEntity } from 'svm-nest-lib-v3';
import {
  AfterLoad,
  Column,
  Entity,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import {
  IncidentInvestigationReviewStatus,
  IncidentOriginEnum,
  VesselScreeningFinalRiskEnum,
  VesselScreeningObservedRiskEnum,
  VesselScreeningPotentialRiskEnum,
} from '../../../commons/enums';
import { hashAttachmentValues } from '../../../commons/functions';
import { CreatedUserHistoryModel } from '../../../commons/models';
import { IncidentMaster } from '../../../modules-qa/incident-master/incident-master.entity';
import { Company } from '../../../modules/company/company.entity';
import { PortMaster } from '../../../modules/port-master/port-master.entity';
import { RiskMatrixMaster } from '../../../modules/risk-matrix-master/entities/risk-matrix-master.entity';
import { RiskValueMapping } from '../../../modules/risk-matrix-master/entities/risk-value-mapping.entity';
import { PriorityMaster } from '../../../modules/priority-master/priority-master.entity';
import { UserAssignment } from '../../../modules/user-assignment/user-assignment.entity';
import { Vessel } from '../../../modules/vessel/entity/vessel.entity';
import { IncidentInvestigationComment } from './incident-investigation-comment.entity';
import { IncidentInvestigationHistory } from './incident-investigation-history.entity';
import { IncidentInvestigationRemark } from './incident-investigation-remark.entity';
import { IncidentInvestigationReview } from './incident-investigation-review.entity';
import { SubIncidentType } from '../../../modules-qa/sub-incident-type/sub-incident-type.entity';
import { Injury } from '../../injury/entity/injury.entity';
import { VoyageType } from '../../../modules/voyage-type/entities/voyage-type.entity';
import { IncidentMainCategory } from './incident-main-category.entity';
import { RightShipIncidents } from '../../../modules-qa/right-ship/entity/right-ship-incidents.entity';
import { MailPlanning } from '../../../modules/mail-template/entity/mail-planning.entity';
import { CauseMapping } from '../../../modules/cause-mapping/entities/cause-mapping.entity';

export enum IncidentInvestigationStatus {
  DRAFT = 'Draft',
  SUBMITTED = 'Submitted',
  REVIEWED = 'Reviewed',
  REASSIGN = 'Reassign',
  CLOSEOUT = 'Close out',
}

export enum ScoreStateEnum {
  VERY_LOW = 'VERY LOW',
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  VERY_HIGH = 'VERY HIGH',
}

@Entity()
export class IncidentInvestigation extends IdentifyEntity {
  @PrimaryGeneratedColumn('uuid')
  public id: string;

  @Column()
  public description: string;

  @Column()
  public title: string;

  @Column({ nullable: true })
  public sNo: string;

  @Column({ nullable: true })
  public refId: string;

  @Column({ nullable: true })
  public voyageNo: string;

  @Column({ type: 'smallint', nullable: true })
  public totalNumberOfCrew: number;

  @Column({ type: 'timestamp' })
  public dateTimeOfIncident: Date;

  @Column({ type: 'integer', nullable: true })
  public dateTimeOfIncident_Year: number;

  @Column({ type: 'varchar', length: 3, nullable: true })
  public dateTimeOfIncident_Month: string;

  @Column({ nullable: true })
  public otherType: string;

  @Column({ nullable: true, default: false })
  public atPort: boolean;

  @Column({ nullable: true })
  public latitude: string;

  @Column({ nullable: true })
  public longitude: string;

  @Column({ nullable: true })
  public typeOfLoss: string;

  @Column({ nullable: true })
  public immediateDirectCause: string;

  @Column({ nullable: true })
  public basicUnderlyingCauses: string;

  @Column({ nullable: true })
  public rootCause: string;

  @Column({ nullable: true })
  public contributionFactor: string;

  @Column({ nullable: true })
  public nonContributionFactor: string;

  @Column({ nullable: true })
  public immediateAction: string;

  @Column({ nullable: true })
  public preventiveAction: string;

  @Column({ nullable: true })
  public correctionAction: string;

  @Column({ nullable: true })
  public actionControlNeeds: string;

  @Column({
    type: 'smallint',
    enum: VesselScreeningPotentialRiskEnum,
    nullable: true,
  })
  public potentialRisk: VesselScreeningPotentialRiskEnum;

  @Column({
    type: 'smallint',
    nullable: true,
  })
  public potentialScore: number;

  @Column({ type: 'enum', enum: ScoreStateEnum, nullable: true })
  public potentialRiskState: string;

  @Column({
    type: 'smallint',
    enum: VesselScreeningObservedRiskEnum,
    nullable: true,
  })
  public observedRisk: VesselScreeningObservedRiskEnum;

  @Column({
    type: 'smallint',
    nullable: true,
  })
  public observedScore: number;

  @Column({ type: 'enum', enum: ScoreStateEnum, nullable: true })
  public observedRiskState: string;

  @Column({
    type: 'smallint',
    enum: VesselScreeningFinalRiskEnum,
    nullable: true,
  })
  public finalRisk: VesselScreeningFinalRiskEnum;

  @Column({
    type: 'smallint',
    nullable: true,
  })
  public finalScore: number;

  @Column({ type: 'enum', enum: ScoreStateEnum, nullable: true })
  public finalRiskState: string;

  @Column({
    nullable: true,
  })
  public timeLoss: boolean;

  @Column({
    type: 'enum',
    enum: IncidentInvestigationReviewStatus,
    nullable: true,
  })
  public reviewStatus: string;

  @Column({
    type: 'enum',
    enum: IncidentInvestigationStatus,
    nullable: true,
  })
  public status: string;

  @Column({ type: 'uuid', nullable: true })
  public createdUserId: string;

  @Column({ type: 'uuid', nullable: true })
  public updatedUserId?: string;

  @Column({
    type: 'uuid',
    array: true,
    default: [],
  })
  public attachments: string[];

  @Column({ nullable: true, type: 'json' })
  createdUser?: CreatedUserHistoryModel;

  @Column({ nullable: true, type: 'json' })
  updatedUser?: CreatedUserHistoryModel;

  @Column({ type: 'uuid' })
  public companyId: string;

  @Column({ type: 'uuid' })
  public vesselId: string;

  @Column({ type: 'uuid' })
  public portId: string;

  @Column({ type: 'uuid', nullable: true })
  public portToId: string;

  @Column({ type: 'enum', enum: IncidentOriginEnum, default: IncidentOriginEnum.MANUAL })
  public incidentOrigin: string;

  @Column({ type: 'uuid', nullable: true })
  public voyageTypeId: string;

  @Column({ type: 'integer', nullable: true })
  public fatalities?: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  public pollution?: number;

  // Risk Matrix Integration Fields
  @Column({ type: 'uuid', nullable: true })
  public riskMatrixId: string;

  // Potential Risk Matrix Fields
  @Column({ type: 'varchar', length: 10, nullable: true })
  public potentialRiskCellPosition: string; // e.g., "r3c4"

  @Column({ type: 'uuid', nullable: true })
  public potentialRiskValueMappingId: string;

  @Column({ type: 'uuid', nullable: true })
  public potentialPriorityMasterId: string;

  // Observed Risk Matrix Fields
  @Column({ type: 'varchar', length: 10, nullable: true })
  public observedRiskCellPosition: string;

  @Column({ type: 'uuid', nullable: true })
  public observedRiskValueMappingId: string;

  @Column({ type: 'uuid', nullable: true })
  public observedPriorityMasterId: string;

  // Final Risk Matrix Fields
  @Column({ type: 'varchar', length: 10, nullable: true })
  public finalRiskCellPosition: string;

  @Column({ type: 'uuid', nullable: true })
  public finalRiskValueMappingId: string;

  @Column({ type: 'uuid', nullable: true })
  public finalPriorityMasterId: string;

  @Column({
    type: 'smallint',
    nullable: true,
    default: 0,
  })
  public hours: number;

  @Column({ type: 'varchar', length: 3, nullable: true })
  public doos?: string;

  @Column({ type: 'uuid', nullable: true })
  public rightShipIncidentId: string;

  @Column({ type: 'uuid', nullable: true })
  public causeMappingId: string;

  // relationship
  @ManyToOne(() => Company, { onDelete: 'CASCADE' })
  company: Company;

  @ManyToOne(() => Vessel, { onDelete: 'CASCADE' })
  vessel: Vessel;

  @ManyToOne(() => PortMaster, { onDelete: 'CASCADE' })
  port: PortMaster;

  @ManyToOne(() => PortMaster, { onDelete: 'CASCADE' })
  portTo: PortMaster;

  @ManyToMany(() => IncidentMaster)
  @JoinTable({ name: 'incident_investigation_incident_master' })
  typeIncidents: IncidentMaster[];


  @ManyToMany(() => SubIncidentType)
  @JoinTable({ name: 'incident_investigation_sub_incident_type' })
  subIncidents: SubIncidentType[];

  @ManyToMany(() => IncidentMaster)
  @JoinTable({ name: 'incident_investigation_secondary_incident_master' })
  secondaryIncidents: IncidentMaster[];

  @ManyToMany(() => SubIncidentType)
  @JoinTable({ name: 'incident_investigation_secondary_sub_incident_type' })
  secondarySubIncidents: SubIncidentType[];

  @OneToMany(
    () => IncidentInvestigationRemark,
    (incidentInvestigationRemark) => incidentInvestigationRemark.incidentInvestigation,
  )
  incidentInvestigationRemarks: IncidentInvestigationRemark[];



  @OneToMany(
    () => IncidentInvestigationReview,
    (incidentInvestigationReview) => incidentInvestigationReview.incidentInvestigation,
  )
  incidentInvestigationReviews: IncidentInvestigationReview[];

  @OneToMany(
    () => IncidentInvestigationComment,
    (incidentInvestigationComment) => incidentInvestigationComment.incidentInvestigation,
  )
  incidentInvestigationComments: IncidentInvestigationComment[];

  @OneToMany(
    () => IncidentInvestigationHistory,
    (incidentInvestigationHistory) => incidentInvestigationHistory.incidentInvestigation,
  )
  incidentInvestigationHistories: IncidentInvestigationHistory[];

  @OneToMany(() => UserAssignment, (userAssignment) => userAssignment.incidentInvestigation)
  userAssignments: UserAssignment[];

  @OneToMany(() => Injury, (injury) => injury.incidentInvestigation)
  public injuries: Injury[];

  @ManyToOne(() => VoyageType, { onDelete: 'NO ACTION' })
  voyageType: VoyageType;

  // Risk Matrix Relationships
  @ManyToOne(() => RiskMatrixMaster, { onDelete: 'SET NULL' })
  riskMatrix: RiskMatrixMaster;

  @ManyToOne(() => RiskValueMapping, { onDelete: 'SET NULL' })
  potentialRiskValueMapping: RiskValueMapping;

  @ManyToOne(() => PriorityMaster, { onDelete: 'SET NULL' })
  potentialPriorityMaster: PriorityMaster;

  @ManyToOne(() => RiskValueMapping, { onDelete: 'SET NULL' })
  observedRiskValueMapping: RiskValueMapping;

  @ManyToOne(() => PriorityMaster, { onDelete: 'SET NULL' })
  observedPriorityMaster: PriorityMaster;

  @ManyToOne(() => RiskValueMapping, { onDelete: 'SET NULL' })
  finalRiskValueMapping: RiskValueMapping;

  @ManyToOne(() => PriorityMaster, { onDelete: 'SET NULL' })
  finalPriorityMaster: PriorityMaster;

  @OneToMany(
    () => IncidentMainCategory,
    (incidentMainCategory) => incidentMainCategory.incidentInvestigation,
  )
  incidentMainCategory: IncidentMainCategory[];

  @ManyToOne(() => RightShipIncidents, { onDelete: 'SET NULL' })
  rightShipIncident: RightShipIncidents;  

  @OneToMany(() => MailPlanning, (mailPlanning) => mailPlanning.incidentInvestigation)
  mailPlannings: MailPlanning[];

  @ManyToOne(() => CauseMapping, (causeMapping) => causeMapping.incidentInvestigation)
  causeMapping: CauseMapping;

  @AfterLoad()
  async transformAttachment() {
    this.attachments = await hashAttachmentValues(this.attachments);
  }
}
