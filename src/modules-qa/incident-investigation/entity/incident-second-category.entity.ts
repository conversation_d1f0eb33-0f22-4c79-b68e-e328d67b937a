import { Entity, Column, ManyToOne, OneToMany } from 'typeorm';
import { IdentifyEntity } from 'svm-nest-lib-v3';
import { IncidentMainCategory } from './incident-main-category.entity';
import { User } from '../../../modules/user/user.entity';
import { Company } from '../../../modules/company/company.entity';
import { IncidentSecondSubCategory } from './incident-second-sub-category.entity';

@Entity()
export class IncidentSubCategory extends IdentifyEntity {

  @Column({ type: 'uuid' })
  subCategoryId: string;

  @Column()
  subCategoryName: string;

  @Column({ type: 'uuid' })
  incidentMainCategoryId: string;

  @Column({ type: 'uuid' })
  companyId: string;

  @Column({ type: 'uuid' })
  createdUserId: string;

  @Column({ type: 'uuid', nullable: true })
  updatedUserId?: string;

  @ManyToOne(() => Company, { onDelete: 'CASCADE' })
  company: Company;

  @ManyToOne(() => User, { onDelete: 'NO ACTION' })
  createdUser: User;

  @ManyToOne(() => User, { onDelete: 'NO ACTION' })
  updatedUser?: User;

  @ManyToOne(() => IncidentMainCategory, { onDelete: 'CASCADE' })
  incidentMainCategory: IncidentMainCategory;

  @OneToMany(
    () => IncidentSecondSubCategory,
    (incidentSecondSubCategory) => incidentSecondSubCategory.incidentSubCategory,
  )
  incidentSecondSubCategory: IncidentSecondSubCategory[];
}