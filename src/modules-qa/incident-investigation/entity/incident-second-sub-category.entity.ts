import { Entity, Column, ManyToOne } from 'typeorm';
import { IdentifyEntity } from 'svm-nest-lib-v3';
import { IncidentSubCategory } from './incident-second-category.entity';
import { User } from '../../../modules/user/user.entity';
import { Company } from '../../../modules/company/company.entity';

@Entity()
export class IncidentSecondSubCategory extends IdentifyEntity {

  @Column({ type: 'uuid' })
  secSubCategoryId: string;

  @Column()
  secSubCategoryName: string;

  @Column({ type: 'uuid' })
  incidentSubCategoryId: string;

  @Column({ type: 'uuid' })
  companyId: string;

  @Column({ type: 'uuid' })
  createdUserId: string;

  @Column({ type: 'uuid', nullable: true })
  updatedUserId?: string;

  @ManyToOne(() => Company, { onDelete: 'CASCADE' })
  company: Company;

  @ManyToOne(() => User, { onDelete: 'NO ACTION' })
  createdUser: User;

  @ManyToOne(() => User, { onDelete: 'NO ACTION' })
  updatedUser?: User;

  @ManyToOne(() => IncidentSubCategory, { onDelete: 'CASCADE' })
  incidentSubCategory: IncidentSubCategory;
}