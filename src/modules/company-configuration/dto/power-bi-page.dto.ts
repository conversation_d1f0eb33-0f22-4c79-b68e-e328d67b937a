import { ApiProperty } from '@nestjs/swagger';
import { IsN<PERSON>ber, IsOptional, IsString, IsUUID } from 'class-validator';

export class PowerBiPageDto {
  @ApiProperty({ type: String, required: false })
  @IsOptional()
  @IsUUID('all')
  id?: string;

  @ApiProperty({ type: Number, required: true })
  @IsNumber()
  pageOrder: number;

  @ApiProperty({ type: String, required: true })
  @IsString()
  pageName: string;

  @ApiProperty({ type: String, required: true })
  @IsString()
  url: string;

  @ApiProperty({ type: String, required: true })
  @IsString()
  clientId: string;

  @ApiProperty({ type: String, required: true })
  @IsString()
  clientSecret: string;

  @ApiProperty({ type: String, required: true })
  @IsString()
  tenantId: string;

  @ApiProperty({ type: String, required: true })
  @IsString()
  workspaceId: string;

  @ApiProperty({ type: String, required: true })
  @IsString()
  reportId: string;
}
