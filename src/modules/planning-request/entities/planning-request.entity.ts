import { IdentifyEntity } from 'svm-nest-lib-v3';
import {
  AfterLoad,
  Column,
  Entity,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import {
  AuditEntity,
  AuditorType,
  PlanningRequestStatus,
  PlanningRequestWorkingType,
  TypeOfAudit,
} from '../../../commons/enums';
import { AuditChecklist } from '../../audit-checklist/entity/audit-checklist.entity';
import { AuditTimeTable } from '../../audit-time-table/entities/audit-time-table.entity';
import { AuditType } from '../../audit-type/audit-type.entity';
import { AuditWorkspace } from '../../audit-workspace/entities/audit-workspace.entity';
import { Company } from '../../company/company.entity';
import { CorrectiveActionRequest } from '../../corrective-action-request/entities/car.entity';
import { CarVerificationPlanning } from '../../corrective-action-request/entities/car_verification_planning.entity';
import { FollowUp } from '../../corrective-action-request/entities/follow-up.entity';
import { Department } from '../../department-master/department.entity';
import { InternalAuditReport } from '../../internal-audit-report/entities/internal-audit-report.entity';
import { MailPlanning } from '../../mail-template/entity/mail-planning.entity';
import { PortMaster } from '../../port-master/port-master.entity';
import { ReportFindingForm } from '../../report-finding/entities/report-finding-form.entity';
import { UserAssignment } from '../../user-assignment/user-assignment.entity';
import { User } from '../../user/user.entity';
import { Vessel } from '../../vessel/entity/vessel.entity';
import { PlanningRequestHistory } from './planning-request-history.entity';
import { PRAdditionalReviewer } from './pr-additional-reviewer.entity';
import { PRDueDateAndDateOfLastInspection } from './pr-due-date-and-date-of-last-inspection.entity';
import { PRFocusRequest } from './pr-focus-request.entity';
import { PROfficeComment } from './pr-office-comment.entity';
// import { Location } from '../../location/location.entity';
import { hashAttachmentValues } from '../../../commons/functions';
import { Country } from './planning-request-country.entity';
import { VoyageType } from '../../../modules/voyage-type/entities/voyage-type.entity';
@Entity()
export class PlanningRequest extends IdentifyEntity {
  @PrimaryGeneratedColumn('uuid')
  public id: string;

  @Column({ nullable: true })
  public refId: string;

  @Column({ nullable: true })
  public auditNo: string;

  @Column({ type: 'uuid', nullable: true })
  public vesselId: string;

  @Column({ type: 'uuid', nullable: true })
  public auditCompanyId: string;

  @Column({ type: 'uuid', nullable: true })
  public internalAuditId: string; // iauditid

  @Column({ type: 'enum', enum: TypeOfAudit, nullable: true })
  public typeOfAudit: string; // typeofreport

  @Column({ type: 'uuid', nullable: true })
  public fromPortId: string; // plannedfromport

  @Column({ type: 'uuid', nullable: true })
  public toPortId: string; // plannedtoport

  @Column({ type: 'timestamp', nullable: true })
  public estimatedTimeDeparture: Date; // etd

  @Column({ type: 'timestamp', nullable: true })
  public toPortEstimatedTimeArrival: Date;

  @Column({ type: 'timestamp', nullable: true })
  public toPortEstimatedTimeDeparture: Date;

  @Column({ type: 'timestamp', nullable: true })
  public fromPortEstimatedTimeArrival: Date;

  @Column({ type: 'timestamp', nullable: true })
  public fromPortEstimatedTimeDeparture: Date;

  @Column({ type: 'uuid', nullable: true })
  public auditRefId: string;

  @Column({ nullable: true })
  public departmentCode: string;

  @Column({ type: 'timestamp', nullable: true })
  public estimatedTimeArrival: Date;

  @Column()
  public timezone: string;

  @Column({ type: 'timestamp', nullable: true })
  public plannedFromDate: Date;

  @Column({ type: 'timestamp', nullable: true })
  public plannedToDate: Date;

  @Column({ type: 'uuid', nullable: true })
  public leadAuditorId: string;

  @Column({ nullable: true })
  public extensionReq: string;

  @Column({ type: 'uuid', nullable: true })
  public workflowId: string;

  @Column({ type: 'enum', enum: PlanningRequestStatus, default: PlanningRequestStatus.DRAFT })
  public status: string;

  @Column({ type: 'enum', enum: PlanningRequestStatus, default: PlanningRequestStatus.DRAFT })
  public previousStatus: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  public globalStatus: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  public reassignFrom: string;

  @Column({ type: 'enum', enum: AuditorType, nullable: true })
  public auditorType: string;

  @Column({ nullable: true })
  public memo: string;

  @Column({ nullable: true })
  public auditPlanStatus: string;

  @Column({
    type: 'enum',
    enum: AuditEntity,
    default: AuditEntity.VESSEL,
  })
  public entityType: string;

  @Column({ type: 'uuid', nullable: true })
  public departmentId: string;

  @Column({ type: 'timestamp', nullable: true })
  public dateOfLastInspection: Date;

  @Column({
    nullable: true,
    type: 'enum',
    enum: PlanningRequestWorkingType,
    default: PlanningRequestWorkingType.PHYSICAL,
  })
  public workingType: string;

  //start: for map view: with type: Office => save longlat of location
  @Column({ type: 'float4', nullable: true })
  public latitude: number;

  @Column({ type: 'float4', nullable: true })
  public longitude: number;
  //end

  @Column({
    type: 'uuid',
    array: true,
    nullable: true,
  })
  public attachments: string[];

  @Column({ type: 'uuid' })
  public createdUserId: string;

  @Column({ type: 'uuid', nullable: true })
  public updatedUserId?: string;

  @Column({ type: 'uuid' })
  public companyId: string;

  @Column({ type: 'uuid', nullable: true })
  public docHolderCurrentCompanyId?: string;

  @Column({ type: 'uuid', nullable: true })
  public vesselChartererCurrentCompanyId?: string;

  @Column({ type: 'uuid', nullable: true })
  public vesselOwnerCurrentCompanyId?: string;

  @Column({ type: 'integer', nullable: true })
  public locationId: number;

  @Column({ type: 'varchar', length: 3, nullable: true })
  public plannedFromDate_Month: string;

  @Column({ type: 'integer', nullable: true })
  public plannedFromDate_Year: number;

  @Column({ type: 'varchar', length: 3, nullable: true })
  public plannedToDate_Month: string;

  @Column({ type: 'integer', nullable: true })
  public plannedToDate_Year: number;

  @Column({ type: 'text', nullable: true })
  public agentDetail?: string;

  @Column({ type: 'text', nullable: true })
  public vesselPIC?: string;

  @Column({ type: 'text', nullable: true })
  public group?: string;

  @Column({ type: 'text', nullable: true })
  public team?: string;

  @Column({ type: 'text', nullable: true })
  public customerRef: string;

  @Column({ type: 'varchar', nullable: true })
  public nameOfInspectors?: string;

  @Column({ type: 'varchar', nullable: false })
  public auditTypesName?: string;

  @Column({ type: 'varchar', length: 3, nullable: true })
  public doos?: string;

  @Column({ type: 'uuid', nullable: true })
  public voyageTypeId: string;

  @Column({ default: false })
  public isSA: boolean;
  /** Relationship */
  /** One to One */

  @ManyToOne(() => Company, { onDelete: 'NO ACTION' })
  public docHolderCurrentCompany: Company;

  @ManyToOne(() => Company, { onDelete: 'NO ACTION' })
  public vesselChartererCurrentCompany: Company;

  @ManyToOne(() => Company, { onDelete: 'NO ACTION' })
  public vesselOwnerCurrentCompany: Company;

  @OneToOne(() => ReportFindingForm, (reportFindingForm) => reportFindingForm.planningRequest)
  public reportFindingForm: ReportFindingForm;

  @OneToOne(() => InternalAuditReport, (internalAuditReport) => internalAuditReport.planningRequest)
  public internalAuditReport: InternalAuditReport;

  @ManyToMany(() => AuditType, (auditType) => auditType.planningRequests)
  @JoinTable({ name: 'planning_request_audit_type' })
  auditTypes: AuditType[];

  @ManyToMany(() => User)
  @JoinTable({ name: 'planning_request_auditor' })
  auditors: User[];

  @ManyToOne(() => User, { onDelete: 'NO ACTION' })
  public leadAuditor: User;

  @ManyToMany(() => AuditChecklist)
  @JoinTable({ name: 'planning_request_audit_checklist' })
  auditChecklist: AuditChecklist[];

  @ManyToOne(() => Company, { onDelete: 'CASCADE' })
  public company: Company;

  @ManyToOne(() => Company, { onDelete: 'CASCADE' })
  public auditCompany: Company;

  @ManyToOne(() => PortMaster, { onDelete: 'CASCADE' })
  public fromPort: PortMaster;

  @ManyToOne(() => PortMaster, { onDelete: 'CASCADE' })
  public toPort: PortMaster;

  /** One to One */
  @OneToOne(() => AuditTimeTable, (timeTable) => timeTable.planningRequest)
  auditTimeTable: AuditTimeTable;

  /** One to Many */
  @OneToMany(
    () => PROfficeComment,
    (planningRequestOfficeComment) => planningRequestOfficeComment.planningRequest,
  )
  planningRequestOfficeComments: PROfficeComment[];

  @OneToMany(() => PRFocusRequest, (pRFocusRequest) => pRFocusRequest.planningRequest)
  pRFocusRequests: PRFocusRequest[];

  @OneToMany(
    () => PRAdditionalReviewer,
    (planningRequestAdditionalReviewer) => planningRequestAdditionalReviewer.planningRequest,
  )
  planningRequestAdditionalReviewers: PRAdditionalReviewer[];

  @OneToMany(
    () => PlanningRequestHistory,
    (planningRequestHistory) => planningRequestHistory.planningRequest,
  )
  planningRequestHistories: PlanningRequestHistory[];

  @OneToMany(() => MailPlanning, (mailPlanning) => mailPlanning.planningRequest)
  mailPlannings: MailPlanning[];

  @ManyToOne(() => User, { onDelete: 'NO ACTION' })
  public createdUser: User;

  @ManyToOne(() => User, { onDelete: 'NO ACTION' })
  public updatedUser: User;

  @ManyToOne(() => Vessel, { onDelete: 'SET NULL' })
  public vessel: Vessel;

  @ManyToOne(() => VoyageType, { onDelete: 'NO ACTION' })
  public voyageType: VoyageType;

  //Update DB Planning to save multiple department
  @ManyToMany(() => Department)
  @JoinTable({ name: 'planning_request_department' })
  public departments: Department[];

  @ManyToOne(() => Department, { onDelete: 'SET NULL' })
  public department: Department;

  @OneToOne(() => AuditWorkspace, (auditWorkspace) => auditWorkspace.planningRequest)
  public auditWorkspace: AuditWorkspace;

  @OneToMany(() => CorrectiveActionRequest, (car) => car.planningRequest)
  cars: CorrectiveActionRequest[];

  @OneToOne(() => FollowUp, (followUp) => followUp.planningRequest)
  public followUp: FollowUp;

  @OneToMany(
    () => CarVerificationPlanning,
    (carVerificationPlanning) => carVerificationPlanning.planningRequest,
  )
  carVerificationPlannings: CarVerificationPlanning[];

  @OneToMany(() => UserAssignment, (userAssignment) => userAssignment.planningRequest)
  userAssignments: UserAssignment[];

  @OneToMany(
    () => PRDueDateAndDateOfLastInspection,
    (prDueDateAndDateOfLastInspection) => prDueDateAndDateOfLastInspection.planningRequest,
  )
  prDueDateAndDateOfLastInspections: PRDueDateAndDateOfLastInspection[];

  @ManyToOne(() => Country, { onDelete: 'NO ACTION' })
  public location: Country;

  @AfterLoad()
  async transformAttachment() {
    this.attachments = await hashAttachmentValues(this.attachments);
  }
}
