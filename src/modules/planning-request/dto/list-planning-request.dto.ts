import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayUnique,
  IsArray,
  IsBoolean,
  IsDateString,
  IsEnum,
  IsOptional,
  IsUUID,
  ValidateIf,
} from 'class-validator';
import { IsGreaterThanOrEqual } from 'svm-nest-lib-v3';
import { ListQueryDto } from '../../../commons/dtos';
import { AuditEntity, PlanningRequestStatus, PlanningTab } from '../../../commons/enums';
import { DataType, FilterField } from '../../../utils';

export class ListPlanningRequestQueryDTO extends ListQueryDto {
  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @ValidateIf((o) => o.reportOfFinding || o.auditTimeTable)
  @IsUUID('all')
  vesselId?: string;

  @ApiProperty({
    type: 'string',
    description: 'ISO string',
    example: '2020-10-05T14:48:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsDateString({ strict: true })
  planningFrom?: string;

  @ApiProperty({
    type: 'string',
    description: 'ISO string',
    example: '2023-10-05T14:48:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsDateString({ strict: true })
  @IsGreaterThanOrEqual('planningFrom', { message: 'common.INVALID_DATE_RANGE' })
  planningTo?: string;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @IsUUID('all')
  departmentId?: string;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @IsUUID('all')
  auditCompanyId?: string;

  @ApiProperty({
    type: 'string',
    description: 'ISO 8601 date time',
    example: '2021-11-21T12:09:32.142z',
    required: false,
  })
  @IsOptional()
  @IsDateString({ strict: true })
  fromDate?: string;

  @ApiProperty({
    type: 'string',
    description: 'ISO 8601 date time',
    example: '2021-11-28T12:09:32.142z',
    required: false,
  })
  @IsOptional()
  @IsDateString({ strict: true })
  @IsGreaterThanOrEqual('fromDate', { message: 'common.INVALID_DATE_RANGE' })
  toDate?: string;

  @ApiProperty({ type: String, enum: PlanningRequestStatus })
  @IsOptional()
  @IsEnum(PlanningRequestStatus)
  status?: string;

  // Flag for Listing planning request has submitted audit time table of given vessel
  @ApiProperty({ type: Boolean, required: false })
  @IsOptional()
  @Type(() => Boolean)
  @IsBoolean()
  reportFinding?: boolean;

  // Flag for Listing planning request in audit time table
  @ApiProperty({ type: Boolean, required: false })
  @IsOptional()
  @Type(() => Boolean)
  @IsBoolean()
  auditTimeTable?: boolean;

  @ApiProperty({ type: String, enum: AuditEntity })
  @IsOptional()
  @IsEnum(AuditEntity)
  entityType?: string;

  @ApiProperty({ type: Boolean, required: false })
  @IsOptional()
  @Type(() => Boolean)
  @IsBoolean()
  unplanned?: boolean;

  @ApiProperty({ type: Boolean, required: false })
  @IsOptional()
  @Type(() => Boolean)
  @IsBoolean()
  isDashboard?: boolean;

  @ApiProperty({ type: String, enum: PlanningTab })
  @IsOptional()
  @IsEnum(PlanningTab)
  tab?: string;

  @ApiProperty({ type: Boolean, required: false })
  @IsOptional()
  @Type(() => Boolean)
  @IsBoolean()
  isSafetyEngagementType?: boolean;

  @ApiProperty({ type: Boolean, required: false })
  @IsOptional()
  @Type(() => Boolean)
  @IsBoolean()
  isInspectionHistory?: boolean;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @ValidateIf((o) => o.isInspectionHistory)
  @IsUUID('all')
  planningRequestId?: string;

  @ApiProperty({ type: Boolean, required: false })
  @IsOptional()
  @Type(() => Boolean)
  @IsBoolean()
  isInspectorSchedule?: boolean;

  @IsOptional()
  @IsUUID('all', { each: true })
  @IsArray()
  @ArrayUnique()
  ids?: string[];
}

export const PLANNING_FILTER_FIELDS: FilterField[] = [
  {
    field: 'vesselName',
    column: '"vessel_name"',
    type: DataType.TEXT,
  },
  {
    field: 'vesselTypeName',
    column: '"vesselType_name"',
    type: DataType.TEXT,
  },
  {
    field: 'auditCompany',
    column: '"companyVesselDocHolders_name"',
    type: DataType.TEXT,
  },
  {
    field: 'officeName',
    column: '"auditCompany_name"',
    type: DataType.TEXT,
  },
  {
    field: 'department',
    column: '"departmentsName"',
    type: DataType.TEXT,
  },
  {
    field: 'location',
    column: '"location_name"',
    type: DataType.TEXT,
  },
  {
    field: 'inspectionType',
    column: '"planningRequest_auditTypesName"',
    type: DataType.TEXT,
  },
  {
    field: 'nameOfInspector',
    column: '"planningRequest_nameOfInspectors"',
    type: DataType.TEXT,
  },
  {
    field: 'leadInspectorName',
    column: '"leadAuditor_username"',
    type: DataType.TEXT,
  },
  {
    field: 'status',
    column: '"planningRequest_status"',
    type: DataType.TEXT,
  },
  {
    field: 'inspectionPlannedFromDate',
    column: '"planningRequest_plannedFromDate"',
    type: DataType.DATE,
  },
  {
    field: 'inspectionPlannedToDate',
    column: '"planningRequest_plannedToDate"',
    type: DataType.DATE,
  },
  {
    field: 'entityType',
    column: '"planningRequest_entityType"',
    type: DataType.TEXT,
  },
  {
    field: 'vesselCountryFlag',
    column: '"country_name"',
    type: DataType.TEXT,
  },
  {
    field: 'workingType',
    column: '"planningRequest_workingType"',
    type: DataType.TEXT,
  },
  {
    field: 'visitType',
    column: '"planningRequest_typeOfAudit"',
    type: DataType.TEXT,
  },
  {
    field: 'dateOfLastInspection',
    column: '"prDueDateAndDateOfLastAudits_dateOfLastInspection"',
    type: DataType.DATE,
  },
  {
    field: 'dueDate',
    column: '"prDueDateAndDateOfLastAudits_dueDate"',
    type: DataType.DATE,
  },
  {
    field: 'fromPort',
    column: '"fromPort_name"',
    type: DataType.TEXT,
  },
  {
    field: 'toPort',
    column: '"toPort_name"',
    type: DataType.TEXT,
  },
  {
    field: 'eta',
    column: '"planningRequest_fromPortEstimatedTimeArrival"',
    type: DataType.DATE,
  },
  {
    field: 'etd',
    column: '"planningRequest_fromPortEstimatedTimeDeparture"',
    type: DataType.DATE,
  },
  {
    field: 'globalStatus',
    column: '"planningRequest_globalStatus"',
    type: DataType.TEXT,
  },
  {
    field: 'createCompanyName',
    column: '"company_name"',
    type: DataType.TEXT,
  },
  {
    field: 'SNo',
    column: '"planningRequest_auditNo"',
    type: DataType.TEXT,
  },
  {
    field: 'refId',
    column: '"planningRequest_refId"',
    type: DataType.TEXT,
  },
  {
    field: 'plannedFromDate_Year',
    column: '"plannedFromDateYear"',
    type: DataType.TEXT,
  },
  {
    field: 'plannedFromDate_Month',
    column: '"plannedFromDateMonth"',
    type: DataType.TEXT,
  },
  {
    field: 'plannedToDate_Year',
    column: '"planningRequest_plannedToDate_Year"',
    type: DataType.TEXT,
  },
  {
    field: 'plannedToDate_Month',
    column: '"planningRequest_plannedToDate_Month"',
    type: DataType.TEXT,
  },
  {
    field: 'voyageType',
    column: '"voyageType_name"',
    type: DataType.TEXT,
  },
  {
    field: 'doos',
    column: '"planningRequest_doos"',
    type: DataType.TEXT,
  },
];

export enum TitlePlanningRequest {
  VESSEL_NAME = 'Vessel Name',
  VESSEL_TYPE = 'Vessel Type',
  DOC_HOLDER_COMPANY = 'DOC Holder Company',
  OFFICE_NAME = 'Office Name',
  DEPARTMENT = 'Department',
  LOCATION = 'Location',
  INSPECTION_TYPE = 'Inspection Type',
  INSPECTOR_NAME = 'Inspector Name',
  LEAD_INSPECTOR_NAME = 'Lead Inspector Name',
  SCHEDULE_STATUS = 'Schedule Status',
  INS_PLANNED_FROM = 'Ins Planned From',
  INS_PLANNED_TO = 'Ins Planned To',
  ENTITY = 'Entity',
  FLAG = 'Flag',
  WORKING_TYPE = 'Working Type',
  VISIT_TYPE = 'Visit Type',
  DATE_OF_LAST_INSPECTION = 'Date Of Last Inspection',
  DUE_DATE = 'Due Date',
  FROM_PORT = 'From Port',
  TO_PORT = 'To Port',
  ETA = 'ETA',
  ETD = 'ETD',
  GLOBAL_STATUS = 'Global Status',
  CREATED_BY_COMPANY = 'Created By Company',
  REF_ID = 'Ref.ID',
  INSPECTION_PLANNDED_FROM_YEAR = 'Inspection Planned From - Year',
  INSPECTION_PLANNDED_FROM_MONTH = 'Inspection Planned From - Month',
  INSPECTION_PLANNDED_TO_YEAR = 'Inspection Planned To - Year',
  INSPECTION_PLANNDED_TO_MONTH = 'Inspection Planned To - Month',
}
