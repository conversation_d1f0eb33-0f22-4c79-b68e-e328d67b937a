import { ApiProperty } from '@nestjs/swagger';
import { PlanningRequestWorkingType, TypeOfAudit } from '../../../commons/enums';
import {
  ArrayMinSize,
  ArrayUnique,
  IsArray,
  IsDateString,
  IsEnum,
  IsIn,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsUUID,
  MaxLength,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import {
  IsGreaterThanOrEqual,
  IsLatitudeDMS,
  IsLongitudeDMS,
  IsMomentTimeZoneName,
} from 'svm-nest-lib-v3';

import { CreatePROfficeCommentDTO } from './create-PR-office-comment.dto';
import { Transform, Type } from 'class-transformer';
import { CreatePRFocusRequestDTO } from './create-PR-focus-request.dto';
import { CreateUserAssignmentDTO } from '../../user-assignment/dto';
import { DueDateAndDateOfLastInspectionDTO } from './due-date-and-date-of-last-inspection.dto';
import { decryptAttachmentValues } from '../../../commons/functions';
export class CreatePlanningRequestDTO {
  @ApiProperty({ type: 'string' })
  // @ValidateIf((o) => !o.departmentId)
  @IsOptional()
  @IsUUID('all')
  vesselId: string;

  @ApiProperty({ type: [String] })
  @IsOptional()
  @IsArray()
  //@ArrayMinSize(1)
  @ArrayUnique()
  @IsUUID('all', { each: true })
  departmentIds: string[];

  @ApiProperty({ type: 'string' })
  // @ValidateIf((o) => !o.vesselId)
  @IsOptional()
  @IsUUID('all')
  departmentId: string;

  @ApiProperty({ type: 'string' })
  @ValidateIf((o) => o.departmentId)
  @IsNotEmpty()
  @IsUUID('all')
  auditCompanyId: string;

  @ApiProperty({ type: 'string', example: 'Asia/Ho_Chi_Minh' })
  @IsNotEmpty()
  @IsMomentTimeZoneName()
  timezone: string;

  // TODO chagne to require
  @ApiProperty({ type: String, enum: TypeOfAudit, required: false })
  @IsOptional()
  @IsEnum(TypeOfAudit)
  typeOfAudit: string;

  @ApiProperty({ type: String, enum: PlanningRequestWorkingType, required: false })
  @IsOptional()
  @IsEnum(PlanningRequestWorkingType)
  workingType: string;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @IsUUID('all')
  fromPortId?: string;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @IsUUID('all')
  toPortId?: string;

  @ApiProperty({
    type: 'string',
    description: 'ISO 8601 date only',
    example: '2011-10-05T14:48:00.000Z',
    required: false,
  })
  // @Transform((value: string) => value.substr(0, 10))
  @IsOptional()
  @IsDateString({ strict: true })
  // @IsAfterCurrentDayTZ('timezone')
  toPortEstimatedTimeArrival?: string;

  @ApiProperty({
    type: 'string',
    description: 'ISO 8601 date only',
    example: '2011-10-05T14:48:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsDateString({ strict: true })
  dueDate: string;

  @ApiProperty({
    type: 'string',
    description: 'ISO 8601 date only',
    example: '2011-10-05T14:48:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsDateString({ strict: true })
  dateOfLastInspection: string;

  @ApiProperty({
    type: 'string',
    description: 'ISO string',
    example: '2011-10-05T14:48:00.000Z',
    required: false,
  })
  @IsOptional()
  // @Transform((value: string) => value.substr(0, 10))
  @IsDateString({ strict: true })
  @IsGreaterThanOrEqual('toPortEstimatedTimeArrival', { message: 'common.INVALID_DATE_RANGE' })
  toPortEstimatedTimeDeparture?: string;

  @ApiProperty({
    type: 'string',
    description: 'ISO 8601 date only',
    example: '2011-10-05T14:48:00.000Z',
    required: false,
  })
  // @Transform((value: string) => value.substr(0, 10))
  @IsOptional()
  @IsDateString({ strict: true })
  // @IsAfterCurrentDayTZ('timezone')
  fromPortEstimatedTimeArrival?: string;

  @ApiProperty({
    type: 'string',
    description: 'ISO string',
    example: '2011-10-05T14:48:00.000Z',
    required: false,
  })
  @IsOptional()
  // @Transform((value: string) => value.substr(0, 10))
  @IsDateString({ strict: true })
  @IsGreaterThanOrEqual('fromPortEstimatedTimeArrival', { message: 'common.INVALID_DATE_RANGE' })
  fromPortEstimatedTimeDeparture?: string;

  @ApiProperty({
    type: 'string',
    description: 'ISO 8601 date only',
    example: '2011-10-05T14:48:00.000Z',
    required: false,
  })
  // @Transform((value: string) => value.substr(0, 10))
  @IsOptional()
  @IsDateString({ strict: true })
  // @IsAfterCurrentDayTZ('timezone')
  plannedFromDate: string;

  @ApiProperty({
    type: 'string',
    description: 'ISO string',
    example: '2011-10-05T14:48:00.000Z',
    required: false,
  })
  @IsOptional()
  // @Transform((value: string) => value.substr(0, 10))
  @IsDateString({ strict: true })
  @IsGreaterThanOrEqual('plannedFromDate', { message: 'common.INVALID_DATE_RANGE' })
  plannedToDate: string;

  // @ApiProperty({ type: String, enum: AuditorType })
  // @IsNotEmpty()
  // @IsEnum(AuditorType)
  // auditorType: string;

  @ApiProperty({ type: [String] })
  @IsNotEmpty()
  @IsArray()
  @ArrayMinSize(1)
  @ArrayUnique()
  @IsUUID('all', { each: true })
  auditTypeIds: string[];

  // @ApiProperty({ type: [String] })
  // @IsNotEmpty()
  // @IsArray()
  // @ArrayMinSize(1)
  // @ArrayUnique()
  // @IsUUID('all', { each: true })
  // auditChecklistIds: string[];

  @ApiProperty({ type: String, required: false })
  @IsOptional()
  memo?: string;

  @ApiProperty({ type: 'number', required: false })
  @IsOptional()
  @IsNumber()
  latitude: number;

  @ApiProperty({ type: 'number', required: false })
  @IsOptional()
  @IsNumber()
  longitude: number;

  @ApiProperty({ type: [String], required: false })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @ArrayUnique()
  @Transform((value: string[]) => {
    const newValue = decryptAttachmentValues(value);
    return newValue;
  })
  attachments?: string[];

  @ApiProperty({ type: [String] })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @ArrayUnique()
  @IsUUID('all', { each: true })
  auditorIds: string[];

  @ApiProperty({ type: 'string' })
  @IsOptional()
  @IsUUID('all')
  leadAuditorId: string;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @MaxLength(500)
  agentDetail?: string;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @MaxLength(128)
  vesselPIC?: string;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @MaxLength(128)
  group?: string;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @MaxLength(128)
  team?: string;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @MaxLength(20)
  customerRef?: string;
  // @ApiProperty({ type: [CreateAuditorInfoDTO] })
  // @IsNotEmpty()
  // @ValidateNested({ each: true })
  // @Type(() => CreateAuditorInfoDTO)
  // @IsArray()
  // @ArrayUnique()
  // @ArrayMinSize(1)
  // auditorInfos: CreateAuditorInfoDTO[];

  @ApiProperty({ type: [CreatePROfficeCommentDTO], required: false })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreatePROfficeCommentDTO)
  @IsArray()
  @ArrayUnique()
  @ArrayMinSize(1)
  officeComments?: CreatePROfficeCommentDTO[];

  @ApiProperty({ type: [CreatePRFocusRequestDTO], required: false })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreatePRFocusRequestDTO)
  @IsArray()
  @ArrayUnique<CreatePRFocusRequestDTO>((o) => o.focusRequestId)
  focusRequests: CreatePRFocusRequestDTO[];

  @ApiProperty({ type: [DueDateAndDateOfLastInspectionDTO], required: false })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => DueDateAndDateOfLastInspectionDTO)
  @IsArray()
  dueDateAndDateOfLastInspections: DueDateAndDateOfLastInspectionDTO[];

  @ApiProperty({ type: CreateUserAssignmentDTO, required: false })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateUserAssignmentDTO)
  userAssignment?: CreateUserAssignmentDTO;

  @ApiProperty({ type: 'number', required: false })
  @ValidateIf((o) => !o.vesselId)
  @IsNotEmpty()
  // @IsUUID('all')
  locationId: number;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  nameOfInspectors?: string;

  @ApiProperty({ type: 'string', required: true })
  @IsNotEmpty()
  auditTypesName?: string;

  @ApiProperty({ type: 'string' })
  @IsOptional()
  @IsUUID()
  voyageTypeId: string;

  @ApiProperty({ type: 'boolean', required: false })
  @IsOptional()
  isSA?: boolean;

  @ApiProperty({ type: 'string', required: false, enum: ['Yes', 'No'] })
  @IsOptional()
  @IsIn(['Yes', 'No'])
  doos?: string;
}
