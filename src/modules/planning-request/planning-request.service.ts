/* eslint-disable indent */
import { BadRequestException, Injectable } from '@nestjs/common';
import ExcelJS from 'exceljs';
import { Response } from 'express';
import { Parser } from 'json2csv';
import { map, omit } from 'lodash';
import { Readable } from 'stream';
import { BaseError, CommonStatus, TokenPayloadModel, Utils } from 'svm-nest-lib-v3';

import {
  ActionEnum,
  AuditEntity,
  FeatureEnum,
  PlanningRequestStatus,
  PlanningTab,
  SubFeatureEnum,
  TemplateTypeEnum,
} from '../../commons/enums';
import {
  AcceptPRBodyDTO,
  AcceptPRQueryDTO,
  ApprovePRQueryDTO,
  CheckAvailableAuditorsDTO,
  CheckSAInProgressDto,
  CreatePlanningRequestDTO,
  ListPlanningRequestQueryDTO,
  ListPRGraphicallyDTO,
  ListTotalUnplannedDTO,
  RejectPRBodyDTO,
  ReviewPRBodyDTO,
  ReviewPRQueryDTO,
  SubmitPRQueryDTO,
  TitlePlanningRequest,
  UpdatePlanningRequestDTO,
} from './dto';
import { GraphicalGroupByEnum } from './planning-request.enum';
import { PlanningRequestRepository } from './repositories/planning-request.repository';

import { AppConst } from 'src/commons/consts/app.const';
import { PayloadAGGridDto } from 'src/utils';
import { getRepository } from 'typeorm';
import { TimezoneDTO } from '../../commons/dtos';
import { EmailProducer } from '../../micro-services/async/email.producer';
import { NotificationProducer } from '../../micro-services/async/notification.producer';
import { SVMIAMService } from '../../micro-services/sync/svm-iam.service';
import { SVMSupportService } from '../../micro-services/sync/svm-support.service';
import { VoyageMasterDetailsRepository } from '../../modules-qa/vessel-scheduler-module/repository/vessel-scheduler.repository';
import { createPdf } from '../../utils/generate-pdf/create-pdf';
import { createPdfVersion2 } from '../../utils/generate-pdf/generate-pdf-version2';
import { AuditType } from '../audit-type/audit-type.entity';
import { Company } from '../company/company.entity';
import { CompanyRepository } from '../company/company.repository';
import { DateRangeDto } from '../dashboard/dtos/date-range.dto';
import { Department } from '../department-master/department.entity';
import { FocusRequestRepository } from '../focus-request/focus-request.repository';
import { InspectorTimeOffRepository } from '../inspector-time-off/inspector-time-off.repository';
import { PortMasterRepository } from '../port-master/port-master.repository';
import { CreateUserAssignmentDTO } from '../user-assignment/dto';
import { WorkflowPermission } from '../user-assignment/user-assignment.enum';
import { User } from '../user/user.entity';
import { UserRepository } from '../user/user.repository';
import { VesselRepository } from '../vessel/vessel.repository';
import { PRAuditorsDto } from './dto/pr-auditors.dto';
import { UpdatePRForGrantChartDTO } from './dto/update-PR-grant-chart.dto';
import { PlanningRequest } from './entities/planning-request.entity';
import { PRFocusRequest } from './entities/pr-focus-request.entity';
import { PROfficeComment } from './entities/pr-office-comment.entity';
import {
  AuditorPlanningsModel,
  CompanyPlanningsModel,
  DepartmentPlanningModel,
  VesselPlanningsModel,
} from './model';
import { InspectionMappingRepository } from '../inspection-mapping/repositories/inspection-mapping.repository';
import { CheckExistPlansDto } from './dto/check-exist-plans.dto';

@Injectable()
export class PlanningRequestService {
  constructor(
    private readonly planningRequestRepository: PlanningRequestRepository,
    private readonly voyageMasterDetailsRepository: VoyageMasterDetailsRepository,
    private readonly vesselRepository: VesselRepository,
    private readonly inspectorTimeOffRepository: InspectorTimeOffRepository,
    private readonly svmSupportService: SVMSupportService,
    private readonly companyRepository: CompanyRepository,
    private readonly notificationProducer: NotificationProducer,
    private readonly emailProducer: EmailProducer,
    private readonly svmIAMService: SVMIAMService,
    private readonly userRepository: UserRepository,
    private readonly portMasterRepository: PortMasterRepository,
    private readonly focusRequestRepository: FocusRequestRepository,
    private readonly inspectionMappingRepository: InspectionMappingRepository,
  ) {
    // this.planningRequestRepository._migrateGlobalStatus();
    this.planningRequestRepository._migrateGlobalStatusComplex();
    this.planningRequestRepository._migratePlanningRequestWithUserAssignment();
    this.planningRequestRepository._migrationUpdateGlobalStatus();
  }

  async createPR(
    params: CreatePlanningRequestDTO,
    query: SubmitPRQueryDTO,
    token: TokenPayloadModel,
  ) {
    const planningRequestId = Utils.strings.generateUUID();

    await this._validateCreatePRDto(params, token);

    let preparedPlanningRequestParams = {
      id: planningRequestId,
      auditCompanyId: params.auditCompanyId,
      vesselId: params.vesselId,
      departments: params.departmentIds?.map(
        (departmentId) => ({ id: departmentId } as Department),
      ),
      //departmentId: params.departmentId,
      timezone: params.timezone,
      typeOfAudit: params.typeOfAudit ? params.typeOfAudit : null,
      fromPortId: params.fromPortId ? params.fromPortId : null,
      workingType: params.workingType,
      toPortId: params.toPortId ? params.toPortId : null,
      toPortEstimatedTimeArrival: params.toPortEstimatedTimeArrival
        ? new Date(params.toPortEstimatedTimeArrival)
        : null,
      toPortEstimatedTimeDeparture: params.toPortEstimatedTimeDeparture
        ? new Date(params.toPortEstimatedTimeDeparture)
        : null,
      fromPortEstimatedTimeArrival: params.fromPortEstimatedTimeArrival
        ? new Date(params.fromPortEstimatedTimeArrival)
        : null,
      fromPortEstimatedTimeDeparture: params.fromPortEstimatedTimeDeparture
        ? new Date(params.fromPortEstimatedTimeDeparture)
        : null,
      plannedFromDate: params.plannedFromDate ? new Date(params.plannedFromDate) : null,
      plannedToDate: params.plannedToDate ? new Date(params.plannedToDate) : null,
      memo: params.memo ? params.memo : null,
      customerRef: params.customerRef ? params.customerRef : null,
      longitude: params.longitude ? params.longitude : null,
      latitude: params.latitude ? params.latitude : null,
      attachments: params.attachments ? params.attachments : null,
      leadAuditorId: params.leadAuditorId,
      auditTypes: params.auditTypeIds?.map((auditTypeId) => ({ id: auditTypeId } as AuditType)),
      auditors: params.auditorIds?.map((auditorId) => ({ id: auditorId } as User)),
      createdUserId: token.id,
      companyId: token.companyId,
      entityType: params.vesselId ? AuditEntity.VESSEL : AuditEntity.OFFICE,
      locationId: params.locationId,
      agentDetail: params.agentDetail ? params.agentDetail : null,
      vesselPIC: params.vesselPIC ? params.vesselPIC : null,
      group: params.group ? params.group : null,
      team: params.team ? params.team : null,
      nameOfInspectors: params.nameOfInspectors ? params.nameOfInspectors : '',
      auditTypesName: params.auditTypesName ? params.auditTypesName : '',
      voyageTypeId: params?.voyageTypeId ? params?.voyageTypeId : null,
      isSA: params?.isSA,
      doos: params?.doos,
    };

    const dueDateAndDateOfLastInspections = params.dueDateAndDateOfLastInspections?.map(
      (dueDateAndDateOfLastInspection) => ({
        ...dueDateAndDateOfLastInspection,
        planningRequestId,
      }),
    );
    // Check if SUBMITTED
    // Prepare user assignment
    let preparedUserAssignment = null;
    if (query.isSubmitted && query.isSubmitted.toString() === 'true' && params.userAssignment) {
      preparedUserAssignment = {
        planningRequestId,
        usersPermissions: params.userAssignment.usersPermissions,
      } as CreateUserAssignmentDTO;

      preparedPlanningRequestParams = Object.assign(preparedPlanningRequestParams, {
        status: PlanningRequestStatus.SUBMITTED,
        previousStatus: PlanningRequestStatus.DRAFT,
      });

      for (const value of preparedUserAssignment.usersPermissions) {
        if (value.permission === 'reviewer1' || value.permission === 'creator') {
          value['isView'] = true;
          value['isEdit'] = true;
        } else {
          value['isView'] = false;
        }
      }
    }

    // if (params.userAssignment) {
    //   preparedUserAssignment = {
    //     planningRequestId,
    //     usersPermissions: params.userAssignment.usersPermissions,
    //   } as CreateUserAssignmentDTO;
    // }

    // prepare office comments
    const preparedPROfficeComments: PROfficeComment[] = [];
    if (params.officeComments && params.officeComments.length > 0) {
      for (let j = 0; j < params.officeComments.length; j++) {
        const PROfficeCommentId = Utils.strings.generateUUID();
        const officeComment = params.officeComments[j];
        preparedPROfficeComments.push({
          id: PROfficeCommentId,
          planningRequestId,
          serialNumber: officeComment.serialNumber,
          comment: officeComment.comment,
        } as PROfficeComment);
      }
    }

    // Prepare focusRequest
    const preparedFocusRequest: PRFocusRequest[] = map(
      params.focusRequests,
      function (focusRequest) {
        return {
          answer: focusRequest.answer,
          memo: focusRequest.memo,
          focusRequestId: focusRequest.focusRequestId,
          planningRequestId,
          focusRequestObj: {
            status: CommonStatus.ACTIVE,
            question: focusRequest.question,
            deleted: false,
          },
        } as PRFocusRequest;
      },
    );

    const { dataNoti, dataMail, remanderMail } = await this.planningRequestRepository.createPR(
      preparedPlanningRequestParams as PlanningRequest,
      preparedFocusRequest,
      preparedPROfficeComments,
      token,
      preparedUserAssignment,
      dueDateAndDateOfLastInspections,
    );

    for (const data of dataNoti) {
      this.notificationProducer.publishNotification(data);
    }

    if (dataMail.length > 0) {
      this.emailProducer.publishEmail(dataMail);
    }

    if (remanderMail.length > 0) {
      this.emailProducer.publishEmail(remanderMail);
    }

    return 1;
  }

  async listPRDashboard(query: ListPlanningRequestQueryDTO, token: TokenPayloadModel) {
    const permissionToCheck =
      FeatureEnum.AUDIT_INSPECTION +
      '::' +
      SubFeatureEnum.PLANNING_AND_REQUEST +
      '::' +
      ActionEnum.VIEW;

    const { rolePermissions } = await this.svmIAMService.listRolePermissionsByUser(token.id, {
      companyId: token.companyId,
      parentCompanyId: token.parentCompanyId,
    });

    if (!rolePermissions.includes(permissionToCheck)) {
      return [];
    }
    Object.assign(query, {
      isDashboard: true,
    });
    return this.planningRequestRepository.listPlanningRequest(query, token);
  }

  async listPlanningRequest(query: ListPlanningRequestQueryDTO, token: TokenPayloadModel) {
    const { planningFrom, planningTo } = query;
    if ((!planningFrom && planningTo) || (planningFrom && !planningTo)) {
      throw new BadRequestException('planningFrom and planningTo must coexist or not');
    }
    if (query.reportFinding) {
      return this.planningRequestRepository.listPRForCreatingReportFinding(query, token);
    }
    if (query.auditTimeTable) {
      return this.planningRequestRepository.listPRForCreatingAuditTimeTable(query, token);
    }

    const listPr = await this.planningRequestRepository.listPlanningRequest(query, token, {
      filterModel: undefined,
      groupKeys: [],
      pivotCols: [],
      rowGroupCols: [],
      sortModel: [],
      valueCols: [],
      pivotMode: false,
    });

    // const roles = await this.userRepository.getUserRoles([token.id]);
    // if (!RoleScopeCheck.isAdmin(token)) {
    //   const filterPrByWorkflow = await this._filterPRByWorkflow(listPr.data, token);
    //   listPr.data = filterPrByWorkflow;
    //   listPr.totalItem = filterPrByWorkflow.length;
    // } else
    // if (RoleScopeCheck.isAdmin(token) && roles.size > 0) {
    //   const filterPrByWorkflow = await this.planningRequestRepository.filterPRByWorkflow(
    //     listPr.data,
    //     token.id,
    //     false,
    //     null,
    //     false,
    //   );
    //   listPr.data = filterPrByWorkflow;
    //   listPr.totalItem = filterPrByWorkflow.length;
    // }

    return listPr;
  }

  async listOnlyPlanningRequest(
    query: ListPlanningRequestQueryDTO,
    token: TokenPayloadModel,
    body: PayloadAGGridDto,
  ) {
    const { planningFrom, planningTo } = query;
    if ((!planningFrom && planningTo) || (planningFrom && !planningTo)) {
      throw new BadRequestException('planningFrom and planningTo must coexist or not');
    }

    const listPr = await this.planningRequestRepository.listPlanningRequest(query, token, body);

    return listPr;
  }

  async exportPlanningRequest(
    query: ListPlanningRequestQueryDTO,
    token: TokenPayloadModel,
    body: PayloadAGGridDto,
    res: Response,
  ) {
    const { planningFrom, planningTo } = query;
    if ((!planningFrom && planningTo) || (planningFrom && !planningTo)) {
      throw new BadRequestException('planningFrom and planningTo must exist or not');
    }

    const listPr = await this.planningRequestRepository.listPlanningRequest(query, token, body);

    const fileType = query?.fileType;

    const defaultValue = [];

    if (listPr?.data?.length) {
      listPr?.data.forEach((value) => {
        const {
          vessel,
          auditCompany,
          entityType,
          company,
          departments,
          location,
          auditTypes,
          leadAuditor,
          status,
          plannedFromDate,
          plannedToDate,
          workingType,
          typeOfAudit,
          dateOfLastInspection,
          dueDate,
          fromPort,
          toPort,
          fromPortEstimatedTimeArrival,
          fromPortEstimatedTimeDeparture,
          globalStatus,
          refId,
          plannedFromDate_Year,
          plannedFromDate_Month,
          plannedToDate_Year,
          plannedToDate_Month,
        } = value;

        defaultValue.push({
          [TitlePlanningRequest.VESSEL_NAME]: vessel?.name || '',
          [TitlePlanningRequest.VESSEL_TYPE]: vessel?.vesselType?.name || '',
          [TitlePlanningRequest.DOC_HOLDER_COMPANY]:
            entityType === 'Office'
              ? auditCompany?.name
              : vessel?.vesselDocHolders
                  ?.map((item) => (item?.status === 'active' ? item?.company?.name : ''))
                  ?.filter((item) => item)
                  .join(', '),
          [TitlePlanningRequest.OFFICE_NAME]: auditCompany?.name || '',
          [TitlePlanningRequest.DEPARTMENT]: departments?.map((i) => i.name).join(', ') || '',
          [TitlePlanningRequest.LOCATION]: location?.name || '',
          [TitlePlanningRequest.INSPECTION_TYPE]:
            auditTypes
              ?.map((i) => i.name)
              ?.toString()
              ?.replace(',', ', ') || '',
          [TitlePlanningRequest.LEAD_INSPECTOR_NAME]: leadAuditor?.username || '',
          [TitlePlanningRequest.SCHEDULE_STATUS]: status || '',
          [TitlePlanningRequest.INS_PLANNED_FROM]: plannedFromDate || '',
          [TitlePlanningRequest.INS_PLANNED_TO]: plannedToDate || '',
          [TitlePlanningRequest.ENTITY]: entityType || '',
          [TitlePlanningRequest.FLAG]: vessel?.country?.name || '',
          [TitlePlanningRequest.WORKING_TYPE]: workingType || '',
          [TitlePlanningRequest.VISIT_TYPE]: typeOfAudit || '',
          [TitlePlanningRequest.DATE_OF_LAST_INSPECTION]: dateOfLastInspection || '',
          [TitlePlanningRequest.DUE_DATE]: dueDate || '',
          [TitlePlanningRequest.FROM_PORT]: fromPort?.name || '',
          [TitlePlanningRequest.TO_PORT]: toPort?.name || '',
          [TitlePlanningRequest.ETA]: fromPortEstimatedTimeArrival || '',
          [TitlePlanningRequest.ETD]: fromPortEstimatedTimeDeparture || '',
          [TitlePlanningRequest.GLOBAL_STATUS]: globalStatus || '',
          [TitlePlanningRequest.CREATED_BY_COMPANY]: company?.name || '',
          [TitlePlanningRequest.REF_ID]: refId || '',
          [TitlePlanningRequest.INSPECTION_PLANNDED_FROM_YEAR]: plannedFromDate_Year || '',
          [TitlePlanningRequest.INSPECTION_PLANNDED_FROM_MONTH]: plannedFromDate_Month || '',
          [TitlePlanningRequest.INSPECTION_PLANNDED_TO_YEAR]: plannedToDate_Year || '',
          [TitlePlanningRequest.INSPECTION_PLANNDED_TO_MONTH]: plannedToDate_Month || '',
        });
      });
    }
    if (fileType === 'xlsx') {
      const rs = this.downloadResourceExcel(res, 'Planning-Request', defaultValue);
      return rs.xlsx.write(res).then(function () {
        res.status(200).end();
      });
    } else {
      return this.downloadResource(
        res,
        'Planning-Request.csv',
        AppConst.EXPORT_FIELDS_PLANNING_REQUEST,
        defaultValue,
      );
    }
  }

  private downloadResource(res: Response, fileName: string, fields, data) {
    const json2csv = new Parser({ fields });
    const csv = json2csv.parse(data);
    res.header('Content-Type', 'text/csv');
    res.attachment(fileName);
    const stream = this.bufferToStream(csv); // convert buffer to stream
    stream.pipe(res);
  }

  private bufferToStream(binary) {
    const readableInstanceStream = new Readable({
      read() {
        this.push(binary);
        this.push(null);
      },
    });
    return readableInstanceStream;
  }

  private downloadResourceExcel(res, fileName: string, data: any[]) {
    try {
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Sheet1');

      worksheet.addRow([
        TitlePlanningRequest.VESSEL_NAME,
        TitlePlanningRequest.VESSEL_TYPE,
        TitlePlanningRequest.DOC_HOLDER_COMPANY,
        TitlePlanningRequest.OFFICE_NAME,
        TitlePlanningRequest.DEPARTMENT,
        TitlePlanningRequest.LOCATION,
        TitlePlanningRequest.INSPECTION_TYPE,
        TitlePlanningRequest.LEAD_INSPECTOR_NAME,
        TitlePlanningRequest.SCHEDULE_STATUS,
        TitlePlanningRequest.INS_PLANNED_FROM,
        TitlePlanningRequest.INS_PLANNED_TO,
        TitlePlanningRequest.ENTITY,
        TitlePlanningRequest.FLAG,
        TitlePlanningRequest.WORKING_TYPE,
        TitlePlanningRequest.VISIT_TYPE,
        TitlePlanningRequest.DATE_OF_LAST_INSPECTION,
        TitlePlanningRequest.DUE_DATE,
        TitlePlanningRequest.FROM_PORT,
        TitlePlanningRequest.TO_PORT,
        TitlePlanningRequest.ETA,
        TitlePlanningRequest.ETD,
        TitlePlanningRequest.GLOBAL_STATUS,
        TitlePlanningRequest.CREATED_BY_COMPANY,
        TitlePlanningRequest.REF_ID,
        TitlePlanningRequest.INSPECTION_PLANNDED_FROM_YEAR,
        TitlePlanningRequest.INSPECTION_PLANNDED_FROM_MONTH,
        TitlePlanningRequest.INSPECTION_PLANNDED_TO_YEAR,
        TitlePlanningRequest.INSPECTION_PLANNDED_TO_MONTH,
      ]);
      data.forEach((value) => {
        worksheet.addRow(Object.values(value));
      });

      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
      res.setHeader('Content-Disposition', 'attachment; filename=' + `${fileName}.xlsx`);
      return workbook;
    } catch (error) {
      throw new BadRequestException(error);
    }
  }

  async listPlanRequestsGraphically(user: TokenPayloadModel, query: ListPRGraphicallyDTO) {
    const planningRequestsPaging = await this.planningRequestRepository.listPRForGranttChart(
      user,
      query,
    );
    const transferPlanningData = this.assignEditPermissionPlanning(
      planningRequestsPaging.data,
      user,
    );

    // if (query.groupBy == GraphicalGroupByEnum.AUDITOR) {
    // }
    if (query.groupBy == GraphicalGroupByEnum.VESSEL) {
      return this._groupPRsByVessel(transferPlanningData, user, query?.vesselName || null);
    }
    if (query.groupBy == GraphicalGroupByEnum.OFFICE) {
      return this._groupPRsByOffice(transferPlanningData, user, query?.companyName || null);
    }
    return this._groupPRsByAuditor(transferPlanningData, user, query);
  }

  async getDetailPRById(id: string, token: TokenPayloadModel, shortVersion?: boolean) {
    if (shortVersion) {
      return this.planningRequestRepository.getDetailInGeneral(id, token);
    }
    return await this.planningRequestRepository.getDetailPRById(id, token);
  }

  async listAuditCheckListsByPR(planningId: string) {
    return this.planningRequestRepository.listAuditCheckListsByPR(planningId);
  }

  async listUpcomingPlanningRequest(user: TokenPayloadModel, query: DateRangeDto) {
    return this.planningRequestRepository.listUpcomingPlanningRequest(user, query);
  }

  async updatePR(
    id: string,
    params: UpdatePlanningRequestDTO,
    token: TokenPayloadModel,
    query: SubmitPRQueryDTO,
  ) {
    const prDetail = await this.planningRequestRepository.getDetailPRForUpdate(id, token);
    //Validate Data
    await this._validateUpdatePRDto(params, token, prDetail);
    // user in user assignment can update
    const userCanUpdate = await this.planningRequestRepository.checkUserCanUpdateRecord(
      token.id,
      id,
    );
    if (!userCanUpdate && prDetail.createdUserId !== token.id) {
      throw new BaseError({
        status: 403,
        message: 'common.UPDATE_FORBIDDEN',
      });
    }
    // check if vesselId and department id coexist
    if (params.departmentId && params.vesselId) {
      throw new BaseError({
        status: 400,
        message: 'planningRequest.VESSEL_ID_AND_DEPARTMENT_ID_COEXIST',
      });
    }

    const paramsUpdatePR = omit(params, [
      'auditorInfos',
      'additionalReviewers',
      'officeComments',
      'auditTypeIds',
      // 'auditChecklistIds',
      'plannedToDate',
      'plannedFromDate',
      'attachments',
      'toPortEstimatedTimeArrival',
      'toPortEstimatedTimeDeparture',
      'toPortEstimatedTimeDeparture',
      'fromPortEstimatedTimeDeparture',
      'userAssignment',
    ]);
    const preparedPRParams = {
      ...paramsUpdatePR,
      id,
      leadAuditorId: params.leadAuditorId || null,
      updatedUserId: token.id,
      attachments: params.attachments ? params.attachments : null,
      auditTypes: params.auditTypeIds.map((auditTypeId) => ({ id: auditTypeId } as AuditType)),
      auditors: params.auditorIds?.map((auditorId) => ({ id: auditorId } as User)) || [{} as User],
      departments: params.departmentIds?.map(
        (departmentId) =>
          ({
            id: departmentId,
          } as Department),
      ) || [{} as Department],
      // auditChecklist: params.auditChecklistIds.map(
      //   (auditChecklistId) => ({ id: auditChecklistId } as AuditChecklist),
      // ),
      toPortEstimatedTimeArrival: params.toPortEstimatedTimeArrival
        ? new Date(params.toPortEstimatedTimeArrival)
        : null,
      toPortEstimatedTimeDeparture: params.toPortEstimatedTimeDeparture
        ? new Date(params.toPortEstimatedTimeDeparture)
        : null,
      fromPortEstimatedTimeArrival: params.fromPortEstimatedTimeArrival
        ? new Date(params.fromPortEstimatedTimeArrival)
        : null,
      fromPortEstimatedTimeDeparture: params.fromPortEstimatedTimeDeparture
        ? new Date(params.fromPortEstimatedTimeDeparture)
        : null,
      plannedFromDate: new Date(params.plannedFromDate),
      plannedToDate: new Date(params.plannedToDate),
      entityType: params.vesselId ? AuditEntity.VESSEL : AuditEntity.OFFICE,
      locationId: params.locationId ? params.locationId : null,
      agentDetail: params.agentDetail ? params.agentDetail : null,
      vesselPIC: params.vesselPIC ? params.vesselPIC : null,
      group: params.group ? params.group : null,
      team: params.team ? params.team : null,
      nameOfInspectors: params.nameOfInspectors ? params.nameOfInspectors : '',
      auditTypesName: params.auditTypesName ? params.auditTypesName : '',
    } as PlanningRequest;
    // Prepare Office comment
    const preparedPROfficeComments: PROfficeComment[] = [];
    if (params.officeComments && params.officeComments.length > 0) {
      for (let j = 0; j < params.officeComments.length; j++) {
        const officeComment = params.officeComments[j];
        preparedPROfficeComments.push({
          id: officeComment.id ? officeComment.id : Utils.strings.generateUUID(),
          serialNumber: officeComment.serialNumber,
          planningRequestId: id,
          createdUser: officeComment.createdUser,
          comment: officeComment.comment,
        } as PROfficeComment);
      }
    }

    const { dataNoti, dataMail } = await this.planningRequestRepository.updatePR(
      id,
      params,
      preparedPRParams,
      preparedPROfficeComments,
      params.focusRequests as PRFocusRequest[],
      token,
      query,
      params.dueDateAndDateOfLastInspections,
    );
    for (const data of dataNoti) {
      this.notificationProducer.publishNotification(data);
    }

    if (dataMail.length > 0) {
      this.emailProducer.publishEmail(dataMail);
    }

    return 1;
  }

  async updatePRForGrantChart(id: string, body: UpdatePRForGrantChartDTO, user: TokenPayloadModel) {
    return await this.planningRequestRepository.updatePRForGrantChart(id, body, user);
  }

  async deletePlanningRequest(id: string, token: TokenPayloadModel) {
    return this.planningRequestRepository.deletePlanningRequest(id, token);
  }

  async approvePlanningRequest(
    id: string,
    body: RejectPRBodyDTO,
    query: ApprovePRQueryDTO,
    token: TokenPayloadModel,
  ) {
    if (query.isRejected && query.isRejected.toString() === 'true') {
      const { dataNoti, dataMail } = await this.planningRequestRepository.rejectPlanningRequest(
        id,
        body,
        token,
      );
      for (const data of dataNoti) {
        this.notificationProducer.publishNotification(data);
      }
      if (dataMail.length > 0) {
        this.emailProducer.publishEmail(dataMail);
      }
    } else {
      const { dataNoti, dataMail } = await this.planningRequestRepository.approvePlanningRequest(
        id,
        body,
        token,
      );
      for (const data of dataNoti) {
        this.notificationProducer.publishNotification(data);
      }
      if (dataMail.length > 0) {
        this.emailProducer.publishEmail(dataMail);
      }
    }
    return 1;
  }

  async reviewPlanningRequest(
    id: string,
    body: ReviewPRBodyDTO,
    query: ReviewPRQueryDTO,
    token: TokenPayloadModel,
    workflowPermissions: string[],
  ) {
    if (query.isRejected && query.isRejected.toString() === 'true') {
      const { dataNoti, dataMail } = await this.planningRequestRepository.rejectPlanningRequest(
        id,
        body,
        token,
      );
      for (const data of dataNoti) {
        this.notificationProducer.publishNotification(data);
      }
      if (dataMail.length > 0) {
        this.emailProducer.publishEmail(dataMail);
      }
    } else {
      const { dataNoti, dataMail } = await this.planningRequestRepository.reviewPlanningRequest(
        id,
        body,
        token,
        workflowPermissions,
      );
      for (const data of dataNoti) {
        this.notificationProducer.publishNotification(data);
      }
      if (dataMail.length > 0) {
        this.emailProducer.publishEmail(dataMail);
      }
    }
    return 1;
  }

  async auditorAcceptPlanningRequest(
    id: string,
    body: AcceptPRBodyDTO,
    query: AcceptPRQueryDTO,
    token: TokenPayloadModel,
  ) {
    if (query.isRejected && query.isRejected.toString() === 'true') {
      const { dataNoti, dataMail } = await this.planningRequestRepository.rejectPlanningRequest(
        id,
        body,
        token,
      );
      for (const data of dataNoti) {
        this.notificationProducer.publishNotification(data);
      }
      if (dataMail.length > 0) {
        this.emailProducer.publishEmail(dataMail);
      }
    } else {
      if (!body.userAssignment) {
        throw new BaseError({ message: 'userAssignment.REQUIRED' });
      }
      const {
        dataNoti,
        dataMail,
      } = await this.planningRequestRepository.auditorAcceptPlanningRequest(id, body, token);
      for (const data of dataNoti) {
        this.notificationProducer.publishNotification(data);
      }
      if (dataMail.length > 0) {
        this.emailProducer.publishEmail(dataMail);
      }
    }
    return 1;
  }

  async auditeeAcceptPlanningRequest(
    id: string,
    body: AcceptPRBodyDTO,
    query: AcceptPRQueryDTO,
    token: TokenPayloadModel,
  ) {
    if (query.isRejected && query.isRejected.toString() === 'true') {
      const { dataNoti, dataMail } = await this.planningRequestRepository.rejectPlanningRequest(
        id,
        body,
        token,
      );
      for (const data of dataNoti) {
        this.notificationProducer.publishNotification(data);
      }
      if (dataMail.length > 0) {
        this.emailProducer.publishEmail(dataMail);
      }
    } else {
      const {
        dataNoti,
        dataMail,
      } = await this.planningRequestRepository.auditeeAcceptPlanningRequest(id, body, token);
      for (const data of dataNoti) {
        this.notificationProducer.publishNotification(data);
      }
      if (dataMail.length > 0) {
        this.emailProducer.publishEmail(dataMail);
      }
    }
    return 1;
  }

  async cancelPlanningRequest(id: string, body: RejectPRBodyDTO, token: TokenPayloadModel) {
    const { dataNoti, dataMail } = await this.planningRequestRepository.cancelPlanningRequest(
      id,
      body,
      token,
    );
    for (const data of dataNoti) {
      this.notificationProducer.publishNotification(data);
    }
    if (dataMail.length > 0) {
      this.emailProducer.publishEmail(dataMail);
    }
    return 1;
  }

  async createPlaningRequestPdf(planningRequestId: string, res, user) {
    const data = await this.planningRequestRepository.getPlanningRequestDataForCreatePdf(
      planningRequestId,
    );
    const logoId = await this.companyRepository.getCompanyLogoId(user.companyId);
    let logoLink = '';
    if (logoId) {
      const data: any = await this.svmSupportService.getDetailImage([logoId]);
      logoLink = data[0].link;
    }

    return await createPdf(
      'planing-request-template',
      'checklistTemplate',
      { ...data, logoLink },
      res,
    );
  }

  async checkAvailableAuditors(params: CheckAvailableAuditorsDTO) {
    return this.planningRequestRepository.checkAvailableAuditors(params);
  }

  private _getMappedAuditors(uniqueAllAuditors, auditorPRsArr, timeOffs) {
    const mappedAuditors = [];
    for (let i = 0; i < uniqueAllAuditors.length; i++) {
      const findAuditor = auditorPRsArr.find((auditorPr) => {
        return auditorPr.auditor.id === uniqueAllAuditors[i].id;
      });
      const timeOffByAuditors = timeOffs.filter(
        (timeOff) => timeOff.offUserId === uniqueAllAuditors[i].id,
      );
      if (!findAuditor?.planningRequests?.length && !timeOffByAuditors?.length) {
        continue;
      }
      if (findAuditor) {
        mappedAuditors.push({
          ...findAuditor,
          timeOffs: timeOffByAuditors,
        });
      } else {
        mappedAuditors.push({
          auditor: {
            id: uniqueAllAuditors[i].id,
            username: uniqueAllAuditors[i].username,
            companyId: uniqueAllAuditors[i].companyId,
          },
          planningRequests: [],
          timeOffs: timeOffByAuditors,
        });
      }
    }
    return mappedAuditors;
  }

  private async _groupPRsByAuditor(
    planningRequests: PlanningRequest[],
    user: TokenPayloadModel,
    query: ListPRGraphicallyDTO,
  ) {
    const auditorPRsMap = new Map<string, AuditorPlanningsModel>();
    const unassignedPRs: PlanningRequest[] = [];

    const auditors = await this.svmIAMService.getAuditorsByCompany(user.companyId, {
      auditorName: query.auditorName,
    });

    // Loop all planningRequests param arr
    for (let i = 0; i < planningRequests.length; i++) {
      // Check unassigned pr
      if (!planningRequests[i].auditors || planningRequests[i].auditors.length === 0) {
        unassignedPRs.push(planningRequests[i]);
        continue;
      }

      // Loop all auditors of pr
      for (let j = 0; j < planningRequests[i].auditors.length; j++) {
        const auditor = planningRequests[i].auditors[j];
        if (auditor && !auditorPRsMap.has(auditor.id)) {
          const newValue: AuditorPlanningsModel = {
            auditor: { id: auditor.id, username: auditor.username, companyId: auditor.companyId },
            planningRequests: [planningRequests[i]],
          };
          auditorPRsMap.set(auditor.id, newValue);
        } else {
          auditorPRsMap.get(auditor.id).planningRequests.push(planningRequests[i]);
        }
      }
    }

    const auditorPRsArr = Array.from(auditorPRsMap.values()).sort((item1, item2) =>
      item1.auditor.username.localeCompare(item2.auditor.username),
    );

    // mapped data with all auditors
    const auditorsPr = auditorPRsArr.map((auditorPr) => auditorPr.auditor);
    const uniqueAllAuditors = [
      ...new Map([...auditors, ...auditorsPr].map((obj) => [obj.id, obj])).values(),
    ];

    // get TimeOf by AuditorIds
    const auditorIds = uniqueAllAuditors.map((auditor) => auditor.id);
    let timeOffs = [];
    if (auditorIds.length > 0) {
      timeOffs = await this.inspectorTimeOffRepository.listTimeOffByAuditors(
        auditorIds,
        user,
        query,
      );
    }

    const mappedAuditors = this._getMappedAuditors(uniqueAllAuditors, auditorPRsArr, timeOffs);

    if (!query.auditorName) {
      mappedAuditors.unshift({
        auditor: {
          id: '',
          username: 'Unassigned Inspector',
        },
        planningRequests: unassignedPRs,
        timeOffs: [],
      });
    }

    // sort by numbers of planning request
    return mappedAuditors.sort((a, b) => b.planningRequests.length - a.planningRequests.length);
  }

  private async _groupPRsByVessel(
    planningRequests: PlanningRequest[],
    user: TokenPayloadModel,
    vesselName: string,
  ) {
    const vesselPRsMap = new Map<string, VesselPlanningsModel>();

    // Loop all planningRequests param arr
    for (let i = 0; i < planningRequests.length; i++) {
      const vesselByPlanning = planningRequests[i].vessel;
      // Loop all vessel of pr
      if (vesselByPlanning && !vesselPRsMap.has(vesselByPlanning.id)) {
        const newValue: VesselPlanningsModel = {
          vessel: { id: vesselByPlanning.id, name: vesselByPlanning.name },
          planningRequests: [planningRequests[i]],
        };
        vesselPRsMap.set(vesselByPlanning.id, newValue);
      } else {
        vesselPRsMap.get(vesselByPlanning.id).planningRequests.push(planningRequests[i]);
      }
    }

    const vesselPRsArr = Array.from(vesselPRsMap.values()).sort((item1, item2) =>
      item1.vessel.name.localeCompare(item2.vessel.name),
    );

    // Commend vessel schedule because this data will be used in future
    // MAP old logic data with all vessels
    // const vesselSchedules = await this.voyageMasterDetailsRepository.listVoyageForGraphicalChart(
    //   user,
    //   vesselName,
    // );
    // console.log('vesselSchedules: ', new Date(), vesselSchedules.length);
    // const vesselMasters = await this.vesselRepository.listVesselForGraphical(user, {
    //   vesselName,
    // });
    // console.log('vesselMasters: ', new Date(), vesselMasters.length);
    // const vesselUsedSchedules = vesselSchedules.map((vs) => vs.vessel);
    // console.log('vesselUsedSchedules: ', new Date());

    // const uniqueVessels = [...new Map([...vesselMasters].map((obj) => [obj.id, obj])).values()];
    // console.log('uniqueVessels: ', new Date(), uniqueVessels.length);
    // const mappedVessels = [];

    // for (let i = 0; i < uniqueVessels.length; i++) {
    //   const findVessel = vesselPRsArr.find((vesselPr) => {
    //     return vesselPr.vessel.id === uniqueVessels[i].id;
    //   });

    // const scheduleByVessel = vesselSchedules.filter(
    //   (schedule) =>
    //     schedule.vesselId === uniqueVessels[i].id && schedule.ETA_LT && schedule.ETD_LT,
    // );
    // if (!findVessel?.planningRequests?.length) {
    //   continue;
    // }
    // if (findVessel) {
    // mappedVessels.push({
    //   ...vesselPRsArr,
    // vesselSchedules: scheduleByVessel,
    // });
    // } else {
    //   mappedVessels.push({
    //     vessel: {
    //       id: uniqueVessels[i].id,
    //       name: uniqueVessels[i].name,
    //     },
    //     planningRequests: [],
    //     // vesselSchedules: scheduleByVessel,
    //   });
    // }
    // }

    // sort by numbers of planning request
    return vesselPRsArr.sort((a, b) => b.planningRequests.length - a.planningRequests.length);
  }

  private async _getMappedOffice(companies, companyResArr) {
    const mappedOffice = [];
    for (let i = 0; i < companies.length; i++) {
      const findOffice = companyResArr.find((companyRes) => {
        return companyRes.company.id === companies[i].id;
      });
      const departmentByOffice = companies[i]?.departments?.map((dp) => {
        return {
          department: {
            id: dp.id,
            name: dp.name,
          },
          planningRequests: [],
        };
      });
      if (!findOffice?.planningRequests?.length && !departmentByOffice?.length) {
        continue;
      }
      if (findOffice) {
        mappedOffice.push(findOffice);
      } else {
        mappedOffice.push({
          company: {
            id: companies[i].id,
            name: companies[i].name,
          },
          departments: departmentByOffice,
          planningRequests: [],
        });
      }
    }
    return mappedOffice;
  }

  private _getCompanyPRsMap(planningRequests) {
    const companyPRsMap = new Map<string, CompanyPlanningsModel>();
    // Loop PR
    for (let i = 0; i < planningRequests.length; i++) {
      // Check existed and create Company Map
      const companyByPR = planningRequests[i].auditCompany;
      if (companyByPR && !companyPRsMap.has(companyByPR.id)) {
        companyPRsMap.set(companyByPR.id, {
          company: { id: companyByPR.id, name: companyByPR.name },
          departments: [],
          planningRequests: [],
        });
      }

      if (companyByPR) {
        // Push PR to company
        // Check company Map existed?
        if (!companyPRsMap.has(companyByPR.id)) {
          companyPRsMap.set(companyByPR.id, {
            company: { id: companyByPR.id, name: companyByPR.name },
            planningRequests: [planningRequests[i]],
          });
        } else {
          companyPRsMap.get(companyByPR.id).planningRequests.push(planningRequests[i]);
        }
      }
    }
    return companyPRsMap;
  }

  private _getDepartmentPRsMap(planningRequests) {
    const departmentPRsMap = new Map<string, DepartmentPlanningModel>();
    // Loop PR
    for (let i = 0; i < planningRequests.length; i++) {
      if (!planningRequests[i].departments?.length) {
        continue;
      }
      for (let j = 0; j < planningRequests[i].departments.length; j++) {
        const departmentByPR = planningRequests[i].departments[j];
        if (departmentByPR) {
          if (!departmentPRsMap.has(departmentByPR.id)) {
            departmentPRsMap.set(departmentByPR.id, {
              department: {
                id: departmentByPR.id,
                name: departmentByPR.name,
                companyId: departmentByPR.companyId,
              },
              planningRequests: [planningRequests[i]],
            });
          } else {
            departmentPRsMap.get(departmentByPR.id).planningRequests.push(planningRequests[i]);
          }
        }
      }
    }
    return departmentPRsMap;
  }

  private async _groupPRsByOffice(
    planningRequests: PlanningRequest[],
    user: TokenPayloadModel,
    companyName: string,
  ) {
    const companyPRsMap = this._getCompanyPRsMap(planningRequests);
    const departmentPRsMap = this._getDepartmentPRsMap(planningRequests);

    const departmentResArr = Array.from(departmentPRsMap.values()).sort((item1, item2) =>
      item1.department.name.localeCompare(item2.department.name),
    );
    const companyArr = Array.from(companyPRsMap.values());
    let companyResArr: CompanyPlanningsModel[] = [];
    for (let m = 0; m < companyArr.length; m++) {
      let companyTemp: CompanyPlanningsModel;
      if (companyArr[m].planningRequests.length > 0) {
        companyTemp = Object.assign(companyArr[m], { departments: [] });
      } else {
        companyTemp = Object.assign(companyArr[m], { departments: [], planningRequests: [] });
      }
      // Sort department to correct company
      for (let n = 0; n < departmentResArr.length; n++) {
        if (companyArr[m].company.id == departmentResArr[n].department.companyId) {
          companyTemp.departments.push(departmentResArr[n]);
        }
      }
      companyResArr.push(companyTemp);
    }
    companyResArr = companyResArr.sort((item1, item2) =>
      item1.company.name.localeCompare(item2.company.name),
    );

    // MAP old logic data with all companies
    const companies = await this.companyRepository.listCompanyForGrantChart(user, companyName);
    const mappedOffice = await this._getMappedOffice(companies, companyResArr);

    // sort by numbers of planning request
    return mappedOffice.sort((a, b) => b.planningRequests.length - a.planningRequests.length);
  }

  async totalUnplannedPR(user: TokenPayloadModel, query?: ListTotalUnplannedDTO) {
    const listQuery = {
      pageSize: -1,
      tab: PlanningTab.UNPLANNED,
    };
    if (query.entityType) {
      Object.assign(listQuery, {
        entityType: query.entityType,
      });
    }
    return await this.planningRequestRepository.listPlanningRequest(
      listQuery,
      user,
      new PayloadAGGridDto(),
      true,
    );
  }

  async createPlaningRequestPdfv2(
    planningRequestId,
    user,
    res,
    { timezone }: TimezoneDTO = { timezone: 'Asia/Ho_Chi_Minh' },
  ) {
    const data = await this.planningRequestRepository.getPlanningRequestDataForCreatePdf(
      planningRequestId,
    );
    const logoId = await this.companyRepository.getCompanyLogoId(user.companyId);
    let logoBase64 =
      'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/+vXfwAJ5wP0lHD7egAAAABJRU5ErkJggg==';
    if (logoId) {
      const data = await this.svmSupportService.downloadDetailImages([logoId]);
      logoBase64 = data[0].base64DataFormat;
    }

    return await createPdfVersion2(
      TemplateTypeEnum.PLANNING_AND_REQUEST,
      '',
      { ...data[0], logoBase64, timezone },
      res,
    );
  }

  async planningRequestAuditors(user: TokenPayloadModel, body: PRAuditorsDto) {
    return await this.planningRequestRepository.planningRequestAuditors(user, body);
  }

  // getting company mails for office entity
  async getOfficeAssociatedToMailIds(auditCompanyId: string) {
    let officeReceivedEmails = [];
    const getcompanyMailId = await getRepository(Company)
      .createQueryBuilder('company')
      .select(['company.email'])
      .where('company.id = :companyId', { companyId: auditCompanyId })
      .getMany();
    for (const data of getcompanyMailId) {
      const email = data.email.split(/[;]/);
      officeReceivedEmails = officeReceivedEmails.concat(email);
    }
    return { receiverEmails: officeReceivedEmails };
  }

  public async _filterPRByWorkflow(planningRequests, user: TokenPayloadModel) {
    return await this.planningRequestRepository.filterPRByWorkflow(planningRequests, user.id);
  }

  public assignEditPermissionPlanning(planningRequests, user: TokenPayloadModel) {
    const filteredPr = [];
    for (let i = 0; i < planningRequests.length; i++) {
      const planningRequest = planningRequests[i];
      let isEdit = false;
      switch (planningRequest.status) {
        case PlanningRequestStatus.DRAFT:
          isEdit = planningRequest.userAssignments.some(
            (ua) => ua.userId === user.id && ua.permission === WorkflowPermission.CREATOR,
          );
          break;
        case PlanningRequestStatus.SUBMITTED:
          isEdit = planningRequest.userAssignments.some(
            (ua) => ua.userId === user.id && ua.permission === WorkflowPermission.REVIEWER1,
          );
          break;
        case PlanningRequestStatus.REVIEWED_1:
          isEdit = planningRequest.userAssignments.some(
            (ua) =>
              ua.userId === user.id &&
              (ua.permission === WorkflowPermission.REVIEWER2 ||
                ua.permission === WorkflowPermission.APPROVER),
          );
          break;
        case PlanningRequestStatus.REVIEWED_2:
          isEdit = planningRequest.userAssignments.some(
            (ua) =>
              ua.userId === user.id &&
              (ua.permission === WorkflowPermission.REVIEWER3 ||
                ua.permission === WorkflowPermission.APPROVER),
          );
          break;

        case PlanningRequestStatus.REVIEWED_3:
          isEdit = planningRequest.userAssignments.some(
            (ua) =>
              ua.userId === user.id &&
              (ua.permission === WorkflowPermission.REVIEWER4 ||
                ua.permission === WorkflowPermission.APPROVER),
          );
          break;
        case PlanningRequestStatus.REVIEWED_4:
          isEdit = planningRequest.userAssignments.some(
            (ua) =>
              ua.userId === user.id &&
              (ua.permission === WorkflowPermission.REVIEWER5 ||
                ua.permission === WorkflowPermission.APPROVER),
          );
          break;
        case PlanningRequestStatus.APPROVED:
          isEdit = planningRequest.userAssignments.some(
            (ua) => ua.userId === user.id && ua.permission === WorkflowPermission.APPROVER,
          );
          break;
        case PlanningRequestStatus.IN_PROGRESS:
        case PlanningRequestStatus.COMPLETED:
          isEdit = false;
          break;
        default:
          isEdit = false;
      }

      filteredPr.push({ ...omit(planningRequest, ['userAssignments']), isEdit });
    }
    return filteredPr;
  }

  async _validateCreatePRDto(params: CreatePlanningRequestDTO, token: TokenPayloadModel) {
    //Port
    if (params.fromPortId) {
      await this.portMasterRepository._checkActivePort([params.fromPortId], token.companyId);
    }
    if (params.toPortId) {
      await this.portMasterRepository._checkActivePort([params.toPortId], token.companyId);
    }
    //vessel
    if (params.vesselId) {
      await this.vesselRepository._checkActiveVessel([params.vesselId], token);
    }
    //Focus request
    if (params.focusRequests?.length) {
      const focusIds = params.focusRequests.map((it) => it.focusRequestId);
      await this.focusRequestRepository._checkActiveFocusRequest(focusIds, token);
    }
    //check active audit types
    await this.inspectionMappingRepository._checkActiveInspectionMapping(
      params.auditTypeIds,
      token.companyId,
    );
  }
  async _validateUpdatePRDto(
    params: UpdatePlanningRequestDTO,
    token: TokenPayloadModel,
    currentPR: PlanningRequest,
  ) {
    //Port
    const activePortIds = await this.portMasterRepository._getPortByStatus(
      token.companyId,
      CommonStatus.ACTIVE,
    );

    if (
      params.fromPortId &&
      currentPR.fromPortId !== params.fromPortId &&
      !activePortIds.includes(params.fromPortId)
    ) {
      throw new BaseError({ status: 400, message: 'portMaster.PORT_INACTIVE' });
    }
    if (
      params.toPortId &&
      currentPR.toPortId !== params.toPortId &&
      !activePortIds.includes(params.toPortId)
    ) {
      throw new BaseError({ status: 400, message: 'portMaster.PORT_INACTIVE' });
    }
    // Check active vessel
    await this.vesselRepository._compareAndCheckActiveVessel(
      currentPR.vesselId,
      params.vesselId,
      token,
    );
    //Focus request
    const focusRequestMap = new Map<string, PRFocusRequest>();
    currentPR.pRFocusRequests?.forEach((prFocus) => focusRequestMap.set(prFocus.id, prFocus));
    const addfocusRequest = params.focusRequests?.filter(
      (focusRequest) => !focusRequestMap.has(focusRequest.id),
    );
    if (addfocusRequest.length) {
      const focusIds = addfocusRequest.map((it) => it.focusRequestId);
      await this.focusRequestRepository._checkActiveFocusRequest(focusIds, token);
    }
    //check active audit types
    const currentAuditTypeIds = currentPR.auditTypes.map((auditType) => auditType.id);
    const auditTypeIds = params.auditTypeIds.filter((x) => !currentAuditTypeIds.includes(x));
    if (auditTypeIds.length) {
      await this.inspectionMappingRepository._checkActiveInspectionMapping(
        auditTypeIds,
        token.companyId,
      );
    }
  }

  /**
   * Check if there are existing plans for a vessel within a given date range
   * @param body - Contains vessel, audit company, audit types and date range details
   * @returns Existing planning requests that match the criteria
   */
  async checkExistingPlan(body: CheckExistPlansDto) {
    const { vesselId, auditCompanyId, auditTypeIds, plannedFromDate } = body;
    const existingPlan = await this.planningRequestRepository.checkExistingPlan(
      auditTypeIds,
      plannedFromDate,
      vesselId,
      auditCompanyId,
    );
    return existingPlan;
  }

  /**
   * Check if there are existing SA inspections in progress for the same company and Standard names
   * @param body - Contains vessel ID, audit company ID, and audit type IDs
   * @param token - User token containing company information
   * @returns Existing SA planning requests that are in progress
   */
  async checkSAInspectionInProgress(body: CheckSAInProgressDto, token: TokenPayloadModel) {
    const { auditCompanyId, auditTypeIds } = body;
    const existingSAInProgress = await this.planningRequestRepository.checkSAInspectionInProgress(
      token.companyId,
      auditTypeIds,
      auditCompanyId,
    );
    return existingSAInProgress;
  }
}
