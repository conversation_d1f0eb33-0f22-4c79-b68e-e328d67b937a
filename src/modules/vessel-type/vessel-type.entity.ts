import { Entity, Column, Index, OneToMany, ManyToOne } from 'typeorm';
import { IdentifyEntity } from 'svm-nest-lib-v3';
import { DBIndexes } from '../../commons/consts/db.const';
import { StatusCommon } from '../../commons/enums';
import { Vessel } from '../vessel/entity/vessel.entity';
import { Company } from '../company/company.entity';
import { User } from '../user/user.entity';
import { VesselTypeEnum } from '../../modules-qa/sire-viq/enums';

@Entity()
@Index(DBIndexes.IDX_VESSEL_TYPE_CODE_COMPANYID, ['code', 'companyId'], {
  unique: true,
  where: 'deleted = false',
})
@Index(DBIndexes.IDX_VESSEL_TYPE_NAME_COMPANYID, ['name', 'companyId'], {
  unique: true,
  where: 'deleted = false',
})
export class VesselType extends IdentifyEntity {
  @Column({ type: 'citext' })
  public code: string;

  @Column({ type: 'citext' })
  public name: string;

  @Column({ type: 'smallint' })
  public vettingRiskScore: number;

  @Column({ type: 'enum', enum: StatusCommon })
  public status: string;

  @Column({ nullable: true })
  public description: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  public icon: string;

  @Column({ type: 'uuid' })
  public companyId: string;

  @Column({ type: 'uuid', nullable: true })
  public createdUserId: string;

  @Column({ type: 'uuid', nullable: true })
  public updatedUserId?: string;

  @Column({ type: 'boolean', nullable: false, default: false })
  public isSIRECVIQ?: boolean;

  @Column({ type: 'enum', enum: VesselTypeEnum, nullable: true })
  public sireVesselType?: string;

  /** Mapping foreign keys */
  @ManyToOne(() => Company, { onDelete: 'CASCADE' })
  company: Company;

  @ManyToOne(() => User, { onDelete: 'NO ACTION' })
  createdUser: User;

  @ManyToOne(() => User, { onDelete: 'NO ACTION' })
  updatedUser: User;

  /** One to Many */
  @OneToMany(() => Vessel, (vessel) => vessel.vesselType)
  vessels: Vessel[];
}
