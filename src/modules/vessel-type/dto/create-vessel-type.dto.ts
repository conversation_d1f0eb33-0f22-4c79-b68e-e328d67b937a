import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsBoolean, IsEnum, IsInt, IsNotEmpty, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>eng<PERSON>, <PERSON> } from 'class-validator';
import { StatusCommon } from '../../../commons/enums';
import { IconEnum } from '../../../commons/enums/vesssel-type.enum';
import { VesselTypeEnum } from '../../../modules-qa/sire-viq/enums';

export class CreateVesselTypeDto {
  @ApiProperty({ type: 'string' })
  @IsNotEmpty({ message: 'common.REQUIRED_FIELD' })
  @Transform((value: string) => value.trim())
  @MaxLength(128)
  code: string;

  @ApiProperty({ type: 'string' })
  @IsNotEmpty({ message: 'common.REQUIRED_FIELD' })
  @Transform((value: string) => value.trim())
  @MaxLength(128)
  name: string;

  @ApiProperty({ type: 'string' })
  @IsNotEmpty({ message: 'common.REQUIRED_FIELD' })
  @MaxLength(50)
  @IsEnum(IconEnum)
  icon: string;

  @ApiProperty({
    type: 'number',
  })
  @Type(() => Number)
  @IsInt()
  @Min(0)
  @Max(5)
  vettingRiskScore: number;

  @ApiProperty({ type: 'string' })
  @ApiProperty({ enum: StatusCommon })
  @IsEnum(StatusCommon)
  status: string;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @Transform((value: string) => value.trim())
  @MaxLength(128)
  description?: string;

  @ApiProperty({ type: 'boolean', required: false })
  @IsOptional()
  @IsBoolean()
  isSIRECVIQ?: boolean;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @IsEnum(VesselTypeEnum)
  sireVesselType?: string;
}
