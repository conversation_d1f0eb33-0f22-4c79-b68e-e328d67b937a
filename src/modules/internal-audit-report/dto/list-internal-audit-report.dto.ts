import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  <PERSON>rrayUnique,
  IsArray,
  IsBoolean,
  IsDateString,
  IsEnum,
  IsOptional,
  IsUUID,
} from 'class-validator';
import { IsGreaterThanOrEqual } from 'svm-nest-lib-v3';
import { ListQueryDto } from '../../../commons/dtos';
import { AuditEntity, InternalAuditReportStatus } from '../../../commons/enums';
import { DataType, FilterField } from '../../../utils';

export class ListInternalAuditReportDTO extends ListQueryDto {
  @ApiProperty({
    type: 'string',
    description: 'ISO 8601 date only',
    example: '2021-09-21',
    required: false,
  })
  @Transform((value: string) => value.substr(0, 10))
  @IsOptional()
  @IsDateString()
  fromDate?: string;

  @ApiProperty({
    type: 'string',
    description: 'ISO 8601 date only',
    example: '2021-09-21',
    required: false,
  })
  @Transform((value: string) => value.substr(0, 10))
  @IsOptional()
  @IsDateString()
  @IsGreaterThanOrEqual('fromDate', { message: 'common.INVALID_DATE_RANGE' })
  toDate?: string;

  @ApiProperty({
    enum: InternalAuditReportStatus,
    required: false,
    description: 'IAR status',
  })
  @IsOptional()
  @IsEnum(InternalAuditReportStatus)
  status?: string; // common status

  @ApiProperty({
    enum: AuditEntity,
    required: false,
    description: 'entity status',
  })
  @IsOptional()
  @IsEnum(AuditEntity)
  entityType?: string;

  @ApiProperty({
    type: 'string',
    description: 'ISO string',
    example: '2020-10-05T14:48:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsDateString({ strict: true })
  planningFrom?: string;

  @ApiProperty({
    type: 'string',
    description: 'ISO string',
    example: '2023-10-05T14:48:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsDateString({ strict: true })
  @IsGreaterThanOrEqual('planningFrom', { message: 'common.INVALID_DATE_RANGE' })
  planningTo?: string;

  // Flag for Listing all reassigned IAR
  @ApiProperty({ type: Boolean, required: false })
  @IsOptional()
  @Type(() => Boolean)
  @IsBoolean()
  dashboard?: boolean;

  @IsOptional()
  @IsUUID('all', { each: true })
  @IsArray()
  @ArrayUnique()
  ids?: string[];
}

export enum InternalAuditReportFields {
  VESSEL_NAME = 'vesselName',
  VESSEL_TYPE = 'vesselType',
  AUDIT_COMPANY_NAME = 'auditCompanyName',
  OFFICE_NAME = 'officeName',
  INSPECTION_TYPE = 'inspectiontype',
  DEPARTMENT = 'departments',
  LEAD_AUDITOR = 'leadAuditor',
  STATUS = 'status',
  PLANNED_FROM_DATE = 'plannedFromDate',
  PLANNED_TO_DATE = 'plannedToDate',
  ENTITY_TYPE = 'internalAuditReport_entityType',
  FLAG = 'flag',
  GLOBAL_STATUS = 'globalStatus',
  CREATE_COMPANY_NAME = 'createCompanyName',
  REF_ID = 'refId',
  AUDIT_FROM_DATE = 'auditFromDate',
  AUDIT_TO_DATE = 'auditToDate',
  TOTAL_NO_FINDINGS = 'totalNoFindings',
  TOTAL_NO_NC = 'totalNoNc',
  TOTAL_NO_OBS = 'totalNoOBS',
  AUDIT_NO = 'auditNo',
  SNO = 'SNo',
  TOTAL_NO_OPEN_NC = 'totalNoOpenNC',
  TOTAL_NO_CLOSED_NC = 'totalNoClosedNC',
  TOTAL_NO_OPEN_OBS = 'totalNoOpenOBS',
  TOTAL_NO_CLOSED_OBS = 'totalNoClosedOBS',
  INSPECTION_START_DATE_APP = 'inspectionStartDateApp',
  INSPECTION_END_DATE_APP = 'inspectionEndDateApp',
  AUDIT_REF_ID = 'auditRefId',
  SUBMITTED_MONTH = 'submittedMonth',
  SUBMITTED_YEAR = 'submittedYear',
  CUSTOMER_REF = 'customerRef',
  VOYAGE_TYPE = 'voyageType',
  DOOS = 'doos',
}

export const INTERNAL_AUDIT_REPORT_FILTER_FIELDS: FilterField[] = [
  {
    field: InternalAuditReportFields.VESSEL_NAME,
    column: '"vesselName"',
    type: DataType.TEXT,
  },
  {
    field: InternalAuditReportFields.VESSEL_TYPE,
    column: '"vesselTypeName"',
    type: DataType.TEXT,
  },
  {
    field: InternalAuditReportFields.AUDIT_COMPANY_NAME,
    column: '"holderCompany"',
    type: DataType.TEXT,
  },
  {
    field: InternalAuditReportFields.OFFICE_NAME,
    column: '"auditCompanyName"',
    type: DataType.TEXT,
  },
  {
    field: InternalAuditReportFields.INSPECTION_TYPE,
    column: '"auditTypesName"',
    type: DataType.TEXT,
  },
  {
    field: InternalAuditReportFields.DEPARTMENT,
    column: '"departmentNames"',
    type: DataType.TEXT,
  },
  {
    field: InternalAuditReportFields.LEAD_AUDITOR,
    column: '"leadAuditorName"',
    type: DataType.TEXT,
  },
  {
    field: InternalAuditReportFields.STATUS,
    column: '"internalAuditReport_status"',
    type: DataType.TEXT,
  },
  {
    field: InternalAuditReportFields.PLANNED_FROM_DATE,
    column: '"planningRequest_plannedFromDate"',
    type: DataType.DATE,
  },
  {
    field: InternalAuditReportFields.PLANNED_TO_DATE,
    column: '"planningRequest_plannedToDate"',
    type: DataType.DATE,
  },
  {
    field: InternalAuditReportFields.ENTITY_TYPE,
    column: '"internalAuditReport_entityType"',
    type: DataType.TEXT,
  },
  {
    field: InternalAuditReportFields.FLAG,
    column: '"countryFlag"',
    type: DataType.TEXT,
  },
  {
    field: InternalAuditReportFields.GLOBAL_STATUS,
    column: '"planningRequest_globalStatus"',
    type: DataType.TEXT,
  },
  {
    field: InternalAuditReportFields.CREATE_COMPANY_NAME,
    column: '"company_name"',
    type: DataType.TEXT,
  },
  {
    field: InternalAuditReportFields.REF_ID,
    column: '"internalAuditReport_refId"',
    type: DataType.TEXT,
  },
  {
    field: InternalAuditReportFields.AUDIT_FROM_DATE,
    column: '"actualFrom"',
    type: DataType.DATE,
  },
  {
    field: InternalAuditReportFields.AUDIT_TO_DATE,
    column: '"actualTo"',
    type: DataType.DATE,
  },
  {
    field: InternalAuditReportFields.TOTAL_NO_FINDINGS,
    column: '"totalFindings"',
    type: DataType.NUMBER,
  },
  {
    field: InternalAuditReportFields.TOTAL_NO_NC,
    column: '"totalNonConformity"',
    type: DataType.NUMBER,
  },
  {
    field: InternalAuditReportFields.TOTAL_NO_OBS,
    column: '"totalObservation"',
    type: DataType.NUMBER,
  },
  {
    field: InternalAuditReportFields.AUDIT_NO,
    column: '"prAuditNo"',
    type: DataType.TEXT,
  },
  {
    field: InternalAuditReportFields.SNO,
    column: '"internalAuditReport_serialNumber"',
    type: DataType.TEXT,
  },
  {
    field: InternalAuditReportFields.TOTAL_NO_OPEN_NC,
    column: '"totalOpenNC"',
    type: DataType.NUMBER,
  },
  {
    field: InternalAuditReportFields.TOTAL_NO_CLOSED_NC,
    column: '"totalCloseNC"',
    type: DataType.NUMBER,
  },
  {
    field: InternalAuditReportFields.TOTAL_NO_OPEN_OBS,
    column: '"totalOpenOBS"',
    type: DataType.NUMBER,
  },
  {
    field: InternalAuditReportFields.TOTAL_NO_CLOSED_OBS,
    column: '"totalCloseOBS"',
    type: DataType.NUMBER,
  },
  {
    field: InternalAuditReportFields.INSPECTION_START_DATE_APP,
    column: '"auditWorkspace_mobileInspectionStartDate"',
    type: DataType.DATE,
  },
  {
    field: InternalAuditReportFields.INSPECTION_END_DATE_APP,
    column: '"auditWorkspace_mobileInspectionEndDate"',
    type: DataType.DATE,
  },
  {
    field: InternalAuditReportFields.AUDIT_REF_ID,
    column: '"prRefId"',
    type: DataType.TEXT,
  },
  {
    field: InternalAuditReportFields.SUBMITTED_MONTH,
    column: '"auditWorkspace_submittedDate_Month"',
    type: DataType.TEXT,
  },
  {
    field: InternalAuditReportFields.SUBMITTED_YEAR,
    column: '"auditWorkspace_submittedDate_Year"',
    type: DataType.TEXT,
  },
  {
    field: InternalAuditReportFields.CUSTOMER_REF,
    column: '"planningRequest_customerRef"',
    type: DataType.TEXT,
  },
  {
    field: InternalAuditReportFields.VOYAGE_TYPE,
    column: '"voyageType_name"',
    type: DataType.TEXT,
  },
  {
    field: InternalAuditReportFields.DOOS,
    column: '"planningRequest_doos"',
    type: DataType.TEXT,
  },
];

export enum TitleInternalAuditReport {
  VESSEL_NAME = 'Vessel Name',
  VESSEL_TYPE = 'Vessel Type',
  AUDIT_COMPANY_NAME = 'DOC Holder Company',
  OFFICE_NAME = 'Office Name',
  DEPARTMENT = 'Department',
  LEAD_AUDITOR = 'Lead Inspector',
  STATUS = 'Status',
  PLANNED_FROM_DATE = 'Inspection Planned From Date',
  PLANNED_TO_DATE = 'Inspection Planned To Date',
  ENTITY_TYPE = 'Entity',
  FLAG = 'Flag',
  GLOBAL_STATUS = 'Global Status',
  CREATE_COMPANY_NAME = 'Created By Company',
  REF_ID = 'Ref.ID',
  AUDIT_FROM_DATE = 'Inspection Actual From Date',
  AUDIT_TO_DATE = 'Inspection Actual To Date',
  TOTAL_NO_FINDINGS = 'Total.No.Findings',
  TOTAL_NO_NC = 'Total.No.NC',
  TOTAL_NO_OBS = 'Total.No.OBS',
  AUDIT_NO = 'Inspection S.No',
  SNO = 'S.No',
  TOTAL_NO_OPEN_NC = 'Total.No Open NC',
  TOTAL_NO_CLOSED_NC = 'Total.No Closed NC',
  TOTAL_NO_OPEN_OBS = 'Total.No Open OBS',
  TOTAL_NO_CLOSED_OBS = 'Total.No Closed OBS',
  INSPECTION_START_DATE_APP = 'Inspection Start Date (App)',
  INSPECTION_END_DATE_APP = 'Inspection End Date (App)',
  AUDIT_REF_ID = 'Inspection Ref.ID',
  SUBMITTED_MONTH = 'Submitted Month',
  SUBMITTED_YEAR = 'Submitted Year',
  CUSTOMER_REF = 'Customer Ref ID',
}
