/* eslint-disable indent */
import { Injectable } from '@nestjs/common';

import { background } from '../../../utils/generate-pdf/image-base64/image-base64';
import { ListFindingItemOfIARQueryDTO, StatisticPreviousItemsDTO } from '../../audit-workspace/dto';
import {
  ApproveIARDTO,
  ApproveIARQueryDTO,
  CloseoutIARDTO,
  ListInternalAuditReportDTO,
  ListPreviousIARQueryDTO,
  ReviewFindingItemDto,
  ReviewIARDTO,
  ReviewIARQueryDTO,
  SubmitIARQueryDTO,
  SubmitInternalAuditReportDTO,
  TitleInternalAuditReport,
  UpdateFindingItemForPICDto,
} from '../dto';
import { InternalAuditReportRepository } from '../repositories/internal-audit-report.repository';

import moment from 'moment';
import { BaseError, RoleScopeCheck, TokenPayloadModel, Utils } from 'svm-nest-lib-v3';
import { ListQueryDto } from 'svm-nest-lib-v3/dist/commons/dtos';
import {
  ActionEnum,
  FeatureEnum,
  InspectionExportPdfHeaderEnum,
  InternalAuditReportStatus,
  PrintOptionType,
  SubFeatureEnum,
  TemplateTypeEnum,
  WorkflowPermission,
} from '../../../commons/enums';
import { EmailProducer } from '../../../micro-services/async/email.producer';
import { NotificationProducer } from '../../../micro-services/async/notification.producer';
import { SVMIAMService } from '../../../micro-services/sync/svm-iam.service';
import { SVMSupportService } from '../../../micro-services/sync/svm-support.service';
import { createPdfVersion2 } from '../../../utils/generate-pdf/generate-pdf-version2';
import { ReportFindingItemRepository } from '../../audit-workspace/repositories/report-finding-item.repository';
import { CompanyRepository } from '../../company/company.repository';
import { CARRepository } from '../../corrective-action-request/repositories/car.repository';
import { UpdateFindingItemAndAssignPICDto } from '../dto';
import { createIarPdfDTO } from '../dto/create-IAR-Pdf.dto';
import { IARReportHeaderDescription } from '../entities/iar-report-header-description.entity';
import { IARReportHeader } from '../entities/iar-report-header.entity';
import { InternalAuditReportOfficeComment } from '../entities/internal-audit-report-office-comment.entity';
import { StatisticPreviousFindingItemsRepository } from '../repositories/statistic-previous-finding-items.repository';
import { getRepository } from 'typeorm';
import { Company } from 'src/modules/company/company.entity';
import APP_CONFIG from 'src/configs/app.config';
import { downloadResource, getTextData, PayloadAGGridDto } from '../../../utils';
import { Response } from 'express';
import { createPdf } from '../../../utils/generate-pdf/create-pdf';
import { ReportFindingFormRepository } from 'src/modules/report-finding/repositories/report-finding-form.repository';

@Injectable()
export class InternalAuditReportService {
  constructor(
    private readonly internalAuditReportRepository: InternalAuditReportRepository,
    private readonly reportFindingItemRepository: ReportFindingItemRepository,
    private readonly findingItemRepo: ReportFindingItemRepository,
    private readonly svmSupportService: SVMSupportService,
    private readonly companyRepository: CompanyRepository,
    private readonly carRepository: CARRepository,
    private readonly statisticPrevFindingItemsRepository: StatisticPreviousFindingItemsRepository,
    private readonly notificationProducer: NotificationProducer,
    private readonly emailProducer: EmailProducer,
    private readonly svmIAMService: SVMIAMService,
    private readonly reportFindingFormRepository: ReportFindingFormRepository,
  ) {
    this.internalAuditReportRepository._migrateInternalAuditReport();
  }

  async submitIAR(
    internalAuditReportId: string,
    params: SubmitInternalAuditReportDTO,
    query: SubmitIARQueryDTO,
    token: TokenPayloadModel,
  ) {
    const prDetail = await this.internalAuditReportRepository
      .createQueryBuilder('internalAuditReport')
      .innerJoin('internalAuditReport.planningRequest', 'planningRequest')
      .leftJoin('planningRequest.leadAuditor', 'leadAuditor')
      .where('internalAuditReport.id = :internalAuditReportId', { internalAuditReportId })
      .select([
        'internalAuditReport.status',
        'internalAuditReport.createdUserId',
        'planningRequest.id',
        'leadAuditor.id',
      ])
      .getOne();
    // if draft => lead auditor can update record
    const leadAuditorId = prDetail.planningRequest?.leadAuditor.id || null;
    if (prDetail.status === InternalAuditReportStatus.DRAFT && token.id !== leadAuditorId) {
      throw new BaseError({
        status: 403,
        message: 'common.UPDATE_FORBIDDEN',
      });
    }

    if (!(token.id === leadAuditorId && prDetail.status === InternalAuditReportStatus.REASSIGNED)) {
      // if status != draft => user in user assignment can update
      const userCanUpdate = await this.internalAuditReportRepository.checkUserCanUpdateRecord(
        token.id,
        internalAuditReportId,
      );
      if (
        !userCanUpdate &&
        prDetail.createdUserId !== token.id &&
        prDetail.status !== InternalAuditReportStatus.DRAFT
      ) {
        throw new BaseError({
          status: 403,
          message: 'common.UPDATE_FORBIDDEN',
        });
      }
    }

    const preparedIAROfficeComments: InternalAuditReportOfficeComment[] = [];
    if (params.officeComments && params.officeComments.length > 0) {
      for (const officeComment of params.officeComments) {
        preparedIAROfficeComments.push({
          id: officeComment.id ? officeComment.id : Utils.strings.generateUUID(),
          comment: officeComment.comment,
          internalAuditReportId,
        } as InternalAuditReportOfficeComment);
      }
    }

    const preparedIARReportHeaders: IARReportHeader[] = [];
    if (params.IARReportHeaders && params.IARReportHeaders.length > 0) {
      params.IARReportHeaders.forEach((header: IARReportHeader) => {
        preparedIARReportHeaders.push({
          id: header.id ? header.id : Utils.strings.generateUUID(),
          headerComment: header.headerComment ? header.headerComment : null,
          createdUserId: token.id,
          internalAuditReportId,
          reportHeaderId: header.reportHeaderId,
        } as IARReportHeader);
      });
    }

    const preparedIARReportHeaderDescriptions = this.getPreparedIARReportHeaderDescriptions(
      params,
      token,
    );
    const {
      dataNoti,
      dataSendMail,
    } = await this.internalAuditReportRepository.submitInternalAuditReport(
      internalAuditReportId,
      params,
      preparedIAROfficeComments,
      preparedIARReportHeaders,
      preparedIARReportHeaderDescriptions,
      query,
      token,
    );
    for (const data of dataNoti) {
      await this.notificationProducer.publishNotification(data);
    }
    this.emailProducer.publishEmail(dataSendMail);
    return 1;
  }

  private getPreparedIARReportHeaderDescriptions(params, token) {
    const preparedIARReportHeaderDescriptions: IARReportHeaderDescription[] = [];
    if (params.IARReportHeaders) {
      params.IARReportHeaders.forEach((header: IARReportHeader) => {
        if (header.IARReportHeaderDescriptions) {
          header.IARReportHeaderDescriptions.forEach(
            (headerDescription: IARReportHeaderDescription) => {
              preparedIARReportHeaderDescriptions.push({
                id: headerDescription.id || Utils.strings.generateUUID(),
                iARReportHeaderId: header.id || Utils.strings.generateUUID(),
                topic: headerDescription.topic || null,
                score: headerDescription.score || null,
                description: headerDescription.description || null,
                createdUserId: token.id,
              } as IARReportHeaderDescription);
            },
          );
        }
      });
    }
    return preparedIARReportHeaderDescriptions;
  }

  async listPreviousIAR(id: string, query: ListPreviousIARQueryDTO, token: TokenPayloadModel) {
    return this.internalAuditReportRepository.listPreviousIAR(id, query, token);
  }

  async listInternalAuditReportDashboard(
    query: ListInternalAuditReportDTO,
    token: TokenPayloadModel,
  ) {
    const permissionToCheck =
      FeatureEnum.AUDIT_INSPECTION +
      '::' +
      SubFeatureEnum.INTERNAL_AUDIT_REPORT +
      '::' +
      ActionEnum.VIEW;

    const { rolePermissions } = await this.svmIAMService.listRolePermissionsByUser(token.id, {
      companyId: token.companyId,
      parentCompanyId: token.parentCompanyId,
    });

    if (!rolePermissions.includes(permissionToCheck)) {
      return [];
    }

    return this.internalAuditReportRepository.listReassignedIar(query, token);
  }

  async listInternalAuditReport(
    query: ListInternalAuditReportDTO,
    token: TokenPayloadModel,
    body?: PayloadAGGridDto,
  ) {
    if (query.dashboard) {
      return this.internalAuditReportRepository.listReassignedIar(query, token);
    }
    //filter by permission
    const inspectionReports = await this.internalAuditReportRepository.listInternalAuditReport(
      { ...query, page: 1, pageSize: -1 },
      token,
      new PayloadAGGridDto(),
    );

    if (!RoleScopeCheck.isAdmin(token)) {
      const { rolePermissions } = await this.svmIAMService.listRolePermissionsByUser(token.id, {
        companyId: token.companyId,
        parentCompanyId: token.parentCompanyId,
      });
      const isViewIAR = rolePermissions.includes(
        FeatureEnum.AUDIT_INSPECTION +
          '::' +
          SubFeatureEnum.INTERNAL_AUDIT_REPORT +
          '::' +
          ActionEnum.VIEW,
      );
      const filterIARByWorkflow = this._filterIARByWorkflow(
        inspectionReports.data,
        token,
        isViewIAR,
      );
      query.ids = filterIARByWorkflow.map((value) => value.internalAuditReport_id);
    }

    return await this.internalAuditReportRepository.listInternalAuditReport(query, token, body);
  }

  async getDetailIARById(id: string, token: TokenPayloadModel) {
    return this.internalAuditReportRepository.getDetailIARById(id, token);
  }

  async updateFindingItemAndAssignPIC(
    iarId: string,
    findingItemId: string,
    body: UpdateFindingItemAndAssignPICDto,
    token: TokenPayloadModel,
  ) {
    return this.internalAuditReportRepository.updateFindingItemAndAssignPIC(
      iarId,
      findingItemId,
      body,
      token,
    );
  }

  async updateFindingItemForPICUser(
    iarId: string,
    findingItemId: string,
    body: UpdateFindingItemForPICDto,
    token: TokenPayloadModel,
  ) {
    return this.internalAuditReportRepository.updateFindingItemForPICUser(
      iarId,
      findingItemId,
      body,
      token,
    );
  }

  async updateFindingItemForActor(
    iarId: string,
    findingItemId: string,
    body: UpdateFindingItemForPICDto,
    token: TokenPayloadModel,
  ) {
    return this.internalAuditReportRepository.updateFindingItemForActor(
      iarId,
      findingItemId,
      body,
      token,
    );
  }

  async reviewFindingItem(
    iarId: string,
    findingItemId: string,
    body: ReviewFindingItemDto,
    token: TokenPayloadModel,
  ) {
    return this.internalAuditReportRepository.reviewFindingItem(iarId, findingItemId, body, token);
  }

  async reviewIAR(
    IARid: string,
    params: ReviewIARDTO,
    token: TokenPayloadModel,
    workflowPermissions: string[],
    query: ReviewIARQueryDTO,
  ) {
    const preparedIARReportHeaders: IARReportHeader[] = [];
    if (params.IARReportHeaders && params.IARReportHeaders.length > 0) {
      params.IARReportHeaders.forEach((header: IARReportHeader) => {
        preparedIARReportHeaders.push({
          id: header.id ? header.id : Utils.strings.generateUUID(),
          headerComment: header.headerComment ? header.headerComment : null,
          internalAuditReportId: IARid,
          createdUserId: token.id,
          reportHeaderId: header.reportHeaderId,
        } as IARReportHeader);
      });
    }

    const preparedIARReportHeaderDescriptions = this.getPreparedIARReportHeaderDescriptions(
      params,
      token,
    );
    const { dataNoti, dataSendMail } = await this.internalAuditReportRepository.reviewIAR(
      IARid,
      params,
      preparedIARReportHeaders,
      preparedIARReportHeaderDescriptions,
      token,
      workflowPermissions,
      query,
    );
    for (const data of dataNoti) {
      await this.notificationProducer.publishNotification(data);
    }
    this.emailProducer.publishEmail(dataSendMail);
  }

  // async reassignIAR(IARid: string, params: ReassignIARDTO, token: TokenPayloadModel) {
  //   return this.internalAuditReportRepository.reassignIAR(IARid, params, token);
  // }

  async approveIAR(
    IARid: string,
    params: ApproveIARDTO,
    token: TokenPayloadModel,
    query: ApproveIARQueryDTO,
  ) {
    const preparedIARReportHeaders: IARReportHeader[] = [];
    if (params.IARReportHeaders && params.IARReportHeaders.length > 0) {
      params.IARReportHeaders.forEach((header: IARReportHeader) => {
        preparedIARReportHeaders.push({
          id: header.id ? header.id : Utils.strings.generateUUID(),
          headerComment: header.headerComment ? header.headerComment : null,
          createdUserId: token.id,
          internalAuditReportId: IARid,
          reportHeaderId: header.reportHeaderId,
        } as IARReportHeader);
      });
    }

    const preparedIARReportHeaderDescriptions = this.getPreparedIARReportHeaderDescriptions(
      params,
      token,
    );
    const result = await this.internalAuditReportRepository.approveIAR(
      IARid,
      params,
      preparedIARReportHeaders,
      preparedIARReportHeaderDescriptions,
      token,
      query,
    );
    const { dataNoti, dataSendMail } = result;
    const vesselId = (result as any).vesselId;
    if (vesselId) {
      await this.reportFindingFormRepository.updateChartererInspectionScore(vesselId, token);
    }
    for (const data of dataNoti) {
      await this.notificationProducer.publishNotification(data);
    }
    this.emailProducer.publishEmail(dataSendMail);
    return 1;
  }

  async closeoutIAR(IARid: string, params: CloseoutIARDTO, token: TokenPayloadModel) {
    const preparedIARReportHeaders: IARReportHeader[] = [];
    if (params.IARReportHeaders && params.IARReportHeaders.length > 0) {
      params.IARReportHeaders.forEach((header: IARReportHeader) => {
        preparedIARReportHeaders.push({
          id: header.id ? header.id : Utils.strings.generateUUID(),
          headerComment: header.headerComment ? header.headerComment : null,
          createdUserId: token.id,
          internalAuditReportId: IARid,
          reportHeaderId: header.reportHeaderId,
        } as IARReportHeader);
      });
    }

    const preparedIARReportHeaderDescriptions = this.getPreparedIARReportHeaderDescriptions(
      params,
      token,
    );
    const { dataNoti, dataSendMail } = await this.internalAuditReportRepository.closeoutIAR(
      IARid,
      params,
      preparedIARReportHeaders,
      preparedIARReportHeaderDescriptions,
      token,
    );

    for (const data of dataNoti) {
      await this.notificationProducer.publishNotification(data);
    }
    this.emailProducer.publishEmail(dataSendMail);
  }

  async listFindingItemFromIar(query: ListQueryDto, user: TokenPayloadModel) {
    return await this.reportFindingItemRepository.listFindingItemFromIar(query, user);
  }

  async listFindingItemsOfIAR(
    id: string,
    query: ListFindingItemOfIARQueryDTO,
    token: TokenPayloadModel,
  ) {
    return this.internalAuditReportRepository.listFindingItemsOfIAR(id, query, token);
  }

  async createIarPdf(iarId: string, res, user: TokenPayloadModel, body: createIarPdfDTO) {
    const detailIar = await this.internalAuditReportRepository.getDetailIARById(iarId, user, true);
    if (detailIar?.attachments?.length > 0) {
      const attachments = await this.svmSupportService.getDetailImage(detailIar.attachments);
      Object.assign(detailIar.attachments, attachments);
    }
    console.log("detailIar?.coverImage", detailIar?.coverImage);
    let coverImageLink = '';
    if (detailIar?.coverImage?.length > 0) {
      const coverImage = await this.svmSupportService.getDetailImage(detailIar.coverImage);
      coverImageLink = coverImage[0].link;
    }
    if (!detailIar.reportFindingFormId) {
      Object.assign(detailIar, {
        reportFindingForm: {
          id: '',
          refNo: '',
          status: '',
          totalFindings: 0,
          totalNonConformity: 0,
          totalObservation: 0,
          planningRequestId: '',
        },
      });
    }
    // fill header by type
    // detailIar.IARReportHeaders = detailIar.IARReportHeaders.filter(
    //   (header) =>
    //     header.printOption == body.printOption || header.printOption == PrintOptionType.ALL,
    // );

    // filter header by auditType
    // if (detailIar.IARReportHeaders[0]?.auditTypes) {
    //   const temp = detailIar.IARReportHeaders;

    //   // fill subHeader with inherit parent AuditType
    //   detailIar.IARReportHeaders = temp.map((header) => {
    //     if (!header.parentId) return header;
    //     const parentHeader = temp.find(
    //       (parentHeader) => parentHeader.serialNumber.charAt(0) == header.serialNumber.charAt(0),
    //     );
    //     const parentAuditTypes = parentHeader.auditTypes;
    //     return {
    //       ...header,
    //       auditTypes: parentAuditTypes,
    //     };
    //   });

    //   // filter by audit
    //   detailIar.IARReportHeaders = detailIar.IARReportHeaders.filter(
    //     (header) => header.auditTypes.includes(body.inspectionTypeId) || header.isDefault == true,
    //   );

    //   detailIar.IARReportHeaders = this._updateSerialNumberForHeadersAndGet(
    //     detailIar.IARReportHeaders,
    //   );
    // }

    const startOfMonth = moment().utc().startOf('month').format();
    const endOfMonth = moment().utc().endOf('month').format();
    const staticPreviousFindingItems = await this.findingItemRepo.statisticPreviousFindingItems(
      {
        vesselId: detailIar.iarPlanningRequest.vesselId,
        fromDate: startOfMonth,
        toDate: endOfMonth,
        departmentId: detailIar.iarPlanningRequest.departmentId,
      } as StatisticPreviousItemsDTO,
      user,
    );

    const staticManualPreviousFindingItems = await this.statisticPrevFindingItemsRepository.listManualStatisticPreviousFindingItems(
      { internalAuditReportId: iarId },
      user,
    );

    const cars = await this.carRepository.listCar(
      { planningRequestId: detailIar.planningRequestId },
      user,
    );
    const previousIar = await this.internalAuditReportRepository.listPreviousIAR(
      detailIar.id,
      {} as ListPreviousIARQueryDTO,
      user,
    );

    let logoBase64 =
      'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/+vXfwAJ5wP0lHD7egAAAABJRU5ErkJggg==';
    // let background64 = background;

    const logoId = await this.companyRepository.getCompanyLogoId(user.companyId);

    let logoLink = '';
    if (logoId) {
      const data = await this.svmSupportService.downloadDetailImages([logoId]);
      const dataImage: any = await this.svmSupportService.getDetailImage([logoId]);
      logoLink = dataImage[0].link;
      logoBase64 = data[0].base64DataFormat;
    }
    // remove these background image from pdf
    // if (detailIar.background) {
    //   const data = await this.svmSupportService.downloadDetailImages([detailIar.background]);
    //   background64 = data[0].base64DataFormat;
    // } else {
    //   background64 = logoBase64;
    // }
    detailIar.IARReportHeaders = detailIar.IARReportHeaders.filter((item) => {
      if (APP_CONFIG.ENV.CONFIG_SYNC_DATA_VESSEL.COMPANY_ID === detailIar?.company?.id) {
        if (item.topic != InspectionExportPdfHeaderEnum.Inspectionhistoryandstatus) return item;
      } else {
        return item;
      }
    });

    // return await createPdfVersion2(
    //   TemplateTypeEnum.INTERNAL_AUDIT_REPORT,
    //   'InternalAuditReport',
    //   {
    //     detailIar,
    //     staticPreviousFindingItems,
    //     staticManualPreviousFindingItems,
    //     previousIar,
    //     // background64,
    //     logoBase64,
    //     title: body.name,
    //     timezone: body.timezone,
    //     cars,
    //   },
    //   res,
    // );

    return await createPdf(
      TemplateTypeEnum.INTERNAL_AUDIT_REPORT,
      'InternalAuditReport',
      {
        detailIar,
        staticPreviousFindingItems,
        staticManualPreviousFindingItems,
        previousIar,
        // background64,
        logoLink,
        title: body.name,
        timezone: body.timezone,
        cars: cars.data,
        cap: cars.data.map((car) => car.cap).filter((cap) => cap?.id),
        createToc: true,
        coverImageLink,
      },
      res,
    );
  }

  private _updateSerialNumberForHeadersAndGet(iARReportHeaders) {
    const arrayToTree = (arr, parentId = null) =>
      arr
        .filter((item) => item.parentId == parentId)
        .map((child) => ({ ...child, children: arrayToTree(arr, child.reportHeaderId) }));
    const treeArray = arrayToTree(iARReportHeaders);

    const flat: any = [];

    const flattenArray = (treeArray, serialNumber = 0) => {
      for (const [index, item] of treeArray.entries()) {
        const tem = {
          ...item,
          children: null,
          serialNumber: serialNumber == 0 ? index - 1 : `${serialNumber}.${index + 1}`,
        };
        flat.push(tem);
        if (item.children?.length > 0) {
          flattenArray(item.children, tem.serialNumber);
        }
      }
    };

    flattenArray(treeArray);

    return flat;
  }

  private _filterIARByWorkflow(inspectionReports, user: TokenPayloadModel, isViewIAR: boolean) {
    const filteredPr = [];
    for (const inspectionReport of inspectionReports) {
      let viewPermission = false;
      const isLeadAuditor =
        inspectionReport.internalAuditReport_leadAuditorId === user.id && isViewIAR;
      switch (inspectionReport.internalAuditReport_status) {
        case InternalAuditReportStatus.DRAFT:
          viewPermission = isLeadAuditor;
          break;
        case InternalAuditReportStatus.SUBMITTED:
          viewPermission = inspectionReport.userAssignments.some(
            (ua) =>
              ua.userId === user.id &&
              (ua.permission === WorkflowPermission.CREATOR ||
                ua.permission === WorkflowPermission.REVIEWER1),
          );

          break;

        case InternalAuditReportStatus.REASSIGNED:
          let isApproverRejected = false;
          let statusUserReview = InternalAuditReportStatus.SUBMITTED;
          const userApproved = inspectionReport.userAssignments.find(
            (user) => user.permission === WorkflowPermission.APPROVER,
          );

          const listIRHistories = inspectionReport.iarHistories;

          const userApprover = listIRHistories?.filter(
            (value) =>
              value?.createdUser.id === userApproved?.userId &&
              value.status === InternalAuditReportStatus.REASSIGNED,
          );

          if (userApprover?.length) isApproverRejected = true;

          const listUserReviews = listIRHistories?.filter((user) =>
            user.status.includes('reviewed'),
          );

          if (listUserReviews?.length) {
            const lastUserReview = listUserReviews.reduce((max, current) => {
              if (current.status.localeCompare(max.status) > 0) {
                return current;
              } else {
                return max;
              }
            });

            statusUserReview = lastUserReview.status;
          }

          if (isApproverRejected) {
            viewPermission = inspectionReport.userAssignments.some(
              (ua) =>
                ua.userId === user.id &&
                (ua.permission === WorkflowPermission.CREATOR ||
                  ua.permission === WorkflowPermission.REVIEWER1 ||
                  ua.permission === WorkflowPermission.REVIEWER2 ||
                  ua.permission === WorkflowPermission.REVIEWER3 ||
                  ua.permission === WorkflowPermission.REVIEWER4 ||
                  ua.permission === WorkflowPermission.REVIEWER5 ||
                  ua.permission === WorkflowPermission.APPROVER),
            );
            break;
          }

          if (statusUserReview === InternalAuditReportStatus.REVIEWED_1) {
            viewPermission = inspectionReport.userAssignments.some(
              (ua) =>
                ua.userId === user.id &&
                (ua.permission === WorkflowPermission.CREATOR ||
                  ua.permission === WorkflowPermission.REVIEWER1),
            );
          }
          if (statusUserReview === InternalAuditReportStatus.REVIEWED_2) {
            viewPermission = inspectionReport.userAssignments.some(
              (ua) =>
                ua.userId === user.id &&
                (ua.permission === WorkflowPermission.CREATOR ||
                  ua.permission === WorkflowPermission.REVIEWER1 ||
                  ua.permission === WorkflowPermission.REVIEWER2),
            );
          }
          if (statusUserReview === InternalAuditReportStatus.REVIEWED_3) {
            viewPermission = inspectionReport.userAssignments.some(
              (ua) =>
                ua.userId === user.id &&
                (ua.permission === WorkflowPermission.CREATOR ||
                  ua.permission === WorkflowPermission.REVIEWER1 ||
                  ua.permission === WorkflowPermission.REVIEWER2 ||
                  ua.permission === WorkflowPermission.REVIEWER3),
            );
          }

          if (statusUserReview === InternalAuditReportStatus.REVIEWED_4) {
            viewPermission = inspectionReport.userAssignments.some(
              (ua) =>
                ua.userId === user.id &&
                (ua.permission === WorkflowPermission.CREATOR ||
                  ua.permission === WorkflowPermission.REVIEWER1 ||
                  ua.permission === WorkflowPermission.REVIEWER2 ||
                  ua.permission === WorkflowPermission.REVIEWER3 ||
                  ua.permission === WorkflowPermission.REVIEWER4),
            );
          }

          if (statusUserReview === InternalAuditReportStatus.REVIEWED_5) {
            viewPermission = inspectionReport.userAssignments.some(
              (ua) =>
                ua.userId === user.id &&
                (ua.permission === WorkflowPermission.CREATOR ||
                  ua.permission === WorkflowPermission.REVIEWER1 ||
                  ua.permission === WorkflowPermission.REVIEWER2 ||
                  ua.permission === WorkflowPermission.REVIEWER3 ||
                  ua.permission === WorkflowPermission.REVIEWER4 ||
                  ua.permission === WorkflowPermission.REVIEWER5),
            );
          }
          break;
        case InternalAuditReportStatus.REVIEWED_1:
          viewPermission = inspectionReport.userAssignments.some(
            (ua) =>
              ua.userId === user.id &&
              (ua.permission === WorkflowPermission.CREATOR ||
                ua.permission === WorkflowPermission.REVIEWER1 ||
                ua.permission === WorkflowPermission.REVIEWER2 ||
                WorkflowPermission.APPROVER),
          );

          break;
        case InternalAuditReportStatus.REVIEWED_2:
          viewPermission = inspectionReport.userAssignments.some(
            (ua) =>
              ua.userId === user.id &&
              (ua.permission === WorkflowPermission.CREATOR ||
                ua.permission === WorkflowPermission.REVIEWER1 ||
                ua.permission === WorkflowPermission.REVIEWER2 ||
                ua.permission === WorkflowPermission.REVIEWER3 ||
                WorkflowPermission.APPROVER),
          );

          break;

        case InternalAuditReportStatus.REVIEWED_3:
          viewPermission = inspectionReport.userAssignments.some(
            (ua) =>
              ua.userId === user.id &&
              (ua.permission === WorkflowPermission.CREATOR ||
                ua.permission === WorkflowPermission.REVIEWER1 ||
                ua.permission === WorkflowPermission.REVIEWER2 ||
                ua.permission === WorkflowPermission.REVIEWER3 ||
                ua.permission === WorkflowPermission.REVIEWER4 ||
                WorkflowPermission.APPROVER),
          );

          break;
        case InternalAuditReportStatus.REVIEWED_4:
          viewPermission = inspectionReport.userAssignments.some(
            (ua) =>
              ua.userId === user.id &&
              (ua.permission === WorkflowPermission.CREATOR ||
                ua.permission === WorkflowPermission.REVIEWER1 ||
                ua.permission === WorkflowPermission.REVIEWER2 ||
                ua.permission === WorkflowPermission.REVIEWER3 ||
                ua.permission === WorkflowPermission.REVIEWER4 ||
                ua.permission === WorkflowPermission.REVIEWER5 ||
                WorkflowPermission.APPROVER),
          );

          break;
        case InternalAuditReportStatus.REVIEWED_5:
          viewPermission = inspectionReport.userAssignments.some(
            (ua) =>
              ua.userId === user.id &&
              (ua.permission === WorkflowPermission.CREATOR ||
                ua.permission === WorkflowPermission.REVIEWER1 ||
                ua.permission === WorkflowPermission.REVIEWER2 ||
                ua.permission === WorkflowPermission.REVIEWER3 ||
                ua.permission === WorkflowPermission.REVIEWER4 ||
                ua.permission === WorkflowPermission.REVIEWER5 ||
                WorkflowPermission.APPROVER),
          );

          break;
        case InternalAuditReportStatus.APPROVED:
        case InternalAuditReportStatus.CLOSEOUT:
          viewPermission = true;
          break;
        default:
      }

      if (viewPermission || isLeadAuditor) {
        filteredPr.push(inspectionReport);
      }
    }
    return filteredPr;
  }

  // getting company mails for office entity
  async getOfficeAssociatedToMailIds(auditCompanyId: string) {
    let officeReceivedEmails = [];
    const getcompanyMailId = await getRepository(Company)
      .createQueryBuilder('company')
      .select(['company.email'])
      .where('company.id = :companyId', { companyId: auditCompanyId })
      .getMany();
    for (const data of getcompanyMailId) {
      const email = data.email.split(/[;]/);
      officeReceivedEmails = officeReceivedEmails.concat(email);
    }
    return { receiverEmails: officeReceivedEmails };
  }

  async exportInternalAuditReport(
    query: ListInternalAuditReportDTO,
    token: TokenPayloadModel,
    body: PayloadAGGridDto,
    res: Response,
  ) {
    const inspectionReports = await this.listInternalAuditReport(query, token, body);
    const data = [];
    inspectionReports?.data?.forEach((item) => {
      data.push({
        [TitleInternalAuditReport.VESSEL_NAME]: getTextData(item.vesselName),
        [TitleInternalAuditReport.VESSEL_TYPE]: getTextData(item.vesselTypeName),
        [TitleInternalAuditReport.AUDIT_COMPANY_NAME]: getTextData(item.holderCompany),
        [TitleInternalAuditReport.OFFICE_NAME]: getTextData(item.auditCompanyName),
        [TitleInternalAuditReport.DEPARTMENT]: getTextData(item.departmentNames),
        [TitleInternalAuditReport.LEAD_AUDITOR]: getTextData(item.leadAuditorName),
        [TitleInternalAuditReport.STATUS]: getTextData(item.internalAuditReport_status),
        [TitleInternalAuditReport.PLANNED_FROM_DATE]: getTextData(
          item.planningRequest_plannedFromDate,
        ),
        [TitleInternalAuditReport.PLANNED_TO_DATE]: getTextData(item.planningRequest_plannedToDate),
        [TitleInternalAuditReport.ENTITY_TYPE]: getTextData(item.internalAuditReport_entityType),
        [TitleInternalAuditReport.FLAG]: getTextData(item.countryFlag),
        [TitleInternalAuditReport.GLOBAL_STATUS]: getTextData(item.planningRequest_globalStatus),
        [TitleInternalAuditReport.CREATE_COMPANY_NAME]: getTextData(item.company_name),
        [TitleInternalAuditReport.REF_ID]: getTextData(item.internalAuditReport_refId),
        [TitleInternalAuditReport.AUDIT_FROM_DATE]: getTextData(item.actualFrom),
        [TitleInternalAuditReport.AUDIT_TO_DATE]: getTextData(item.actualTo),
        [TitleInternalAuditReport.TOTAL_NO_FINDINGS]: getTextData(item.totalFindings),
        [TitleInternalAuditReport.TOTAL_NO_NC]: getTextData(item.totalNonConformity),
        [TitleInternalAuditReport.TOTAL_NO_OBS]: getTextData(item.totalObservation),
        [TitleInternalAuditReport.AUDIT_NO]: getTextData(item.prAuditNo),
        [TitleInternalAuditReport.SNO]: getTextData(item.internalAuditReport_serialNumber),
        [TitleInternalAuditReport.TOTAL_NO_OPEN_NC]: getTextData(item.totalOpenNC),
        [TitleInternalAuditReport.TOTAL_NO_CLOSED_NC]: getTextData(item.totalCloseNC),
        [TitleInternalAuditReport.TOTAL_NO_OPEN_OBS]: getTextData(item.totalOpenOBS),
        [TitleInternalAuditReport.TOTAL_NO_CLOSED_OBS]: getTextData(item.totalCloseOBS),
        [TitleInternalAuditReport.INSPECTION_START_DATE_APP]: getTextData(
          item.auditWorkspace_mobileInspectionStartDate,
        ),
        [TitleInternalAuditReport.INSPECTION_END_DATE_APP]: getTextData(
          item.auditWorkspace_mobileInspectionEndDate,
        ),
        [TitleInternalAuditReport.AUDIT_REF_ID]: getTextData(item.prRefId),
        [TitleInternalAuditReport.SUBMITTED_MONTH]: getTextData(
          item.auditWorkspace_submittedDate_Month,
        ),
        [TitleInternalAuditReport.SUBMITTED_YEAR]: getTextData(
          item.auditWorkspace_submittedDate_Year,
        ),
        [TitleInternalAuditReport.CUSTOMER_REF]: getTextData(item.planningRequest_customerRef),
      });
    });

    return downloadResource(
      query,
      res,
      'Inspection-Report',
      Object.values(TitleInternalAuditReport),
      data,
    );
  }
}
