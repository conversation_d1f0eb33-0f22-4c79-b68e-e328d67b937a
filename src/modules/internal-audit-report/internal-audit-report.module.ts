import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { InternalAuditReportController } from './controller/internal-audit-report.controller';
import { InternalAuditReportService } from './services/internal-audit-report.service';
import { IARReportHeaderRepository } from './repositories/iar-report-header.repository';
import { InternalAuditReportRepository } from './repositories/internal-audit-report.repository';
import { IARReportHeaderDescriptionRepository } from './repositories/iar-report-header-description.repository';
import { InternalAuditReportHistoryRepository } from './repositories/internal-audit-report-history.repository';
import { InternalAuditReportCommentRepository } from './repositories/internal-audit-report-comment.repository';
import { InternalAuditReportOfficeCommentRepository } from './repositories/internal-audit-report-office-comment.repository';
import { ReportFindingItemRepository } from '../audit-workspace/repositories/report-finding-item.repository';
import {
  IARAuditTypeRepository,
  IARUserRepository,
  IARPlanningRequestRepository,
} from './repositories/iar-caches.repository';
import { MicroservicesSyncModule } from '../../micro-services/sync';
import { CompanyRepository } from '../company/company.repository';
import { StatisticPreviousFindingItemsRepository } from './repositories/statistic-previous-finding-items.repository';
import { ManualStatisticPreviousFindingItemsController } from './controller/manual-statistic-previous-finding-items.controller';
import { StatisticPreviousFindingItemsService } from './services/statisticPreviousFindingItems.service';
import { CARRepository } from '../corrective-action-request/repositories/car.repository';
import { MicroservicesAsyncModule } from '../../micro-services/async';
import { ReportFindingFormRepository } from '../report-finding/repositories/report-finding-form.repository';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      IARReportHeaderRepository,
      InternalAuditReportRepository,
      IARReportHeaderDescriptionRepository,
      InternalAuditReportHistoryRepository,
      InternalAuditReportCommentRepository,
      InternalAuditReportOfficeCommentRepository,
      ReportFindingItemRepository,
      IARAuditTypeRepository,
      IARUserRepository,
      IARPlanningRequestRepository,
      CompanyRepository,
      StatisticPreviousFindingItemsRepository,
      CARRepository,
      ReportFindingFormRepository,
    ]),
    MicroservicesSyncModule,
    MicroservicesAsyncModule,
  ],
  controllers: [InternalAuditReportController, ManualStatisticPreviousFindingItemsController],
  providers: [InternalAuditReportService, StatisticPreviousFindingItemsService],
  exports: [],
})
export class InternalAuditReportModule {}
