import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToOne,
  PrimaryColumn,
} from 'typeorm';
import { MailSend } from '../../mail-send/entity/mail-send.entity';
import { PlanningRequest } from '../../planning-request/entities/planning-request.entity';
import { IncidentInvestigation } from '../../../modules-qa/incident-investigation/entity/incident-investigation.entity';

@Entity()
export class MailPlanning {
  @PrimaryColumn({ type: 'uuid' })
  public mailSendId: string;

  @Column({ type: 'uuid' })
  public planningRequestId: string;

  @Column({ type: 'uuid' })
  public incidentInvestigationId: string;

  // Relationship
  @ManyToOne(() => MailSend, (mailSend) => mailSend.mailPlannings, { onDelete: 'CASCADE' })
  public mailSend: MailSend;

  @ManyToOne(() => PlanningRequest, (pr) => pr.mailPlannings, {
    onDelete: 'CASCADE',
  })
  public planningRequest: PlanningRequest;

  @ManyToOne(
    () => IncidentInvestigation,
    (incidentInvestigation) => incidentInvestigation.mailPlannings,
    {
      onDelete: 'CASCADE',
    },
  )
  public incidentInvestigation: IncidentInvestigation;
}
