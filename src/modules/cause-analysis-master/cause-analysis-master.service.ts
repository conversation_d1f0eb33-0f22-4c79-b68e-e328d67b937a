import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { TokenPayloadModel, Utils, BaseError } from 'svm-nest-lib-v3';
import { ListCauseAnalysisMasterDto } from './dto';
import { CreateCauseAnalysisMasterDto } from './dto/create-cause-analysis-master.dto';
import { UpdateCauseAnalysisMasterDto } from './dto/update-cause-analysis-master.dto';
import { CauseMainCategory } from './entities/cause-main-category.entity';
import { CauseSubCategory } from './entities/cause-sub-category.entity';
import { CauseAnalysisMaster } from './entities/cause-analysis-master.entity';
import { CauseAnalysisMasterRepository } from './repositories/cause-analysis-master.repository';
import { In } from 'typeorm';

@Injectable()
export class CauseAnalysisMasterService {
  constructor(
    @InjectRepository(CauseAnalysisMaster)
    private readonly causeAnalysisMasterRepo: CauseAnalysisMasterRepository,
  ) {}

  private async _checkExistingUdfVersionNo(udfVersionNo: string, companyId: string) {
    const queryBuilder = this.causeAnalysisMasterRepo
      .createQueryBuilder('causeAnalysisMaster')
      .where('causeAnalysisMaster.udfVersionNo = :udfVersionNo', { udfVersionNo })
      .andWhere('(causeAnalysisMaster.companyId = :companyId OR company.parentId = :companyId)', {
        companyId,
      })
      .leftJoin('causeAnalysisMaster.company', 'company');

    const existingRecord = await queryBuilder.getOne();
    if (existingRecord) {
      throw new BaseError({
        status: 400,
        message: `User Defined Version number ${udfVersionNo} already exists.`,
      });
    }
  }

  async createCauseAnalysisMaster(
    entityParam: CreateCauseAnalysisMasterDto,
    user: TokenPayloadModel,
  ) {
    try {
      // Check if udfVersionNo already exists
      await this._checkExistingUdfVersionNo(entityParam.udfVersionNo, user.companyId);

      const causeAnalysisMasterId = Utils.strings.generateUUID();
      const causeAnalysisMaster = new CauseAnalysisMaster();
      causeAnalysisMaster.id = causeAnalysisMasterId;
      causeAnalysisMaster.causeType = entityParam.causeType;
      causeAnalysisMaster.udfVersionNo = entityParam.udfVersionNo;
      causeAnalysisMaster.status = entityParam.status;
      causeAnalysisMaster.timezone = entityParam.timezone;
      causeAnalysisMaster.companyId = user.companyId;
      causeAnalysisMaster.createdUserId = user.id;

      const preparedMains: CauseMainCategory[] = [];
      const preparedSubs: CauseSubCategory[] = [];

      for (const mainCategoryData of entityParam.causeMainCategories) {
        const mainCategoryId = Utils.strings.generateUUID();
        const mainCategory = new CauseMainCategory();
        mainCategory.id = mainCategoryId;
        mainCategory.mainCategoryNo = mainCategoryData.mainCategoryNo;
        mainCategory.mainCategoryName = mainCategoryData.mainCategoryName;
        mainCategory.potentialRiskId = mainCategoryData.potentialRiskId;
        mainCategory.causeAnalysisMasterId = causeAnalysisMasterId;
        mainCategory.companyId = user.companyId;
        preparedMains.push(mainCategory);

        if (mainCategoryData.causeSubCategories) {
          for (const subCategoryData of mainCategoryData.causeSubCategories) {
            this._prepareSubCategories(
              subCategoryData,
              mainCategoryId,
              user.companyId,
              preparedSubs,
              null,
              1,
            );
          }
        }
      }

      await this.causeAnalysisMasterRepo.createCauseAnalysisMaster(
        causeAnalysisMaster,
        preparedMains,
        preparedSubs,
      );
      return causeAnalysisMasterId;
    } catch (error) {
      if (error instanceof BaseError) {
        throw error;
      }
      throw new BaseError({
        status: 500,
        message: `Failed to create cause analysis master: ${error.message}`,
      });
    }
  }

  private _prepareSubCategories(
    subCategoryData: any,
    mainCategoryId: string,
    companyId: string,
    preparedSubs: CauseSubCategory[],
    parentId: string | null,
    level: number,
  ) {
    const subCategoryId = subCategoryData.id || Utils.strings.generateUUID();
    const subCategory = new CauseSubCategory();
    subCategory.id = subCategoryId;
    subCategory.subCategoryName = subCategoryData.subCategoryName;
    subCategory.subRefNo = subCategoryData.subRefNo;
    subCategory.level = level;
    subCategory.parentId = parentId;
    subCategory.causeMainCategoryId = mainCategoryId;
    subCategory.companyId = companyId;
    preparedSubs.push(subCategory);

    // Handle children (second sub categories)
    if (subCategoryData.children && subCategoryData.children.length > 0) {
      for (const childData of subCategoryData.children) {
        this._prepareSubCategories(
          childData,
          mainCategoryId,
          companyId,
          preparedSubs,
          subCategoryId,
          2,
        );
      }
    }
  }

  async getListCauseAnalysisMaster(query: ListCauseAnalysisMasterDto, user: TokenPayloadModel) {
    return this.causeAnalysisMasterRepo.getListCauseAnalysisMaster(query, user);
  }

  async getDetailCauseAnalysisMaster(id: string, user: TokenPayloadModel) {
    return this.causeAnalysisMasterRepo.getDetailCauseAnalysisMaster(id, user);
  }

  async updateCauseAnalysisMaster(
    id: string,
    entityParam: UpdateCauseAnalysisMasterDto,
    user: TokenPayloadModel,
  ) {
    try {
      // Verify record exists and user has access
      await this.causeAnalysisMasterRepo.getDetailCauseAnalysisMaster(id, user);

      // Get existing data from database using repository methods
      const existingMainCategories = await this.causeAnalysisMasterRepo.getExistingMainCategories(
        id,
      );
      const existingSubCategories = await this.causeAnalysisMasterRepo.getExistingSubCategories(
        existingMainCategories.map((mc) => mc.id),
      );

      // Prepare new data from DTO
      const newMainCategories: CauseMainCategory[] = [];
      const newSubCategories: CauseSubCategory[] = [];

      if (entityParam.causeMainCategories) {
        for (const mainCategoryData of entityParam.causeMainCategories) {
          const mainCategoryId = mainCategoryData.id || Utils.strings.generateUUID();
          const mainCategory = new CauseMainCategory();
          mainCategory.id = mainCategoryId;
          mainCategory.mainCategoryNo = mainCategoryData.mainCategoryNo;
          mainCategory.mainCategoryName = mainCategoryData.mainCategoryName;
          mainCategory.potentialRiskId = mainCategoryData.potentialRiskId;
          mainCategory.causeAnalysisMasterId = id;
          mainCategory.companyId = user.companyId;
          newMainCategories.push(mainCategory);

          if (mainCategoryData.causeSubCategories) {
            for (const subCategoryData of mainCategoryData.causeSubCategories) {
              this._prepareSubCategories(
                subCategoryData,
                mainCategoryId,
                user.companyId,
                newSubCategories,
                null,
                1,
              );
            }
          }
        }
      }

      // === DIFFERENTIAL UPDATE LOGIC ===

      // Find main categories to delete (exist in DB but not in DTO)
      const mainCategoriesToDelete = existingMainCategories.filter(
        (existing) =>
          !newMainCategories.some(
            (newMain) =>
              newMain.id === existing.id ||
              (newMain.mainCategoryNo === existing.mainCategoryNo &&
                newMain.mainCategoryName === existing.mainCategoryName),
          ),
      );

      // Find main categories to add (exist in DTO but not in DB)
      const mainCategoriesToAdd = newMainCategories.filter(
        (newMain) =>
          !existingMainCategories.some(
            (existing) =>
              existing.id === newMain.id ||
              (existing.mainCategoryNo === newMain.mainCategoryNo &&
                existing.mainCategoryName === newMain.mainCategoryName),
          ),
      );

      // Find main categories to update (exist in both but with different data)
      const mainCategoriesToUpdate = newMainCategories.filter((newMain) =>
        existingMainCategories.some(
          (existing) =>
            existing.id === newMain.id &&
            (existing.mainCategoryNo !== newMain.mainCategoryNo ||
              existing.mainCategoryName !== newMain.mainCategoryName ||
              existing.potentialRiskId !== newMain.potentialRiskId),
        ),
      );

      // Find sub categories to delete (exist in DB but not in DTO)
      const subCategoriesToDelete = existingSubCategories.filter(
        (existing) =>
          !newSubCategories.some(
            (newSub) =>
              newSub.id === existing.id ||
              (newSub.subCategoryName === existing.subCategoryName &&
                newSub.causeMainCategoryId === existing.causeMainCategoryId),
          ),
      );

      // Find sub categories to add (exist in DTO but not in DB)
      const subCategoriesToAdd = newSubCategories.filter(
        (newSub) =>
          !existingSubCategories.some(
            (existing) =>
              existing.id === newSub.id ||
              (existing.subCategoryName === newSub.subCategoryName &&
                existing.causeMainCategoryId === newSub.causeMainCategoryId),
          ),
      );

      // Find sub categories to update (exist in both but with different data)
      const subCategoriesToUpdate = newSubCategories.filter((newSub) =>
        existingSubCategories.some(
          (existing) =>
            existing.id === newSub.id && existing.subCategoryName !== newSub.subCategoryName,
        ),
      );

      // === EXECUTE DIFFERENTIAL UPDATE USING REPOSITORY ===

      const results = await this.causeAnalysisMasterRepo.updateCauseAnalysisMasterDifferential(
        id,
        entityParam,
        mainCategoriesToDelete,
        mainCategoriesToAdd,
        mainCategoriesToUpdate,
        subCategoriesToDelete,
        subCategoriesToAdd,
        subCategoriesToUpdate,
        user,
      );

      console.log('Differential update completed successfully:', results);

      return id;
    } catch (error) {
      if (error instanceof BaseError) {
        throw error;
      }
      throw new BaseError({
        status: 500,
        message: `Failed to update cause analysis master: ${error.message}`,
      });
    }
  }

  async deleteCauseAnalysisMaster(id: string, user: TokenPayloadModel) {
    return this.causeAnalysisMasterRepo.deleteCauseAnalysisMaster(id, user);
  }

  async getActiveCauseAnalysisMaster(companyId: string) {
    return this.causeAnalysisMasterRepo.getActiveCauseAnalysisMaster(companyId);
  }
}
