import { VesselScreeningRisk } from '../../../modules-qa/vessel-screening/entity/vessel-screening-risk.entity';
import { Column, Entity, ManyToOne } from 'typeorm';
import { Vessel } from '../../vessel/entity/vessel.entity';

@Entity()
export class ChartererInspectionRequest extends VesselScreeningRisk {
  @Column({ type: 'uuid', nullable: true })
  public vesselId: string;

  @ManyToOne(() => Vessel, { onDelete: 'CASCADE' })
  vessel: Vessel;
}
