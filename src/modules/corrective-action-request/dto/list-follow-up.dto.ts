import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayUnique,
  IsArray,
  IsDateString,
  IsEnum,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { IsGreaterThanOrEqual } from 'svm-nest-lib-v3';
import { ListQueryDto } from '../../../commons/dtos';
import { DataType, FilterField } from 'src/utils';
import { AuditEntity } from '../../../commons/enums';

export class ListFollowUpDto extends ListQueryDto {
  @IsOptional()
  @IsUUID('all', { each: true })
  @IsArray()
  @ArrayUnique()
  ids?: string[];

  @ApiProperty({
    type: 'string',
    description: 'ISO string',
    example: '2020-10-05T14:48:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsDateString({ strict: true })
  planningFrom?: string;

  @ApiProperty({
    type: 'string',
    description: 'ISO string',
    example: '2023-10-05T14:48:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsDateString({ strict: true })
  @IsGreaterThanOrEqual('planningFrom', { message: 'common.INVALID_DATE_RANGE' })
  planningTo?: string;

  @IsOptional()
  @IsUUID('all', { each: true })
  vesselId?: string;

  @ApiProperty({
    enum: AuditEntity,
    required: false,
    description: 'entity status',
  })
  @IsOptional()
  @IsEnum(AuditEntity)
  entityType?: string;
}
export const INSPECTION_FOLLOW_UP_FILTER_FIELDS: FilterField[] = [
  {
    field: 'vesselName',
    column: '"vessel_name"',
    type: DataType.TEXT,
  },
  {
    field: 'vesselTypeName',
    column: '"vesselType_name"',
    type: DataType.TEXT,
  },
  {
    field: 'auditCompany',
    column: '"companyVesselDocHolders_name"',
    type: DataType.TEXT,
  },
  {
    field: 'officeName',
    column: '"auditCompany_name"',
    type: DataType.TEXT,
  },
  {
    field: 'inspectiontype',
    column: '"planningRequest_auditTypesName"',
    type: DataType.TEXT,
  },
  {
    field: 'entityType',
    column: '"planningRequest_entityType"',
    type: DataType.TEXT,
  },
  {
    field: 'leadAuditorName',
    column: '"leadAuditor_username"',
    type: DataType.TEXT,
  },
  {
    field: 'status',
    column: '"followUp_status"',
    type: DataType.TEXT,
  },
  {
    field: 'plannedFromDate',
    column: '"planningRequest_plannedFromDate"',
    type: DataType.DATE,
  },
  {
    field: 'plannedToDate',
    column: '"planningRequest_plannedToDate"',
    type: DataType.DATE,
  },
  {
    field: 'department',
    column: '"departmentsName"',
    type: DataType.TEXT,
  },
  {
    field: 'globalStatus',
    column: '"planningRequest_globalStatus"',
    type: DataType.TEXT,
  },
  {
    field: 'totalCars',
    column: '"totalCars"',
    type: DataType.NUMBER,
  },
  {
    field: 'totalCloseCars',
    column: '"totalCloseCars"',
    type: DataType.NUMBER,
  },
  {
    field: 'totalCaps',
    column: '"totalCaps"',
    type: DataType.NUMBER,
  },
  {
    field: 'totalAcceptCaps',
    column: '"totalAcceptCaps"',
    type: DataType.NUMBER,
  },
  {
    field: 'totalDeniedCaps',
    column: '"totalDeniedCaps"',
    type: DataType.NUMBER,
  },
  {
    field: 'createCompanyName',
    column: '"company_name"',
    type: DataType.TEXT,
  },
  {
    field: 'inspectionStartDateApp',
    column: '"auditWorkspace_mobileInspectionStartDate"',
    type: DataType.TEXT,
  },
  {
    field: 'inspectionEndDateApp',
    column: '"auditWorkspace_mobileInspectionEndDate"',
    type: DataType.TEXT,
  },
  {
    field: 'sNo',
    column: '"planningRequest_auditNo"',
    type: DataType.TEXT,
  },
  {
    field: 'refId',
    column: '"followUp_refId"',
    type: DataType.TEXT,
  },
  {
    field: 'rofRefId',
    column: '"reportFindingForm_refNo"',
    type: DataType.TEXT,
  },
  {
    field: 'submittedDateMonth',
    column: '"auditWorkspace_submittedDate_Month"',
    type: DataType.TEXT,
  },
  {
    field: 'submittedDateYear',
    column: '"auditWorkspace_submittedDate_Year"',
    type: DataType.TEXT,
  },
  {
    field: 'customerRef',
    column: '"planningRequest_customerRef"',
    type: DataType.TEXT,
  },
  {
    field: 'voyageType',
    column: '"voyageType_name"',
    type: DataType.TEXT,
  },
  {
    field: 'doos',
    column: '"planningRequest_doos"',
    type: DataType.TEXT,
  },
];

export enum TitleInspectionFollowUp {
  VESSEL_NAME = 'Vessel Name',
  VESSEL_TYPE = 'Vessel Type',
  DOC_HOLDER_COMPANY = 'DOC Holder Company',
  OFFICE_NAME = 'Office Name',
  ENTITY = 'Entity',
  LEAD_INSPECTOR_NAME = 'Lead Inspector Name',
  STATUS = 'Status',
  INSPECTION_PLANNED_FROM = 'Inspection Planned From Date',
  INSPECTION_PLANNED_TO = 'Inspection Planned To Date',
  DEPARTMENT = 'Department',
  GLOBAL_STATUS = 'Global Status',
  TOTAL_CARS = 'Total Number Of CAR',
  TOTAL_CLOSED_CARS = 'Total Number Of Closed CAR',
  TOTAL_CAPS = 'Total Number Of CAP',
  TOTAL_ACCEPT_CAPS = 'Total Number Of Accept CAP',
  TOTAL_DENIED_CAPS = 'Total Number Of Denied CAP',
  CREATED_BY_COMPANY = 'Created By Company',
  INSPECTION_START_DATE = 'Inspection Start Date(App)',
  INSPECTION_END_DATE = 'Inspection End Date(App)',
  INSPECTION_S_NO = 'Inspection S.No',
  REF_ID = 'Ref.ID',
  ROF_REF_ID = 'ROF Ref.ID',
  SUBMITTED_DATE_MONTH = 'Submitted Month',
  SUBMITTED_DATE_YEAR = 'Submitted Year',
  CUSTOMER_REF_ID = 'Customer Ref ID',
}
