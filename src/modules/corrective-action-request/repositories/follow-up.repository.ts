import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  RoleScope<PERSON>heck,
  TokenPayloadModel,
  TypeORMRepository,
} from 'svm-nest-lib-v3';
import { Connection, EntityRepository, getConnection, In } from 'typeorm';
import {
  AuditEntity,
  CapStatusEnum,
  CarStatusEnum,
  CarVerificationStatusEnum,
  CompanyLevelEnum,
} from '../../../commons/enums';
import { ReportFindingForm } from '../../report-finding/entities/report-finding-form.entity';
import { INSPECTION_FOLLOW_UP_FILTER_FIELDS, ListFollowUpDto } from '../dto/list-follow-up.dto';

import { CreatedUserHistoryModel } from '../../../commons/models';
import { AuditActivityEnum, AuditModuleEnum } from '../../audit-log/audit-log.entity';
import { AuditLogRepository } from '../../audit-log/audit-log.repository';
import { ReportFindingFormRepository } from '../../report-finding/repositories/report-finding-form.repository';
import { UpdateFollowUpCommentDTO } from '../dto/update-follow-up.dto';
import { FollowUpComment } from '../entities/follow-up-comment.entity';
import { FollowUp } from '../entities/follow-up.entity';
import { cloneDeep } from 'lodash';
import { CARRepository } from './car.repository';
import { ROFPlanningRequest } from '../../report-finding/entities/rof-planning-request.entity';
import { ROFAuditType } from '../../report-finding/entities/rof-audit-type.entity';
import { ROFUser } from '../../report-finding/entities/rof-user.entity';
import { ListCarCapNeedReview } from '../../dashboard/dtos/list-car-cap-need-review';
import { TrendOfOutstandingCarCapDto } from '../../dashboard/dtos/trend-of-outstanding-car-cap.dto';
import { CompanyRepository } from '../../company/company.repository';
import { TrendOfOutstandingCarCapDetailDto } from '../../dashboard/dtos/trend-of-outstanding-car-cap-detail.dto';
import { CarCapStatusFilterEnum } from '../../dashboard/enums';
import { PlanningRequestRepository } from '../../planning-request/repositories/planning-request.repository';
import { InternalAuditReportRepository } from '../../internal-audit-report/repositories/internal-audit-report.repository';
import {
  convertFilterField,
  createSelectSql,
  createWhereSql,
  handleGetDataForAGGrid,
  PayloadAGGridDto,
} from 'src/utils';
import { VesselRepository } from 'src/modules/vessel/vessel.repository';
import { AnalyticalReportInspectionPerformanceRepository } from 'src/modules/analytical-report/repositories/analytical-report-inspection-performance.repository';
import {
  _supportCheckRoleScopeForGetList,
  _supportWhereDOCChartererOwner,
} from '../../../commons/functions';

@EntityRepository(FollowUp)
export class FollowUpRepository extends TypeORMRepository<FollowUp> {
  constructor(private readonly connection: Connection) {
    super();
  }
  async getDetailFollowUp(id: string, user: TokenPayloadModel) {
    try {
      let whereVesselDocHolder = `(1=1)`;
      if (user.companyLevel === CompanyLevelEnum.EXTERNAL_COMPANY) {
        whereVesselDocHolder = `(vesselDocHolders.responsiblePartyInspection = true AND vesselDocHolders.companyId = '${user.explicitCompanyId}' AND vesselDocHolders.deleted = false)`;
      }
      let whereVesselCharterer = `(1=1)`;
      if (user.companyLevel === CompanyLevelEnum.EXTERNAL_COMPANY) {
        whereVesselCharterer = `(vesselCharterers.responsiblePartyInspection = true AND vesselCharterers.companyId = '${user.explicitCompanyId}' AND vesselCharterers.deleted = false)`;
      }
      let whereVesselOwner = `(1=1)`;
      if (user.companyLevel === CompanyLevelEnum.EXTERNAL_COMPANY) {
        whereVesselOwner = `(vesselOwnersPlans.responsiblePartyInspection = true AND vesselOwnersPlans.companyId = '${user.explicitCompanyId}' AND vesselOwnersPlans.deleted = false)`;
      }
      const form = await this.manager
        .createQueryBuilder(ReportFindingForm, 'reportFindingForm')
        .leftJoin('reportFindingForm.rofPlanningRequest', 'rofPlanningRequest')
        .leftJoin('reportFindingForm.rofAuditTypes', 'rofAuditTypes')
        .leftJoin('reportFindingForm.followUp', 'followUp')
        .leftJoin('followUp.followUpComments', 'followUpComments')
        .leftJoin('reportFindingForm.planningRequest', 'planningRequest')
        .leftJoin('planningRequest.auditWorkspace', 'auditWorkspace')
        .leftJoin('planningRequest.auditCompany', 'auditCompany')
        .leftJoin('planningRequest.leadAuditor', 'leadAuditor')
        .leftJoin('planningRequest.location', 'location')
        .leftJoin('planningRequest.cars', 'cars')
        .leftJoin('cars.cap', 'cap')
        .leftJoin('reportFindingForm.vessel', 'vessel')
        .leftJoin('reportFindingForm.rofUsers', 'rofUsers')
        .leftJoin('rofUsers.user', 'rofUser')
        .leftJoin('reportFindingForm.company', 'company')
        .leftJoin('planningRequest.auditTimeTable', 'auditTimeTable')
        .leftJoin('planningRequest.voyageType', 'voyageType')
        .select([
          'reportFindingForm.refNo',
          'reportFindingForm.id',
          'vessel.id',
          'vessel.isVesselRestricted',
          'vessel.isCompanyRestricted',
          'followUp.status',
          'followUp.refId',
          'followUp.id',
          'followUp.createdAt',
          'rofPlanningRequest',
          'planningRequest.id',
          'planningRequest.refId',
          'planningRequest.auditNo',
          'planningRequest.typeOfAudit',
          'planningRequest.plannedFromDate',
          'planningRequest.plannedToDate',
          'planningRequest.memo',
          'planningRequest.globalStatus',
          'planningRequest.customerRef',
          'auditCompany.name',
          'auditCompany.isCompanyRestricted',
          'auditWorkspace.id',
          'auditWorkspace.submittedDate',
          'auditWorkspace.mobileInspectionStartDate',
          'auditWorkspace.mobileInspectionEndDate',
          'auditWorkspace.attachments',
          'leadAuditor.id',
          'leadAuditor.email',
          'leadAuditor.username',
          'location.id',
          'location.name',
          'rofUsers',
          'followUpComments',
          'rofAuditTypes',
          'cars',
          'cap',
          'auditTimeTable.actualFrom',
          'auditTimeTable.actualTo',
          'rofUser.email',
          'voyageType.id',
          'voyageType.name',
        ])
        .leftJoin('vessel.vesselDocHolders', 'vesselDocHolders', whereVesselDocHolder)
        .leftJoin('vesselDocHolders.company', 'companyVesselDocHolders')
        .addSelect([
          'vesselDocHolders.id',
          'vesselDocHolders.companyId',
          'vesselDocHolders.fromDate',
          'vesselDocHolders.toDate',
          'vesselDocHolders.responsiblePartyInspection',
          'vesselDocHolders.responsiblePartyQA',
          'vesselDocHolders.status',
          'companyVesselDocHolders.name',
        ])
        .leftJoin('vessel.vesselOwners', 'vesselOwnersPlans', whereVesselOwner)
        .leftJoin('vesselOwnersPlans.company', 'companyVesselOwnersPlans')
        .addSelect([
          'vesselOwnersPlans.id',
          'vesselOwnersPlans.companyId',
          'vesselOwnersPlans.fromDate',
          'vesselOwnersPlans.toDate',
          'vesselOwnersPlans.responsiblePartyInspection',
          'vesselOwnersPlans.responsiblePartyQA',
          'vesselOwnersPlans.status',
          'companyVesselOwnersPlans.name',
        ])
        .leftJoin('vessel.vesselCharterers', 'vesselCharterers', whereVesselCharterer)
        .leftJoin('vesselCharterers.company', 'companyVesselCharterers')
        .addSelect([
          'vesselCharterers.id',
          'vesselCharterers.companyId',
          'vesselCharterers.fromDate',
          'vesselCharterers.toDate',
          'vesselCharterers.responsiblePartyInspection',
          'vesselCharterers.responsiblePartyQA',
          'vesselCharterers.status',
          'companyVesselCharterers.name',
        ])
        .where(
          '(followUp.id = :id AND (reportFindingForm.companyId = :companyId OR company.parentId = :companyId))',
          {
            id,
            companyId: user.companyId,
          },
        )
        .getOne();

      const internalAuditReport = await this.manager
        .getCustomRepository(InternalAuditReportRepository)
        .createQueryBuilder('internalAuditReport')
        .select(['internalAuditReport.id', 'internalAuditReport.status'])
        .where(
          'internalAuditReport.planningRequestId = :planningRequestId AND internalAuditReport.companyId = :companyId',
          {
            planningRequestId: form.planningRequest.id,
            companyId: user.companyId,
          },
        )
        .getOne();

      const cars = form.planningRequest.cars;
      const totalCars = cars.length;
      const totalCloseCars = cars.filter((car) => car?.status == CarStatusEnum.CLOSED).length;
      const totalOpenCars = totalCars - totalCloseCars;

      // fetching the caps accepted/denied history for relavant vessel
      const fetchHistoryForVesselCap = await this.manager
        .getCustomRepository(VesselRepository)
        .createQueryBuilder('vessel')
        .innerJoin('vessel.planningRequest', 'planningRequest')
        .innerJoin('planningRequest.auditWorkspace', 'auditWorkspace')
        .innerJoin('planningRequest.auditTypes', 'auditTypes')
        .innerJoin('planningRequest.cars', 'cars')
        .leftJoin('cars.cARVerification', 'cARVerification')
        .innerJoin('cars.cap', 'cap')
        .leftJoin('cap.capHistories', 'capHistories')
        .innerJoin('planningRequest.followUp', 'followUp')
        .select(['vessel.id', 'vessel.name'])
        .addSelect([
          'followUp.id',
          'followUp.status',
          'cars.id',
          'cap.status',
          'capHistories',
          'planningRequest.auditNo',
          'auditTypes.name',
          'auditWorkspace.id',
          'cARVerification.id',
          'cARVerification.status',
        ])
        .where(`vessel.id = :vesselId`, {
          vesselId: form?.vessel?.id,
        })
        .getOne();
      const historyForVesselCaps = [];
      fetchHistoryForVesselCap?.planningRequest.forEach((item) => {
        const capHistories = item?.cars
          ?.map((data) => data?.cap?.capHistories)
          .reduce((acc, curr) => acc.concat(curr), []);
        historyForVesselCaps.push({
          inspectionSNo: item?.auditNo,
          acceptedCaps: item?.cars?.filter(
            (data) =>
              data?.cap?.status === CapStatusEnum.REVIEWED_1 ||
              data?.cap?.status === CapStatusEnum.REVIEWED_2 ||
              data?.cap?.status === CapStatusEnum.REVIEWED_3 ||
              data?.cap?.status === CapStatusEnum.REVIEWED_4 ||
              data?.cap?.status === CapStatusEnum.REVIEWED_5 ||
              data?.cARVerification?.status === CarVerificationStatusEnum.HOLDING ||
              data?.cARVerification?.status === CarVerificationStatusEnum.PENDING ||
              data?.cARVerification?.status === CarVerificationStatusEnum.VERIFIED_AND_CLOSE ||
              data?.cARVerification?.status === CarVerificationStatusEnum.OVERRIDING_CLOSURE,
          )?.length,
          deniedCaps: item?.cars?.filter((data) => data?.cap?.status === CapStatusEnum.DENIED)
            .length,
          inprogessCaps: item?.cars?.filter((data) => data?.cap?.status === CapStatusEnum.DRAFT)
            ?.length,
          overallCountOfEachCapDenied: capHistories?.filter(
            (item) => item?.status === CapStatusEnum.DENIED,
          )?.length,
          followUpStatus: item?.followUp?.status,
          followUpId: item?.followUp?.id,
          auditWorkspaceId: item?.auditWorkspace?.id,
        });
      });

      return {
        ...form,
        totalCars,
        totalCloseCars,
        totalOpenCars,
        internalAuditReport,
        historyForVesselCaps,
      };
    } catch (ex) {
      LoggerCommon.error('[FollowUpRepository] getDetailFollowUp error ', ex.message || ex);
      throw ex;
    }
  }

  private _createQueryListFollowup() {
    return (
      this.manager
        .createQueryBuilder(ReportFindingForm, 'reportFindingForm')
        // .leftJoin('reportFindingForm.rofAuditTypes', 'rofAuditTypes')
        // .leftJoin('reportFindingForm.rofPlanningRequest', 'rofPlanningRequest')
        .leftJoin('reportFindingForm.followUp', 'followUp')
        // .leftJoin('followUp.followUpComments', 'followUpComments')
        .leftJoin('reportFindingForm.planningRequest', 'planningRequest')
        // .leftJoin('planningRequest.auditTypes', 'auditTypes')
        .leftJoin('planningRequest.auditWorkspace', 'auditWorkspace')
        .leftJoin('planningRequest.vessel', 'vessel')
        .leftJoin('vessel.vesselType', 'vesselType')
        // .leftJoin('reportFindingForm.rofUsers', 'rofUsers')
        .leftJoin('reportFindingForm.company', 'company')
        .leftJoin('planningRequest.cars', 'cars')
        .leftJoin('cars.cap', 'cap')
        .leftJoin('planningRequest.auditors', 'auditors')
        .leftJoin('planningRequest.leadAuditor', 'leadAuditor')
        .leftJoin('planningRequest.auditCompany', 'auditCompany')
        .leftJoin('reportFindingForm.userAssignments', 'userAssignments')
        .leftJoin('userAssignments.user', 'user')
        .leftJoin(
          'vessel.vesselDocHolders',
          'vesselDocHolders',
          `vesselDocHolders.status = 'active'`,
        )
        .leftJoin('vesselDocHolders.company', 'companyVesselDocHolders')
        .leftJoin('planningRequest.auditTimeTable', 'auditTimeTable')
        .leftJoin('planningRequest.voyageType', 'voyageType')
        .select([
          'followUp',
          'reportFindingForm.refNo',
          'reportFindingForm.id',
          'reportFindingForm.createdAt',
          'planningRequest.id',
          'planningRequest.refId',
          'planningRequest.auditNo',
          'planningRequest.plannedFromDate',
          'planningRequest.plannedToDate',
          'planningRequest.memo',
          'planningRequest.globalStatus',
          'planningRequest.customerRef',
          'planningRequest.doos',
          'planningRequest.entityType',
          'planningRequest.auditTypesName',
          // 'auditTypes',
          'auditWorkspace.id',
          'leadAuditor.id',
          'leadAuditor.username',
          'auditWorkspace.submittedDate',
          'auditWorkspace.id',
          'auditWorkspace.mobileInspectionStartDate',
          'auditWorkspace.mobileInspectionEndDate',
          'auditWorkspace.submittedDate_Month',
          'auditWorkspace.submittedDate_Year',
          'company.id',
          'company.name',
          'vessel.id',
          'cars.status',
          'vessel.name',
          'vesselType.name',
          'vesselDocHolders.id',
          // 'vesselDocHolders.companyId',
          // 'vesselDocHolders.fromDate',
          // 'vesselDocHolders.toDate',
          // 'vesselDocHolders.responsiblePartyInspection',
          // 'vesselDocHolders.responsiblePartyQA',
          // 'vesselDocHolders.status',
          'companyVesselDocHolders.name',
          'auditTimeTable.actualFrom',
          'auditTimeTable.actualTo',
          'auditTimeTable.actualFrom_Year',
          'auditTimeTable.actualTo_Year',
          'auditTimeTable.actualFrom_Month',
          'auditTimeTable.actualTo_Month',
          'auditCompany.name',
          'voyageType.name',
        ])
    );
  }

  async listFollowUp(query: ListFollowUpDto, user: TokenPayloadModel, body?: PayloadAGGridDto) {
    try {
      const queryBuilder = this._createQueryListFollowup().where(
        `(reportFindingForm.companyId = '${user.companyId}' AND followUp.id IS NOT NULL)`,
      );

      const fieldSelects = [
        'reportFindingForm.id',
        'followUp.id',
        'followUp.status',
        'followUp.refId',
        'followUp.createdAt',
        'reportFindingForm.refNo',
        'reportFindingForm.createdAt',
        'planningRequest.id',
        'planningRequest.refId',
        'planningRequest.auditNo',
        'planningRequest.plannedFromDate',
        'planningRequest.plannedToDate',
        'planningRequest.memo',
        'planningRequest.globalStatus',
        'planningRequest.customerRef',
        'planningRequest.doos',
        'planningRequest.entityType',
        'planningRequest.auditTypesName',
        // 'auditTypes.id',
        // 'auditTypes.name',
        'auditWorkspace.id',
        'leadAuditor.id',
        'cars.status',
        'leadAuditor.username',
        'auditWorkspace.submittedDate',
        'auditWorkspace.mobileInspectionStartDate',
        'auditWorkspace.mobileInspectionEndDate',
        'auditWorkspace.submittedDate_Month',
        'auditWorkspace.submittedDate_Year',
        'company.id',
        'company.name',
        'vessel.id',
        'vessel.name',
        'vesselType.name',
        'vesselDocHolders.id',
        // 'vesselDocHolders.companyId',
        // 'vesselDocHolders.fromDate',
        // 'vesselDocHolders.toDate',
        // 'vesselDocHolders.responsiblePartyInspection',
        // 'vesselDocHolders.responsiblePartyQA',
        // 'vesselDocHolders.status',
        'companyVesselDocHolders.name',
        'auditTimeTable.actualFrom',
        'auditTimeTable.actualTo',
        'auditTimeTable.actualFrom_Year',
        'auditTimeTable.actualTo_Year',
        'auditTimeTable.actualFrom_Month',
        'auditTimeTable.actualTo_Month',
        'auditCompany.name',
        'voyageType.name',
      ];

      if (query.ids?.length) {
        queryBuilder.andWhere('reportFindingForm.id IN (:...ids)', {
          ids: query.ids,
        });

        return await this._supportOptimizeListFuForHomepage(queryBuilder, query);
      }

      if (query.planningFrom) {
        queryBuilder.andWhere(`planningRequest.plannedFromDate >= '${query.planningFrom}'`);
      }

      if (query.planningTo) {
        queryBuilder.andWhere(`planningRequest.plannedFromDate <= '${query.planningTo}'`);
      }
      if (query.vesselId) {
        queryBuilder.andWhere(`vessel.id = '${query.vesselId}'`);
      }

      if (query.entityType) {
        queryBuilder.andWhere(`planningRequest.entityType = '${query.entityType}'`);
      }

      if (!RoleScopeCheck.isAdmin(user)) {
        const { whereForExternal, whereForMainAndInternal } = await _supportWhereDOCChartererOwner(
          this.manager,
          user.explicitCompanyId,
          'reportFindingForm',
          'planningRequest',
        );
        queryBuilder
          .leftJoin('vessel.divisionMapping', 'divisionMapping')
          .leftJoin('divisionMapping.division', 'division')
          .leftJoin('division.users', 'users')
          .addSelect([
            'vesselDocHolders.id',
            'vesselDocHolders.companyId',
            'vesselDocHolders.fromDate',
            'vesselDocHolders.toDate',
            'vesselDocHolders.responsiblePartyInspection',
            'vesselDocHolders.responsiblePartyQA',
            'vesselDocHolders.status',
            'companyVesselDocHolders.name',
          ])
          .leftJoin('vessel.vesselOwners', 'vesselOwners')
          .addSelect([
            'vesselOwners.id',
            'vesselOwners.companyId',
            'vesselOwners.fromDate',
            'vesselOwners.toDate',
            'vesselOwners.responsiblePartyInspection',
            'vesselOwners.responsiblePartyQA',
            'vesselOwners.status',
          ])
          .leftJoin('vessel.vesselCharterers', 'vesselCharterers')
          .addSelect([
            'vesselCharterers.id',
            'vesselCharterers.companyId',
            'vesselCharterers.fromDate',
            'vesselCharterers.toDate',
            'vesselCharterers.responsiblePartyInspection',
            'vesselCharterers.responsiblePartyQA',
            'vesselCharterers.status',
          ]);
        fieldSelects.push(
          'vesselDocHolders.id',
          'vesselDocHolders.companyId',
          'vesselDocHolders.fromDate',
          'vesselDocHolders.toDate',
          'vesselDocHolders.responsiblePartyInspection',
          'vesselDocHolders.responsiblePartyQA',
          'vesselDocHolders.status',
          'companyVesselDocHolders.name',
          'vesselOwners.id',
          'vesselOwners.companyId',
          'vesselOwners.fromDate',
          'vesselOwners.toDate',
          'vesselOwners.responsiblePartyInspection',
          'vesselOwners.responsiblePartyQA',
          'vesselOwners.status',
          'vesselCharterers.id',
          'vesselCharterers.companyId',
          'vesselCharterers.fromDate',
          'vesselCharterers.toDate',
          'vesselCharterers.responsiblePartyInspection',
          'vesselCharterers.responsiblePartyQA',
          'vesselCharterers.status',
        );
        await _supportCheckRoleScopeForGetList(
          this.manager,
          queryBuilder,
          user,
          whereForMainAndInternal,
          whereForExternal,
          'planningRequest',
        );
      }
      const connection = getConnection();
      const subQueryBuilder = connection.createQueryBuilder();

      this.buildSql(body, queryBuilder, user, subQueryBuilder, fieldSelects);

      const subQuerySelect = [
        // `"departmentsName"`,
        // `"holderCompany"`,
        // `"auditCompany_name"`,
        `"totalCars"`,
        `"totalCloseCars"`,
        `"totalAcceptCaps"`,
        `"totalCaps"`,
        `"totalPendingCaps"`,
        `"totalDeniedCaps"`,
        `"totalOpenCaps"`,
        `"totalAcceptedCaps"`,
      ];

      // const queryBuilderNew = queryBuilder.addSelect('"auditCompany"."name"').addGroupBy(
      //   `"followUp"."id",
      //         "reportFindingForm"."id" ,
      //         "planningRequest"."id",
      //         "auditTypes"."id",
      //         "auditWorkspace"."id",
      //         "company"."id",
      //         "vessel"."id",
      //         "cars"."id",
      //         "vesselType"."id",
      //         "vesselDocHolders"."id",
      //         "companyVesselDocHolders"."id",
      //         "auditTimeTable"."id",
      //         "departmentsGroup"."departmentsName",
	    //         "holderCompanyGroup"."holderCompany",
	    //         "holderCompanyGroup"."auditCompany_name",
	    //         "auditCompany"."name",
      //         "countCarCapGroup"."totalCars",
	    //         "countCarCapGroup"."totalCloseCars",
      //         "countCarCapGroup"."totalCaps",
      //         "countCarCapGroup"."totalAcceptCaps",
      //         "countCarCapGroup"."totalDeniedCaps",
      //         "countCarCapGroup"."totalOpenCaps",
      //         "countCarCapGroup"."totalAcceptedCaps",
      //         ${fieldSelects.join(', ')}`,
      // );
      const queryBuilderNew = this._createQueryListFollowup();
      let dataList;
      if (!body) {
        return {
          data: dataList || [],
          page: dataList?.page,
          pageSize: dataList?.pageSize,
          totalPage: dataList?.totalPage,
          totalItem: dataList.totalItem,
        };
      }
      dataList = await handleGetDataForAGGrid(
        this,
        queryBuilder,
        query,
        body,
        subQueryBuilder,
        queryBuilderNew,
        'reportFindingForm',
        subQuerySelect,
      );
      // if (query?.vesselId && dataList?.data?.length) {
      //   for (const item of dataList?.data) {
      //     const analyticalReport = await this.manager
      //       .getCustomRepository(AnalyticalReportInspectionPerformanceRepository)
      //       .getInfoAnalyticalReportInspectionPerformance(
      //         item?.planningRequest?.auditWorkspace?.id,
      //         user,
      //       );

      //     // Calculate scoring logic
      //     let observedRiskScore = 0;
      //     let potentialRiskScore = 0;
      //     // let observedRiskCategory = 'Negligible';
      //     // let potentialRiskCategory = 'Negligible';

      //     if (
      //       analyticalReport &&
      //       'maxValueVesselObservedRisk' in analyticalReport &&
      //       'totalObservedRisk' in analyticalReport &&
      //       typeof (analyticalReport as any).maxValueVesselObservedRisk === 'number' &&
      //       typeof (analyticalReport as any).totalObservedRisk === 'number' &&
      //       (analyticalReport as any).maxValueVesselObservedRisk > 0
      //     ) {
      //       observedRiskScore = Math.round(
      //         (Math.abs((analyticalReport as any).totalObservedRisk) /
      //           (analyticalReport as any).maxValueVesselObservedRisk) *
      //           100,
      //       );
      //     }

      //     if (
      //       analyticalReport &&
      //       'maxValueVesselPotentialRisk' in analyticalReport &&
      //       'totalPotentialRisk' in analyticalReport &&
      //       typeof (analyticalReport as any).maxValueVesselPotentialRisk === 'number' &&
      //       typeof (analyticalReport as any).totalPotentialRisk === 'number' &&
      //       (analyticalReport as any).maxValueVesselPotentialRisk > 0
      //     ) {
      //       potentialRiskScore = Math.round(
      //         (Math.abs((analyticalReport as any).totalPotentialRisk) /
      //           (analyticalReport as any).maxValueVesselPotentialRisk) *
      //           100,
      //       );
      //     }

          // Determine risk categories based on scoring ranges
          // Negligible: 0, Low: 1-19, Medium: 20-49, High: >=50
          // if (observedRiskScore >= 50) {
          //   observedRiskCategory = 'High';
          // } else if (observedRiskScore >= 20) {
          //   observedRiskCategory = 'Medium';
          // } else if (observedRiskScore >= 1) {
          //   observedRiskCategory = 'Low';
          // } else {
          //   observedRiskCategory = 'Negligible';
          // }

          // if (potentialRiskScore >= 50) {
          //   potentialRiskCategory = 'High';
          // } else if (potentialRiskScore >= 20) {
          //   potentialRiskCategory = 'Medium';
          // } else if (potentialRiskScore >= 1) {
          //   potentialRiskCategory = 'Low';
          // } else {
          //   potentialRiskCategory = 'Negligible';
          // }

          // Add calculated scores to analytical report
          // const enhancedAnalyticalReport = {
            // ...analyticalReport,
            // observedRiskScore,
            // potentialRiskScore,
            // observedRiskCategory,
            // potentialRiskCategory,
          // };

      //     Object.assign(item, { analyticalReport: enhancedAnalyticalReport });
      //   }
      //   return {
      //     data: dataList?.data,
      //     page: dataList?.page,
      //     pageSize: dataList?.pageSize,
      //     totalPage: dataList?.totalPage,
      //     totalItem: dataList?.totalItem,
      //   };
      // }

      return {
        data: dataList?.data,
        page: dataList?.page,
        pageSize: dataList?.pageSize,
        totalPage: dataList?.totalPage,
        totalItem: dataList?.totalItem,
      };
    } catch (ex) {
      LoggerCommon.error('[FollowUpRepository] listFollowUp error ', ex.message || ex);
      throw ex;
    }
  }

  async updateFollowUp(
    followUpId: string,
    updateComments: UpdateFollowUpCommentDTO[],
    deletedCommentIds: string[],
    createdUser: CreatedUserHistoryModel,
  ) {
    try {
      await this.connection.transaction(async (manager) => {
        const followUp = await manager.findOne(FollowUp, {
          where: { id: followUpId },
          select: ['id', 'planningRequestId'],
        });

        if (!followUp) {
          throw new BadRequestError({ message: 'common.NOT_FOUND' });
        }

        await manager.save(FollowUpComment, updateComments);
        await manager.delete(FollowUpComment, { id: In(deletedCommentIds) });

        // Handle add Audit log
        await manager.getCustomRepository(AuditLogRepository).createAuditLog(
          {
            module: AuditModuleEnum.INSPECTION_FOLLOW_UP,
            planningId: followUp.planningRequestId,
          },
          null,
          [AuditActivityEnum.UPDATED_INFO],
          createdUser,
        );
      });
    } catch (ex) {
      LoggerCommon.error('[CAPRepository] updateCap error ', ex.message || ex);
      throw ex;
    }
  }
  async listCapCarNeedReview(query: ListCarCapNeedReview, user: TokenPayloadModel) {
    const queryBuilder = this.connection
      .getCustomRepository(ReportFindingFormRepository)
      .createQueryBuilder('reportFindingForm')
      // .leftJoin('reportFindingForm.rofAuditTypes', 'rofAuditTypes')
      // .leftJoin('reportFindingForm.rofPlanningRequest', 'rofPlanningRequest')
      .leftJoin('reportFindingForm.followUp', 'followUp')
      // .leftJoin('followUp.followUpComments', 'followUpComments')
      .leftJoin('reportFindingForm.planningRequest', 'planningRequest')
      .leftJoin('planningRequest.vessel', 'vessel')
      // .leftJoin('reportFindingForm.rofUsers', 'rofUsers')
      .leftJoin('planningRequest.cars', 'cars')
      .leftJoin('cars.cap', 'cap')
      .leftJoin(
        'cars.reportFindingItems',
        'reportFindingItems',
        'reportFindingItems.deleted = false',
      )
      .leftJoin('planningRequest.auditors', 'auditors')
      .leftJoin('planningRequest.userAssignments', 'userAssignments')
      .leftJoin('userAssignments.user', 'user')
      .select([
        'reportFindingForm.id',
        'reportFindingForm.totalFindings',
        'planningRequest.id',
        'planningRequest.entityType',
        'followUp.status',
        'followUp.refId',
        'followUp.id',
        'followUp.createdAt',
        // 'rofPlanningRequest',
        // 'followUpComments',
        // 'rofUsers',
        // 'rofAuditTypes',
        'cars.id',
        'reportFindingItems.id',
        'cap.planAction',
        'cap.ecdCap',
      ])
      .where(
        '(reportFindingForm.companyId = :companyId AND followUp.id IS NOT NULL and cap.status = :status)',
        {
          companyId: user.companyId,
          status: CapStatusEnum.SUBMITTED,
        },
      );
    if (query.fromDate) {
      queryBuilder.andWhere('followUp.createdAt >= :fromDateParam', {
        fromDateParam: new Date(query.fromDate),
      });
    }

    if (query.toDate) {
      queryBuilder.andWhere('followUp.createdAt <= :toDateParam', {
        toDateParam: new Date(query.toDate),
      });
    }

    if (query.entityType) {
      queryBuilder.andWhere('planningRequest.entityType = :entityType', {
        entityType: query.entityType,
      });
    }
    if (!RoleScopeCheck.isAdmin(user)) {
      await this._buildQueryDOCChartererOwner(user, queryBuilder);
    }
    const result = await this.manager.getCustomRepository(ReportFindingFormRepository).list(
      {
        page: query.page,
        limit: query.pageSize,
      },
      {
        queryBuilder,
        sort: query.sort || 'followUp.createdAt:-1',
      },
    );
    if (result.data.length == 0) {
      return result;
    }
    const data = [];
    for (const temp of result.data) {
      for (const car of temp.planningRequest.cars) {
        data.push({
          id: temp.followUp.id,
          refId: temp.followUp.refId,
          numberOfFindings: car.reportFindingItems?.length,
          entity: temp.planningRequest.entityType,
          cap: car.cap.planAction,
          estimatedClosureDate: car.cap.ecdCap,
        });
      }
    }
    return {
      data: data,
      page: result.page,
      pageSize: result.pageSize,
      totalPage: result.totalPage,
      totalItem: result.totalItem,
    };
  }

  async trendOfOutstandingCarCap(query: TrendOfOutstandingCarCapDto, user: TokenPayloadModel) {
    const result: any = {};
    result.data = [];
    if (user.companyLevel === CompanyLevelEnum.MAIN_COMPANY) {
      // get all company of company parent
      const company = await this.manager
        .getCustomRepository(CompanyRepository)
        .createQueryBuilder('company')
        .where('company.id = :companyId', {
          companyId: user.companyId,
        })
        .select(['company.name', 'company.id', 'company.companyLevel'])
        .getOne();
      const { data, listIdResult } = await this._getDataTrendOfOutstandingCarCapByCompany(
        query,
        user,
        company.name,
      );
      const listROFId = listIdResult;
      if (!query.entityType || query.entityType === AuditEntity.OFFICE) {
        const { data, listIdResult } = await this._getDataTrendOfOutstandingCarCapByCompany(
          query,
          user,
          company.name,
          company.id,
          company.companyLevel,
          listROFId,
        );
        result.data.push(data);
      }

      const listCompany = await this.manager
        .getCustomRepository(CompanyRepository)
        .createQueryBuilder('company')
        .where('company.parentId = :companyId and company.deleted = false', {
          companyId: user.companyId,
        })
        .select(['company.name', 'company.id', 'company.companyLevel'])
        .getMany();
      for (const company of listCompany) {
        if (
          company.companyLevel === CompanyLevelEnum.EXTERNAL_COMPANY ||
          ((company.companyLevel === CompanyLevelEnum.MAIN_COMPANY ||
            company.companyLevel === CompanyLevelEnum.INTERNAL_COMPANY) &&
            (!query.entityType || query.entityType === AuditEntity.OFFICE))
        ) {
          // console.log('---------------', company.companyLevel, query.entityType);
          const { data, listIdResult } = await this._getDataTrendOfOutstandingCarCapByCompany(
            query,
            user,
            company.name,
            company.id,
            company.companyLevel,
            listROFId,
          );
          result.data.push(data);
        }
      }
      Object.assign(result, {
        total: {
          totalNumberOfOpenCar: data.numberOfOpenCar,
          totalNumberOfHoldCar: data.numberOfHoldCar,
          totalNumberOfPendingCar: data.numberOfPendingCar,
          totalNumberOfDeniedCar: data.numberOfDeniedCar,
          // listNumberOfOpenCar: data.listOfOpenCar,
          // listNumberOfHoldCar: data.listOfHoldCar,
          // listNumberOfPendingCar: data.listOfPendingCar,
          // listNumberOfDeniedCar: data.listOfDeniedCar,
        },
      });
    } else {
      const company = await this.manager
        .getCustomRepository(CompanyRepository)
        .createQueryBuilder('company')
        .where('company.id = :companyId and deleted = false', {
          companyId: user.explicitCompanyId,
        })
        .select(['company.name', 'company.id', 'company.companyLevel'])
        .getOne();
      const { data, listIdResult } = await this._getDataTrendOfOutstandingCarCapByCompany(
        query,
        user,
        company.name,
      );
      const listROFId = listIdResult;
      if (!query.entityType || query.entityType === AuditEntity.OFFICE) {
        const { data, listIdResult } = await this._getDataTrendOfOutstandingCarCapByCompany(
          query,
          user,
          company.name,
          company.id,
          company.companyLevel,
          listROFId,
        );
        if (data.numberOfHoldCar > 0 || data.numberOfOpenCar > 0 || data.numberOfPendingCar > 0) {
          result.data.push(data);
        }
      }
      // result.data.push(data);
      const listCompany = await this.manager
        .getCustomRepository(CompanyRepository)
        .createQueryBuilder('company')
        .where(
          '(company.parentId = :companyId or company.id =:companyId) and (company.id <> :explicitCompanyId) and company.deleted = false',
          {
            companyId: user.companyId,
            explicitCompanyId: user.explicitCompanyId,
          },
        )
        .select(['company.name', 'company.id', 'company.companyLevel'])
        .getMany();
      for (const company of listCompany) {
        if (
          company.companyLevel === CompanyLevelEnum.EXTERNAL_COMPANY ||
          ((company.companyLevel === CompanyLevelEnum.MAIN_COMPANY ||
            company.companyLevel === CompanyLevelEnum.INTERNAL_COMPANY) &&
            (!query.entityType || query.entityType === AuditEntity.OFFICE))
        ) {
          // console.log('---------------', company.companyLevel, query.entityType);
          const { data, listIdResult } = await this._getDataTrendOfOutstandingCarCapByCompany(
            query,
            user,
            company.name,
            company.id,
            company.companyLevel,
            listROFId,
          );
          if (
            data.numberOfHoldCar === 0 &&
            data.numberOfOpenCar === 0 &&
            data.numberOfPendingCar === 0
          ) {
            continue;
          }
          result.data.push(data);
        }
      }
      Object.assign(result, {
        total: {
          totalNumberOfOpenCar: data.numberOfOpenCar,
          totalNumberOfHoldCar: data.numberOfHoldCar,
          totalNumberOfPendingCar: data.numberOfPendingCar,
          totalNumberOfDeniedCar: data.numberOfDeniedCar,
        },
      });
      // console.log('aaaa');
    }
    return result;
  }

  async _getDataTrendOfOutstandingCarCapByCompany(
    query: TrendOfOutstandingCarCapDto,
    user: TokenPayloadModel,
    companyName: string,
    companyId?: string,
    companyLevel?: string,
    listReportFindingFormId?: string[],
  ) {
    // console.log(companyName, companyId, companyLevel);
    const queryBuilder = this.connection
      .getCustomRepository(ReportFindingFormRepository)
      .createQueryBuilder('reportFindingForm')
      // .leftJoin('reportFindingForm.rofAuditTypes', 'rofAuditTypes')
      // .leftJoin('reportFindingForm.rofPlanningRequest', 'rofPlanningRequest')
      .leftJoin('reportFindingForm.followUp', 'followUp')
      // .leftJoin('followUp.followUpComments', 'followUpComments')
      .leftJoin('reportFindingForm.planningRequest', 'planningRequest')
      .leftJoin('planningRequest.vessel', 'vessel')
      // .leftJoin('reportFindingForm.rofUsers', 'rofUsers')
      .leftJoin('planningRequest.cars', 'cars')
      .leftJoin('cars.cap', 'cap')
      .leftJoin(
        'cars.reportFindingItems',
        'reportFindingItems',
        'reportFindingItems.deleted = false',
      )
      .leftJoin('cars.cARVerification', 'cARVerification')
      .leftJoin('planningRequest.auditors', 'auditors')
      .leftJoin('planningRequest.userAssignments', 'userAssignments')
      .leftJoin('userAssignments.user', 'user')
      .select([
        'reportFindingForm.id',
        'reportFindingForm.totalFindings',
        'reportFindingForm.refNo',
        'planningRequest.id',
        'planningRequest.entityType',
        'followUp.status',
        'followUp.refId',
        'followUp.id',
        'followUp.createdAt',
        // 'rofPlanningRequest',
        // 'followUpComments',
        // 'rofUsers',
        // 'rofAuditTypes',
        'cars.id',
        'cars.status',
        'cap.planAction',
        'cars.actionRequest',
        'cars.capTargetEndDate',
        'cap.status',
        'cap.ecdCap',
        'cARVerification',
        'reportFindingItems.reference',
      ])
      .distinct(true)
      .where('(reportFindingForm.companyId = :companyId AND followUp.id IS NOT NULL)', {
        companyId: user.companyId,
      });
    if (query.fromDate) {
      queryBuilder.andWhere('followUp.createdAt >= :fromDateParam', {
        fromDateParam: new Date(query.fromDate),
      });
    }

    if (query.toDate) {
      queryBuilder.andWhere('followUp.createdAt <= :toDateParam', {
        toDateParam: new Date(query.toDate),
      });
    }

    if (query.entityType) {
      queryBuilder.andWhere('planningRequest.entityType = :entityType', {
        entityType: query.entityType,
      });
    }
    if (!RoleScopeCheck.isAdmin(user) && !companyId && !companyLevel) {
      const { whereForExternal, whereForMainAndInternal } = await _supportWhereDOCChartererOwner(
        this.manager,
        user.explicitCompanyId,
        'reportFindingForm',
        'planningRequest',
      );
      queryBuilder
        // .leftJoin('planningRequest.userAssignments', 'userAssignments')
        // .leftJoin('userAssignments.user', 'user')
        // .leftJoin('planningRequest.auditors', 'auditors')
        .leftJoin('vessel.divisionMapping', 'divisionMapping')
        .leftJoin('divisionMapping.division', 'division')
        .leftJoin('division.users', 'users')
        .leftJoin('vessel.vesselDocHolders', 'vesselDocHolders')
        .leftJoin('vessel.vesselOwners', 'vesselOwners')
        .leftJoin('vessel.vesselCharterers', 'vesselCharterers');
      await _supportCheckRoleScopeForGetList(
        this.manager,
        queryBuilder,
        user,
        whereForMainAndInternal,
        whereForExternal,
        'planningRequest',
      );
    }

    if (companyId && companyLevel) {
      if (listReportFindingFormId?.length > 0) {
        queryBuilder.andWhere('reportFindingForm.id IN (:...listReportFindingFormId)', {
          listReportFindingFormId,
        });
      } else {
        queryBuilder.andWhere('1=0');
      }
      const { whereForExternal, whereForMainAndInternal } = await _supportWhereDOCChartererOwner(
        this.manager,
        companyId,
        'reportFindingForm',
        'planningRequest',
      );
      queryBuilder
        // .leftJoin('planningRequest.userAssignments', 'userAssignments')
        // .leftJoin('userAssignments.user', 'user')
        // .leftJoin('planningRequest.auditors', 'auditors')
        .leftJoin('vessel.vesselDocHolders', 'vesselDocHolders')
        .leftJoin('vessel.vesselOwners', 'vesselOwners')
        .leftJoin('vessel.vesselCharterers', 'vesselCharterers');
      if (
        companyLevel === CompanyLevelEnum.INTERNAL_COMPANY ||
        companyLevel === CompanyLevelEnum.MAIN_COMPANY
      ) {
        queryBuilder.andWhere(
          `((planningRequest.entityType = :entityTypeVessel and ( 1=0 ` +
            whereForMainAndInternal +
            ' )) or (planningRequest.entityType = :entityTypeOffice and (planningRequest.auditCompanyId = :explicitCompanyId)))',
          {
            entityTypeVessel: AuditEntity.VESSEL,
            entityTypeOffice: AuditEntity.OFFICE,
            explicitCompanyId: companyId,
          },
        );
        if (!query.entityType) {
          queryBuilder.andWhere('planningRequest.entityType = :entityType', {
            entityType: AuditEntity.OFFICE,
          });
        }
      } else {
        // console.log('companyName', companyName);
        // console.log('whereForExternal', whereForExternal);
        queryBuilder.andWhere(
          `((planningRequest.entityType = :entityTypeVessel and ( 1=0 ` +
            whereForExternal +
            ' )) or (planningRequest.entityType = :entityTypeOffice and (planningRequest.auditCompanyId = :explicitCompanyId)))',
          {
            entityTypeVessel: AuditEntity.VESSEL,
            entityTypeOffice: AuditEntity.OFFICE,
            explicitCompanyId: companyId,
          },
        );
      }
    }
    const listIdResult = [];
    const result = await queryBuilder.getMany();
    let numberOfOpenCar = 0;
    let numberOfHoldCar = 0;
    let numberOfPendingCar = 0;
    let numberOfDeniedCar = 0;
    const listOfOpenCar = [];
    const listOfHoldCar = [];
    const listOfPendingCar = [];
    const listOfDeniedCar = [];
    for (const data of result) {
      listIdResult.push(data.id);
      for (const car of data.planningRequest.cars) {
        // console.log(car);
        if (car.cap?.status === CapStatusEnum.DENIED) {
          numberOfDeniedCar++;
          listOfDeniedCar.push({
            id: car.id,
            rofRefId: data.refNo,
            carSNo: numberOfDeniedCar,
            carReference: car.reportFindingItems[0]?.reference,
            car: car.actionRequest,
            cap: car.cap?.planAction,
            capTargetEndDate: car.capTargetEndDate,
            planningRequestId: data.planningRequest.id,
            followUpId: data.followUp?.id,
          });
        }
        if (car.cARVerification?.status === CarVerificationStatusEnum.HOLDING) {
          numberOfHoldCar++;
          listOfHoldCar.push({
            id: car.id,
            rofRefId: data.refNo,
            carSNo: numberOfHoldCar,
            carReference: car.reportFindingItems[0]?.reference,
            car: car.actionRequest,
            cap: car.cap?.planAction,
            capTargetEndDate: car.capTargetEndDate,
            planningRequestId: data.planningRequest.id,
            followUpId: data.followUp?.id,
          });
        }
        if (car.cARVerification?.status === CarVerificationStatusEnum.PENDING) {
          numberOfPendingCar++;
          listOfPendingCar.push({
            id: car.id,
            rofRefId: data.refNo,
            carSNo: numberOfPendingCar,
            carReference: car.reportFindingItems[0]?.reference,
            car: car.actionRequest,
            cap: car.cap?.planAction,
            capTargetEndDate: car.capTargetEndDate,
            planningRequestId: data.planningRequest.id,
            followUpId: data.followUp?.id,
          });
        }
        if (car.status === CarStatusEnum.OPEN) {
          numberOfOpenCar++;
          listOfOpenCar.push({
            id: car.id,
            rofRefId: data.refNo,
            carSNo: numberOfOpenCar,
            carReference: car.reportFindingItems[0]?.reference,
            car: car.actionRequest,
            cap: car.cap?.planAction,
            capTargetEndDate: car.capTargetEndDate,
            planningRequestId: data.planningRequest.id,
            followUpId: data.followUp?.id,
          });
        }
      }
    }
    return {
      data: {
        companyName,
        numberOfOpenCar,
        numberOfHoldCar,
        numberOfPendingCar,
        numberOfDeniedCar,
        listOfOpenCar,
        listOfHoldCar,
        listOfPendingCar,
        listOfDeniedCar,
      },
      listIdResult,
    };
  }

  async trendOfOutstandingCarCapDetail(
    query: TrendOfOutstandingCarCapDetailDto,
    user: TokenPayloadModel,
  ) {
    const queryBuilder = this.connection
      .getCustomRepository(ReportFindingFormRepository)
      .createQueryBuilder('reportFindingForm')
      // .leftJoin('reportFindingForm.rofAuditTypes', 'rofAuditTypes')
      // .leftJoin('reportFindingForm.rofPlanningRequest', 'rofPlanningRequest')
      .leftJoin('reportFindingForm.followUp', 'followUp')
      // .leftJoin('followUp.followUpComments', 'followUpComments')
      .leftJoin('reportFindingForm.planningRequest', 'planningRequest')
      .leftJoin('planningRequest.vessel', 'vessel')
      // .leftJoin('reportFindingForm.rofUsers', 'rofUsers')
      .leftJoin('planningRequest.cars', 'cars')
      .leftJoin('cars.cap', 'cap')
      .leftJoin(
        'cars.reportFindingItems',
        'reportFindingItems',
        'reportFindingItems.deleted = false',
      )
      .leftJoin('cars.cARVerification', 'cARVerification')
      .leftJoin('planningRequest.auditors', 'auditors')
      .leftJoin('planningRequest.userAssignments', 'userAssignments')
      .leftJoin('userAssignments.user', 'user')
      .select([
        'reportFindingForm.id',
        'reportFindingForm.totalFindings',
        'planningRequest.id',
        'planningRequest.entityType',
        'followUp.status',
        'followUp.refId',
        'followUp.id',
        'followUp.createdAt',
        // 'rofPlanningRequest',
        // 'followUpComments',
        // 'rofUsers',
        // 'rofAuditTypes',
        'cars.id',
        'reportFindingItems.id',
        'cars.actionRequest',
        'cap.planAction',
        'cap.ecdCap',
        'cap.acdCap',
        'cap.picPrevent',
        'cars.capTargetEndDate',
        'cARVerification',
      ])
      .where('(reportFindingForm.companyId = :companyId AND followUp.id IS NOT NULL)', {
        companyId: user.companyId,
      })
      .orderBy('followUp.createdAt', 'DESC');
    if (query.createdAtFrom) {
      queryBuilder.andWhere('followUp.createdAt >= :fromDateParam', {
        fromDateParam: new Date(query.createdAtFrom),
      });
    }

    if (query.createdAtTo) {
      queryBuilder.andWhere('followUp.createdAt <= :toDateParam', {
        toDateParam: new Date(query.createdAtTo),
      });
    }

    if (query.entityType) {
      queryBuilder.andWhere('planningRequest.entityType = :entityType', {
        entityType: query.entityType,
      });
    }
    if (query.statusFilter === CarCapStatusFilterEnum.OPEN) {
      queryBuilder.andWhere('cars.status = :status', {
        status: query.statusFilter,
      });
    } else if (
      query.statusFilter === CarCapStatusFilterEnum.HOLDING ||
      query.statusFilter === CarCapStatusFilterEnum.PENDING
    ) {
      queryBuilder.andWhere('cARVerification.status = :status', {
        status: query.statusFilter,
      });
    } else if (query.statusFilter === CarCapStatusFilterEnum.DENIED) {
      queryBuilder.andWhere('cap.status = :status', {
        status: query.statusFilter,
      });
    }
    if (!RoleScopeCheck.isAdmin(user)) {
      await this._buildQueryDOCChartererOwner(user, queryBuilder);
    }
    const result = await queryBuilder.getMany();
    const data = [];
    for (const temp of result) {
      for (const car of temp.planningRequest.cars) {
        data.push({
          id: temp.followUp.id,
          refId: temp.followUp.refId,
          numberOfFindings: car.reportFindingItems?.length,
          cap: car.cap?.planAction,
          car: car.actionRequest,
          capTargetEndDate: car.capTargetEndDate,
          actualClosureDate: car.cap?.acdCap,
          pic: car.cap?.picPrevent,
        });
      }
    }
    return data;
  }

  private async _buildQueryDOCChartererOwner(user, queryBuilder) {
    const { whereForExternal, whereForMainAndInternal } = await _supportWhereDOCChartererOwner(
      this.manager,
      user.explicitCompanyId,
      'reportFindingForm',
      'planningRequest',
    );
    queryBuilder
      // .leftJoin('planningRequest.userAssignments', 'userAssignments')
      // .leftJoin('userAssignments.user', 'user')
      // .leftJoin('planningRequest.auditors', 'auditors')
      .leftJoin('vessel.divisionMapping', 'divisionMapping')
      .leftJoin('divisionMapping.division', 'division')
      .leftJoin('division.users', 'users')
      .leftJoin('vessel.vesselDocHolders', 'vesselDocHolders')
      .leftJoin('vessel.vesselOwners', 'vesselOwners')
      .leftJoin('vessel.vesselCharterers', 'vesselCharterers');
    if (
      user.companyLevel === CompanyLevelEnum.EXTERNAL_COMPANY &&
      (await this.connection
        .getCustomRepository(PlanningRequestRepository)
        .checkRoleAndCompanyType(user))
    ) {
      queryBuilder.andWhere(
        `((planningRequest.entityType = :entityTypeVessel and (auditors.id = :userId or user.id = :userId)) 
          or (planningRequest.entityType = :entityTypeOffice and (auditors.id = :userId or user.id = :userId or planningRequest.auditCompanyId = :explicitCompanyId)))`,
        {
          userId: user.id,
          entityTypeVessel: AuditEntity.VESSEL,
          entityTypeOffice: AuditEntity.OFFICE,
          explicitCompanyId: user.explicitCompanyId,
        },
      );
    } else {
      if (user.companyLevel === CompanyLevelEnum.MAIN_COMPANY) {
        queryBuilder.andWhere(
          `((planningRequest.entityType = :entityTypeVessel and ( users.id = :userId ` +
            whereForMainAndInternal +
            ' )) or (planningRequest.entityType = :entityTypeOffice))',
          {
            userId: user.id,
            entityTypeVessel: AuditEntity.VESSEL,
            entityTypeOffice: AuditEntity.OFFICE,
          },
        );
      } else if (user.companyLevel === CompanyLevelEnum.INTERNAL_COMPANY) {
        queryBuilder.andWhere(
          `((planningRequest.entityType = :entityTypeVessel and ( users.id = :userId ` +
            whereForMainAndInternal +
            ' )) or (planningRequest.entityType = :entityTypeOffice and (auditors.id = :userId or user.id = :userId or planningRequest.auditCompanyId = :explicitCompanyId)))',
          {
            userId: user.id,
            entityTypeVessel: AuditEntity.VESSEL,
            entityTypeOffice: AuditEntity.OFFICE,
            explicitCompanyId: user.explicitCompanyId,
          },
        );
      } else {
        queryBuilder.andWhere(
          `((planningRequest.entityType = :entityTypeVessel and ( users.id = :userId ` +
            whereForExternal +
            ' )) or (planningRequest.entityType = :entityTypeOffice and (auditors.id = :userId or user.id = :userId or planningRequest.auditCompanyId = :explicitCompanyId)))',
          {
            userId: user.id,
            entityTypeVessel: AuditEntity.VESSEL,
            entityTypeOffice: AuditEntity.OFFICE,
            explicitCompanyId: user.explicitCompanyId,
          },
        );
      }
    }
  }

  async _supportOptimizeListFuForHomepage(queryBuilder: any, query: ListFollowUpDto) {
    const forms = await this.manager.getCustomRepository(ReportFindingFormRepository).list(
      {
        page: query.page,
        limit: query.pageSize,
      },
      {
        queryBuilder,
        sort: query.sort || 'reportFindingForm.createdAt:-1',
        advanceConditions: {
          createdAtFrom: query.createdAtFrom,
          createdAtTo: query.createdAtTo,
        },
      },
    );
    if (forms.data.length === 0) {
      return forms;
    }
    const rofIds: string[] = [];
    const planningRequestIds: string[] = [];
    const rofData = cloneDeep(forms.data);
    for (const data of forms.data) {
      rofIds.push(data.id);
      planningRequestIds.push(data.planningRequest.id);
    }
    const queryBuilderCar = this.manager
      .getCustomRepository(CARRepository)
      .createQueryBuilder('car')
      .leftJoin('car.cap', 'cap')
      .where('car.planningRequestId IN (:...planningRequestIds)', {
        planningRequestIds,
      })
      .addSelect(['car', 'cap']);
    const queryBuilderFollowUp = this.manager
      .getCustomRepository(FollowUpRepository)
      .createQueryBuilder('followUp')
      .leftJoin('followUp.followUpComments', 'followUpComments')
      .where('followUp.reportFindingFormId IN (:...rofIds)', {
        rofIds,
      })
      .select([
        'followUp.status',
        'followUp.refId',
        'followUp.id',
        'followUp.reportFindingFormId',
        'followUpComments',
      ]);
    //Promise all get related tables
    const [rofPlanningRequests, rofAuditTypes, rofUsers, rofCars, followUp] = await Promise.all([
      this.manager.find(ROFPlanningRequest, {
        where: { reportFindingFormId: In(rofIds) },
      }),
      this.manager.find(ROFAuditType, {
        where: { reportFindingFormId: In(rofIds) },
      }),
      this.manager.find(ROFUser, {
        where: { reportFindingFormId: In(rofIds) },
      }),
      queryBuilderCar.getMany(),
      queryBuilderFollowUp.getMany(),
    ]);
    // console.log('followUp', followUp);
    for (const data of rofData) {
      data.reportFindingHistories = [];
      data.rofAuditTypes = [];
      data.rofUsers = [];
      let cars = [];

      for (const rofPlanningReq of rofPlanningRequests) {
        if (data.id === rofPlanningReq.reportFindingFormId) {
          data.rofPlanningRequest = rofPlanningReq;
        }
      }

      data.rofAuditTypes = rofAuditTypes.filter(
        (rofAuditType) => data.id === rofAuditType.reportFindingFormId,
      );

      data.rofUsers = rofUsers.filter((rofUser) => data.id === rofUser.reportFindingFormId);
      const dataFilterFollowUp = followUp.filter((temp) => data.id === temp.reportFindingFormId);
      data.followUp = dataFilterFollowUp.length > 0 ? dataFilterFollowUp[0] : undefined;
      cars = rofCars.filter((rofCar) => data.planningRequest.id === rofCar.planningRequestId);
      // console.log('cars', cars);
      Object.assign(data.planningRequest, { cars });
    }
    // count number of car, cap for each ROF
    const data = [];

    for (const form of rofData) {
      const cars = form.planningRequest.cars;
      // check editable flow up
      const hiddenEdit = cars.every((car) => car.status === CarStatusEnum.CLOSED);
      const caps = form.planningRequest.cars.map((car) => car.cap);
      const totalCars = cars.length;
      const totalCloseCars = cars.filter((car) => car?.status == CarStatusEnum.CLOSED).length;
      const totalCaps = caps.length;
      const totalAcceptCaps = caps.filter((cap) => cap?.status == CapStatusEnum.ACCEPTED).length;
      const totalDeniedCaps = caps.filter((cap) => cap?.status == CapStatusEnum.DENIED).length;
      data.push({
        ...form,
        hiddenEdit,
        totalCars,
        totalCloseCars,
        totalCaps,
        totalAcceptCaps,
        totalDeniedCaps,
      });
    }

    return { ...forms, data };
  }

  buildSql(body: PayloadAGGridDto, queryBuilder, token, subQueryBuilder, fieldSelects?: string[]) {
    if (body) {
      convertFilterField(body, INSPECTION_FOLLOW_UP_FILTER_FIELDS);

      // const queryDocHolder = this.manager
      //   .createQueryBuilder(ReportFindingForm, 'reportFindingForm')
      //   .leftJoin('reportFindingForm.planningRequest', 'planningRequest')
      //   .leftJoin('planningRequest.auditCompany', 'auditCompany')
      //   .leftJoin('planningRequest.vessel', 'vessel')
      //   .leftJoin('vessel.vesselDocHolders', 'vesselDocHolders')
      //   .leftJoin('vesselDocHolders.company', 'companyVesselDocHolders')
      //   .select('reportFindingForm.id')
      //   .addSelect([
      //     `CASE WHEN "planningRequest"."entityType" = 'Office' 
      //               THEN "auditCompany"."name" ELSE STRING_AGG(DISTINCT "companyVesselDocHolders"."name", ', ') 
      //               END AS "holderCompany"`,
      //     `"auditCompany"."name" AS "auditCompany_name"`,
      //   ])
      //   .groupBy(`reportFindingForm.id, auditCompany.name, "planningRequest"."entityType"`);

      // const queryDepartment = this.manager
      //   .createQueryBuilder(ReportFindingForm, 'reportFindingForm')
      //   .leftJoin('reportFindingForm.planningRequest', 'planningRequest')
      //   .leftJoin('planningRequest.departments', 'departments')
      //   .select('reportFindingForm.id')
      //   .addSelect(`STRING_AGG(DISTINCT departments.name, ', ') AS "departmentsName"`)
      //   .groupBy(`reportFindingForm.id`);

      const queryCountCarCap = this.manager
        .createQueryBuilder(ReportFindingForm, 'reportFindingForm')
        .leftJoin('reportFindingForm.planningRequest', 'planningRequest')
        .leftJoin('planningRequest.cars', 'cars')
        .leftJoin('cars.cARVerification', 'cARVerification')
        .leftJoin('cars.cap', 'cap')
        .select(' DISTINCT reportFindingForm.id as "reportFindingForm_id"')
        .addSelect([
          `COUNT(CASE WHEN "cars"."status" = '${CarStatusEnum.CLOSED}' then 1 else null end) as "totalCloseCars"`,
          `COUNT("cars"."id") as "totalCars"`,
          `COUNT("cap"."id") as "totalCaps"`,
          `COUNT(CASE WHEN "cars"."status" = '${CarStatusEnum.OPEN}' OR "cap"."status" = '${CapStatusEnum.SUBMITTED}' then 1 else null end) as "totalPendingCaps"`,
          `COUNT(CASE WHEN "cap"."status" = '${CapStatusEnum.ACCEPTED}' then 1 else null end) as "totalAcceptCaps"`,
          `COUNT(CASE WHEN "cap"."status" = '${CapStatusEnum.DENIED}' then 1 else null end) as "totalDeniedCaps"`,
          `COUNT(CASE WHEN "cap"."status" IN ('${CapStatusEnum.DRAFT}', '${CapStatusEnum.SUBMITTED}', '${CapStatusEnum.REVIEWED_1}',
          '${CapStatusEnum.REVIEWED_2}', '${CapStatusEnum.REVIEWED_3}', '${CapStatusEnum.REVIEWED_4}', '${CapStatusEnum.REVIEWED_5}') then 1 else null end) as "totalOpenCaps"`,
          `COUNT(CASE WHEN "cARVerification"."status" IN ('${CarVerificationStatusEnum.VERIFIED_AND_CLOSE}', '${CarVerificationStatusEnum.HOLDING}', '${CarVerificationStatusEnum.OVERRIDING_CLOSURE}', '${CarVerificationStatusEnum.PENDING}') then 1 else null end) as "totalAcceptedCaps"`,
        ])
        .groupBy(`reportFindingForm.id `);

      queryBuilder
        // .innerJoin(
        //   `(${queryDocHolder.getQuery()})`,
        //   'holderCompanyGroup',
        //   `"holderCompanyGroup"."reportFindingForm_id" = "reportFindingForm"."id"`,
        // )
        // .innerJoin(
        //   `(${queryDepartment.getQuery()})`,
        //   'departmentsGroup',
        //   `"departmentsGroup"."reportFindingForm_id" = "reportFindingForm"."id"`,
        // )
        .innerJoin(
          `(${queryCountCarCap.getQuery()})`,
          'countCarCapGroup',
          `"countCarCapGroup"."reportFindingForm_id" = "reportFindingForm"."id"`,
        );

      queryBuilder
        // .select(fieldSelects)
        // .addSelect(`"departmentsGroup"."departmentsName"`)
        // .addSelect(`"holderCompanyGroup"."holderCompany"`)
        // .addSelect(`"holderCompanyGroup"."auditCompany_name"`)
        .addSelect(`"countCarCapGroup"."totalCars"`)
        .addSelect(`"countCarCapGroup"."totalCloseCars"`)
        .addSelect(`"countCarCapGroup"."totalCaps"`)
        .addSelect(`"countCarCapGroup"."totalPendingCaps"`)
        .addSelect(`"countCarCapGroup"."totalAcceptCaps"`)
        .addSelect(`"countCarCapGroup"."totalDeniedCaps"`)
        .addSelect(`"countCarCapGroup"."totalOpenCaps"`)
        .addSelect(`"countCarCapGroup"."totalAcceptedCaps"`)
        .andWhere(`reportFindingForm.deleted = false`)
        .groupBy(
          `
          "countCarCapGroup"."totalCars",
          "countCarCapGroup"."totalCloseCars",
          "countCarCapGroup"."totalCaps",
          "countCarCapGroup"."totalPendingCaps",
          "countCarCapGroup"."totalAcceptCaps",
          "countCarCapGroup"."totalDeniedCaps",
          "countCarCapGroup"."totalOpenCaps",
          "countCarCapGroup"."totalAcceptedCaps",
          ${fieldSelects.join(', ')}`,
        )
        .distinctOn(['reportFindingForm.id']);

      subQueryBuilder
        .select(`DISTINCT "reportFindingForm_id" AS id`)
        .from(`(${queryBuilder.getQuery()})`, 'subquery');

      createWhereSql(body, subQueryBuilder);
      createSelectSql(body, subQueryBuilder, null, '"reportFindingForm_id"');
    }

    queryBuilder.groupBy();
  }
}
