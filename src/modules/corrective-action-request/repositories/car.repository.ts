import { omit } from 'lodash';
import {
  <PERSON><PERSON>rror,
  <PERSON>gger<PERSON>ommon,
  TokenPayloadModel,
  TypeORMRepository,
  Utils,
} from 'svm-nest-lib-v3';
import { Connection, EntityManager, EntityRepository, In, Not } from 'typeorm';
import {
  ActionValueChangeEnum,
  CapStatusEnum,
  CarStatusEnum,
  CarVerificationStatusEnum,
  CarVerificationTypeEnum,
  FollowUpStatusEnum,
  GlobalStatusEnum,
  InternalAuditReportStatus,
  ModuleEnum,
} from '../../../commons/enums';
import { commonCheckValueChange } from '../../../commons/functions/value-change-history';
import { AuditActivityEnum, AuditModuleEnum } from '../../audit-log/audit-log.entity';
import { AuditLogRepository } from '../../audit-log/audit-log.repository';
import { ReportFindingItem } from '../../audit-workspace/entities/report-finding-item.entity';
import { SAFindingItem } from '../../audit-workspace/entities/sa-finding-items.entity';
import { PlanningRequest } from '../../planning-request/entities/planning-request.entity';
import { ListNcAndCarOfPreviousInspectionDTO } from '../../report-finding/dto/list-nc-and-car-previous-inspection.dto';
import { UpdateNcAndCarOfPreviousInspectionDTO } from '../../report-finding/dto/update-nc-and-car-previous-inspection.dto';
import { ReportFindingForm } from '../../report-finding/entities/report-finding-form.entity';
import { UserRepository } from '../../user/user.repository';
import { ValueChangeHistory } from '../../value-change-history/value-change-history.entity';
import { CreateCarDto } from '../dto/create-car.dto';
import { ListCarDto } from '../dto/list-car.dto';
import { VerificationCarDto } from '../dto/verification-car.dto';
import { CorrectiveActionPlan } from '../entities/cap.entity';
import { CarVerification } from '../entities/car-verification.entity';
import { CorrectiveActionRequest } from '../entities/car.entity';
import { CarVerificationPlanning } from '../entities/car_verification_planning.entity';
import { FollowUp } from '../entities/follow-up.entity';
import { InternalAuditReportRepository } from '../../internal-audit-report/repositories/internal-audit-report.repository';
import { FillAuditChecklistQuestion } from 'src/modules/audit-workspace/entities/fill-audit-checklist-question.entity';
import { CAPRepository } from './cap.repository';
import { decryptAttachmentValues } from 'src/commons/functions';
import { ReportFindingItemRepository } from 'src/modules/audit-workspace/repositories/report-finding-item.repository';
import { CarVerificationRepository } from './car-verification.repository';

@EntityRepository(CorrectiveActionRequest)
export class CARRepository extends TypeORMRepository<CorrectiveActionRequest> {
  constructor(private readonly connection: Connection) {
    super();
  }
  async createCar(entityParam: CreateCarDto, user: TokenPayloadModel) {
    try {
      return await this.connection.transaction(async (manager) => {
        // Check if the planning request is for self-assessment
        const planningRequest = await manager.findOne(PlanningRequest, {
          where: { id: entityParam.planningRequestId },
        });

        const carId = Utils.strings.generateUUID();
        await manager.insert(CorrectiveActionRequest, {
          id: carId,
          status: CarStatusEnum.OPEN,
          actionRequest: entityParam.actionRequest,
          capTargetPeriod: entityParam.capTargetPeriod,
          periodType: entityParam.periodType,
          capTargetEndDate: new Date(entityParam.capTargetEndDate),
          createdUserId: user.id,
          planningRequestId: entityParam.planningRequestId,
          companyId: user.companyId,
          attachments: entityParam.attachments,
          isDup: true,
        } as CorrectiveActionRequest);

        const carFound = await manager.findOne(CorrectiveActionRequest, {
          where: { id: carId },
        });
        // save field change history
        const valueChanges = commonCheckValueChange(carFound, entityParam, true);
        const userHistory = await this.manager
          .getCustomRepository(UserRepository)
          ._getUserInfoForHistory(user.id);

        const preparedValueChanges = [];
        if (valueChanges) {
          for (let i = 0; i < valueChanges.length; i++) {
            preparedValueChanges.push({
              module: ModuleEnum.CAR,
              action: ActionValueChangeEnum.CREATE,
              key: valueChanges[i].key,
              createdUser: userHistory,
              companyId: user.companyId,
              recordId: carFound.id,
              fieldLabel: valueChanges[i].fieldLabel,
              oldValue: null,
              newValue: valueChanges[i].newValue,
            });
          }
        }
        await manager.save(ValueChangeHistory, preparedValueChanges);

        // change item status to nonConformity when add car
        // const nonConformityFinding = await manager
        //   .createQueryBuilder(NatureFinding, 'natureFinding')
        //   .where('natureFinding.name = :name', {
        //     name: AppConst.NATURE_FINDING_DEFAULT.NON_CONFORMITY,
        //   })
        //   .select(['natureFinding.id'])
        //   .getOne();

        // Check if this is a self-assessment and update the appropriate entity
        if (planningRequest?.isSA) {
          // For self-assessment, update SAFindingItem
          await manager.update(
            SAFindingItem,
            { id: In(entityParam.reportFindingItemIds) },
            {
              carId,
            },
          );
        } else {
          // For normal audit, update ReportFindingItem
          await manager.update(
            ReportFindingItem,
            { id: In(entityParam.reportFindingItemIds) },
            {
              carId,
              // natureFindingId: nonConformityFinding.id,
              // natureFindingName: AppConst.NATURE_FINDING_DEFAULT.NON_CONFORMITY,
            },
          );
        }

        // const attachmentFillQuestion = await manager
        //   .getCustomRepository(ReportFindingItemRepository)
        //   .findByIds(entityParam.reportFindingItemIds);
        // const attSync = [];
        // for (const value of attachmentFillQuestion) {
        //   attSync.push(...value.findingAttachments);
        // }
        // const newAtt = Array.from(new Set(attSync));
        // newAtt.push(...carFound.attachments);

        // await manager
        //   .getCustomRepository(CARRepository)
        //   .updateCarAttachment(carId, decryptAttachmentValues(newAtt));

        await manager.update(
          FollowUp,
          { planningRequestId: entityParam.planningRequestId },
          {
            status: FollowUpStatusEnum.IN_PROGRESS,
          },
        );

        return { carId };
      });
    } catch (ex) {
      LoggerCommon.error('[CarRepository] createCar error ', ex.message || ex);
      throw ex;
    }
  }

  async listCar(query: ListCarDto, user: TokenPayloadModel) {
    const planningRequest = await this.manager.findOne(PlanningRequest, {
      where: { id: query.planningRequestId },
    });
    let queryBuilder;
    if (planningRequest?.isSA) {
      queryBuilder = this.createQueryBuilder('car')
        .leftJoinAndSelect('car.cap', 'cap')
        .leftJoin('car.cARVerification', 'cARVerification')
        .leftJoin('car.saFindingItems', 'saFindingItems', 'saFindingItems.deleted = false')
        .leftJoin('saFindingItems.auditType', 'auditType')
        .leftJoin('saFindingItems.elementMaster', 'elementMaster')
        .where('car.companyId = :companyId AND car.planningRequestId = :planningRequestId', {
          companyId: user.companyId,
          planningRequestId: query.planningRequestId,
        })
        .select()
        .addSelect([
          'saFindingItems.id',
          'elementMaster.code',
          'elementMaster.elementStageQ',
          'cARVerification.status',
          'cARVerification.isNeeded',
          'auditType.name',
        ]);
    } else {
      queryBuilder = this.createQueryBuilder('car')
        .leftJoinAndSelect('car.cap', 'cap')
        .leftJoin('car.cARVerification', 'cARVerification')
        .leftJoin(
          'car.reportFindingItems',
          'reportFindingItems',
          'reportFindingItems.deleted = false',
        )
        .leftJoin('reportFindingItems.auditType', 'auditType')
        .leftJoin('reportFindingItems.chkQuestion', 'chkQuestion')
        .where('car.companyId = :companyId AND car.planningRequestId = :planningRequestId', {
          companyId: user.companyId,
          planningRequestId: query.planningRequestId,
        })
        .select()
        .addSelect([
          'reportFindingItems.id',
          'reportFindingItems.reference',
          'chkQuestion.code',
          'cARVerification.status',
          'cARVerification.isNeeded',
          'auditType.name',
        ]);
    }
    if (query.content) {
      queryBuilder.andWhere('(car.name ILIKE :content OR car.code ILIKE :content)', {
        content: `%${query.content}%`,
      });
    }

    if (query.status) {
      queryBuilder.andWhere('car.status = :status', {
        status: query.status,
      });
    }
    const data = this.list(
      {
        page: query.page,
        limit: query.pageSize,
      },
      {
        queryBuilder,
        sort: query.sort || 'car.createdAt:-1',
      },
    );

    return data;
  }

  async getDetailCar(carId: string, user: TokenPayloadModel) {
    const isSACheck = await this.createQueryBuilder('car')
      .leftJoin('car.planningRequest', 'planningRequest')
      .where('planningRequest.isSA = true AND car.id = :carId', { carId })
      .getOne();
    let car;
    if (isSACheck) {
      car = await this.createQueryBuilder('car')
        .leftJoinAndSelect('car.cap', 'cap')
        .leftJoin('car.saFindingItems', 'saFindingItems', 'saFindingItems.deleted = false')
        .leftJoin('saFindingItems.auditType', 'auditType')
        .leftJoin('saFindingItems.elementMaster', 'elementMaster')
        .leftJoinAndSelect('cap.capComments', 'capComments')
        .leftJoinAndSelect('cap.capHistories', 'capHistories')
        .leftJoinAndSelect('car.cARVerification', 'cARVerification')
        .where('car.companyId = :companyId AND car.id = :carId', {
          companyId: user.companyId,
          carId,
        })
        .select()
        .addSelect([
          'saFindingItems.id',
          'saFindingItems.findingRemark',
          'saFindingItems.findingComment',
          'saFindingItems.natureFindingName',
          'elementMaster.code',
          'elementMaster.elementStageQ',
          'auditType.name',
        ])
        .getOne();
    } else {
      car = await this.getOneQB(
        this.createQueryBuilder('car')
          .leftJoinAndSelect('car.cap', 'cap')
          .leftJoin(
            'car.reportFindingItems',
            'reportFindingItems',
            'reportFindingItems.deleted = false',
          )
          .leftJoin('reportFindingItems.auditType', 'auditType')
          .leftJoin('reportFindingItems.chkQuestion', 'chkQuestion')
          .leftJoinAndSelect('cap.capComments', 'capComments')
          .leftJoinAndSelect('cap.capHistories', 'capHistories')
          .leftJoinAndSelect('car.cARVerification', 'cARVerification')
          .select()
          .addSelect([
            'reportFindingItems.id',
            'reportFindingItems.reference',
            'reportFindingItems.natureFindingName',
            'reportFindingItems.findingComment',
            'reportFindingItems.findingRemark',
            'auditType.name',
            'chkQuestion.code',
          ])
          .where('car.companyId = :companyId AND car.id = :carId', {
            companyId: user.companyId,
            carId,
          }),
      );
    }

    if (car) {
      return car;
    } else {
      throw new BaseError({ status: 404, message: 'car.NOT_FOUND' });
    }
  }

  async updateCar(
    id: string,
    deletedCarIdOfROFItemList: string[],
    updatedCarIdOfROFItemList: string[],
    car: CorrectiveActionRequest,
    token: TokenPayloadModel,
  ) {
    try {
      // check can update car by status of cap
      const capFound = await this.manager
        .createQueryBuilder(CorrectiveActionPlan, 'cap')
        .where('cap.carId = :id', { id })
        .select(['cap.id', 'cap.status'])
        .getOne();

      if (!!capFound && capFound.status != CapStatusEnum.DRAFT) {
        throw new BaseError({ message: 'car.CAN_NOT_UPDATE' });
      }
      await this.connection.transaction(async (manager) => {
        const carFound = await manager.findOne(CorrectiveActionRequest, {
          where: { id: car.id },
        });
        // save field change history
        const valueChanges = commonCheckValueChange(carFound, car);
        const userHistory = await manager
          .getCustomRepository(UserRepository)
          ._getUserInfoForHistory(token.id);

        const preparedValueChanges = [];
        if (valueChanges) {
          for (let i = 0; i < valueChanges.length; i++) {
            preparedValueChanges.push({
              module: ModuleEnum.CAR,
              action: ActionValueChangeEnum.SUBMIT,
              createdUser: userHistory,
              key: valueChanges[i].key,
              companyId: token.companyId,
              recordId: car.id,
              fieldLabel: valueChanges[i].fieldLabel,
              oldValue: valueChanges[i].oldValue,
              newValue: valueChanges[i].newValue,
            });
          }
        }
        await manager.save(ValueChangeHistory, preparedValueChanges);
        await manager.save(CorrectiveActionRequest, car);

        if (deletedCarIdOfROFItemList.length > 0) {
          await manager.update(
            ReportFindingItem,
            { id: In(deletedCarIdOfROFItemList) },
            { carId: null },
          );
        }

        if (updatedCarIdOfROFItemList.length > 0) {
          await manager.update(
            ReportFindingItem,
            { id: In(updatedCarIdOfROFItemList) },
            { carId: car.id },
          );

          // const attachmentFillQuestion = await manager
          //   .getCustomRepository(ReportFindingItemRepository)
          //   .findByIds(updatedCarIdOfROFItemList);
          // const attSync = [];
          // for (const value of attachmentFillQuestion) {
          //   attSync.push(...value.findingAttachments);
          // }
          // const newAtt = Array.from(new Set(attSync));
          // newAtt.push(...carFound.attachments);
          // await manager.getCustomRepository(CARRepository).updateCarAttachment(car.id, newAtt);
        }
      });
    } catch (ex) {
      LoggerCommon.error('[CarRepository] updateCar error ', ex.message || ex);
      throw ex;
    }
  }
  async deleteCar(id: string, user: TokenPayloadModel) {
    try {
      // check can deleted car by status of cap
      const capFound = await this.manager
        .createQueryBuilder(CorrectiveActionPlan, 'cap')
        .where('cap.carId = :id', { id })
        .select(['cap.id', 'cap.status'])
        .getOne();

      if (!!capFound && capFound.status != CapStatusEnum.DRAFT) {
        throw new BaseError({ message: 'cap.CAN_NOT_DELETE' });
      }

      const res = await this.delete({
        id,
        companyId: user.companyId,
      });
      // to delete CAR also in backup checklist table
      await this.manager.update(FillAuditChecklistQuestion, { carId: id }, { carId: null });
      if (res.affected === 0) {
        throw new BaseError({ message: 'car.NOT_FOUND' });
      }
      return 1;
    } catch (ex) {
      LoggerCommon.error('[CarRepository] deleteCar error ', ex.message || ex);
      throw ex;
    }
  }

  async verificationCar(
    token: TokenPayloadModel,
    carId: string,
    body: VerificationCarDto,
    workflowSteps?: any[],
  ) {
    try {
      return await this.connection.transaction(async (manager) => {
        const allSteps = [];
        workflowSteps
          .map((wf) => wf.wfrPermission)
          .filter((wf) => wf !== 'reviewer')
          .forEach((step) => {
            if (step && step.startsWith('reviewer')) {
              allSteps.push(Number(step.slice(-1)));
            }
          });
        const getCar = await this.manager
          .createQueryBuilder(CorrectiveActionRequest, 'car')
          .where('car.id = :id ', {
            id: carId,
          })
          .getOne();
        let getCars = await this.manager
          .createQueryBuilder(CorrectiveActionRequest, 'car')
          .leftJoinAndSelect('car.cARVerification', 'cARVerification')
          .where('car.planningRequestId = :planningRequestId ', {
            planningRequestId: getCar?.planningRequestId,
          })
          .getMany();
        const capFound = await manager
          .createQueryBuilder(CorrectiveActionPlan, 'cap')
          .leftJoin('cap.car', 'car')
          .where('cap.carId =:carId', { carId })
          .select(['cap.id', 'cap.status'])
          .addSelect(['car.id', 'car.planningRequestId'])
          .getOne();
        const iarInfo = await manager
          .getCustomRepository(InternalAuditReportRepository)
          .createQueryBuilder('iar')
          .leftJoin('iar.planningRequest', 'planningRequest')
          .select(['iar.id', 'iar.status', 'planningRequest.globalStatus'])
          .where('iar.planningRequestId = :id', { id: capFound.car.planningRequestId })
          .getOne();
        if (iarInfo?.status === InternalAuditReportStatus.APPROVED) {
          getCars = getCars?.filter((car) => car?.status != CarStatusEnum.OPEN);
        }
        // let capLength = false;
        let isNeeded = false;
        let approvedVerification = false;
        let approvedVerificationHelper = false;
        let isNeededFalseCount = 0;
        let isNeededCount = 0;
        let isCarVerificationStatus = false;
        let verifyAndcloseCount = 0;
        let carVerificationCount = 0;
        let isDenied = false;
        if (
          body?.status == CapStatusEnum.DRAFT ||
          body?.status == CapStatusEnum.SUBMITTED ||
          body?.status == CapStatusEnum.REVIEWED_1 ||
          body?.status == CapStatusEnum.REVIEWED_2 ||
          body?.status == CapStatusEnum.REVIEWED_3 ||
          body?.status == CapStatusEnum.REVIEWED_4 ||
          body?.status == CapStatusEnum.REVIEWED_5 ||
          body?.status == CapStatusEnum.DENIED
        ) {
          isNeeded = true;
        }
        if (
          body?.status === CarVerificationStatusEnum.PENDING ||
          body?.status === CarVerificationStatusEnum.HOLDING ||
          body?.status === CarVerificationStatusEnum.OVERRIDING_CLOSURE ||
          body?.status === CarVerificationStatusEnum.VERIFIED_AND_CLOSE
        ) {
          carVerificationCount++;
        }
        if (body?.isNeeded) {
          isNeededCount++;
        }
        if (body?.isNeeded === false) {
          isNeededFalseCount++;
        }
        if (
          body?.status === CarVerificationStatusEnum.PENDING ||
          body?.status === CarVerificationStatusEnum.HOLDING ||
          body?.status === CarVerificationStatusEnum.OVERRIDING_CLOSURE
        ) {
          isCarVerificationStatus = true;
        }
        if (body?.status === CarVerificationStatusEnum.VERIFIED_AND_CLOSE) {
          verifyAndcloseCount++;
        }
        for (const item of getCars) {
          const getCap = await this.manager
            .createQueryBuilder(CorrectiveActionPlan, 'cap')
            .where('cap.carId = :carId and cap.status IN (:...status)', {
              carId: item?.id,
              status: [
                CapStatusEnum.DRAFT,
                CapStatusEnum.SUBMITTED,
                CapStatusEnum.REVIEWED_1,
                CapStatusEnum.REVIEWED_2,
                CapStatusEnum.REVIEWED_3,
                CapStatusEnum.REVIEWED_4,
                CapStatusEnum.REVIEWED_5,
                CapStatusEnum.DENIED,
              ],
            })
            .getOne();
          if (getCap?.status === CapStatusEnum.DENIED) {
            isDenied = true;
            carVerificationCount++;
          }
          if (item?.cARVerification?.isNeeded === false && carId != item?.id) {
            isNeededFalseCount++;
          }
          if (
            (getCap?.status == CapStatusEnum.DRAFT ||
              getCap?.status == CapStatusEnum.SUBMITTED ||
              getCap?.status == CapStatusEnum.REVIEWED_1 ||
              getCap?.status == CapStatusEnum.REVIEWED_2 ||
              getCap?.status == CapStatusEnum.REVIEWED_3 ||
              getCap?.status == CapStatusEnum.REVIEWED_4 ||
              getCap?.status == CapStatusEnum.REVIEWED_5 ||
              getCap?.status == CapStatusEnum.DENIED) &&
            item?.cARVerification?.status != CarVerificationStatusEnum.VERIFIED_AND_CLOSE &&
            carId != item?.id
          ) {
            isNeeded = true;
          } else if (
            body?.status === CarVerificationStatusEnum.VERIFIED_AND_CLOSE &&
            body?.isNeeded &&
            carId == item?.id
          ) {
            approvedVerificationHelper = true;
            approvedVerification = true;
          }
          if (
            (item?.cARVerification?.status == CarVerificationStatusEnum.PENDING ||
              item?.cARVerification?.status == CarVerificationStatusEnum.HOLDING ||
              item?.cARVerification?.status == CarVerificationStatusEnum.OVERRIDING_CLOSURE ||
              item?.cARVerification?.status === CarVerificationStatusEnum.VERIFIED_AND_CLOSE) &&
            carId != item?.id
          ) {
            carVerificationCount++;
          }
          if (
            item?.cARVerification?.isNeeded &&
            item?.cARVerification != null &&
            carId != item?.id
          ) {
            // isNeeded = true;
            isNeededCount++;
            if (
              item?.cARVerification?.status == CarVerificationStatusEnum.PENDING ||
              item?.cARVerification?.status == CarVerificationStatusEnum.HOLDING ||
              item?.cARVerification?.status == CarVerificationStatusEnum.OVERRIDING_CLOSURE
            ) {
              isCarVerificationStatus = true;
            }
            if (item?.cARVerification?.status == CarVerificationStatusEnum.VERIFIED_AND_CLOSE) {
              verifyAndcloseCount++;
              approvedVerification = true;
              approvedVerificationHelper = true;
            }
          }
        }

        if (!capFound || capFound.status != `reviewed_${allSteps.slice(-1)[0]}`) {
          throw new BaseError({ status: 404, message: 'cap.REQUIRED_LAST_STEP' });
        }

        await this._checkCanCloseOutFollowUpAndCloseCAR(
          manager,
          capFound.car.planningRequestId,
          carId,
          body,
          token.id,
          false,
          iarInfo?.status,
        );

        if (getCars?.length === carVerificationCount && isDenied) {
          if (
            iarInfo.status === InternalAuditReportStatus.APPROVED &&
            this.manager
              .getCustomRepository(CAPRepository)
              ._checkNeedUpdateGlobalStatus(
                iarInfo.planningRequest.globalStatus,
                GlobalStatusEnum.DISAPPROVED_CAP,
              )
          ) {
            await manager.update(
              PlanningRequest,
              {
                id: capFound.car.planningRequestId,
              },
              {
                globalStatus: GlobalStatusEnum.DISAPPROVED_CAP,
              },
            );
          }
        } else if (
          iarInfo.status === InternalAuditReportStatus.APPROVED &&
          isCarVerificationStatus &&
          isNeededCount === getCars?.length
        ) {
          if (
            this.manager
              .getCustomRepository(CAPRepository)
              ._checkNeedUpdateGlobalStatus(
                iarInfo.planningRequest.globalStatus,
                GlobalStatusEnum.WAITING_VERIFICATION,
              )
          ) {
            await manager.update(
              PlanningRequest,
              {
                id: capFound.car.planningRequestId,
              },
              {
                globalStatus: GlobalStatusEnum.WAITING_VERIFICATION,
              },
            );
          }
        } else if (
          iarInfo.status === InternalAuditReportStatus.APPROVED &&
          isNeededFalseCount === getCars?.length
        ) {
          if (
            this.manager
              .getCustomRepository(CAPRepository)
              ._checkNeedUpdateGlobalStatus(
                iarInfo.planningRequest.globalStatus,
                GlobalStatusEnum.APPROVED_CAP_NO_VERIFICATION,
              )
          ) {
            await manager.update(
              PlanningRequest,
              {
                id: capFound.car.planningRequestId,
              },
              {
                globalStatus: GlobalStatusEnum.APPROVED_CAP_NO_VERIFICATION,
              },
            );
          }
        } else if (approvedVerification && approvedVerificationHelper && !isNeeded) {
          if (
            this.manager
              .getCustomRepository(CAPRepository)
              ._checkNeedUpdateGlobalStatus(
                iarInfo.planningRequest.globalStatus,
                GlobalStatusEnum.APPROVED_VERIFICATION,
              )
          ) {
            await manager.update(
              PlanningRequest,
              {
                id: capFound.car.planningRequestId,
              },
              {
                globalStatus: GlobalStatusEnum.APPROVED_VERIFICATION,
              },
            );
          }
        }

        // if (!capLength || isNeeded) {
        //   if (!body.isNeeded) {
        //     // Update global status
        //     // Update only in case !(in car_verification have isNeeded = true)
        //     const isExistAnyNeededVerification = await this._checkExistAnyNeededVerification(
        //       manager,
        //       capFound.car.planningRequestId,
        //     );
        //     if (
        //       !isExistAnyNeededVerification &&
        //       iarInfo.status === InternalAuditReportStatus.APPROVED
        //     ) {
        //       if (
        //         this.manager
        //           .getCustomRepository(CAPRepository)
        //           ._checkNeedUpdateGlobalStatus(
        //             iarInfo.planningRequest.globalStatus,
        //             GlobalStatusEnum.APPROVED_CAP_NO_VERIFICATION,
        //           ) &&
        //         !capLength
        //       ) {
        //         console.log('APPROVED_CAP_NO_VERIFICATION');
        //         await manager.update(
        //           PlanningRequest,
        //           {
        //             id: capFound.car.planningRequestId,
        //           },
        //           {
        //             globalStatus: GlobalStatusEnum.APPROVED_CAP_NO_VERIFICATION,
        //           },
        //         );
        //       }
        //     }
        //   } else {
        //     if (
        //       (body.status === CarVerificationStatusEnum.PENDING ||
        //         body.status === CarVerificationStatusEnum.HOLDING ||
        //         body.status === CarVerificationStatusEnum.OVERRIDING_CLOSURE) &&
        //       iarInfo.status === InternalAuditReportStatus.APPROVED &&
        //       isCarVerificationStatus &&
        //       this.manager
        //         .getCustomRepository(CAPRepository)
        //         ._checkNeedUpdateGlobalStatus(
        //           iarInfo.planningRequest.globalStatus,
        //           GlobalStatusEnum.WAITING_VERIFICATION,
        //         )
        //     ) {
        //       // Update global status
        //       console.log('WAITING_VERIFICATION');
        //       await manager.update(
        //         PlanningRequest,
        //         {
        //           id: capFound.car.planningRequestId,
        //         },
        //         {
        //           globalStatus: GlobalStatusEnum.WAITING_VERIFICATION,
        //         },
        //       );
        //     } else if (
        //       iarInfo.status === InternalAuditReportStatus.APPROVED &&
        //       verifyAndclose === getCar?.length
        //     ) {
        //       // Update global status
        //       const isAllVerificationIsDoneExcept = await this._checkAllVerificationIsDoneExcept(
        //         manager,
        //         capFound.car.planningRequestId,
        //         carId,
        //       );
        //       if (
        //         isAllVerificationIsDoneExcept &&
        //         this.manager
        //           .getCustomRepository(CAPRepository)
        //           ._checkNeedUpdateGlobalStatus(
        //             iarInfo.planningRequest.globalStatus,
        //             GlobalStatusEnum.APPROVED_VERIFICATION,
        //           )
        //       ) {
        //         console.log('APPROVED_VERIFICATION');
        //         await manager.update(
        //           PlanningRequest,
        //           {
        //             id: capFound.car.planningRequestId,
        //           },
        //           {
        //             globalStatus: GlobalStatusEnum.APPROVED_VERIFICATION,
        //           },
        //         );
        //       }
        //       else {
        //         if (
        //           this.manager
        //             .getCustomRepository(CAPRepository)
        //             ._checkNeedUpdateGlobalStatus(
        //               iarInfo.planningRequest.globalStatus,
        //               GlobalStatusEnum.WAITING_VERIFICATION,
        //             )
        //         ) {
        //           // Update global status
        //           await manager.update(
        //             PlanningRequest,
        //             {
        //               id: capFound.car.planningRequestId,
        //             },
        //             {
        //               globalStatus: GlobalStatusEnum.WAITING_VERIFICATION,
        //             },
        //           );
        //         }
        //       }
        //     }
        //   }
        // }
        const getCarDetails = await this.manager
          .getCustomRepository(CarVerificationRepository)
          .createQueryBuilder('carVerification')
          .where('carVerification.carId =:carId', { carId })
          .getOne();

        const preparedCarVerification = {
          isNeeded: body.isNeeded,
          reason: body.reason,
          type: body.types,
          verifiedDate: new Date(body.verifiedDate),
          verifiedById: body.verifiedById,
          carId,
          status: body.status,
          attachments: body.attachments,
          companyId: token.companyId,
          createdUserId: token.id,
        } as CarVerification;

        if (getCarDetails?.id) {
          preparedCarVerification.id = getCarDetails?.id;
        }

        const carVerification = await manager.save(
          CarVerification,
          preparedCarVerification as CarVerification,
        );

        // save field change history
        const valueChanges = commonCheckValueChange(
          omit(carVerification, ['verifiedById']),
          body,
          true,
        );

        if (body.isNeeded && body.types.length > 0) {
          valueChanges.push({ key: 'types', fieldLabel: 'Types', newValue: body.types.toString() });
        }

        const userHistory = await this.manager
          .getCustomRepository(UserRepository)
          ._getUserInfoForHistory(token.id);

        if (body.verifiedById) {
          const verifiedByUser = await this.manager
            .getCustomRepository(UserRepository)
            ._getUserInfoForHistory(body.verifiedById);
          valueChanges.push({
            key: 'verifiedBy',
            fieldLabel: 'Verified By',
            newValue: verifiedByUser.username,
          });
        }

        const preparedValueChanges = [];
        if (valueChanges) {
          for (let i = 0; i < valueChanges.length; i++) {
            preparedValueChanges.push({
              module: ModuleEnum.CAR,
              action: ActionValueChangeEnum.CREATE,
              key: valueChanges[i].key,
              createdUser: userHistory,
              companyId: token.companyId,
              recordId: carId,
              fieldLabel: valueChanges[i].fieldLabel,
              oldValue: valueChanges[i].fieldLabel === 'Status' ? capFound.status : null,
              newValue: valueChanges[i].newValue,
            });
          }
        }
        await manager.save(ValueChangeHistory, preparedValueChanges);
        // end

        return carVerification;
      });
    } catch (ex) {
      LoggerCommon.error('[CarRepository] verificationCar error ', ex.message || ex);
      throw ex;
    }
  }

  async listNcPreviousCar(
    formId: string,
    query: ListNcAndCarOfPreviousInspectionDTO,
    user: TokenPayloadModel,
  ) {
    try {
      const pr = await this.manager
        .createQueryBuilder(PlanningRequest, 'pr')
        .where('pr.id = :id', { id: query.planningRequestId })
        .select(['pr.id', 'pr.vesselId', 'pr.departmentId', 'pr.auditCompanyId'])
        .getOne();

      if (!pr) {
        throw new BaseError({ status: 404, message: 'planningRequest.NOT_FOUND' });
      }

      const queryBuilder = this.createQueryBuilder('car')
        .innerJoin(
          'car.cARVerification',
          'cARVerification',
          'cARVerification.status IN (:...verifiedStatus) AND cARVerification.isNeeded = :cARVerification AND (cARVerification.type && :types = TRUE)',
          {
            verifiedStatus: [CarVerificationStatusEnum.PENDING, CarVerificationStatusEnum.HOLDING],
            types: [
              CarVerificationTypeEnum.ADDITIONAL_INSPECTION,
              CarVerificationTypeEnum.NEXT_INSPECTION,
            ],
            cARVerification: true,
          },
        )
        .innerJoin('car.planningRequest', 'planningRequest')
        .leftJoin('car.cap', 'cap')
        .innerJoin(
          'car.reportFindingItems',
          'reportFindingItems',
          'reportFindingItems.deleted = false',
        )
        .leftJoin('planningRequest.internalAuditReport', 'internalAuditReport')
        .leftJoin('planningRequest.reportFindingForm', 'reportFindingForm')
        .leftJoin('planningRequest.followUp', 'followUp')
        .where('(internalAuditReport.status IN (:...status) AND reportFindingForm.id != :formId)', {
          status: [
            InternalAuditReportStatus.SUBMITTED,
            InternalAuditReportStatus.REVIEWED_1,
            InternalAuditReportStatus.REVIEWED_2,
            InternalAuditReportStatus.REVIEWED_3,
            InternalAuditReportStatus.REVIEWED_4,
            InternalAuditReportStatus.REVIEWED_5,
            InternalAuditReportStatus.APPROVED,
          ],
          formId,
        })
        .select([
          'car.id',
          'car.actionRequest',
          'car.createdAt',
          'planningRequest.id',
          'followUp.refId',
          'followUp.id',
          'cap.planAction',
          'cARVerification.status',
          'cARVerification.reason',
          'cARVerification.type',
          'reportFindingItems.id',
          'planningRequest.vesselId',
          'planningRequest.departmentId',
        ]);

      if (pr.vesselId) {
        queryBuilder.andWhere('(planningRequest.vesselId = :vesselId)', {
          vesselId: pr.vesselId,
        });
      }

      if (pr.departmentId) {
        queryBuilder.andWhere('planningRequest.departmentId = :departmentId', {
          departmentId: pr.departmentId,
        });
      }
      if (!pr.departmentId && !pr.vesselId) {
        queryBuilder.andWhere('planningRequest.auditCompanyId = :auditCompanyId', {
          auditCompanyId: pr.auditCompanyId,
        });
      }

      return await this.list(
        {
          page: query.page,
          limit: query.pageSize,
        },
        {
          queryBuilder,
          sort: query.sort || 'car.createdAt:-1',
          advanceConditions: {
            createdAtFrom: query.createdAtFrom,
            createdAtTo: query.createdAtTo,
          },
        },
      );
    } catch (ex) {
      LoggerCommon.error('[CarRepository] listCarOfPreviousInspection error ', ex.message || ex);
      throw ex;
    }
  }

  private async _checkExistAnyNeededVerification(manager: EntityManager, planningId: string) {
    const anyNeededVerification = await manager
      .createQueryBuilder(CarVerification, 'verification')
      .innerJoin('verification.car', 'car')
      .where('car.planningRequestId = :planningId', { planningId })
      .andWhere('verification.isNeeded IS TRUE')
      .select(['verification.id'])
      .getOne();

    return !!anyNeededVerification;
  }

  private async _checkAllVerificationIsDoneExcept(
    manager: EntityManager,
    planningId: string,
    carId: string,
  ) {
    const cars = await manager
      .createQueryBuilder(CorrectiveActionRequest, 'car')
      .leftJoin('car.cARVerification', 'cARVerification')
      .where('car.planningRequestId = :planningId AND car.id != :carId', { planningId, carId })
      .select([
        'car.id',
        'cARVerification.id',
        'cARVerification.isNeeded',
        'cARVerification.status',
      ])
      .getMany();

    return cars.every((item) => {
      return (
        item.cARVerification &&
        item.cARVerification.isNeeded &&
        (item.cARVerification.status === CarVerificationStatusEnum.OVERRIDING_CLOSURE ||
          item.cARVerification.status === CarVerificationStatusEnum.VERIFIED_AND_CLOSE)
      );
    });
  }

  async _checkCanCloseOutFollowUpAndCloseCAR(
    manager: EntityManager,
    planningRequestId: string,
    carId: string,
    body: VerificationCarDto,
    userId: string,
    updated?: boolean,
    iarStatus?: string,
  ) {
    if (
      body.status == CarVerificationStatusEnum.VERIFIED_AND_CLOSE ||
      body.status == CarVerificationStatusEnum.OVERRIDING_CLOSURE
    ) {
      await manager.update(
        CorrectiveActionRequest,
        { id: carId },
        { status: CarStatusEnum.CLOSED },
      );

      let cars = await manager
        .createQueryBuilder(CorrectiveActionRequest, 'car')
        .leftJoinAndSelect('car.cap', 'cap')
        .leftJoinAndSelect('car.cARVerification', 'cARVerification')
        .where(`car.planningRequestId = :id`, {
          id: planningRequestId,
        })
        .select()
        .getMany();
      if (iarStatus === InternalAuditReportStatus.APPROVED) {
        cars = cars?.filter((car) => car?.status != CarStatusEnum.OPEN);
      }
      const totalCars = cars.length;
      // console.log('totalCars', totalCars);
      const totalsCloseCars = cars.filter(
        (car) =>
          car.cARVerification?.status == CarVerificationStatusEnum.VERIFIED_AND_CLOSE ||
          car.cARVerification?.status == CarVerificationStatusEnum.OVERRIDING_CLOSURE,
      ).length;
      // console.log('totalCars', totalsCloseCars);
      // console.log('updated', updated);
      // check if all  cars verified, (totalsCloseCars + 1 next closeCar == totalCars)
      if (
        (totalCars == totalsCloseCars && updated === true) ||
        (totalCars == totalsCloseCars + 1 && !updated) ||
        (totalCars == totalsCloseCars && iarStatus === InternalAuditReportStatus.APPROVED)
      ) {
        await manager.update(
          FollowUp,
          { planningRequestId },
          { status: FollowUpStatusEnum.CLOSE_OUT },
        );

        // Handle add Audit log
        await manager.getCustomRepository(AuditLogRepository).createAuditLog(
          {
            module: AuditModuleEnum.INSPECTION_FOLLOW_UP,
            planningId: planningRequestId,
          },
          { id: userId } as TokenPayloadModel,
          [AuditActivityEnum.CLOSE_OUT],
          null,
        );
      }
    }
    // else {
    //   // update follow up status when create CAP
    //   const updateFollowUpRs = await manager.update(
    //     FollowUp,
    //     { planningRequestId, status: Not(FollowUpStatusEnum.IN_PROGRESS) },
    //     { status: FollowUpStatusEnum.IN_PROGRESS },
    //   );

    //   if (updateFollowUpRs.affected > 0) {
    //     // Handle add Audit log
    //     await manager.getCustomRepository(AuditLogRepository).createAuditLog(
    //       {
    //         module: AuditModuleEnum.INSPECTION_FOLLOW_UP,
    //         planningId: planningRequestId,
    //       },
    //       { id: userId } as TokenPayloadModel,
    //       [AuditActivityEnum.IN_PROGRESS],
    //       null,
    //     );
    //   }
    // }

    // if (body.status == CarVerificationStatusEnum.OVERRIDING_CLOSURE) {
    //   await manager.update(
    //     CorrectiveActionRequest,
    //     { id: carId },
    //     { status: CarStatusEnum.CLOSED },
    //   );
    // }
  }

  async updateCarAttachment(carId, attachments: string[]) {
    if (attachments.length) {
      const rawQuery = `
      UPDATE corrective_action_request 
      SET "attachments" = '{${attachments}}'
      WHERE id = '${carId}'
      `;

      await this.manager.query(rawQuery);
    }
  }

  async updateNcPreviousCar(
    formId: string,
    body: UpdateNcAndCarOfPreviousInspectionDTO,
    user: TokenPayloadModel,
  ) {
    try {
      await this.manager.transaction(async (manager) => {
        const formFound = await manager.findOne(ReportFindingForm, { id: formId });
        const carVerification = await manager.findOne(CarVerification, { carId: body.carId });
        const car = await manager.findOne(CorrectiveActionRequest, { id: body.carId });
        await manager.update(
          CarVerification,
          { carId: body.carId },
          { status: body.status, reason: body.reason },
        );

        const verificationPlanningFound = await manager.findOne(CarVerificationPlanning, {
          carVerificationId: carVerification.id,
          planningRequestId: formFound.planningRequestId,
        });

        if (!verificationPlanningFound) {
          await manager.insert(CarVerificationPlanning, {
            carVerificationId: carVerification.id,
            planningRequestId: formFound.planningRequestId,
            createdUserId: user.id,
          });
        }
        // console.log('formFound.planningRequestId', formFound.planningRequestId);
        if (
          body.status == CarVerificationStatusEnum.OVERRIDING_CLOSURE ||
          body.status == CarVerificationStatusEnum.VERIFIED_AND_CLOSE
        ) {
          await this._checkCanCloseOutFollowUpAndCloseCAR(
            manager,
            car.planningRequestId,
            body.carId,
            { status: body.status } as VerificationCarDto,
            user.id,
            true,
          );
        }
      });
    } catch (ex) {
      LoggerCommon.error(
        '[CARRepository] updateNcAndCarOfPreviousInspection error ',
        ex.message || ex,
      );
      throw ex;
    }
  }
}
