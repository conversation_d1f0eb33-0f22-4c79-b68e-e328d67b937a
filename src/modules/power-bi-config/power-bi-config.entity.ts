import { IdentifyEntity } from 'svm-nest-lib-v3';
import { Column, Entity, Index, ManyToOne } from 'typeorm';
import { WidgetModuleEnum } from '../../commons/enums';
import { Company } from '../company/company.entity';

@Entity()
export class PowerBIConfig extends IdentifyEntity {
  @Column({ type: 'enum', enum: WidgetModuleEnum })
  public module: string;

  @Column()
  public url: string;

  @Column({ type: 'uuid', nullable: true })
  public companyId: string;

  @Column({ type: 'bool', default: false })
  public isDefault: boolean;

  @Column({ type: 'bool', default: false })
  public isPage: boolean;

  @Column({ type: 'smallint', nullable: true })
  public pageOrder: number;

  @Column({ nullable: true })
  public pageName: string;

  // New fields for PowerBI Embedded support
  @Column({ type: 'bool', default: false })
  public isEmbedded: boolean;

  @Column({ nullable: true })
  public workspaceId: string;

  @Column({ nullable: true })
  public reportId: string;


  @Column({ nullable: true })
  public clientId: string;

  @Column({ nullable: true })
  public tenantId: string;

  @Column({ nullable: true })
  public clientSecret: string;

  @Column({ nullable: true })
  public scope: string;

  @Column({ type: 'json', nullable: true })
  public embedSettings: {
    filterPaneEnabled?: boolean;
    navContentPaneEnabled?: boolean;
    allowSaveAs?: boolean;
    background?: string;
    layoutType?: string;
    permissions?: string[];
  };

  @Column({ type: 'json', nullable: true })
  public metadata: {
    description?: string;
    tags?: string[];
    lastUpdated?: Date;
    version?: string;
  };

  // Cached token fields for PowerBI Embedded
  @Column({ nullable: true })
  public accessToken: string;

  @Column({ nullable: true })
  public embedUrl: string;

  @Column({ type: 'timestamp', nullable: true })
  public tokenExpiration: Date;

  @ManyToOne(() => Company, { onDelete: 'CASCADE' })
  company: Company;
}
