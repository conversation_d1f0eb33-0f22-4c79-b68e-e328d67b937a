import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { TokenPayloadModel } from 'svm-nest-lib-v3';
import { ListPowerBIConfigDto } from './dto/list-power-bi-config.dto';
import { PowerBIConfigRepository } from './power-bi-config.repository';
import { UpdatePowerBIConfigDto } from './dto/update-power-bi-config.dto';
import { DetailPowerBIConfigDto } from './dto/detail-power-bi-config.dto';
import { PowerBIConfig } from './power-bi-config.entity';
import {
  PowerBIConfigResponseDto,
  UnifiedPowerBIResponseDto,
} from './dto/power-bi-config-response.dto';
import { PowerBiEmbeddedService } from '../power-bi-embedded/power-bi-embedded.service';

@Injectable()
export class PowerBIConfigService {
  private readonly logger = new Logger(PowerBIConfigService.name);

  constructor(
    private readonly powerBIConfigRepository: PowerBIConfigRepository,
    private readonly powerBiEmbeddedService: PowerBiEmbeddedService,
  ) {
    this.powerBIConfigRepository._initDefaultConfigs();
  }

  async listPowerBIConfig(user: TokenPayloadModel, query: ListPowerBIConfigDto) {
    return await this.powerBIConfigRepository.listPowerBIConfig(user, query);
  }

  async updatePowerBIConfig(user: TokenPayloadModel, id: string, body: UpdatePowerBIConfigDto) {
    // Validate embedded configuration
    if (body.isEmbedded) {
      await this.validateEmbeddedConfig(body);
    }

    // Set metadata update timestamp
    if (body.metadata) {
      body.metadata.lastUpdated = new Date();
    }

    return await this.powerBIConfigRepository.updatePowerBIConfig(user, id, body);
  }

  async getPowerBIConfigByModule(user: TokenPayloadModel, query: DetailPowerBIConfigDto) {
    return await this.getUnifiedPowerBIConfig(user, query.module);
  }

  /**
   * Get unified PowerBI configuration with runtime flow detection
   */
  async getUnifiedPowerBIConfig(
    user: TokenPayloadModel,
    module: string,
  ): Promise<UnifiedPowerBIResponseDto> {
    try {
      // Get configuration from repository
      const config = await this.powerBIConfigRepository.getPowerBIConfigByModule(user, module);

      if (!config) {
        throw new HttpException(
          `PowerBI configuration not found for module: ${module}`,
          HttpStatus.NOT_FOUND,
        );
      }

      // Transform to response DTO
      const configResponse = this.transformToResponseDto(config);
      const pages = config.pages || [];

      // Determine flow and prepare response
      if (config.isEmbedded) {
        return await this.handleEmbeddedFlow(configResponse, config, pages);
      } else {
        return this.handlePublicUrlFlow(configResponse, pages);
      }
    } catch (error) {
      this.logger.error(
        `Failed to get unified PowerBI config for module ${module}:`,
        error.message,
      );
      throw error;
    }
  }

  /**
   * Validate embedded configuration
   */
  private async validateEmbeddedConfig(config: UpdatePowerBIConfigDto): Promise<void> {
    if (!config.workspaceId || !config.reportId) {
      throw new HttpException(
        'workspaceId and reportId are required for embedded configuration',
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!config.clientId || !config.clientSecret || !config.tenantId) {
      throw new HttpException(
        'PowerBI credentials are required for embedded configuration. Please provide clientId, clientSecret, and tenantId.',
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * Handle embedded PowerBI flow
   */
  private async handleEmbeddedFlow(
    config: PowerBIConfigResponseDto,
    dbConfig: PowerBIConfig,
    pages: any[],
  ): Promise<UnifiedPowerBIResponseDto> {
    try {
      // Validate required embedded configuration
      if (
        !dbConfig.clientId ||
        !dbConfig.clientSecret ||
        !dbConfig.tenantId ||
        !dbConfig.workspaceId
      ) {
        return {
          config,
          frontendConfig: {
            type: 'embedded',
          },
          isReady: false,
          error:
            'Embedded configuration incomplete. Missing required PowerBI credentials in database.',
          pages,
        };
      }

      // Generate embed token for main config with caching
      // const embedToken = await this.powerBiEmbeddedService.getEmbedTokenWithCaching(
      //   dbConfig,
      //   async (cachedData) => {
      //     await this.powerBIConfigRepository.updateCachedTokenData(dbConfig.id, cachedData);
      //   },
      // );

      // If pages array is empty, return embedded functionality in main response
      // if (!pages || pages.length === 0) {
      //   const configWithEmbed = {
      //     ...config,
      //     embedUrl: embedToken.embedUrl,
      //     embedSettings: config.embedSettings || this.getDefaultEmbedSettings(),
      //   };
      //   return {
      //     config: configWithEmbed,
      //     frontendConfig: {
      //       type: 'embedded',
      //       accessToken: embedToken.accessToken,
      //       embedUrl: embedToken.embedUrl,
      //       reportId: embedToken.reportId,
      //       tokenExpiration: embedToken.tokenExpiration.toISOString(),
      //       embedSettings: configWithEmbed.embedSettings,
      //     },
      //     isReady: true,
      //     pages: [],
      //   };
      // }

      // If pages exist, process them and include embedded functionality in pages
      const processedPages = await Promise.all(
        pages.map(async (page) => {
          if (page.isEmbedded && page.reportId && page.workspaceId) {
            try {
              // Create a page config entity for caching
              const pageConfigEntity = {
                ...page,
                clientId: page.clientId || dbConfig.clientId,
                clientSecret: page.clientSecret || dbConfig.clientSecret,
                tenantId: page.tenantId || dbConfig.tenantId,
                scope:
                  page.scope ||
                  dbConfig.scope ||
                  'https://analysis.windows.net/powerbi/api/.default',
              };

              const pageEmbedToken = await this.powerBiEmbeddedService.getEmbedTokenWithCaching(
                pageConfigEntity,
                async (cachedData) => {
                  await this.powerBIConfigRepository.updateCachedTokenData(page.id, cachedData);
                },
              );

              return {
                ...page,
                accessToken: pageEmbedToken.accessToken,
                embedUrl: pageEmbedToken.embedUrl,
                tokenExpiration: pageEmbedToken.tokenExpiration,
                embedSettings: config.embedSettings || this.getDefaultEmbedSettings(),
              };
            } catch (error) {
              this.logger.error(
                `Failed to generate embed token for page ${page.id}:`,
                error.message,
              );
              return page;
            }
          }
          return page;
        }),
      );

      return {
        config,
        frontendConfig: {
          type: 'embedded',
        },
        isReady: true,
        pages: processedPages,
      };
    } catch (error) {
      this.logger.error(`Failed to handle embedded flow for config ${config.id}:`, error.message);

      return {
        config,
        frontendConfig: {
          type: 'embedded',
        },
        isReady: false,
        error: `Failed to generate embed token: ${error.message}`,
        pages,
      };
    }
  }

  /**
   * Handle public URL PowerBI flow
   */
  private handlePublicUrlFlow(
    config: PowerBIConfigResponseDto,
    pages: any[],
  ): UnifiedPowerBIResponseDto {
    if (!config.publicUrl) {
      return {
        config,
        frontendConfig: {
          type: 'publicUrl',
        },
        isReady: false,
        error: 'Public URL not configured',
        pages,
      };
    }

    return {
      config,
      frontendConfig: {
        type: 'publicUrl',
        url: config.publicUrl,
      },
      isReady: true,
      pages,
    };
  }

  /**
   * Transform entity to response DTO
   */
  private transformToResponseDto(entity: PowerBIConfig): PowerBIConfigResponseDto {
    return {
      id: entity.id,
      module: entity.module,
      companyId: entity.companyId,
      isDefault: entity.isDefault,
      isEmbedded: entity.isEmbedded,
      type: entity.isEmbedded ? 'embedded' : 'publicUrl',
      isPage: entity.isPage,
      pageOrder: entity.pageOrder,
      pageName: entity.pageName,
      publicUrl: entity.isEmbedded ? undefined : entity.url,
      workspaceId: entity.workspaceId,
      reportId: entity.reportId,
      embedUrl: entity.embedUrl,
      clientId: entity.clientId,
      tenantId: entity.tenantId,
      scope: entity.scope,
      embedSettings: entity.embedSettings,
      metadata: entity.metadata,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }

  /**
   * Get default embed settings
   */
  private getDefaultEmbedSettings() {
    return {
      filterPaneEnabled: true,
      navContentPaneEnabled: true,
      allowSaveAs: false,
      background: 'Transparent',
      layoutType: 'Custom',
      permissions: ['Read'],
    };
  }

  /**
   * Test PowerBI connection for embedded configuration
   */
  async testPowerBIConnection(
    user: TokenPayloadModel,
    module: string,
  ): Promise<{ success: boolean; message: string; reportCount?: number }> {
    try {
      const config = await this.powerBIConfigRepository.getPowerBIConfigByModule(user, module);

      if (!config) {
        return { success: false, message: 'Configuration not found' };
      }

      if (!config.isEmbedded) {
        return { success: true, message: 'Public URL configuration - no connection test needed' };
      }

      // Validate embedded configuration
      if (!config.clientId || !config.clientSecret || !config.tenantId || !config.workspaceId) {
        return {
          success: false,
          message:
            'Embedded configuration incomplete. Missing required PowerBI credentials in database.',
        };
      }

      // Prepare PowerBI config for embedded service
      const powerBiConfig = {
        clientId: config.clientId,
        clientSecret: config.clientSecret,
        tenantId: config.tenantId,
        workspaceId: config.workspaceId,
        scope: config.scope || 'https://analysis.windows.net/powerbi/api/.default',
      };

      // Test embedded connection
      const reports = await this.powerBiEmbeddedService.getWorkspaceReports(powerBiConfig);
      const targetReport = reports.find((r) => r.id === config.reportId);

      if (!targetReport) {
        return {
          success: false,
          message: `Report ${config.reportId} not found in workspace. Available reports: ${reports.length}`,
        };
      }

      return {
        success: true,
        message: 'PowerBI embedded connection successful',
        reportCount: reports.length,
      };
    } catch (error) {
      this.logger.error(`PowerBI connection test failed:`, error.message);
      return { success: false, message: error.message };
    }
  }
}
