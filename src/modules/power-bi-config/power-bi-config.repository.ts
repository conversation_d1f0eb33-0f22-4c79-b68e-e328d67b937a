import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  TokenPayloadModel,
  TypeORMRepository,
} from 'svm-nest-lib-v3';
import { Connection, EntityRepository, In } from 'typeorm';
import { ListPowerBIConfigDto } from './dto/list-power-bi-config.dto';
import { PowerBIConfig } from './power-bi-config.entity';
import { UpdatePowerBIConfigDto } from './dto/update-power-bi-config.dto';
import { MetaConfig } from '../../modules-qa/catalog/entity/meta-config.entity';
import { CatalogConst } from '../../modules-qa/catalog/catalog-key.const';
import * as MODULE_CONFIG_DATA from '../../modules-qa/catalog/config/default-module.config';
import { DEFAULT_POWER_BI_CONFIG, POWER_BI_CONFIG_VERSION } from './config/default-power-bi.config';
import { WidgetModuleEnum } from 'src/commons/enums';
import { PowerBiPageDto } from '../company-configuration/dto/power-bi-page.dto';
import { MySet } from '../../utils';

@EntityRepository(PowerBIConfig)
export class PowerBIConfigRepository extends TypeORMRepository<PowerBIConfig> {
  constructor(private readonly connection: Connection) {
    super();
  }

  async listPowerBIConfig(user: TokenPayloadModel, query: ListPowerBIConfigDto) {
    const defaultConfigs = await this.listDefaultPowerBIConfig(query);

    const queryBuilder = this.createQueryBuilder('powerBIConfig')
      .leftJoin('powerBIConfig.company', 'company')
      .where(
        'powerBIConfig.companyId = :companyId ' +
          'AND powerBIConfig.deleted = false ' +
          'AND powerBIConfig.isPage = false ',
        {
          companyId: user.companyId,
        },
      )
      .select()
      .addSelect(['company.id', 'company.name']);

    if (query.module) {
      queryBuilder.andWhere('powerBIConfig.module = :moduleName', {
        moduleName: query.module,
      });
    }
    const qb = await queryBuilder.getMany();
    defaultConfigs.data = defaultConfigs.data.map((value) => {
      const config = qb.find((cf) => cf.module === value.module);
      return config || value;
    });

    return defaultConfigs;
  }

  async listDefaultPowerBIConfig(query: ListPowerBIConfigDto) {
    const queryBuilder = this.createQueryBuilder('powerBIConfig')
      .leftJoin('powerBIConfig.company', 'company')
      .where(
        'powerBIConfig.isDefault = true ' +
          'AND powerBIConfig.deleted = false ' +
          'AND powerBIConfig.isPage = false ',
      )
      .select();

    if (query.module) {
      queryBuilder.andWhere('powerBIConfig.module = :moduleName', {
        moduleName: query.module,
      });
    }

    return this.list(
      {
        page: query.page,
        limit: query.pageSize,
      },
      {
        queryBuilder,
        sort: query.sort || 'powerBIConfig.createdAt:-1',
      },
    );
  }

  async updatePowerBIConfig(user: TokenPayloadModel, id: string, body: UpdatePowerBIConfigDto) {
    try {
      const config = await this.findOne(id);
      if (config.isDefault) {
        await this.save({
          module: config.module,
          url: body.url,
          companyId: user.companyId,
          deleted: false,
        });
      } else {
        const updateResult = await this.update(
          {
            id,
            companyId: user.companyId,
            deleted: false,
          },
          body,
        );
        if (updateResult.affected === 0) {
          throw new BaseError({ status: 404, message: 'common.NOT_FOUND' });
        }
      }
      return 1;
    } catch (ex) {
      LoggerCommon.error('[PowerBIConfigRepository] updatePowerBIConfig error ', ex.message || ex);
      throw ex;
    }
  }

  async getPowerBIConfigByModule(user: TokenPayloadModel, moduleName: string) {
    let powerBIConfig = await this.detailByConditions({
      companyId: user.companyId,
      module: moduleName,
      deleted: false,
      isPage: false,
    });
    if (!powerBIConfig) {
      powerBIConfig = await this.detailByConditions({
        isDefault: true,
        module: moduleName,
        deleted: false,
        isPage: false,
      });
    }

    //get pages
    const powerBIPages = await this.getPowerBIPagesConfigByCompanyId(user.companyId, moduleName);

    return { ...powerBIConfig, pages: powerBIPages };
  }
  catch(ex) {
    LoggerCommon.error(
      '[PowerBIConfigRepository] getPowerBIConfigByModule error ',
      ex.message || ex,
    );
    throw ex;
  }

  async _initDefaultConfigs() {
    const version = await this.manager.findOne(MetaConfig, {
      where: { key: CatalogConst.POWER_BI_CONFIG },
    });

    if (!version || POWER_BI_CONFIG_VERSION > version?.lastTimeSync) {
      return await this.manager.transaction(async (managerTrans) => {
        const queryBuilder = this.createQueryBuilder('powerBIConfig')
          .leftJoin('powerBIConfig.company', 'company')
          .where('powerBIConfig.isDefault = true AND powerBIConfig.deleted = false')
          .select();
        const config = await queryBuilder.getMany();

        const saveConfigs = DEFAULT_POWER_BI_CONFIG.map((value) => {
          const findConfig = config.find((cf) => cf.module === value.module);
          return {
            ...value,
            id: findConfig?.id,
            isDefault: true,
            deleted: false,
          } as PowerBIConfig;
        });

        await managerTrans.save(PowerBIConfig, saveConfigs);

        await managerTrans.save(MetaConfig, {
          key: CatalogConst.POWER_BI_CONFIG,
          lastTimeSync: POWER_BI_CONFIG_VERSION,
        });
      });
    }

    return 1;
  }

  async updatePowerBIConfigBySuperAdmin(companyId: string, powerBIurl: string) {
    try {
      const config = await this.getPowerBIConfigByCompanyId(companyId);
      if (!config) {
        await this.save({
          module: WidgetModuleEnum.HOME_PAGE,
          url: powerBIurl,
          companyId: companyId,
          deleted: false,
        });
      } else {
        await this.save({
          id: config.id,
          module: WidgetModuleEnum.HOME_PAGE,
          url: powerBIurl,
          companyId: companyId,
          deleted: false,
        });
      }
      return 1;
    } catch (ex) {
      LoggerCommon.error('[PowerBIConfigRepository] updatePowerBIConfig error ', ex.message || ex);
      throw ex;
    }
  }

  async updatePowerBIPagesConfigBySuperAdmin(
    companyId: string,
    module: string,
    pages: PowerBiPageDto[],
  ) {
    try {
      const currentPowerBIPages = await this.find({
        companyId,
        module,
        isPage: true,
        deleted: false,
      });
      const preparedPowerBIPagesInsert = [];
      const preparedPowerBIPagesChange = [];
      pages.forEach((page) => {
        if (page.id) {
          preparedPowerBIPagesChange.push({ ...page, companyId, module, isPage: true });
        } else {
          preparedPowerBIPagesInsert.push({ ...page, companyId, module, isPage: true });
        }
      });
      const deletedPowerBIPagesIds = MySet.difference(
        new Set(currentPowerBIPages.map((page) => page.id)),
        new Set(preparedPowerBIPagesChange.map((page) => page.id)),
      );

      await this.connection.transaction(async (manager) => {
        //delete
        if (deletedPowerBIPagesIds.size) {
          await manager.delete(PowerBIConfig, {
            id: In(Array.from(deletedPowerBIPagesIds)),
          });
        }
        // update
        if (preparedPowerBIPagesChange.length) {
          await manager.save(PowerBIConfig, preparedPowerBIPagesChange);
        }
        // insert
        if (preparedPowerBIPagesInsert.length) {
          await manager.insert(PowerBIConfig, preparedPowerBIPagesInsert);
        }
      });
      return 1;
    } catch (ex) {
      LoggerCommon.error(
        '[PowerBIConfigRepository] updatePowerBIPagesConfig error ',
        ex.message || ex,
      );
      throw ex;
    }
  }

  async getPowerBIConfigByCompanyId(companyId: string) {
    const config = await this.detailByConditions({
      module: WidgetModuleEnum.HOME_PAGE,
      companyId: companyId,
      deleted: false,
      isPage: false,
    });
    return config;
  }

  async getPowerBIPagesConfigByCompanyId(companyId: string, module: string) {
    const powerBIPages = await this.createQueryBuilder('powerBIConfig')
      .leftJoin('powerBIConfig.company', 'company')
      .where(
        'powerBIConfig.companyId = :companyId ' +
          'AND powerBIConfig.module = :module ' +
          'AND powerBIConfig.deleted = false ' +
          'AND powerBIConfig.isPage = true ',
        {
          companyId,
          module,
        },
      )
      .orderBy('powerBIConfig.pageOrder', 'ASC')
      .select([
        'powerBIConfig.id',
        'powerBIConfig.pageOrder',
        'powerBIConfig.pageName',
        'powerBIConfig.url',
        'powerBIConfig.isEmbedded',
        'powerBIConfig.workspaceId',
        'powerBIConfig.reportId',
        'powerBIConfig.clientId',
        'powerBIConfig.clientSecret',
        'powerBIConfig.tenantId',
        'powerBIConfig.scope',
        'powerBIConfig.accessToken',
        'powerBIConfig.embedUrl',
        'powerBIConfig.tokenExpiration',
      ])
      .getMany();
    return powerBIPages;
  }

  async updateCachedTokenData(
    configId: string,
    cachedData: {
      accessToken: string;
      embedUrl: string;
      tokenExpiration: Date;
    },
  ) {
    try {
      const updateResult = await this.update(
        {
          id: configId,
          deleted: false,
        },
        cachedData,
      );

      if (updateResult.affected === 0) {
        throw new BaseError({ status: 404, message: 'PowerBI config not found' });
      }

      return 1;
    } catch (ex) {
      LoggerCommon.error(
        '[PowerBIConfigRepository] updateCachedTokenData error ',
        ex.message || ex,
      );
      throw ex;
    }
  }
}
