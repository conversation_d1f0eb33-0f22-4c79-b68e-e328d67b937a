import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  IsOptional,
  IsBoolean,
  ValidateIf,
  ValidateNested,
  IsNumber,
  Min,
  Max,
} from 'class-validator';
import { Type } from 'class-transformer';
import { PowerBIEmbedSettingsDto, PowerBIMetadataDto } from './power-bi-embed-settings.dto';

export class UpdatePowerBIConfigDto {
  @ApiProperty({
    description: 'Public PowerBI URL (required if not using embedded)',
    required: false,
  })
  @ValidateIf((o) => !o.isEmbedded)
  @IsOptional()
  @IsString()
  url?: string;

  @ApiProperty({
    description: 'Whether this configuration uses PowerBI Embedded',
    required: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  isEmbedded?: boolean;

  // Embedded-specific fields
  @ApiProperty({
    description: 'PowerBI workspace ID (required for embedded)',
    required: false,
  })
  @ValidateIf((o) => o.isEmbedded)
  @IsOptional()
  @IsString()
  workspaceId?: string;

  @ApiProperty({
    description: 'PowerBI report ID (required for embedded)',
    required: false,
  })
  @ValidateIf((o) => o.isEmbedded)
  @IsOptional()
  @IsString()
  reportId?: string;

  @ApiProperty({
    description: 'PowerBI embed URL (optional, can be auto-generated)',
    required: false,
  })
  @IsOptional()
  @IsString()
  embedUrl?: string;

  @ApiProperty({
    description: 'Azure AD Client ID (optional, uses global config if not provided)',
    required: false,
  })
  @IsOptional()
  @IsString()
  clientId?: string;

  @ApiProperty({
    description: 'Azure AD Tenant ID (optional, uses global config if not provided)',
    required: false,
  })
  @IsOptional()
  @IsString()
  tenantId?: string;

  @ApiProperty({
    description: 'Azure AD Client Secret (optional, uses global config if not provided)',
    required: false,
  })
  @IsOptional()
  @IsString()
  clientSecret?: string;

  @ApiProperty({
    description: 'PowerBI API scope (optional, uses global config if not provided)',
    required: false,
  })
  @IsOptional()
  @IsString()
  scope?: string;

  @ApiProperty({
    description: 'PowerBI embed settings configuration',
    required: false,
    type: PowerBIEmbedSettingsDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => PowerBIEmbedSettingsDto)
  embedSettings?: PowerBIEmbedSettingsDto;

  @ApiProperty({
    description: 'Additional metadata for the configuration',
    required: false,
    type: PowerBIMetadataDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => PowerBIMetadataDto)
  metadata?: PowerBIMetadataDto;

  // Page-related fields (existing)
  @ApiProperty({
    description: 'Whether this is a page-based report',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isPage?: boolean;

  @ApiProperty({
    description: 'Page order for page-based reports',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(999)
  pageOrder?: number;

  @ApiProperty({
    description: 'Page name for page-based reports',
    required: false,
  })
  @IsOptional()
  @IsString()
  pageName?: string;
}
