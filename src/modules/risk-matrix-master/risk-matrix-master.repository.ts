import { EntityRepository, Not, Connection } from 'typeorm';
import { InternalServerErrorException, BadRequestException } from '@nestjs/common';
import { TokenPayloadModel, CommonStatus, TypeORMRepository, BaseError } from 'svm-nest-lib-v3';
import { RiskMatrixMaster } from './entities/risk-matrix-master.entity';
import { RiskMatrixCell } from './entities/risk-matrix-cell.entity';
import { RiskValueMapping } from './entities/risk-value-mapping.entity';
import { RiskLevelMapping } from './entities/risk-level-mapping.entity';
import { CreateRiskMatrixDTO, UpdateRiskMatrixDTO, ListRiskMatrixQueryDTO } from './dto';
import { StatusCommon } from '../../commons/enums';
import { IncidentInvestigation } from '../../modules-qa/incident-investigation/entity/incident-investigation.entity';

@EntityRepository(RiskMatrixMaster)
export class RiskMatrixMasterRepository extends TypeORMRepository<RiskMatrixMaster> {
  constructor(
    private readonly connection: Connection,
  ) {
    super();
  }
  async createRiskMatrix(
    createRiskMatrixDto: CreateRiskMatrixDTO,
    token: TokenPayloadModel,
  ): Promise<RiskMatrixMaster> {

    try {
      return await this.connection.transaction(async (manager) => {
        const existingMatrixCode = await this.findOne({
          where: {
            matrixCode: createRiskMatrixDto.matrixCode,
            companyId: token.companyId,
            deleted: false,
          },
        });

        if (existingMatrixCode) {
          throw new BadRequestException('Matrix code already exists for this company');
        }
        // Check if there's already an active risk matrix for this company
        const existingActiveMatrix = await this.findOne({
          where: {
            companyId: token.companyId,
            status: StatusCommon.ACTIVE,
            deleted: false,
          },
        });
        if (existingActiveMatrix) {
          existingActiveMatrix.status = StatusCommon.INACTIVE; // <-- modify what you need
          await manager.save(existingActiveMatrix); // <-- save updates
        }

        // Create the main risk matrix
        const riskMatrix = this.create({
          matrixCode: createRiskMatrixDto?.matrixCode,
          rows: createRiskMatrixDto?.rows,
          columns: createRiskMatrixDto?.columns,
          rowsName: createRiskMatrixDto?.rowsName,
          columnsName: createRiskMatrixDto?.columnsName,
          status: StatusCommon.ACTIVE,
          companyId: token?.companyId,
          createdUserId: token.id,
        });

        const savedMatrix = await manager.save(riskMatrix);
        // Create value mappings if provided
        if (createRiskMatrixDto?.valueMappings && createRiskMatrixDto?.valueMappings?.length > 0) {
          const valueMappingRepository = this.manager.getRepository(RiskValueMapping);
          const valueMappings = createRiskMatrixDto?.valueMappings?.map(mappingDto =>
            valueMappingRepository.create({
              ...mappingDto,
              id: mappingDto?.riskValueMappingId,
              riskMatrixId: savedMatrix?.id,
            })
          );
          await manager.save(valueMappings);
        }
        // Create matrix cells if provided
        if (createRiskMatrixDto.cells && createRiskMatrixDto.cells.length > 0) {
          const cellRepository = manager.getRepository(RiskMatrixCell);
          const cells = createRiskMatrixDto.cells.map(cellDto =>
            cellRepository.create({
              ...cellDto,
              riskMatrixId: savedMatrix.id,
            })
          );
          await manager.save(cells);
        }



        // Create level mappings if provided
        if (createRiskMatrixDto.levelMappings && createRiskMatrixDto.levelMappings.length > 0) {
          const levelMappingRepository = manager.getRepository(RiskLevelMapping);
          const levelMappings = createRiskMatrixDto.levelMappings.map(mappingDto =>
            levelMappingRepository.create({
              ...mappingDto,
              riskMatrixId: savedMatrix.id,
            })
          );
          await manager.save(levelMappings);
        }

        return savedMatrix;
      });
    } catch (error) {
      // Re-throw HTTP exceptions as-is
      if (error instanceof BadRequestException || error instanceof BaseError) {
        throw error;
      }
      // Wrap other errors
      throw new InternalServerErrorException(error.message || 'Failed to create risk matrix');
    }
  }

  async listRiskMatrix(query: ListRiskMatrixQueryDTO, token: TokenPayloadModel) {
    const queryBuilder = this.createQueryBuilder('rm')
      .leftJoin('rm.company', 'company')
      .leftJoin('rm.createdUser', 'createdUser')
      .leftJoin('rm.updatedUser', 'updatedUser')
      .leftJoinAndSelect('rm.cells', 'cells')
      .leftJoinAndSelect('rm.valueMappings', 'valueMappings')
      .leftJoinAndSelect('rm.levelMappings', 'levelMappings')
      .leftJoin('levelMappings.priorityMaster', 'priorityMaster')
      .addSelect(['company.id', 'company.name', 'createdUser.username', 'updatedUser.username', 'priorityMaster.risk'])
      .where('(rm.companyId = :companyId OR company.parentId = :companyId)', {
        companyId: token.companyId,
      });

    if (query.companyId) {
      queryBuilder.andWhere('rm.companyId = :companyId', {
        companyId: query.companyId,
      });
    }

    if (query.content) {
      queryBuilder.andWhere('(rm.matrixCode LIKE :content)', {
        content: `%${query.content}%`,
      });
    }

    if (query.status) {
      queryBuilder.andWhere('rm.status = :status', {
        status: query.status,
      });
    }

    if (query.createdAtFrom) {
      queryBuilder.andWhere('rm.createdAt >= :createdAtFrom', {
        createdAtFrom: query.createdAtFrom,
      });
    }

    if (query.createdAtTo) {
      queryBuilder.andWhere('rm.createdAt <= :createdAtTo', {
        createdAtTo: query.createdAtTo,
      });
    }

    const dataList = await this.list(
      { page: query.page, limit: query.pageSize },
      {
        queryBuilder,
        sort: query.sort || 'rm.createdAt:-1',
      },
    );

    return dataList;
  }

  async getDetailRiskMatrixById(id: string): Promise<RiskMatrixMaster> {
    try {
      const riskMatrix = await this.createQueryBuilder('rm')
        .leftJoin('rm.createdUser', 'createdUser')
        .leftJoin('rm.updatedUser', 'updatedUser')
        .leftJoinAndSelect('rm.cells', 'cells')
        .leftJoinAndSelect('rm.valueMappings', 'valueMappings')
        .leftJoinAndSelect('rm.levelMappings', 'levelMappings')
        .leftJoin('levelMappings.priorityMaster', 'priorityMaster')
        .addSelect(['createdUser.username', 'updatedUser.username', 'priorityMaster.risk'])
        .where('rm.id = :id', { id })
        .andWhere('rm.deleted = false')
        .getOne();

      if (!riskMatrix) {
        throw new BadRequestException('Risk matrix not found');
      }

      return riskMatrix;
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to get risk matrix details');
    }
  }

  async updateRiskMatrix(
    id: string,
    updateRiskMatrixDto: UpdateRiskMatrixDTO,
    token: TokenPayloadModel,
  ): Promise<RiskMatrixMaster> {
    try {
      return await this.connection.transaction(async (manager) => {
        // First, find the existing risk matrix
        const existingMatrix = await manager.findOne(RiskMatrixMaster, {
          where: { id, companyId: token.companyId, deleted: false },
        });

        if (!existingMatrix) {
          throw new BaseError({ status: 404, message: 'riskMatrixMaster.NOT_FOUND' });
        }

        const hasRef = await this.hasRefInGivenTables(
          id,
          'riskMatrixId',
          [],
          [{ tableName: 'incident_investigation', field: 'riskMatrixId', noDelCol: false }],
        );
        if (hasRef) {
          throw new BaseError({ status: 400, message: 'common.CANNOT_DELETE_DUE_TO_REF' });
        }

        // Update the main risk matrix record
        Object.assign(existingMatrix, {
          matrixCode: updateRiskMatrixDto.matrixCode,
          rows: updateRiskMatrixDto.rows,
          columns: updateRiskMatrixDto.columns,
          rowsName: updateRiskMatrixDto.rowsName,
          columnsName: updateRiskMatrixDto.columnsName,
          status: updateRiskMatrixDto.status,
          updatedUserId: token.id,
          updatedAt: new Date(),
        });

        const updatedMatrix = await manager.save(existingMatrix);

        // Delete existing related records
        await manager.delete(RiskValueMapping, { riskMatrixId: id });
        await manager.delete(RiskMatrixCell, { riskMatrixId: id });
        await manager.delete(RiskLevelMapping, { riskMatrixId: id });

        // Create new value mappings if provided
        if (updateRiskMatrixDto.valueMappings && updateRiskMatrixDto.valueMappings.length > 0) {
          const valueMappingRepository = manager.getRepository(RiskValueMapping);
          const valueMappings = updateRiskMatrixDto?.valueMappings?.map((mappingDto) =>
            valueMappingRepository.create({
              ...mappingDto,
              id: mappingDto?.riskValueMappingId,
              riskMatrixId: updatedMatrix.id,
            })
          );
          await manager.save(valueMappings);
        }

        // Create new matrix cells if provided
        if (updateRiskMatrixDto.cells && updateRiskMatrixDto.cells.length > 0) {
          const cellRepository = manager.getRepository(RiskMatrixCell);
          const cells = updateRiskMatrixDto.cells.map((cellDto) =>
            cellRepository.create({
              ...cellDto,
              riskMatrixId: updatedMatrix.id,
            })
          );
          await manager.save(cells);
        }

        // Create new level mappings if provided
        if (updateRiskMatrixDto.levelMappings && updateRiskMatrixDto.levelMappings.length > 0) {
          const levelMappingRepository = manager.getRepository(RiskLevelMapping);
          const levelMappings = updateRiskMatrixDto.levelMappings.map((mappingDto) =>
            levelMappingRepository.create({
              ...mappingDto,
              riskMatrixId: updatedMatrix.id,
            })
          );
          await manager.save(levelMappings);
        }

        return updatedMatrix;
      });
    } catch (error) {
      throw new BaseError({ status: 500, message: error.message });
    }
  }

  async deleteRiskMatrix(id: string, companyId: string) {
    const hasRef = await this.hasRefInGivenTables(
      id,
      'riskMatrixId',
      [],
      [{ tableName: 'incident_investigation', field: 'riskMatrixId', noDelCol: false }],
    );
    if (hasRef) {
      throw new BaseError({ status: 400, message: 'common.CANNOT_DELETE_DUE_TO_REF' });
    }

    // soft delete
    const updateResult = await this.softDelete({
      id: id,
      companyId: companyId,
    });
    if (updateResult.affected === 0) {
      throw new BaseError({ status: 404, message: 'riskMatrixMaster.NOT_FOUND' });
    } else {
      return 1;
    }
  }
}