import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import axios, { AxiosResponse } from 'axios';
import { ConfigService } from '@nestjs/config';
import { PowerBIConfig as PowerBIConfigEntity } from '../power-bi-config/power-bi-config.entity';

interface AzureTokenResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  ext_expires_in: number;
  scope: string;
}

interface PowerBIEmbedTokenResponse {
  token: string;
  tokenId: string;
  expiration: string;
}

interface PowerBIAvailableFeaturesResponse {
  features: {
    name: string;
    additionalInfo: {
      usagePercent?: number;
    };
  }[];
}

interface CachedToken {
  token: string;
  expiresAt: number;
}

interface PowerBIConfig {
  clientId: string;
  clientSecret: string;
  tenantId: string;
  workspaceId: string;
  scope: string;
}

@Injectable()
export class PowerBiEmbeddedService {
  private readonly logger = new Logger(PowerBiEmbeddedService.name);
  private cachedAzureToken: CachedToken | null = null;
  private readonly BUFFER_TIME = 5 * 60 * 1000; // 5 minutes buffer before token expires

  constructor(private readonly configService: ConfigService) {}

  /**
   * Get Azure AD access token with caching and auto-refresh
   */
  private async getAzureAccessToken(config: PowerBIConfig): Promise<string> {
    // Check if we have a valid cached token
    // if (this.cachedAzureToken && Date.now() < this.cachedAzureToken.expiresAt) {
    //   this.logger.debug('Using cached Azure AD token');
    //   return this.cachedAzureToken.token;
    // }

    this.logger.debug('Fetching new Azure AD token');
    console.log('Current local time:', new Date().toLocaleString());

    const tokenUrl = `https://login.microsoftonline.com/${config.tenantId}/oauth2/v2.0/token`;

    const params = new URLSearchParams();
    params.append('client_id', config.clientId);
    params.append('client_secret', config.clientSecret);
    params.append('scope', config.scope);
    params.append('grant_type', 'client_credentials');

    try {
      const response: AxiosResponse<AzureTokenResponse> = await axios.post(tokenUrl, params, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });

      const { access_token, expires_in } = response.data;
      console.log('expires_in', expires_in);
      console.log('Token received at local time:', new Date().toLocaleString());
      const expiresAt = Date.now() + expires_in * 1000 - this.BUFFER_TIME;
      console.log('Token expires at local time:', new Date(expiresAt).toLocaleString());

      // Cache the token
      this.cachedAzureToken = {
        token: access_token,
        expiresAt,
      };

      this.logger.debug(`Azure AD token cached, expires in ${expires_in} seconds`);
      return access_token;
    } catch (error) {
      this.logger.error('Failed to get Azure AD token', error?.response?.data || error.message);
      throw new HttpException(
        'Failed to authenticate with Azure AD',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get available features and token usage information
   * This helps monitor the embed token usage quota
   */
  async getAvailableFeatures(config: PowerBIConfig): Promise<PowerBIAvailableFeaturesResponse> {
    try {
      const azureToken = await this.getAzureAccessToken(config);

      const featuresUrl = `https://api.powerbi.com/v1.0/myorg/availableFeatures`;
      const response: AxiosResponse<PowerBIAvailableFeaturesResponse> = await axios.get(
        featuresUrl,
        {
          headers: {
            Authorization: `Bearer ${azureToken}`,
            'Content-Type': 'application/json',
          },
        },
      );
      console.log('response', JSON.stringify(response.data));
      // Log token usage information
      const tokenUsageFeature = response.data.features?.find((f) => f.name === 'embedTokenShare');
      if (tokenUsageFeature && tokenUsageFeature.additionalInfo?.usagePercent !== undefined) {
        this.logger.log(
          `Current embed token usage: ${tokenUsageFeature.additionalInfo.usagePercent}% of quota`,
        );
      }
      return response.data;
    } catch (error) {
      this.logger.warn(
        'Failed to get available features and token usage',
        error?.response?.data || error.message,
      );
      // Don't throw an exception here as this is non-critical information
      // Just return an empty response
      return { features: [] };
    }
  }

  /**
   * Generate Power BI embed token for a specific report with caching support
   */
  async getEmbedTokenWithCaching(
    configEntity: PowerBIConfigEntity,
    saveCallback: (cachedData: {
      accessToken: string;
      embedUrl: string;
      tokenExpiration: Date;
    }) => Promise<void>,
  ): Promise<{
    accessToken: string;
    embedUrl: string;
    reportId: string;
    tokenExpiration: Date;
  }> {
    if (!configEntity.reportId) {
      throw new HttpException('Report ID is required', HttpStatus.BAD_REQUEST);
    }

    if (
      !configEntity.clientId ||
      !configEntity.clientSecret ||
      !configEntity.tenantId ||
      !configEntity.workspaceId
    ) {
      throw new HttpException(
        'PowerBI configuration is incomplete. Missing required fields: clientId, clientSecret, tenantId, or workspaceId',
        HttpStatus.BAD_REQUEST,
      );
    }

    // Check if cached token exists and is still valid
    if (configEntity.accessToken && configEntity.embedUrl && configEntity.tokenExpiration) {
      const currentTime = new Date();
      const tokenExpiration = new Date(configEntity.tokenExpiration);

      if (tokenExpiration.getTime() > currentTime.getTime()) {
        this.logger.debug(`Using cached embed token for report ${configEntity.reportId}`);
        return {
          accessToken: configEntity.accessToken,
          embedUrl: configEntity.embedUrl,
          reportId: configEntity.reportId,
          tokenExpiration: configEntity.tokenExpiration,
        };
      }
    }

    // Generate new token if cache is expired or doesn't exist
    this.logger.debug(`Generating new embed token for report ${configEntity.reportId}`);

    const powerBiConfig = {
      clientId: configEntity.clientId,
      clientSecret: configEntity.clientSecret,
      tenantId: configEntity.tenantId,
      workspaceId: configEntity.workspaceId,
      scope: configEntity.scope || 'https://analysis.windows.net/powerbi/api/.default',
    };

    const tokenData = await this.getEmbedToken(configEntity.reportId, powerBiConfig);

    // Cache the new token data
    const cachedData = {
      accessToken: tokenData.accessToken,
      embedUrl: tokenData.embedUrl,
      tokenExpiration: new Date(tokenData.tokenExpiration),
    };

    // Save to database
    await saveCallback(cachedData);

    this.logger.debug(`Cached new embed token for report ${configEntity.reportId}`);

    return tokenData;
  }

  /**
   * Generate Power BI embed token for a specific report
   */
  async getEmbedToken(
    reportId: string,
    config: PowerBIConfig,
  ): Promise<{
    accessToken: string;
    embedUrl: string;
    reportId: string;
    tokenExpiration: Date;
  }> {
    if (!reportId) {
      throw new HttpException('Report ID is required', HttpStatus.BAD_REQUEST);
    }

    if (!config.clientId || !config.clientSecret || !config.tenantId || !config.workspaceId) {
      throw new HttpException(
        'PowerBI configuration is incomplete. Missing required fields: clientId, clientSecret, tenantId, or workspaceId',
        HttpStatus.BAD_REQUEST,
      );
    }

    try {
      console.log('config', config);
      // First check available features and token usage
      await this.getAvailableFeatures(config);

      const azureToken = await this.getAzureAccessToken(config);

      // Get report details first to get the embed URL
      const reportDetailsUrl = `https://api.powerbi.com/v1.0/myorg/groups/${config.workspaceId}/reports/${reportId}`;

      const reportResponse = await axios.get(reportDetailsUrl, {
        headers: {
          Authorization: `Bearer ${azureToken}`,
          'Content-Type': 'application/json',
        },
      });

      const embedUrl = reportResponse.data.embedUrl;

      // Generate embed token
      const tokenUrl = `https://api.powerbi.com/v1.0/myorg/groups/${config.workspaceId}/reports/${reportId}/GenerateToken`;

      const tokenPayload = {
        accessLevel: 'View',
        allowSaveAs: false,
      };

      const tokenResponse: AxiosResponse<PowerBIEmbedTokenResponse> = await axios.post(
        tokenUrl,
        tokenPayload,
        {
          headers: {
            Authorization: `Bearer ${azureToken}`,
            'Content-Type': 'application/json',
          },
        },
      );

      const { token, expiration } = tokenResponse.data;

      this.logger.debug(`Generated embed token for report ${reportId}`);

      return {
        accessToken: token,
        embedUrl,
        reportId,
        tokenExpiration: new Date(expiration),
      };
    } catch (error) {
      this.logger.error(
        `Failed to get embed token for report ${reportId}`,
        JSON.stringify(error?.response?.data) || error.message,
      );

      if (error?.response?.status === 404) {
        throw new HttpException(
          `Report with ID ${reportId} not found in workspace`,
          HttpStatus.NOT_FOUND,
        );
      }

      if (error?.response?.status === 403) {
        throw new HttpException(
          'Insufficient permissions to access Power BI resources',
          HttpStatus.FORBIDDEN,
        );
      }

      throw new HttpException(
        'Failed to generate Power BI embed token',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get all reports in the workspace
   */
  async getWorkspaceReports(config: PowerBIConfig): Promise<any[]> {
    if (!config.clientId || !config.clientSecret || !config.tenantId || !config.workspaceId) {
      throw new HttpException(
        'PowerBI configuration is incomplete. Missing required fields: clientId, clientSecret, tenantId, or workspaceId',
        HttpStatus.BAD_REQUEST,
      );
    }

    try {
      const azureToken = await this.getAzureAccessToken(config);

      const reportsUrl = `https://api.powerbi.com/v1.0/myorg/groups/${config.workspaceId}/reports`;

      const response = await axios.get(reportsUrl, {
        headers: {
          Authorization: `Bearer ${azureToken}`,
          'Content-Type': 'application/json',
        },
      });

      return response.data.value || [];
    } catch (error) {
      this.logger.error('Failed to get workspace reports', error?.response?.data || error.message);
      throw new HttpException(
        'Failed to retrieve workspace reports',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Clear cached tokens (useful for testing or manual refresh)
   */
  clearTokenCache(): void {
    this.cachedAzureToken = null;
    this.logger.debug('Token cache cleared');
  }
}
