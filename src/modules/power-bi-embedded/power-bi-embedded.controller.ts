import { Controller, Post, Body, Logger, Delete } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger';
import { PowerBiEmbeddedService } from './power-bi-embedded.service';
import {
  PowerBiEmbedResponseDto,
  PowerBiReportsResponseDto,
} from './dto/power-bi-embed-response.dto';
import { PowerBiEmbedRequestDto, PowerBiConfigRequestDto } from './dto/power-bi-embed-request.dto';

@ApiTags('Power BI Embedded')
@Controller('powerbi')
export class PowerBiEmbeddedController {
  private readonly logger = new Logger(PowerBiEmbeddedController.name);

  constructor(private readonly powerBiEmbeddedService: PowerBiEmbeddedService) {}

  @Post('embed-token')
  @ApiOperation({
    summary: 'Get Power BI embed token for a specific report',
    description:
      'Generates an embed token for the specified Power BI report that can be used to embed the report in your application. Requires PowerBI configuration in request body.',
  })
  @ApiBody({
    type: PowerBiEmbedRequestDto,
    description: 'PowerBI configuration and report ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully generated embed token',
    type: PowerBiEmbedResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Report ID and configuration are required',
  })
  @ApiResponse({
    status: 404,
    description: 'Report not found in workspace',
  })
  @ApiResponse({
    status: 403,
    description: 'Insufficient permissions to access Power BI resources',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async getEmbedToken(@Body() request: PowerBiEmbedRequestDto): Promise<PowerBiEmbedResponseDto> {
    this.logger.debug(`Requesting embed token for report: ${request.reportId}`);

    try {
      // Prepare PowerBI configuration
      const powerBiConfig = {
        clientId: request.clientId,
        clientSecret: request.clientSecret,
        tenantId: request.tenantId,
        workspaceId: request.workspaceId,
        scope: request.scope || 'https://analysis.windows.net/powerbi/api/.default',
      };

      const result = await this.powerBiEmbeddedService.getEmbedToken(
        request.reportId,
        powerBiConfig,
      );

      this.logger.debug(`Successfully generated embed token for report: ${request.reportId}`);

      return {
        accessToken: result.accessToken,
        embedUrl: result.embedUrl,
        reportId: result.reportId,
        tokenExpiration: result.tokenExpiration.toISOString(),
      };
    } catch (error) {
      this.logger.error(`Failed to get embed token for report ${request.reportId}:`, error.message);
      throw error;
    }
  }

  @Post('reports')
  @ApiOperation({
    summary: 'Get all Power BI reports in the workspace',
    description:
      'Retrieves a list of all Power BI reports available in the specified workspace. Requires PowerBI configuration in request body.',
  })
  @ApiBody({
    type: PowerBiConfigRequestDto,
    description: 'PowerBI configuration',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved reports',
    type: PowerBiReportsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - PowerBI configuration is required',
  })
  @ApiResponse({
    status: 403,
    description: 'Insufficient permissions to access Power BI resources',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async getReports(@Body() request: PowerBiConfigRequestDto): Promise<PowerBiReportsResponseDto> {
    this.logger.debug('Requesting workspace reports');

    try {
      // Prepare PowerBI configuration
      const powerBiConfig = {
        clientId: request.clientId,
        clientSecret: request.clientSecret,
        tenantId: request.tenantId,
        workspaceId: request.workspaceId,
        scope: request.scope || 'https://analysis.windows.net/powerbi/api/.default',
      };

      const reports = await this.powerBiEmbeddedService.getWorkspaceReports(powerBiConfig);

      this.logger.debug(`Successfully retrieved ${reports.length} reports`);

      return {
        reports: reports.map((report) => ({
          id: report.id,
          name: report.name,
          embedUrl: report.embedUrl,
          webUrl: report.webUrl,
        })),
      };
    } catch (error) {
      this.logger.error('Failed to get workspace reports:', error.message);
      throw error;
    }
  }

  @Delete('token-cache')
  @ApiOperation({
    summary: 'Clear cached Azure AD tokens',
    description:
      'Clears the cached Azure AD token. This will force the service to get a new token on the next request. Useful for testing or troubleshooting.',
  })
  @ApiResponse({
    status: 200,
    description: 'Token cache cleared successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Token cache cleared successfully',
        },
      },
    },
  })
  clearTokenCache(): { message: string } {
    this.logger.debug('Clearing token cache');

    this.powerBiEmbeddedService.clearTokenCache();

    return {
      message: 'Token cache cleared successfully',
    };
  }
}
