import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { StatusCommon } from '../../commons/enums';
import { Province } from './province.entity';
import { BlackListCountry } from '../vessel/entity/black-list-country.entity';
import { GreyListCountry } from '../vessel/entity/grey-list-country.entity';
import { WhiteListCountry } from '../vessel/entity/white-list-country.entity';

@Entity()
export class Country {
  @PrimaryGeneratedColumn()
  public id: number;

  @Column({ type: 'citext', nullable: false, unique: true })
  public code: string; // case insensitive unique --> citext

  @Column({ nullable: true })
  public code3: string;

  @Column({ type: 'citext', nullable: false, unique: true })
  public name: string;

  @Column({ nullable: true })
  public nationality: string;

  @Column({ nullable: true })
  public dialCode: string;

  @Column({ nullable: true })
  public flagImg: string;

  @Column({ type: 'enum', enum: StatusCommon, default: StatusCommon.ACTIVE, nullable: true })
  public status: string;

  @Column({ nullable: true })
  public avatar: string;

  @Column({ type: 'bool', default: false, nullable: true })
  public deleted: boolean;

  @OneToMany(() => Province, (province) => province.country)
  provinces: Province[];

  @CreateDateColumn()
  public createdAt: Date;

  @UpdateDateColumn()
  public updatedAt: Date;

  @OneToOne(() => BlackListCountry, (blackListCountry) => blackListCountry.country)
  blackListCountry: BlackListCountry;

  @OneToOne(() => GreyListCountry, (greyListCountry) => greyListCountry.country)
  greyListCountry: GreyListCountry;

  @OneToOne(() => WhiteListCountry, (whiteListCountry) => whiteListCountry.country)
  whiteListCountry: WhiteListCountry;
}
