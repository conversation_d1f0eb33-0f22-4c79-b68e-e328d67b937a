import { Injectable } from '@nestjs/common';
import { TokenPayloadModel } from 'svm-nest-lib-v3';
import { EmailTypeEnum, MailSendStatus } from '../../commons/enums';
import { EmailProducer } from '../../micro-services/async/email.producer';
import { CompanyRepository } from '../company/company.repository';
import { CreateMailSendDto, UpdateMailSendDto } from './dto';
import { MailSend } from './entity/mail-send.entity';
import { MailSendRepository } from './mail-send.repository';
import { decryptAttachmentValues, hashAttachmentValues } from 'src/commons/functions';

@Injectable()
export class MailSendService {
  constructor(
    private readonly mailSendRepository: MailSendRepository,
    private readonly emailProducer: EmailProducer,
    private readonly companyRepository: CompanyRepository,
  ) {}

  async createMailSend(
    user: TokenPayloadModel,
    id: string,
    body: CreateMailSendDto,
    type?: string,
  ) {
    const mailConfig = await this.companyRepository.getMailConfig(user.companyId);
    const entityParam = {
      ...body,
      from: mailConfig.senderEmail,
      createdUserId: user.id,
      companyId: user.companyId,
    } as MailSend;
    await this.mailSendRepository._validateMailSend(user, entityParam);
    await this.mailSendRepository.createMailSend(id, entityParam, type);

    if (body.status === MailSendStatus.SENDING) {
      // Handle attachments

      const hashAttachments = hashAttachmentValues(body.attachments);
      const attachments = hashAttachments.map((attachment) => {
        return { id: attachment };
      });

      await this.emailProducer.publishEmail([
        {
          from: mailConfig.senderEmail,
          receiver: {
            email: body.to,
          },
          cc: body.cc,
          bcc: body.bcc,
          subject: body.sub,
          attachments,
          html: body.body,
          type:
            type === 'planning-request'
              ? EmailTypeEnum.CREATE_PLANNING_REQUEST
              : EmailTypeEnum.INCIDENT_INVESTIGATION,
        },
      ]);
    }
  }

  async updateMailSend(
    user: TokenPayloadModel,
    id: string,
    mailSendId: string,
    body: UpdateMailSendDto,
    type?: string,
  ) {
    const mailConfig = await this.companyRepository.getMailConfig(user.companyId);
    const entityParam = {
      from: mailConfig.senderEmail,
      ...body,
    } as MailSend;
    await this.mailSendRepository._validateMailSend(user, entityParam, mailSendId);
    await this.mailSendRepository.updateMailSend(id, user.companyId, mailSendId, entityParam);

    if (body.status === MailSendStatus.SENDING) {
      // Handle attachments
      const hashAttachments = hashAttachmentValues(body.attachments);
      const attachments = hashAttachments.map((attachment) => {
        return { id: attachment };
      });
      await this.emailProducer.publishEmail([
        {
          from: mailConfig.senderEmail,
          receiver: {
            email: body.to,
          },
          cc: body.cc,
          bcc: body.bcc,
          subject: body.sub,
          html: body.body,
          attachments,
          type:
            type === 'planning-request'
              ? EmailTypeEnum.UPDATE_PLANNING_REQUEST
              : EmailTypeEnum.UPDATE_INCIDENT_INVESTIGATION,
        },
      ]);
    }
  }

  async getListDraftMail(user: TokenPayloadModel, id: string, type?: string) {
    return this.mailSendRepository.getListDraftMail(user.companyId, id, type);
  }
}
