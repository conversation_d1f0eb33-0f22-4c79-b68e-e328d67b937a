import {
  Get,
  Controller,
  HttpStatus,
  UseGuards,
  Post,
  Body,
  Param,
  ParseUUI<PERSON>ipe,
  Put,
} from '@nestjs/common';
import {
  Roles,
  RoleScope,
  AuthGuard,
  RolesGuard,
  TokenDecorator,
  TokenPayloadModel,
  RequiredPermissions,
} from 'svm-nest-lib-v3';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiBody,
  ApiParam,
} from '@nestjs/swagger';

import { I18n, I18nContext } from 'nestjs-i18n';
import { MailSendService } from './mail-send.service';
import { CreateMailSendDto, UpdateMailSendDto } from './dto';
import { ActionEnum, FeatureEnum, SubFeatureEnum } from '../../commons/enums';

@ApiTags('Mail Send')
@Controller('/mail-send')
@ApiBearerAuth()
@UseGuards(AuthGuard, RolesGuard)
export class MailSendController {
  constructor(private readonly mailSendService: MailSendService) {}

  @ApiParam({ name: 'id', type: 'string', required: true })
  @ApiResponse({
    description: 'Get detail mail send success',
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Get detail mail send' })
  @ApiOperation({ summary: 'List mail send draft', operationId: 'listMailSendDraft' })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions(
    {
      feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.PLANNING_AND_REQUEST,
      action: ActionEnum.EMAIL,
    },
    {
      feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.REPORT_OF_FINDING,
      action: ActionEnum.EMAIL,
    },
    {
      feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.INSPECTION_FOLLOW_UP,
      action: ActionEnum.EMAIL,
    },
    {
      feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.INTERNAL_AUDIT_REPORT,
      action: ActionEnum.EMAIL,
    },
  )
  @Get('/planning-request/:id/mail-send')
  async getListDraftMail(
    @TokenDecorator() user: TokenPayloadModel,
    @Param('id', ParseUUIDPipe) id: string,
  ) {
    const type = 'planning-request';
    return this.mailSendService.getListDraftMail(user, id, type);
  }

  @ApiResponse({ description: 'Create mail send success', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({ summary: 'Create mail send' })
  @ApiBody({ type: CreateMailSendDto, description: 'Create mail send object body' })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions(
    {
      feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.PLANNING_AND_REQUEST,
      action: ActionEnum.EMAIL,
    },
    {
      feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.REPORT_OF_FINDING,
      action: ActionEnum.EMAIL,
    },
    {
      feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.INSPECTION_FOLLOW_UP,
      action: ActionEnum.EMAIL,
    },
    {
      feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.INTERNAL_AUDIT_REPORT,
      action: ActionEnum.EMAIL,
    },
  )
  @Post('/planning-request/:id/mail-send')
  async createMailSend(
    @TokenDecorator() user: TokenPayloadModel,
    @Body() body: CreateMailSendDto,
    @Param('id', ParseUUIDPipe) id: string,
    @I18n() i18n: I18nContext,
  ) {
    const type = 'planning-request';
    await this.mailSendService.createMailSend(user, id, body, type);
    return { message: await i18n.t('common.CREATE_SUCCESS') };
  }

  @ApiResponse({ description: 'Create mail send success', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({ summary: 'Create mail send' })
  @ApiBody({ type: CreateMailSendDto, description: 'Create mail send object body' })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.PLANNING_AND_REQUEST,
    action: ActionEnum.EMAIL,
  })
  @Put('/planning-request/:id/mail-send/:mailSendId')
  async updateMailSend(
    @TokenDecorator() user: TokenPayloadModel,
    @Body() body: UpdateMailSendDto,
    @Param('id', ParseUUIDPipe) id: string,
    @Param('mailSendId', ParseUUIDPipe) mailSendId: string,
    @I18n() i18n: I18nContext,
  ) {
    const type = 'planning-request';
    await this.mailSendService.updateMailSend(user, id, mailSendId, body, type);
    return { message: await i18n.t('common.UPDATE_SUCCESS') };
  }

  @ApiParam({ name: 'id', type: 'string', required: true })
  @ApiResponse({
    description: 'Get detail mail send success',
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Get detail mail send' })
  @ApiOperation({
    summary: 'List mail send draft',
    operationId: 'listMailSendDraftIncidentInvestigation',
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  // @RequiredPermissions({
  //   feature: FeatureEnum.QUALITY_ASSURANCE + '::' + SubFeatureEnum.INCIDENTS,
  //   action: ActionEnum.EMAIL,
  // })
  @Get('/incident-investigation/:id/mail-send')
  async getListDraftMailIncidentInvestigation(
    @TokenDecorator() user: TokenPayloadModel,
    @Param('id', ParseUUIDPipe) id: string,
  ) {
    const type = 'incident-investigation';
    return this.mailSendService.getListDraftMail(user, id, type);
  }

  @ApiResponse({ description: 'Create mail send success', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({ summary: 'Create mail send' })
  @ApiBody({ type: CreateMailSendDto, description: 'Create mail send object body' })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  // @RequiredPermissions({
  //   feature: FeatureEnum.QUALITY_ASSURANCE + '::' + SubFeatureEnum.INCIDENTS,
  //   action: ActionEnum.EMAIL,
  // })
  @Post('/incident-investigation/:id/mail-send')
  async createMailSendIncidentInvestigation(
    @TokenDecorator() user: TokenPayloadModel,
    @Body() body: CreateMailSendDto,
    @Param('id', ParseUUIDPipe) id: string,
    @I18n() i18n: I18nContext,
  ) {
    const type = 'incident-investigation';
    await this.mailSendService.createMailSend(user, id, body, type);
    return { message: await i18n.t('common.CREATE_SUCCESS') };
  }

  @ApiResponse({ description: 'Update mail send success', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({ summary: 'Update mail send' })
  @ApiBody({ type: UpdateMailSendDto, description: 'Update mail send object body' })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  // @RequiredPermissions({
  //   feature: FeatureEnum.QUALITY_ASSURANCE + '::' + SubFeatureEnum.INCIDENTS,
  //   action: ActionEnum.EMAIL,
  // })
  @Put('/incident-investigation/:id/mail-send/:mailSendId')
  async updateMailSendIncidentInvestigation(
    @TokenDecorator() user: TokenPayloadModel,
    @Body() body: UpdateMailSendDto,
    @Param('id', ParseUUIDPipe) id: string,
    @Param('mailSendId', ParseUUIDPipe) mailSendId: string,
    @I18n() i18n: I18nContext,
  ) {
    const type = 'incident-investigation';
    await this.mailSendService.updateMailSend(user, id, mailSendId, body, type);
    return { message: await i18n.t('common.UPDATE_SUCCESS') };
  }
}
