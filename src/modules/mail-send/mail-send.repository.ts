import { EntityRepository, Connection } from 'typeorm';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>gger<PERSON>om<PERSON>,
  TokenPayloadModel,
  TypeORMRepository,
  Utils,
} from 'svm-nest-lib-v3';
import { MailSend } from './entity/mail-send.entity';
import { MailPlanning } from '../mail-template/entity/mail-planning.entity';
import { MailPlanningRepository } from '../mail-template/repository/mail-planning.repository';
import { CompanyRepository } from '../company/company.repository';
import { UpdateMailSendDto } from './dto';
import { MailSendStatus } from '../../commons/enums';
import { MailTemplateRepository } from '../mail-template/repository/mail-template.repository';

@EntityRepository(MailSend)
export class MailSendRepository extends TypeORMRepository<MailSend> {
  constructor(private readonly connection: Connection) {
    super();
  }

  async _checkMailSendCanUpdate(mailSendId) {
    const mailSend = await this.findOne({
      where: {
        id: mailSendId,
        deleted: false,
      },
      select: ['status'],
    });
    if (mailSend.status !== MailSendStatus.DRAFT) {
      throw new BaseError({ status: 400, message: 'mailSend.CAN_NOT_UPDATE' });
    }
  }

  async createMailSend(id: string, entityParam: MailSend, type?: string) {
    try {
      const rs = await this.connection.transaction(async (manager) => {
        // create mail send
        const mailSend = await manager.insert(MailSend, entityParam);

        // create mail planning
        if (type === 'planning-request') {
          await manager.save(MailPlanning, {
            planningRequestId: id,
            mailSendId: mailSend.identifiers[0].id,
          });
        } else if (type === 'incident-investigation') {
          await manager.save(MailPlanning, {
            incidentInvestigationId: id,
            mailSendId: mailSend.identifiers[0].id,
          });
        }
      });
      return rs;
    } catch (ex) {
      LoggerCommon.error('[MailSendRepository] create mail send error ', ex.message || ex);
      throw ex;
    }
  }

  async updateMailSend(
    planningRequestId: string,
    companyId: string,
    mailSendId: string,
    entityParam: MailSend,
  ) {
    try {
      // get mail sender
      const mailConfig = await this.manager
        .getCustomRepository(CompanyRepository)
        .getMailConfig(companyId);

      // Update mail send
      const result = await this.update(
        {
          id: mailSendId,
          status: MailSendStatus.DRAFT,
        },
        {
          from: mailConfig.senderEmail,
          ...entityParam,
        },
      );

      if (result.affected === 0) {
        throw new BaseError({ status: 404, message: 'mailSend.CAN_NOT_UPDATE' });
      }
    } catch (ex) {
      LoggerCommon.error('[MailSendRepository] update mail send error ', ex.message || ex);
      throw ex;
    }
  }

  async getListDraftMail(companyId: string, id: string, type?: string) {
    const queryBuilder = this.createQueryBuilder('mailSend')
      .leftJoin('mailSend.mailPlannings', 'mailPlannings')
      .leftJoin('mailSend.mailTemplate', 'mailTemplate');

    if (type === 'planning-request') {
      queryBuilder.where(
        'mailPlannings.planningRequestId = :planningRequestId AND mailSend.companyId = :companyId AND mailSend.status = :status',
        {
          planningRequestId: id,
          companyId,
          status: MailSendStatus.DRAFT,
        },
      );
    } else if (type === 'incident-investigation') {
      queryBuilder.where(
        'mailPlannings.incidentInvestigationId = :incidentInvestigationId AND mailSend.companyId = :companyId AND mailSend.status = :status',
        {
          incidentInvestigationId: id,
          companyId,
          status: MailSendStatus.DRAFT,
        },
      );
    }
    queryBuilder.select().addSelect(['mailTemplate.name']);

    return this.getManyQB(queryBuilder);
  }

  async _validateMailSend(user: TokenPayloadModel, entityParam: MailSend, mailSendId?: string) {
    const mailSendFound = mailSendId ? await this.findOne({ where: { id: mailSendId } }) : null;
    if (
      entityParam.mailTemplateId &&
      (!mailSendId || entityParam.mailTemplateId !== mailSendFound?.mailTemplateId)
    ) {
      await this.manager
        .getCustomRepository(MailTemplateRepository)
        ._checkActiveMailTemplate([entityParam.mailTemplateId], user.companyId);
    }
  }
}
