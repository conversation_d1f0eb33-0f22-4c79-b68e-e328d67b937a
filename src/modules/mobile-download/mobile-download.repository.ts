import { Injectable } from '@nestjs/common';
import { Logger<PERSON>ommon, Utils } from 'svm-nest-lib-v3';
import { Connection, EntityManager } from 'typeorm';
import { AuditWorkspace } from '../audit-workspace/entities/audit-workspace.entity';
import { FillAuditChecklist } from '../audit-workspace/entities/fill-audit-checklist.entity';
import { CompanyFeatureVersionRepository } from '../commons/company-feature-version/company-feature-version.repository';
import { FeatureVersionConfig } from '../commons/company-feature-version/feature-version.config';
import { DownloadMobileDto } from './dto/download-mobile.dto';
import { replace } from 'lodash';
import { decryptAttachmentValues, hashAttachmentValues } from 'src/commons/functions';
import { SVMSupportService } from 'src/micro-services/sync/svm-support.service';

@Injectable()
export class MobileDownloadRepository {
  constructor(
    private readonly connection: Connection,
    private readonly svmSupportService: SVMSupportService,
  ) {}

  async _getMasterData(params: DownloadMobileDto) {
    const masterData: [
      {
        chk_auditsections: [];
        firstcategorymaster: [];
        seccategorymaster: [];
        thirdcategorymaster: [];
        inspectionnatureoffindings: [];
        inspectionmapping: [];
        categorymapping: [];
      },
    ] = [
      {
        chk_auditsections: [],
        firstcategorymaster: [],
        seccategorymaster: [],
        thirdcategorymaster: [],
        inspectionnatureoffindings: [],
        inspectionmapping: [],
        categorymapping: [],
      },
    ];
    //  main cate gory
    const firstcategorymasterQuery = `SELECT
                        mc.code AS "firstctgcode",
                        mc.name AS "firstctgdescr",
                        'A' as status
                      FROM
                        main_category mc
                      JOIN company c 
                      ON
                        c.id = mc."companyId"
                      WHERE mc.status = 'active'
                       AND c."code" = '${params.reqinput.companycode}'
                       AND mc."deleted" IS FALSE`;
    const firstcategorymaster = await this.connection.query(firstcategorymasterQuery);

    // second category
    const seccategorymasterQuery = `SELECT
                                      sc.code AS "secctgcode",
                                      sc.name AS "secctgdescr",
                                      'A' as status
                                    FROM
                                      second_category sc
                                      JOIN company c 
                                      ON
                                        c.id = sc."companyId"
                                      WHERE sc.status = 'active'
                                       AND c."code" = '${params.reqinput.companycode}'
                                       AND sc."deleted" IS FALSE`;
    const seccategorymaster = await this.connection.query(seccategorymasterQuery);

    //  third category
    const thirdcategorymasterQuery = `SELECT
                                      tc.code AS "thrcatcode",
                                      tc.name AS "thrdcatdescr",
                                      'A' as status
                                    FROM
                                      third_category tc 
                                      JOIN company c 
                                      ON
                                        c.id = tc."companyId"
                                      WHERE tc.status = 'active'
                                       AND c."code" = '${params.reqinput.companycode}'
                                       AND tc."deleted" IS FALSE`;
    const thirdcategorymaster = await this.connection.query(thirdcategorymasterQuery);
    //  Get data

    // inspection mapping nature finding
    const inspectionnatureoffindingsQuery = `SELECT
                                              nf.name AS "noftype",
                                              'A' as "active_status",
                                              nf.code AS "nofcode"
                                            FROM
                                              nature_finding nf 
                                             LEFT JOIN company c 
                                            ON
                                              c.id = nf."companyId"
                                            WHERE nf.status = 'active'
                                             AND (c."code" = '${params.reqinput.companycode}' OR nf."companyId" IS NULL)
                                             AND nf."deleted" IS FALSE`;
    const inspectionnatureoffindings = await this.connection.query(inspectionnatureoffindingsQuery);

    // inspection mapping nature finding
    const inspectionmappingQuery = `SELECT
                                      at2."name" AS "inspectiontypename" ,
                                      at2.code AS "audittype"
                                    FROM
                                      inspection_mapping im
                                    JOIN audit_type at2 
                                    ON
                                      at2.id = im."auditTypeId"
                                    JOIN company c 
                                    ON
                                      c.id = at2."companyId"
                                    WHERE 
                                      c."code" = '${params.reqinput.companycode}'`;
    const inspectionmapping = await this.connection.query(inspectionmappingQuery);

    // category
    const categorymappingQuery = `SELECT
                                    'A' AS "active_status",
                                    mc."code" AS "firstctgcode",
                                    sc."code" AS "secctgcode",
                                    'CTM' || cm."idForMobile" AS "categoryid"
                                  FROM
                                    category_mapping cm
                                  LEFT JOIN company c 
                                  ON
                                    c.id = cm."companyId"
                                  JOIN main_category mc 
                                  ON
                                    mc.id = cm."mainCategoryId"
                                  JOIN second_category sc 
                                  ON
                                    sc.id = cm."secondCategoryId"
                                  WHERE
                                    c.code = '${params.reqinput.companycode}'`;
    const categorymapping = await this.connection.query(categorymappingQuery);

    masterData[0].firstcategorymaster = firstcategorymaster;
    masterData[0].seccategorymaster = seccategorymaster;
    masterData[0].thirdcategorymaster = thirdcategorymaster;
    masterData[0].inspectionnatureoffindings = inspectionnatureoffindings;
    masterData[0].inspectionmapping = inspectionmapping;
    masterData[0].categorymapping = categorymapping;
    return masterData;
  }
  async _getAuditPlanTemplate(params: DownloadMobileDto) {
    try {
      const companyCode = params.reqinput.companycode;
      // AuditPlanTemplate
      const fillChecklistSql = `SELECT
                             fac."id" AS "fillAuditChecklistId",
                             aw."id" AS "auditWorkspaceId",
                             c."id" AS "companyId",
                             c."code" AS "companyCode",
                            pr."id" AS "planingRequestId",
                            pr."auditNo" AS "iauditid",
                            ac.code AS "checklistcode" ,
                            v.code AS "vesselcode",
                            v.name AS "vesselname",
                            co."name" AS "flag",
                            v."imoNumber" AS "imono",
                            v."callSign" AS "callsign",
                            NULL AS "lastdrydockdate",
                            NULL AS "me_type",
                            vt.code AS "vesseltypecode",
                            NULL AS "port_reg",
                            v."shipyardName" AS "shipyard",
                            v."buildDate" AS "built_date",
                            NULL AS "deliverydate",
                            NULL AS "officialno",
                            v."vesselClass" AS "classtype",
                            v."deadWeightTonnage" AS "deadweight",
                            v."teuCapacity" AS "capacity",
                            NULL AS "mcr_kw_rpm",
                            NULL AS "me_makeme_make",
                            'IA' AS "module"
                          FROM
                            fill_audit_checklist fac
                          JOIN audit_workspace aw 
                          ON
                            aw.id = fac."auditWorkspaceId"
                          JOIN planning_request pr 
                          ON
                            pr.id = aw."planningRequestId"
                          JOIN audit_checklist ac 
                          ON
                            ac.id = fac."auditChecklistId"
                          JOIN vessel v 
                          ON
                            v.id = aw."vesselId"
                          JOIN vessel_type vt 
                            ON vt.id = v."vesselTypeId"
                          JOIN company c 
                          ON
                            c.id = pr."companyId"
                          JOIN planning_request_auditor pra
                          ON pr.id = pra."planningRequestId"
                          JOIN "user" u
                          ON u.id = pra."userId"
                          JOIN "country" co
                          ON co.id = v."countryId"
                          WHERE
                            c."code" = $1
                            AND fac.status = 'Yet To Start'
                            AND u.email = '${params.usercode}' `;
      const fillChecklists = await this.connection.query(fillChecklistSql, [companyCode]);

      // create fill-checklist app instance
      const fillChecklistIds = fillChecklists.map(
        (fillChecklist) => fillChecklist.fillAuditChecklistId,
      );

      if (fillChecklistIds.length !== 0) {
        // all fill checklist have same one planId
        const companyId = fillChecklists[0].companyId;
        const companyCode = fillChecklists[0].companyCode;
        const auditWorkspaceId = fillChecklists[0].auditWorkspaceId;

        for (let i = 0; i < fillChecklistIds.length; i++) {
          const fillChecklistId = fillChecklistIds[i];
          const rs = await this.connection.transaction(async (manager) => {
            const awsRefNoCounter = await manager
              .getCustomRepository(CompanyFeatureVersionRepository)
              .getNextVersion({
                manager: manager,
                companyId,
                feature: FeatureVersionConfig.AUDIT_INSPECTION_WORKSPACE_REF_NO,
                year: new Date().getFullYear(),
              });

            await manager.update(
              AuditWorkspace,
              { id: auditWorkspaceId },
              {
                isNew: false,
                refNo: this._genWorkspaceRefNo(
                  awsRefNoCounter,
                  new Date().getFullYear(),
                  companyCode,
                ),
              },
            );

            // const instanceCounter = await manager
            //   .getCustomRepository(CompanyFeatureVersionRepository)
            //   .getNextVersion({
            //     manager: manager,
            //     companyId,
            //     feature: FeatureVersionConfig.FILL_AUDIT_CHECKLIST_INSTANCE,
            //     year: new Date().getFullYear(),
            //   });

            await manager.update(
              FillAuditChecklist,
              { id: fillChecklistId },
              {
                // appInstance: this._geneFillChecklistRefNo(
                //   instanceCounter,
                //   params.reqinput.companycode,
                //   new Date().getFullYear(),
                // ),
                webInstance: null,
                // submitOn: new Date(),
              },
            );
          });
        }
      }

      return fillChecklists;
    } catch (ex) {
      LoggerCommon.error(
        '[mobileDownloadRepository] _getAuditPlanTemplate error ',
        ex.message || ex,
      );
    }
  }

  async _getAuditPlan(fillChecklists, companyCode: string, usercode: string) {
    // const hasFillChkListPrId = fillChecklists.map(
    //   (fillChecklist) => `'${fillChecklist.planingRequestId}'`,
    // );
    // getting a approved planning
    const query = `select pr.id from planning_request pr join audit_workspace aw
    on pr.id = aw."planningRequestId" join company c
    on c.id = pr."companyId" join planning_request_auditor pra
    on pr.id = pra."planningRequestId" join "user" u
    on u.id = pra."userId"
    where c."code" = '${companyCode}' and u.email = '${usercode}' and pr.status = 'approved'`;
    let planningIds = await this.connection.query(query);
    planningIds = planningIds.map((planningId) => `'${planningId.id}'`);
    if (planningIds.length !== 0) {
      // auditplan
      const auditPlanSql = `SELECT
                              pr.id ,
                              pr."auditNo" AS "iauditid" ,
                              pr."refId" AS "auditrefid",
                              NULL AS "deptcode",
                              c.code AS "companycode",
                              v.code AS "vesselcode",
                              v."name" AS "vesselname",
                              vdocc."name" AS "shipmanagementcompany",
                              ac."name" AS "inspectioncompanyname",
                              vt."code" AS "vesseltype",
                              vt."name" AS "vesseltypename",
                              (SELECT jsonb_agg(tem)      
                                FROM
                                (SELECT 
                              at2."code" AS "audittypecode",
                              at2."name" AS "audittypename",
                                array_agg((nf."code" || '##' || nf."name")::TEXT) AS "findinglist"
                              FROM
                                planning_request_audit_type prat
                              JOIN audit_type at2 
                              ON
                                at2.id = prat."auditTypeId"
                              JOIN inspection_mapping im 
                              ON
                                im."auditTypeId" = at2.id
                              JOIN insp_map_nat_finding imnf 
                              ON
                                imnf."inspectionMappingId" = im.id
                              JOIN nature_finding nf 
                              ON
                                imnf."natureFindingId" = nf.id
                              WHERE
                              prat."planningRequestId" = pr.id
                              GROUP BY
                                at2.code ,
                                at2.name) AS tem)
                              AS "audittypforfindings", 
                              ( SELECT
                                string_agg(at2."code", ',')
                              FROM
                                planning_request_audit_type prat
                              JOIN audit_type at2 
                                ON
                                at2.id = prat."auditTypeId"
                              WHERE
                                prat."planningRequestId" = pr.id
                              GROUP BY
                              prat."planningRequestId" )
                              AS "audittype",
                              ( SELECT
                                    string_agg(at2."name", ',')
                                  FROM
                                    planning_request_audit_type prat
                                  JOIN audit_type at2 
                                    ON
                                    at2.id = prat."auditTypeId"
                                  WHERE
                                    prat."planningRequestId" = pr.id
                                  GROUP BY
                                  prat."planningRequestId" ) AS "audittypename",
                              pr."typeOfAudit" AS "typeofreport",
                              fromPort."name" AS "plannedfromport",
                              toPort."name" AS "plannedtoport",
                              '${usercode}' AS "auditorcode",
                              auditor."username" AS "auditorname",
                              ( SELECT
                                string_agg(au."username", ',')
                              FROM "user" au
                              LEFT JOIN planning_request_auditor pra
                              ON pra."userId" = au.id
                              WHERE
                              pra."planningRequestId" = pr.id
                              GROUP BY
                              pra."planningRequestId" ) AS "auditors",  
                              pr."plannedFromDate" AS "planneddate",
                              pr."plannedToDate" AS "plannedtodate",
                              '${usercode}' AS "usercr",
                              pr."createdAt" AS "datecr",
                              updatedUser.username AS "userup",
                              pr."updatedAt" AS "dateup",
                              'IA' AS "moduletype",
                              ARRAY[]::varchar[] AS "userlist",
                              pr.memo AS "memo"
                            FROM
                              planning_request pr
                            LEFT JOIN company c 
                              ON
                              c.id = pr."companyId"
                            LEFT JOIN vessel v 
                              ON
                              v.id = pr."vesselId"
                            LEFT JOIN vessel_doc_holder vdoc
                              ON
                              v.id = vdoc."vesselId"
                            LEFT JOIN company vdocc
                              ON
                              vdocc.id = vdoc."companyId"
                            LEFT JOIN vessel_type vt 
                              ON
                              vt.id = v."vesselTypeId"
                            LEFT JOIN port_master fromPort 
                              ON
                              fromPort.id = pr."fromPortId"
                            LEFT JOIN port_master toPort
                              ON
                              toPort.id = pr."toPortId"
                            JOIN "user" auditor 
                              ON
                              auditor.id = pr."leadAuditorId"
                            LEFT JOIN company ac
                              ON
                              ac.id = auditor."companyId"  
                            JOIN "user" createdUser
                              ON
                              createdUser.id = pr."createdUserId"
                            LEFT JOIN "user" updatedUser
                              ON
                              updatedUser.id = pr."updatedUserId"
                            WHERE
                              c."code" = $1
                              AND vdocc."status" = 'active'
                              AND pr.id IN (${planningIds})
                              AND pr.deleted = FALSE `;

      return await this.connection.query(auditPlanSql, [companyCode]);
    } else {
      return [];
    }
  }

  async _getResponse(checkListCodes: string[]) {
    const data = await this.connection.transaction(async (manager) => {
      const questionattachmentexpirystartfrom = new Date();
      const uniqueChecklists = [...new Set(checkListCodes)];
      const response = [];
      const transactionid = Utils.strings.generateUUID();
      for (let i = 0; i < uniqueChecklists.length; i++) {
        const checklistSessionId = await manager
          .getCustomRepository(CompanyFeatureVersionRepository)
          .getNextVersion({
            manager,
            companyId: '15ab66b4-978e-47b3-98df-7fc205b18bcd', // use session for each time download, session will auto increment that not depend on companyId so hardcode it
            feature: FeatureVersionConfig.SESSION_ID,
          });
        const checkListCode = uniqueChecklists[i];
        // create response item
        const chklistQuery = `SELECT 
                              'audit checklist' AS "name",
                              'APR' AS "approvalStatus",
                              ac."appType" AS "appname",
                              ac."updatedAt" AS "chgdate",
                              ac."name" AS "chklistname",
                              'USC00109' AS "chguser",
                              ac."chkType" AS "type",
                              ac."revisionDate" AS "revisionDate",
                              createdUser.username AS "cruser",
                              ac."createdAt" AS "crdate",
                              ac."revisionNumber" AS "revisionName",
                              ac."publishedDate" AS "publishdate",
                              ac."idForMobile" AS "id",
                              'CHK202000001' AS "checklistsubtype",
                              ac."code" AS "checklistCode",
                              NULL AS "subtypedesc",
                              NULL AS "shareformstoship",
                              ac."validityFrom" AS "validityfrom",
                              ac."validityTo" AS "validityto",
                              c."code" AS "companycode",
                              NULL AS "transfertype",
                              NULL AS "transferdata"
                            FROM
                              audit_checklist ac
                            LEFT JOIN "user" createdUser 
                              ON
                              createdUser.id = ac."updatedUserId"
                              JOIN company c ON 
                              ac."companyId" = c.id
                            WHERE
                              ac.code = $1 `;

        const chklist = await manager.query(chklistQuery, [checkListCode]);
        const checklistsectionquestionsQuery = `SELECT
                                                  cq.id,
                                                  CASE
                                                      WHEN cq."hasRemark" = 'Specific'
                                                  THEN 'Y'
                                                      WHEN cq."hasRemark" = 'All'
                                                  THEN 'Y'
                                                      WHEN cq."hasRemark" IS NULL
                                                  THEN 'N'
                                                    END "hasremarks",
                                                    NULL AS "formats",
                                                    'N' AS "deletedstatus",
                                                    '' AS "ranges",
                                                    cq."minPictureRequired" AS "minpictures",
                                                    NULL AS "allowpictures",
                                                    ${checklistSessionId} AS "sectionid",
                                                    '' AS "units",
                                                    CASE
                                                      WHEN cq."type" = 'Yes/No' 
                                                  THEN 'yes_no'
                                                      WHEN cq."type" = 'Yes/No/NA'
                                                  THEN 'yes_no_na'
                                                      WHEN cq."type" = 'Radio list'
                                                  THEN 'radio_list'
                                                      WHEN cq."type" = 'Combo list'
                                                  THEN 'combo_list'
                                                    END "type",
                                                    CASE
                                                      WHEN cq."isMandatory" = 'true' THEN 'Y'
                                                      ELSE 'N'
                                                    END "mandatory",
                                                    CASE
                                                      WHEN cq."type" IN ('Yes/No', 'Yes/No/NA')THEN ''
                                                      ELSE (
                                                      SELECT
                                                        string_agg("content" , ',')
                                                      FROM
                                                        chk_question_answer cqa
                                                      WHERE
                                                        cqa."chkQuestionId" = cq.id)
                                                    END "allowedvalues",
                                                    'SVM' AS "cruser",
                                                    REPLACE(cq."hint",'\n',' ')  AS "hint",
                                                    cq."createdAt" AS "crdate",
                                                    cq.question AS "questionname",
                                                    cq."idForMobile" AS "id",
                                                    cq."companyMixCode" AS "companymixcode", 
                                                    cq."vesselTypeMixCode" AS "vesseltypemixCode",
                                                    cq."order" AS "position",
                                                    ac."idForMobile" AS "checklistid",
                                                    cq."attachments" AS "attachment",
                                                    CASE
                                                        WHEN cq."hasRemark" = 'Specific'
                                                    THEN (	SELECT
                                                        '[' || string_agg( '\"' ||  (SELECT CASE
                                                          WHEN cqa."content" = 'Yes' THEN 'YES'
                                                          WHEN cqa."content" = 'No' THEN 'NO'
                                                          ELSE cqa."content"
                                                          END "answer") || '\"', ',') || ']'
                                                        FROM
                                                          chk_question_answer cqa
                                                        WHERE
                                                        "chkQuestionId" = cq.id AND cqa.id = ANY (cq."remarkSpecificAnswers") )
                                                        WHEN cq."hasRemark" = 'All'
                                                    THEN 'A'
                                                        WHEN cq."hasRemark" IS NULL
                                                    THEN 'N'
                                                      END "mandatorydata",
                                                    '{\"Main Category\":\"' || mc.code || '##' || mc."name" || '\"' || ',\"Location\":\"' || l.code || '##' || l."name" || '\"' || (
                                                      SELECT
                                                        CASE
                                                          WHEN string_agg(v."refNo", '') IS NULL THEN ''
                                                          ELSE ',\"VIQ\":\"' || string_agg(v."refNo", '') || '##' || string_agg(v."refNo", '') || '\"'
                                                        END ||
                                                        CASE
                                                          WHEN string_agg(tc."code", '') IS NULL THEN ''
                                                          ELSE ',\"2nd Sub Category\":\"' || string_agg(tc."code", '') || '##' || string_agg(tc."name", '') || '\"'
                                                        END ||
                                                        CASE
                                                          WHEN string_agg(sc."code", '') IS NULL THEN ''
                                                          ELSE ',\"1st Sub Category\":\"' || string_agg(sc."code", '') || '##' || string_agg(sc."name", '') || '\"'
                                                        END ||
                                                        CASE
                                                          WHEN string_agg(c."code", '') IS NULL THEN ''
                                                          ELSE ',\"CDI\":\"' || string_agg(c."code", '') || '##' || string_agg(c."name", '') || '\"'
                                                        END ||
                                                        CASE
                                                          WHEN string_agg(co."code", '') IS NULL THEN ''
                                                          ELSE ',\"Charterer/Owner\":\"' || string_agg(co."code", '') || '##' || string_agg(co."name", '') || '\"'
                                                        END ||
                                                        CASE
                                                          WHEN SUM(pm."order") IS NULL THEN ''
                                                          ELSE ',\"Potential Risk\":\"' || SUM(pm."order") || '##' || string_agg(pm.risk, '') || '\"'
                                                        END ||
                                                        CASE
                                                          WHEN string_agg(sd."code", '') IS NULL THEN ''
                                                          ELSE ',\"Shore Department\":\"[' || string_agg(sd."code", ',') || '##' || string_agg(sd."name", ',') || ']\"'
                                                        END ||
                                                        CASE
                                                          WHEN string_agg(shd."code", '') IS NULL THEN ''
                                                          ELSE ',\"Ship Department\":\"[' || string_agg(shd."code", ',') || '##' || string_agg(shd."name", ',') || ']\"'
                                                        END ||
                                                        CASE
                                                          WHEN string_agg(vt."code", '') IS NULL THEN ''
                                                          ELSE ',\"Ship Type\":\"[' || string_agg(vt."code", ',') || '##' || string_agg(vt."name", ',') || ']\"'
                                                        END ||
                                                        CASE
                                                          WHEN string_agg(sr."code", '') IS NULL THEN ''
                                                          ELSE ',\"Shore Ranks\":\"[' || string_agg(sr."code", ',') || '##' || string_agg(sr."name", ',') || ']\"'
                                                        END ||                                                                                                   
                                                        CASE
                                                        WHEN string_agg(info.value, '') IS NULL THEN ''
                                                          ELSE ',\"Infor\":\"' || string_agg( REPLACE(REPLACE(REPLACE(info.value, '"', '\\"'), '\n', ' '), '\t', '    '), '') || '##' || string_agg( REPLACE(REPLACE(REPLACE(info.value, '"', '\\"'), '\n', ' '), '\t', '    '), '') || '\"'
                                                        END ||
                                                        CASE
                                                          WHEN string_agg(reg.value, '') IS NULL THEN ''
                                                          ELSE ',\"Reg\":\"' || string_agg(REPLACE (reg.value, '\n',' '), '') || '##' || string_agg(REPLACE (reg.value, '\n',' '), '') || '\"'
                                                        END ||
                                                        CASE
                                                          WHEN string_agg(criticality.value, '') IS NULL THEN ''
                                                          ELSE ',\"criticality\":\"' || string_agg(criticality.value, '') || '##' || string_agg(criticality.value, '') || '\"'
                                                        END ||
                                                        CASE
                                                          WHEN string_agg(shr."code", '') IS NULL THEN ''
                                                          ELSE ',\"Ship Ranks Management\":\"[' || string_agg(shr."code", ',') || '##' || string_agg(shr."name", ',') || ']\"'
                                                        END
                                                      FROM
                                                        chk_question_master_table cqmt
                                                      LEFT JOIN cdi c 
                                                      ON
                                                        c.id = "valueId"
                                                        AND "masterTableId" = 'cdi'
                                                      LEFT JOIN viq v 
                                                      ON
                                                        v.id = cqmt."valueId"
                                                        AND cqmt."masterTableId" = 'viq'
                                                      LEFT JOIN third_category tc 
                                                      ON
                                                        tc.id = cqmt."valueId"
                                                        AND cqmt."masterTableId" = 'third-category'
                                                      LEFT JOIN second_category sc 
                                                      ON
                                                        sc.id = cqmt."valueId"
                                                        AND cqmt."masterTableId" = 'second-category'
                                                      LEFT JOIN charter_owner co 
                                                      ON
                                                        co.id = cqmt."valueId"
                                                        AND cqmt."masterTableId" = 'charter-owner'
                                                      LEFT JOIN priority_master pm 
                                                      ON
                                                        pm.id = cqmt."valueId"
                                                        AND cqmt."masterTableId" = 'potential-risk'
                                                      LEFT JOIN department sd 
                                                      ON
                                                        sd."id" = cqmt."valueId"
                                                        AND cqmt."masterTableId" = 'shore-department'
                                                      LEFT JOIN department shd 
                                                      ON
                                                        shd."id" = cqmt."valueId"
                                                        AND cqmt."masterTableId" = 'ship-department'
                                                      LEFT JOIN vessel_type vt 
                                                      ON
                                                        vt.id = cqmt."valueId"
                                                        AND cqmt."masterTableId" = 'vessel-type'
                                                      LEFT JOIN chk_question_master_table info
                                                      ON
                                                      info.id  = cqmt.id
                                                      AND info."masterTableId" = 'info'
                                                      LEFT JOIN chk_question_master_table reg
                                                      ON
                                                      reg.id  = cqmt.id
                                                      AND reg."masterTableId" = 'reg'
                                                      LEFT JOIN chk_question_master_table criticality
                                                      ON
                                                      criticality.id  = cqmt.id
                                                      AND criticality."masterTableId" = 'criticality'
                                                      LEFT JOIN "rank" sr 
                                                      ON
                                                        sr.id = cqmt."valueId"
                                                        AND cqmt."masterTableId" = 'shore-rank'
                                                      LEFT JOIN "rank" shr 
                                                      ON
                                                        shr.id = cqmt."valueId"
                                                        AND cqmt."masterTableId" = 'ship-rank'
                                                      WHERE
                                                        cqmt."chkQuestionId" = cq.id) || '}' AS "category",
                                                    cq."code" AS "questcode",
                                                    REPLACE (cq."ratingCriteria", '\n', ' ')  AS "ratingcriteria",
                                                    t."code" || '##' || t."name" AS "topic",
                                                    l."code" || '##' || l."name" AS "location",
                                                    'N' AS "isdmsdocattached",
                                                    NULL AS "rangeto",
                                                    NULL AS "isallowdecimal",
                                                    ARRAY[]::varchar[] AS "sectionsubquestions"
                                                  FROM
                                                    chk_question cq
                                                  JOIN audit_checklist ac 
                                                    ON
                                                    ac.id = cq."auditChecklistId"
                                                  JOIN "user" u 
                                                      ON
                                                    u.id = cq."createdUserId"
                                                  JOIN topic t 
                                                      ON
                                                    t.id = cq."topicId"
                                                  JOIN "location" l 
                                                    ON
                                                    l.id = cq."locationId"
                                                  JOIN main_category mc 
                                                    ON
                                                    mc.id = cq."mainCategoryId"
                                                  WHERE
                                                    ac.code = $1
                                                  ORDER BY
                                                    cq.order ASC
                                                    `;

        await this._supportSyncQuestionAttachment(
          manager,
          checklistsectionquestionsQuery,
          checkListCode,
          response,
          transactionid,
          chklist,
          checklistSessionId,
          questionattachmentexpirystartfrom,
        );
      }

      return response;
    });

    return data;
  }

  async getDownloadData(params: DownloadMobileDto) {
    // need change company code to parrent if login by child company
    params.reqinput.companycode = await this._applyParrentCompanyCode(params.reqinput.companycode);
    const masterdata = await this._getMasterData(params);
    const AuditPlanTemplate = await this._getAuditPlanTemplate(params);
    const checkListCodes = AuditPlanTemplate.map((i) => i.checklistcode);
    const auditplan = await this._getAuditPlan(
      AuditPlanTemplate,
      params.reqinput.companycode,
      params.usercode,
    );
    const response = await this._getResponse(checkListCodes);

    const data = {
      masterdata,
      AuditPlanTemplate,
      auditplan,
      response,
      status: 200,
      message: null,
      servertime: new Date(),
      cancelledplan: [],
      planidlist: [],
    };

    const dataJson = JSON.stringify(data);
    return JSON.parse(replace(dataJson, RegExp('\t', 'g'), ''));
  }

  private _genWorkspaceRefNo(counter: number, currYear: number, vesselsCode: string) {
    return `${vesselsCode}/IW/${counter}/${currYear}`;
  }

  private async _applyParrentCompanyCode(companyCode: string) {
    const rawQuery = `SELECT
                      CASE 
                        WHEN c."parentId" IS NOT NULL THEN parent."code"
                        ELSE c."code" 
                      END code
                      FROM company c 
                      LEFT JOIN company parent 
                      ON c."parentId" = parent.id
                      WHERE c.code = $1`;

    const data = await this.connection.query(rawQuery, [companyCode]);
    return data[0].code;
  }

  async syncQuestionAttachments(checkListCodes: string[], sectionId: number) {
    try {
      const data = await this.connection.transaction(async (manager) => {
        const questionattachmentexpirystartfrom = new Date();
        const uniqueChecklists = [...new Set(checkListCodes)];
        const response = [];
        const transactionid = Utils.strings.generateUUID();
        for (let i = 0; i < uniqueChecklists.length; i++) {
          // in the sync API there is no need to generate the section id
          // const checklistSessionId = await manager
          //   .getCustomRepository(CompanyFeatureVersionRepository)
          //   .getNextVersion({
          //     manager,
          //     companyId: '15ab66b4-978e-47b3-98df-7fc205b18bcd', // use session for each time download, session will auto increment that not depend on companyId so hardcode it
          //     feature: FeatureVersionConfig.SESSION_ID,
          //   });
          const checkListCode = uniqueChecklists[i];
          // create response item
          const chklistQuery = `SELECT 
                              'audit checklist' AS "name",
                              'APR' AS "approvalStatus",
                              ac."appType" AS "appname",
                              ac."updatedAt" AS "chgdate",
                              ac."name" AS "chklistname",
                              'USC00109' AS "chguser",
                              ac."chkType" AS "type",
                              ac."revisionDate" AS "revisionDate",
                              createdUser.username AS "cruser",
                              ac."createdAt" AS "crdate",
                              ac."revisionNumber" AS "revisionName",
                              ac."publishedDate" AS "publishdate",
                              ac."idForMobile" AS "id",
                              'CHK202000001' AS "checklistsubtype",
                              ac."code" AS "checklistCode",
                              NULL AS "subtypedesc",
                              NULL AS "shareformstoship",
                              ac."validityFrom" AS "validityfrom",
                              ac."validityTo" AS "validityto",
                              c."code" AS "companycode",
                              NULL AS "transfertype",
                              NULL AS "transferdata"
                            FROM
                              audit_checklist ac
                            LEFT JOIN "user" createdUser 
                              ON
                              createdUser.id = ac."updatedUserId"
                              JOIN company c ON 
                              ac."companyId" = c.id
                            WHERE
                              ac.code = $1 `;

          const chklist = await manager.query(chklistQuery, [checkListCode]);
          const checklistsectionquestionsQuery = `SELECT
                                                  cq.id,
                                                  CASE
                                                      WHEN cq."hasRemark" = 'Specific'
                                                  THEN 'Y'
                                                      WHEN cq."hasRemark" = 'All'
                                                  THEN 'Y'
                                                      WHEN cq."hasRemark" IS NULL
                                                  THEN 'N'
                                                    END "hasremarks",
                                                    NULL AS "formats",
                                                    'N' AS "deletedstatus",
                                                    '' AS "ranges",
                                                    cq."minPictureRequired" AS "minpictures",
                                                    NULL AS "allowpictures",
                                                    ${sectionId} AS "sectionid",
                                                    '' AS "units",
                                                    CASE
                                                      WHEN cq."type" = 'Yes/No' 
                                                  THEN 'yes_no'
                                                      WHEN cq."type" = 'Yes/No/NA'
                                                  THEN 'yes_no_na'
                                                      WHEN cq."type" = 'Radio list'
                                                  THEN 'radio_list'
                                                      WHEN cq."type" = 'Combo list'
                                                  THEN 'combo_list'
                                                    END "type",
                                                    CASE
                                                      WHEN cq."isMandatory" = 'true' THEN 'Y'
                                                      ELSE 'N'
                                                    END "mandatory",
                                                    CASE
                                                      WHEN cq."type" IN ('Yes/No', 'Yes/No/NA')THEN ''
                                                      ELSE (
                                                      SELECT
                                                        string_agg("content" , ',')
                                                      FROM
                                                        chk_question_answer cqa
                                                      WHERE
                                                        cqa."chkQuestionId" = cq.id)
                                                    END "allowedvalues",
                                                    'SVM' AS "cruser",
                                                    REPLACE(cq."hint",'\n',' ')  AS "hint",
                                                    cq."createdAt" AS "crdate",
                                                    cq.question AS "questionname",
                                                    cq."idForMobile" AS "id",
                                                    cq."companyMixCode" AS "companymixcode", 
                                                    cq."vesselTypeMixCode" AS "vesseltypemixCode",
                                                    cq."order" AS "position",
                                                    ac."idForMobile" AS "checklistid",
                                                    cq."attachments" AS "attachment",
                                                    CASE
                                                        WHEN cq."hasRemark" = 'Specific'
                                                    THEN (	SELECT
                                                        '[' || string_agg( '\"' ||  (SELECT CASE
                                                          WHEN cqa."content" = 'Yes' THEN 'YES'
                                                          WHEN cqa."content" = 'No' THEN 'NO'
                                                          ELSE cqa."content"
                                                          END "answer") || '\"', ',') || ']'
                                                        FROM
                                                          chk_question_answer cqa
                                                        WHERE
                                                        "chkQuestionId" = cq.id AND cqa.id = ANY (cq."remarkSpecificAnswers") )
                                                        WHEN cq."hasRemark" = 'All'
                                                    THEN 'A'
                                                        WHEN cq."hasRemark" IS NULL
                                                    THEN 'N'
                                                      END "mandatorydata",
                                                    '{\"Main Category\":\"' || mc.code || '##' || mc."name" || '\"' || ',\"Location\":\"' || l.code || '##' || l."name" || '\"' || (
                                                      SELECT
                                                        CASE
                                                          WHEN string_agg(v."refNo", '') IS NULL THEN ''
                                                          ELSE ',\"VIQ\":\"' || string_agg(v."refNo", '') || '##' || string_agg(v."refNo", '') || '\"'
                                                        END ||
                                                        CASE
                                                          WHEN string_agg(tc."code", '') IS NULL THEN ''
                                                          ELSE ',\"2nd Sub Category\":\"' || string_agg(tc."code", '') || '##' || string_agg(tc."name", '') || '\"'
                                                        END ||
                                                        CASE
                                                          WHEN string_agg(sc."code", '') IS NULL THEN ''
                                                          ELSE ',\"1st Sub Category\":\"' || string_agg(sc."code", '') || '##' || string_agg(sc."name", '') || '\"'
                                                        END ||
                                                        CASE
                                                          WHEN string_agg(c."code", '') IS NULL THEN ''
                                                          ELSE ',\"CDI\":\"' || string_agg(c."code", '') || '##' || string_agg(c."name", '') || '\"'
                                                        END ||
                                                        CASE
                                                          WHEN string_agg(co."code", '') IS NULL THEN ''
                                                          ELSE ',\"Charterer/Owner\":\"' || string_agg(co."code", '') || '##' || string_agg(co."name", '') || '\"'
                                                        END ||
                                                        CASE
                                                          WHEN SUM(pm."order") IS NULL THEN ''
                                                          ELSE ',\"Potential Risk\":\"' || SUM(pm."order") || '##' || string_agg(pm.risk, '') || '\"'
                                                        END ||
                                                        CASE
                                                          WHEN string_agg(sd."code", '') IS NULL THEN ''
                                                          ELSE ',\"Shore Department\":\"[' || string_agg(sd."code", ',') || '##' || string_agg(sd."name", ',') || ']\"'
                                                        END ||
                                                        CASE
                                                          WHEN string_agg(shd."code", '') IS NULL THEN ''
                                                          ELSE ',\"Ship Department\":\"[' || string_agg(shd."code", ',') || '##' || string_agg(shd."name", ',') || ']\"'
                                                        END ||
                                                        CASE
                                                          WHEN string_agg(vt."code", '') IS NULL THEN ''
                                                          ELSE ',\"Ship Type\":\"[' || string_agg(vt."code", ',') || '##' || string_agg(vt."name", ',') || ']\"'
                                                        END ||
                                                        CASE
                                                          WHEN string_agg(sr."code", '') IS NULL THEN ''
                                                          ELSE ',\"Shore Ranks\":\"[' || string_agg(sr."code", ',') || '##' || string_agg(sr."name", ',') || ']\"'
                                                        END ||                                                                                                   
                                                        CASE
                                                        WHEN string_agg(info.value, '') IS NULL THEN ''
                                                          ELSE ',\"Infor\":\"' || string_agg( REPLACE(REPLACE(REPLACE(info.value, '"', '\\"'), '\n', ' '), '\t', '    '), '') || '##' || string_agg( REPLACE(REPLACE(REPLACE(info.value, '"', '\\"'), '\n', ' '), '\t', '    '), '') || '\"'
                                                        END ||
                                                        CASE
                                                          WHEN string_agg(reg.value, '') IS NULL THEN ''
                                                          ELSE ',\"Reg\":\"' || string_agg(REPLACE (reg.value, '\n',' '), '') || '##' || string_agg(REPLACE (reg.value, '\n',' '), '') || '\"'
                                                        END ||
                                                        CASE
                                                          WHEN string_agg(criticality.value, '') IS NULL THEN ''
                                                          ELSE ',\"criticality\":\"' || string_agg(criticality.value, '') || '##' || string_agg(criticality.value, '') || '\"'
                                                        END ||
                                                        CASE
                                                          WHEN string_agg(shr."code", '') IS NULL THEN ''
                                                          ELSE ',\"Ship Ranks Management\":\"[' || string_agg(shr."code", ',') || '##' || string_agg(shr."name", ',') || ']\"'
                                                        END
                                                      FROM
                                                        chk_question_master_table cqmt
                                                      LEFT JOIN cdi c 
                                                      ON
                                                        c.id = "valueId"
                                                        AND "masterTableId" = 'cdi'
                                                      LEFT JOIN viq v 
                                                      ON
                                                        v.id = cqmt."valueId"
                                                        AND cqmt."masterTableId" = 'viq'
                                                      LEFT JOIN third_category tc 
                                                      ON
                                                        tc.id = cqmt."valueId"
                                                        AND cqmt."masterTableId" = 'third-category'
                                                      LEFT JOIN second_category sc 
                                                      ON
                                                        sc.id = cqmt."valueId"
                                                        AND cqmt."masterTableId" = 'second-category'
                                                      LEFT JOIN charter_owner co 
                                                      ON
                                                        co.id = cqmt."valueId"
                                                        AND cqmt."masterTableId" = 'charter-owner'
                                                      LEFT JOIN priority_master pm 
                                                      ON
                                                        pm.id = cqmt."valueId"
                                                        AND cqmt."masterTableId" = 'potential-risk'
                                                      LEFT JOIN department sd 
                                                      ON
                                                        sd."id" = cqmt."valueId"
                                                        AND cqmt."masterTableId" = 'shore-department'
                                                      LEFT JOIN department shd 
                                                      ON
                                                        shd."id" = cqmt."valueId"
                                                        AND cqmt."masterTableId" = 'ship-department'
                                                      LEFT JOIN vessel_type vt 
                                                      ON
                                                        vt.id = cqmt."valueId"
                                                        AND cqmt."masterTableId" = 'vessel-type'
                                                      LEFT JOIN chk_question_master_table info
                                                      ON
                                                      info.id  = cqmt.id
                                                      AND info."masterTableId" = 'info'
                                                      LEFT JOIN chk_question_master_table reg
                                                      ON
                                                      reg.id  = cqmt.id
                                                      AND reg."masterTableId" = 'reg'
                                                      LEFT JOIN chk_question_master_table criticality
                                                      ON
                                                      criticality.id  = cqmt.id
                                                      AND criticality."masterTableId" = 'criticality'
                                                      LEFT JOIN "rank" sr 
                                                      ON
                                                        sr.id = cqmt."valueId"
                                                        AND cqmt."masterTableId" = 'shore-rank'
                                                      LEFT JOIN "rank" shr 
                                                      ON
                                                        shr.id = cqmt."valueId"
                                                        AND cqmt."masterTableId" = 'ship-rank'
                                                      WHERE
                                                        cqmt."chkQuestionId" = cq.id) || '}' AS "category",
                                                    cq."code" AS "questcode",
                                                    REPLACE (cq."ratingCriteria", '\n', ' ')  AS "ratingcriteria",
                                                    t."code" || '##' || t."name" AS "topic",
                                                    l."code" || '##' || l."name" AS "location",
                                                    'N' AS "isdmsdocattached",
                                                    NULL AS "rangeto",
                                                    NULL AS "isallowdecimal",
                                                    ARRAY[]::varchar[] AS "sectionsubquestions"
                                                  FROM
                                                    chk_question cq
                                                  JOIN audit_checklist ac 
                                                    ON
                                                    ac.id = cq."auditChecklistId"
                                                  JOIN "user" u 
                                                      ON
                                                    u.id = cq."createdUserId"
                                                  JOIN topic t 
                                                      ON
                                                    t.id = cq."topicId"
                                                  JOIN "location" l 
                                                    ON
                                                    l.id = cq."locationId"
                                                  JOIN main_category mc 
                                                    ON
                                                    mc.id = cq."mainCategoryId"
                                                  WHERE
                                                    ac.code = $1
                                                    `;

          await this._supportSyncQuestionAttachment(
            manager,
            checklistsectionquestionsQuery,
            checkListCode,
            response,
            transactionid,
            chklist,
            sectionId,
            questionattachmentexpirystartfrom,
          );
        }

        return {
          response,
          status: 200,
          message: 'success',
          servertime: new Date(),
        };
      });
      return data;
    } catch (err) {
      return {
        status: 400,
        message: err.message || err,
        servertime: new Date(),
      };
    }
  }
  async _supportSyncQuestionAttachment(
    manager: EntityManager,
    checklistsectionquestionsQuery,
    checkListCode,
    response,
    transactionid,
    chklist,
    sectionId,
    questionattachmentexpirystartfrom,
  ) {
    const checklistsectionquestions = await manager.query(checklistsectionquestionsQuery, [
      checkListCode,
    ]);
    // to fetch attachments details from support service and push into the reponse
    for (let i = 0; i < checklistsectionquestions.length; i++) {
      if (checklistsectionquestions[i].attachment.length > 0) {
        const arrayOfAttachments: any[] = [];
        const encrypted = hashAttachmentValues(checklistsectionquestions[i].attachment);
        const attachmentdata: any = await this.svmSupportService.getDetailImage(encrypted);
        for (let j = 0; j < attachmentdata.length; j++) {
          const id = decryptAttachmentValues([attachmentdata[j].id]);
          const doctype = attachmentdata[j].originName.split('.');
          const attachmentKeys = {
            filename: attachmentdata[j].originName,
            chkcode: checkListCode,
            basepath: attachmentdata[j].link,
            docrefno: id[0],
            doctype: doctype[doctype.length - 1],
            docsize: attachmentdata[j].size,
            uniqueidentifier: attachmentdata[j].key.split('/')[2].split('.')[0],
            questionid: checklistsectionquestions[i].id,
          };
          arrayOfAttachments.push(attachmentKeys);
        }
        Object.assign(checklistsectionquestions[i].attachment, arrayOfAttachments);
      }
    }
    response.push({
      transactionid,
      chklist: chklist[0],
      planid: null,
      checklistheaders: [],
      checklistnotes: [],
      checklistsection: [
        {
          deletedstatus: 'N',
          id: sectionId,
          position: 0,
          type: 'regular',
          mandatory: 'Y',
          checklistid: chklist[0].id,
          sectionname: 'no name',
          secstatus: 'APR',
          category: '{}',
          categoryqueid: null,
          checklistsectioncolumns: [],
          checklistsectionnotes: [],
          sidcompdata: [],
          questionattachmentexpirystartfrom,
          isattachments: !!checklistsectionquestions?.every((item) => {
            return item?.attachment?.length > 0;
          }),
          checklistsectionquestions,
        },
      ],
      checklistsectionquestions: [],
      checklistsectioncolumns: [],
      checklistsectionnotes: [],
      sectionsubquestions: [],
      checklistinstance: [],
      checklistimage: [],
      visittype: [],
      reference: [],
      inspectiontype: [],
      chk_auditsections: {
        reference: [
          {
            grouptype: 'R',
            typecode: 'F002',
            fieldname: 'Location',
          },
          {
            grouptype: 'R',
            typecode: 'F016',
            fieldname: 'Main Category',
          },
          {
            grouptype: 'R',
            typecode: 'F017',
            fieldname: '1st Sub Category',
          },
          {
            grouptype: 'R',
            typecode: 'F018',
            fieldname: '2nd Sub Category',
          },
          {
            grouptype: 'R',
            typecode: 'F007',
            fieldname: 'CDI',
          },
          {
            grouptype: 'R',
            typecode: 'F010',
            fieldname: 'Charterer/Owner',
          },
          {
            grouptype: 'R',
            typecode: 'F019',
            fieldname: 'Criticality',
          },
          {
            grouptype: 'R',
            typecode: 'F021',
            fieldname: 'Reg',
          },
          {
            grouptype: 'R',
            typecode: 'F022',
            fieldname: 'Infor',
          },
          {
            grouptype: 'R',
            typecode: 'F009',
            fieldname: 'Others',
          },
          {
            grouptype: 'R',
            typecode: 'F020',
            fieldname: 'Potential Risk',
          },
          {
            grouptype: 'R',
            typecode: 'F013',
            fieldname: 'Ship Department',
          },
          {
            grouptype: 'R',
            typecode: 'F014',
            fieldname: 'Ship Direct Responsible',
          },
          {
            grouptype: 'R',
            typecode: 'F015',
            fieldname: 'Ship Ranks Management',
          },
          {
            grouptype: 'R',
            typecode: 'F001',
            fieldname: 'Ship Type',
          },
          {
            grouptype: 'R',
            typecode: 'F011',
            fieldname: 'Shore Department',
          },
          {
            grouptype: 'R',
            typecode: 'F012',
            fieldname: 'Shore Ranks',
          },
          {
            grouptype: 'R',
            typecode: 'F005',
            fieldname: 'SMS',
          },
          {
            grouptype: 'R',
            typecode: 'F006',
            fieldname: 'VIQ',
          },
        ],
        inspectiontype: [],
        visittype: [
          {
            grouptype: 'V',
            typecode: 'Port',
            visittypename: null,
          },
          {
            grouptype: 'V',
            typecode: 'Sailing',
            visittypename: null,
          },
          {
            grouptype: 'V',
            typecode: 'VIT001',
            visittypename: null,
          },
          {
            grouptype: 'V',
            typecode: 'VIT0011',
            visittypename: null,
          },
          {
            grouptype: 'V',
            typecode: 'VIT002',
            visittypename: null,
          },
          {
            grouptype: 'V',
            typecode: 'VIT003',
            visittypename: null,
          },
          {
            grouptype: 'V',
            typecode: 'VIT004',
            visittypename: null,
          },
          {
            grouptype: 'V',
            typecode: 'VIT005',
            visittypename: null,
          },
          {
            grouptype: 'V',
            typecode: 'VT1010',
            visittypename: null,
          },
        ],
      },
      reportoffindingsmaster: [],
      sidcompdata: [],
    });
  }
}
