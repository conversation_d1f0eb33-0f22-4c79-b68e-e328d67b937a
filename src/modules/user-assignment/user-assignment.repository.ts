import { TypeORMRepository } from 'svm-nest-lib-v3';
import { Connection, EntityManager, EntityRepository } from 'typeorm';
import { MySet } from '../../utils';
import { CreateUserAssignmentDTO, UserPermissionDTO } from './dto';
import { UserAssignment } from './user-assignment.entity';
import {
  ModuleType,
  MODULE_TYPE_MAP_PERMISSIONS,
  WorkflowPermission,
} from './user-assignment.enum';
import { PlanningRequestStatus } from 'src/commons/enums';

@EntityRepository(UserAssignment)
export class UserAssignmentRepository extends TypeORMRepository<UserAssignment> {
  constructor(private readonly connection: Connection) {
    super();
  }

  async testCreate() {
    return this.save({
      planningRequestId: 'a5c24328-4c8a-4c2d-b420-80835ecc5280',
      userId: 'ed7b2f2a-ffa0-4b6e-bb38-3c8f6c8760ac',
      permission: 'owner/manager',
    });
  }
  async createUserAssignment(params: CreateUserAssignmentDTO) {
    try {
      const listUserAndPermission = params.usersPermissions;
      Reflect.deleteProperty(params, 'usersPermissions');
      const preparedData = [];
      for (const userPermission of listUserAndPermission) {
        for (const userId of userPermission.userIds) {
          const userAssignment = {
            ...params,
            userId,
            permission: userPermission.permission,
          };
          if (userPermission?.isView) {
            userAssignment['isView'] = userPermission.isView;
          }
          if (userPermission?.isEdit) {
            userAssignment['isEdit'] = userPermission.isEdit;
          }
          preparedData.push(userAssignment);
        }
      }
      await this.save(preparedData);
      return preparedData;
    } catch (ex) {
      throw ex;
    }
  }

  // async updateUserAssignment() {}

  async updateUserAssignment(
    managerTrans: EntityManager,
    moduleName: string,
    recordId: string,
    userPermissionParams: UserPermissionDTO[],
    status?: string,
    reassignFrom?: string,
  ) {
    let recordCondition;
    let preparedCreateTemp;
    switch (moduleName) {
      case ModuleType.PLANNING_REQUEST:
        recordCondition = `userAssignment.planningRequestId`;
        preparedCreateTemp = { planningRequestId: recordId };
        break;
      case ModuleType.AUDIT_CHECKLIST:
        recordCondition = `userAssignment.auditChecklistId`;
        preparedCreateTemp = { auditChecklistId: recordId };
        break;
      case ModuleType.REPORT_FINDING:
        recordCondition = `userAssignment.reportFindingFormId`;
        preparedCreateTemp = { reportFindingFormId: recordId };
        break;
      case ModuleType.INTERNAL_AUDIT_REPORT:
        recordCondition = `userAssignment.internalAuditReportId`;
        preparedCreateTemp = { internalAuditReportId: recordId };
        break;
      case ModuleType.SELF_ASSESSMENT:
        recordCondition = `userAssignment.selfAssessmentId`;
        preparedCreateTemp = { selfAssessmentId: recordId };
        break;
      case ModuleType.INCIDENTS:
        recordCondition = `userAssignment.incidentInvestigationId`;
        preparedCreateTemp = { incidentInvestigationId: recordId };
        break;
    }

    const qb = this.createQueryBuilder('userAssignment').where(`${recordCondition} = :recordId`, {
      recordId,
    });
    const listCurrentUA = await this.getManyQB(qb);

    const listCurrentUserAndPermission: UserPermissionDTO[] = [];
    const validPermissions: WorkflowPermission[] =
      MODULE_TYPE_MAP_PERMISSIONS[moduleName as ModuleType] || [];
    for (let i = 0; i < validPermissions.length; i++) {
      const listUserIdsByPermission: string[] = [];
      for (let j = 0; j < listCurrentUA.length; j++) {
        // console.log('user match: ', validPermissions[i], listCurrentUA[j].permission);
        if (validPermissions[i] == listCurrentUA[j].permission) {
          listUserIdsByPermission.push(listCurrentUA[j].userId);
        }
      }

      const userPermission = {
        permission: validPermissions[i],
        userIds: listUserIdsByPermission,
      };
      if (userPermissionParams[i]?.isView) {
        userPermission['isView'] = userPermissionParams[i].isView;
      }

      listCurrentUserAndPermission.push(userPermission);
    }
    // return listCurrentUserAndPermission;

    // Find record to delete and insert
    const listUserAndPermissionCreate: UserPermissionDTO[] = [];
    const listUserAndPermissionDelete: UserPermissionDTO[] = [];
    for (let m = 0; m < userPermissionParams.length; m++) {
      // let listUserIdCreate: string[] = [];
      for (let n = 0; n < listCurrentUserAndPermission.length; n++) {
        // If passing param permission = current permission => update user ids
        if (userPermissionParams[m].permission == listCurrentUserAndPermission[n].permission) {
          const listCreateIdsSet = MySet.difference(
            new Set(userPermissionParams[m].userIds),
            new Set(listCurrentUserAndPermission[n].userIds),
          );
          console.log(
            'create: ',
            listCurrentUserAndPermission[n].permission,
            Array.from(listCreateIdsSet),
          );
          if (Array.from(listCreateIdsSet).length > 0) {
            const listUser = {
              permission: listCurrentUserAndPermission[n].permission,
              userIds: Array.from(listCreateIdsSet),
            };
            if (listCurrentUserAndPermission[n]?.isView) {
              listUser['isView'] = listCurrentUserAndPermission[n].isView;
            }
            listUserAndPermissionCreate.push(listUser);
          }

          // listUserIdCreate = listUserIdCreate.concat(Array.from(listCreateIdsSet));

          const listDeleteIdsSet = MySet.difference(
            new Set(listCurrentUserAndPermission[n].userIds),
            new Set(userPermissionParams[m].userIds),
          );
          console.log(
            'delete: ',
            listCurrentUserAndPermission[n].permission,
            Array.from(listDeleteIdsSet),
          );
          if (Array.from(listDeleteIdsSet).length > 0) {
            listUserAndPermissionDelete.push({
              permission: listCurrentUserAndPermission[n].permission,
              userIds: Array.from(listDeleteIdsSet),
            });
          }
        }
      }
    }
    // return { listUserAndPermissionCreate, listUserAndPermissionDelete };
    console.log('listUserAndPermissionCreate: ', listUserAndPermissionCreate);
    console.log('listUserAndPermissionDelete: ', listUserAndPermissionDelete);

    // Create new record
    const preparedCreateRecords: UserAssignment[] = [];
    for (const createRecord of listUserAndPermissionCreate) {
      for (const userId of createRecord.userIds) {
        const preparedCreateRecord = {
          ...preparedCreateTemp,
          userId,
          isView: createRecord.isView,
          permission: createRecord.permission,
        };

        if (createRecord?.isView) {
          preparedCreateRecord['isView'] = createRecord.isView;
        }
        preparedCreateRecords.push(preparedCreateRecord as UserAssignment);
      }
    }

    console.log('preparedCreateRecords: ', preparedCreateRecords);

    // return preparedCreateRecords;
    if (preparedCreateRecords.length > 0) {
      await managerTrans.save(UserAssignment, preparedCreateRecords);
    }

    const listPromiseDelete = [];
    for (const deleteRecord of listUserAndPermissionDelete) {
      for (const userId of deleteRecord.userIds) {
        listPromiseDelete.push(
          managerTrans.delete(UserAssignment, {
            ...preparedCreateTemp,
            userId,
            permission: deleteRecord.permission,
          }),
        );
      }
    }
    if (listPromiseDelete.length > 0) {
      await Promise.all(listPromiseDelete);
    }

    const userAssignments = await managerTrans.getRepository(UserAssignment).find({
      where: {
        planningRequestId: recordId,
      },
    });

    for (const value of userAssignments) {
      if (status) {
        switch (status) {
          case PlanningRequestStatus.REVIEWED_1:
            if (
              value['permission'] === WorkflowPermission.CREATOR ||
              value['permission'] === WorkflowPermission.REVIEWER1 ||
              value['permission'] === WorkflowPermission.REVIEWER2
            ) {
              value['isView'] = true;
              value['isEdit'] = true;
            }
            break;
          case PlanningRequestStatus.REVIEWED_2:
            if (
              value['permission'] === WorkflowPermission.CREATOR ||
              value['permission'] === WorkflowPermission.REVIEWER1 ||
              value['permission'] === WorkflowPermission.REVIEWER2 ||
              value['permission'] === WorkflowPermission.REVIEWER3
            ) {
              value['isView'] = true;
              value['isEdit'] = true;
            }
            break;
          case PlanningRequestStatus.REVIEWED_3:
            if (
              value['permission'] === WorkflowPermission.CREATOR ||
              value['permission'] === WorkflowPermission.REVIEWER1 ||
              value['permission'] === WorkflowPermission.REVIEWER2 ||
              value['permission'] === WorkflowPermission.REVIEWER3 ||
              value['permission'] === WorkflowPermission.REVIEWER4
            ) {
              value['isView'] = true;
              value['isEdit'] = true;
            }
            break;
          case PlanningRequestStatus.REVIEWED_4:
            if (
              value['permission'] === WorkflowPermission.CREATOR ||
              value['permission'] === WorkflowPermission.REVIEWER1 ||
              value['permission'] === WorkflowPermission.REVIEWER2 ||
              value['permission'] === WorkflowPermission.REVIEWER3 ||
              value['permission'] === WorkflowPermission.REVIEWER4 ||
              value['permission'] === WorkflowPermission.REVIEWER5
            ) {
              value['isView'] = true;
              value['isEdit'] = true;
            }
            break;
          case PlanningRequestStatus.REVIEWED_5:
            if (
              value['permission'] === WorkflowPermission.CREATOR ||
              value['permission'] === WorkflowPermission.REVIEWER1 ||
              value['permission'] === WorkflowPermission.REVIEWER2 ||
              value['permission'] === WorkflowPermission.REVIEWER3 ||
              value['permission'] === WorkflowPermission.REVIEWER4 ||
              value['permission'] === WorkflowPermission.REVIEWER5 ||
              value['permission'] === WorkflowPermission.APPROVER
            ) {
              value['isView'] = true;
              value['isEdit'] = true;
            }
            break;
        }

        if (reassignFrom) {
          switch (reassignFrom) {
            case PlanningRequestStatus.REVIEWED_1:
              if (value['permission'] === WorkflowPermission.REVIEWER1) {
                value['isEdit'] = false;
              }
              break;
            case PlanningRequestStatus.REVIEWED_2:
              if (value['permission'] === WorkflowPermission.REVIEWER2) {
                value['isEdit'] = false;
              }
              break;
            case PlanningRequestStatus.REVIEWED_3:
              if (value['permission'] === WorkflowPermission.REVIEWER3) {
                value['isEdit'] = false;
              }
              break;
            case PlanningRequestStatus.REVIEWED_4:
              if (value['permission'] === WorkflowPermission.REVIEWER4) {
                value['isEdit'] = false;
              }
              break;
            case PlanningRequestStatus.REVIEWED_5:
              if (value['permission'] === WorkflowPermission.REVIEWER5) {
                value['isEdit'] = false;
              }
              break;
            case PlanningRequestStatus.APPROVED:
              if (value['permission'] === WorkflowPermission.APPROVER) {
                value['isEdit'] = false;
              }
              break;
          }
        }
      }
    }
    await managerTrans.save(UserAssignment, userAssignments);

    return 1;
  }

  async listByModule(moduleName: string, recordId: string) {
    const validPermissions: WorkflowPermission[] =
      MODULE_TYPE_MAP_PERMISSIONS[moduleName as ModuleType] || [];

    let recordCondition;
    switch (moduleName) {
      case ModuleType.PLANNING_REQUEST:
        recordCondition = `userAssignment.planningRequestId`;
        break;
      case ModuleType.AUDIT_CHECKLIST:
        recordCondition = `userAssignment.auditChecklistId`;
        break;
      case ModuleType.REPORT_FINDING:
        recordCondition = `userAssignment.reportFindingFormId`;
        break;
      case ModuleType.INTERNAL_AUDIT_REPORT:
        recordCondition = `userAssignment.internalAuditReportId`;
        break;
      case ModuleType.SELF_ASSESSMENT:
        recordCondition = `userAssignment.selfAssessmentId`;
        break;
      case ModuleType.INCIDENTS:
        recordCondition = `userAssignment.incidentInvestigationId`;
        break;
    }

    const qb = this.createQueryBuilder('userAssignment')
      .leftJoin('userAssignment.user', 'user')
      .where(`${recordCondition} = :recordId`, {
        recordId,
      })
      .select([
        'userAssignment.id',
        'userAssignment.permission',
        'userAssignment.userId',
        'user.id',
        'user.username',
        'user.jobTitle',
        'user.email',
      ]);
    const listCurrentUA = await this.getManyQB(qb);
    // return listCurrentUA;
    // console.log('listCurrentUA: ', listCurrentUA);
    const resObj = {};
    for (let i = 0; i < validPermissions.length; i++) {
      resObj[validPermissions[i]] = [];
      for (let j = 0; j < listCurrentUA.length; j++) {
        if (validPermissions[i] == listCurrentUA[j].permission) {
          resObj[validPermissions[i]].push(listCurrentUA[j].user);
        }
      }
    }

    // let listReceiverNoti: IUser[] = [];
    // listReceiverNoti = listReceiverNoti.concat(resObj[WorkflowPermission.APPROVER]);
    // listReceiverNoti = listReceiverNoti.concat(resObj[WorkflowPermission.AUDITOR]);
    // listReceiverNoti = listReceiverNoti.concat(resObj[WorkflowPermission.OWNER_MANAGER]);
    // console.log('listReceiverNoti: ', listReceiverNoti);

    return resObj;
  }

  async _updateEditPermission(ids: string[], planningRequestId: string) {
    await this.createQueryBuilder()
      .update(UserAssignment)
      .set({ isEdit: false, planningRequestId: planningRequestId })
      .where('id IN (:...ids)', { ids: ids })
      .execute();
  }

  async _acceptEditPermission(
    userIds: string[],
    planningRequestId: string,
    permission: WorkflowPermission[],
  ) {
    await this.createQueryBuilder()
      .update(UserAssignment)
      .set({ isEdit: true, isView: true, planningRequestId: planningRequestId })
      .where(
        'userId IN (:...userIds) AND permission IN (:...permission) AND planningRequestId = :planningRequestId ',
        {
          userIds: userIds,
          permission: permission,
          planningRequestId: planningRequestId,
        },
      )
      .execute();
  }

  async _getInciedentHaveCreator() {
    const incidentHaveCreator = await this.createQueryBuilder('userAssignment')
      .select('userAssignment.incidentInvestigationId AS incidentInvestigationId')
      .where(
        'userAssignment.incidentInvestigationId IS NOT NULL AND  userAssignment.deleted = FALSE ',
      )
      .andWhere('userAssignment.permission = :permission', {
        permission: WorkflowPermission.CREATOR,
      })
      .getRawMany();
    return incidentHaveCreator.length
      ? incidentHaveCreator.map((item) => item.incidentinvestigationid)
      : [];
  }
}
