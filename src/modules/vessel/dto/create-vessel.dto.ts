import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsUUID,
  MaxLength,
  ValidateNested,
} from 'class-validator';
import { CommonStatus } from 'svm-nest-lib-v3';
import { decryptImage } from '../../../commons/functions';

import { VesselChartererDTO } from './vessel-charterer.dto';
import { VesselDocHolderDTO } from './vessel-doc-holder.dto';
import { vesselGeneralHistoryDto } from './vessel-general-history.dto';
import { VesselOwnerDTO } from './vessel-owner.dto';
import { VesselBlacklistMOUDto } from './vessel-blacklist-mou.dto';
import { RightShipRestrictionsDto } from 'src/modules-qa/right-ship/dto/rightship-restrictions.dto';

export class CreateVesselDto {
  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @Transform((value: string) => {
    const newValue = decryptImage(value);
    return newValue;
  })
  image: string;

  @ApiProperty({ type: 'string' })
  @IsNotEmpty({ message: 'common.REQUIRED_FIELD' })
  @Transform((value: string) => value.trim())
  @MaxLength(128)
  imoNumber: string;

  @ApiProperty({ type: 'string' })
  @IsNotEmpty({ message: 'common.REQUIRED_FIELD' })
  @Transform((value: string) => value.trim())
  @MaxLength(128)
  name: string;

  @ApiProperty({ type: 'string' })
  @IsNotEmpty({ message: 'common.REQUIRED_FIELD' })
  @Transform((value: string) => value.trim())
  @MaxLength(128)
  code: string;

  @ApiProperty({ type: 'number', required: false })
  @IsOptional()
  @IsNumber()
  countryFlag: number;

  @ApiProperty({ type: 'number', required: false })
  @IsOptional()
  @IsNumber()
  countryId: number;

  @ApiProperty({ type: 'string' })
  @IsNotEmpty()
  @IsUUID('all')
  vesselTypeId: string;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  callSign?: string;

  @ApiProperty({
    type: 'string',
    description: 'ISO string',
    example: '2021-09-21T11:38:06.585Z',
  })
  @IsNotEmpty()
  @IsDateString()
  buildDate: string;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  shipyardName?: string;

  // @ApiProperty({ type: 'string', required: false })
  // @IsOptional()
  // shipyardCountry?: string;

  @ApiProperty({ type: 'number', required: false })
  @IsOptional()
  @IsNumber()
  shipyardCountryId: number;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  // @Transform((value: string) => value.trim())
  officialNumber?: string;

  @ApiProperty({ type: 'string' })
  @IsNotEmpty()
  @IsUUID('all')
  classificationSocietyId: string;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  // @Transform((value: string) => value.trim())
  @MaxLength(128)
  vesselClass?: string;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  // @Transform((value: string) => value.trim())
  hullNumber?: string;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  fleetName?: string;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @IsUUID('all')
  divisionId?: string;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @IsUUID('all')
  docHolderId: string;

  // @ApiProperty({ type: 'string' })
  // @IsNotEmpty()
  // @IsUUID('all')
  // fleetId: string;
  @ApiProperty({ enum: CommonStatus })
  @IsNotEmpty()
  @IsEnum(CommonStatus)
  status: string;

  // management/ownership

  @ApiProperty({ type: [VesselDocHolderDTO], required: false })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => VesselDocHolderDTO)
  @IsArray()
  vesselDocHolders?: VesselDocHolderDTO[];

  @ApiProperty({ type: [vesselGeneralHistoryDto], required: false })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => vesselGeneralHistoryDto)
  @IsArray()
  vesselGeneralHistory?: vesselGeneralHistoryDto[];

  @ApiProperty({ type: [VesselChartererDTO], required: false })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => VesselChartererDTO)
  @IsArray()
  vesselCharterers?: VesselChartererDTO[];

  @ApiProperty({ type: [VesselOwnerDTO], required: false })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => VesselOwnerDTO)
  @IsArray()
  vesselOwners?: VesselOwnerDTO[];

  // Detailed info
  @ApiProperty({ type: 'number' })
  @IsNotEmpty()
  deadWeightTonnage: number;

  @ApiProperty({ type: 'number' })
  @IsNotEmpty()
  grt: number;

  @ApiProperty({ type: 'number' })
  @IsNotEmpty()
  nrt: number;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  teuCapacity: string;

  @ApiProperty({ type: 'number', required: false })
  @IsOptional()
  maxDraft: number;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  lightShip: string;

  // Dimension
  @ApiProperty({ type: 'number' })
  @IsOptional()
  loa: number;

  @ApiProperty({ type: 'number' })
  @IsOptional()
  lbp: number;

  @ApiProperty({ type: 'number' })
  @IsOptional()
  breath: number;

  @ApiProperty({ type: 'number' })
  @IsOptional()
  height: number;

  @ApiProperty({ type: 'number' })
  @IsOptional()
  depth: number;

  // Crew info
  // @ApiProperty({ type: [String], required: false })
  // @IsOptional()
  // @IsArray()
  // officers?: string[];

  // @ApiProperty({ type: [String], required: false })
  // @IsOptional()
  // @IsArray()
  // rating?: string[];

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @IsUUID('all')
  crewGroupingId?: string;

  // i-Nautix specific feature
  @ApiProperty({ type: [String] })
  @IsOptional()
  @IsArray()
  // @ArrayMinSize(1)
  @IsUUID('all', { each: true })
  ownerIds: string[];

  @ApiProperty({ type: Boolean })
  @IsNotEmpty()
  @IsBoolean()
  customerRestricted: boolean;

  @ApiProperty({ type: Boolean })
  @IsNotEmpty()
  @IsBoolean()
  blacklistOnMOUWebsite: boolean;

  @ApiProperty({ type: [VesselBlacklistMOUDto], required: false })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => VesselBlacklistMOUDto)
  @IsArray()
  blacklistData?: VesselBlacklistMOUDto[];

  @ApiProperty({ type: [RightShipRestrictionsDto], required: false })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => RightShipRestrictionsDto)
  @IsArray()
  rightShipRestrictions?: RightShipRestrictionsDto[];

  @ApiProperty({ type: Boolean })
  @IsOptional()
  @IsBoolean()
  isITF: boolean;
}
