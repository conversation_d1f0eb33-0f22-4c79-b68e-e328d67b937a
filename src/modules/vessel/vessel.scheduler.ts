import { Injectable } from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import { Environment, LoggerCommon, Utils } from 'svm-nest-lib-v3';
import { ConfigRepository, VesselRepository } from './vessel.repository';
import { RightShipRepository } from '../../modules-qa/right-ship/right-ship.repository';
import { SyncDataVesselService } from '../../micro-services/sync/sync-data-vessel.service';
import APP_CONFIG from '../../configs/app.config';
import { VesselLogSyncDataRepository } from './repository/vessel-log-sync-data.repository';
import {
  VesselLogSyncData,
  VesselLogSyncDataEnumStatus,
} from './entity/vessel-log-sync-data.entity';
import { Connection } from 'typeorm';
import { DefaultDLs, EmailTypeEnum, MailTemplate } from 'src/commons/enums';
import { AppConst } from 'src/commons/consts/app.const';
import { EmailProducer, IEmailEventModel } from 'src/micro-services/async/email.producer';
import { NykVesselRepository } from '../../modules-qa/nyk-vessels/repository/nyk-vessel.repository';
import { SyncDataNykVesselService } from '../../micro-services/sync/sync-nyk-vessel.service';
import moment from 'moment';
import { VoyageMasterDetailsRepository } from '../../modules-qa/vessel-scheduler-module/repository/vessel-scheduler.repository';
import { SyncZenithPotentialRiskData } from '../../micro-services/sync/zenith-potential-risk-data-sync.service';
import { CompanyConfiguration } from '../company-configuration/company-configuration.entity';
import { Company } from '../company/company.entity';

@Injectable()
export class VesselScheduler {
  constructor(
    private readonly vesselRepository: VesselRepository,
    private readonly rightShipRepository: RightShipRepository,
    private readonly nykVesselRepository: NykVesselRepository,
    private readonly vesselLogSyncDataRepository: VesselLogSyncDataRepository,
    private readonly voyageMasterDetailsRepository: VoyageMasterDetailsRepository,
    private readonly emailProducer: EmailProducer,
    private readonly configRepository: ConfigRepository,
    private readonly connection: Connection,
  ) {}
  // Run at 00h00 every day '0 00 00 * * *'

  @Cron(`0 0 23 * * *`, {
    name: 'updateStatusDOCHolderVesselCharacterOwner',
  })
  async updateStatusDOCHolderVesselCharacterOwner() {
    try {
      LoggerCommon.log('updateStatusDOCHolderVesselCharacterOwner start');
      await this.vesselRepository.updateStatusDOCHolderVesselCharacterOwner();
      LoggerCommon.log('updateStatusDOCHolderVesselCharacterOwner ok');
    } catch (ex) {
      LoggerCommon.error('updateStatusDOCHolderVesselCharacterOwner job error', ex.message || ex);
    }
  }
  // Rio Tinto Data insertion scheduler running every day at 04.00 AM SGT(Singapore). (IST - 1:30 AM)
  @Cron(`0 0 20 * * *`, {
    name: 'syncRioTintoVesselData',
  })
  async syncDataVessel() {
    const logId = Utils.strings.generateUUID();
    try {
      LoggerCommon.log('syncRioTintoVesselData start');
      await this.vesselLogSyncDataRepository.createLog({
        id: logId,
        startedAt: new Date(),
        nameJob: 'syncRioTintoVesselData',
      } as VesselLogSyncData);
      if (APP_CONFIG.ENV.APP.ENV === Environment.PRODUCTION) {
        let today = undefined;
        today = new Date().toISOString().slice(0, 10);
        console.log(today);
        const login: any = await SyncDataVesselService.connectLoginService();
        if (login && login.status == 'success') {
          const vessels: any = await SyncDataVesselService.getDataVesselMaster(
            login.result.token,
            today,
            today,
          );
          const rightShips: any = await SyncDataVesselService.getDataRightShip(
            login.result.token,
            today,
            today,
          );
          const voyages: any = await SyncDataVesselService.getDataVoyage(
            login.result.token,
            today,
            today,
          );
          const voyageMasterDetails: any = await SyncDataVesselService.getDataVoyageMasterDetails(
            login.result.token,
            today,
            today,
          );
          const rigthiShpsIncidents: any = await SyncDataVesselService.getDataRightShipIncidents(
            login.result.token,
            today,
            today,
          );
          let vesselDatas = undefined;
          let rightShipDatas = undefined;
          let voyageDatas = undefined;
          let voyageMasterDetailsDatas = undefined;
          let rigthiShpsIncidentsDatas = undefined;
          if (vessels && vessels.status_code == 200) {
            vesselDatas = vessels.data;
          }
          const vesselRaws_size = vesselDatas ? vesselDatas.length : 0;
          if (rightShips && rightShips.status_code == 200) {
            rightShipDatas = rightShips.data;
          }
          const rightShip_size = rightShipDatas ? rightShipDatas.length : 0;
          if (voyages && voyages.status_code == 200) {
            voyageDatas = voyages.data;
          }
          const voyage_size = voyageDatas ? voyageDatas.length : 0;
          if (voyageMasterDetails && voyageMasterDetails.status_code == 200) {
            voyageMasterDetailsDatas = voyageMasterDetails.data;
          }
          const voyageMasterDetails_size = voyageMasterDetailsDatas
            ? voyageMasterDetailsDatas.length
            : 0;
          if (rigthiShpsIncidents && rigthiShpsIncidents.status_code == 200) {
            rigthiShpsIncidentsDatas = rigthiShpsIncidents.data;
          }
          const rigthiShpsIncidents_size = rigthiShpsIncidentsDatas
            ? rigthiShpsIncidentsDatas.length
            : 0;
          // commented the restrict for exceed of 15 vessels condition
          // const configSize = await this.configRepository.getVesselConfig();
          // const newVessels = [];
          // for (const item of vesselDatas) {
          //   if (
          //     item &&
          //     item.date_inserted.toISOString().slice(0, 10) ===
          //       item.date_updated.toISOString().slice(0, 10)
          //   ) {
          //     newVessels.push(item.date_inserted.toISOString().slice(0, 10));
          //   }
          // }
          // if (newVessels.length > Number.parseInt(configSize.configuration)) {
          //   const user: IEmailEventModel[] = [];
          //   user.push({
          //     receiver: { email: ['<EMAIL>'] },
          //     cc: ['<EMAIL>', '<EMAIL>'],
          //     subject: ' [INFO] Received more than 15 new vessels',
          //     data: {
          //       date: today,
          //       vesselRaws_size: newVessels.length,
          //       allowedCounts: Number.parseInt(configSize.configuration),
          //       logoImage: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.CDN_RESOURCE + AppConst.LOGO_IMAGE,
          //     },
          //     type: EmailTypeEnum.UPDATE_RECORD_STATUS,
          //     templateKey: MailTemplate.WARNING_EXCEEDING_VESSEL,
          //   });
          //   await this.emailProducer.publishEmail(user);
          //   await this.vesselLogSyncDataRepository.updateLog({
          //     id: logId,
          //     finishedAt: new Date(),
          //     numberRightShipImported: 0,
          //     numberVesselImported: 0,
          //     numberVoyageImported: 0,
          //     messageError: 'More Than Vessels Imported',
          //     status: VesselLogSyncDataEnumStatus.FAILURE,
          //   } as VesselLogSyncData);
          // } else {
          const vesselsRecord = await this.rightShipRepository.syncVesselAndRightShipUpdate(
            APP_CONFIG.ENV.CONFIG_SYNC_DATA_VESSEL.COMPANY_ID,
            vesselDatas ? vesselDatas : [],
            rightShipDatas ? rightShipDatas : [],
            voyageDatas ? voyageDatas : [],
            voyageMasterDetailsDatas ? voyageMasterDetailsDatas : [],
          );
          await this.rightShipRepository.docHolderMasterUpdates(
            APP_CONFIG.ENV.CONFIG_SYNC_DATA_VESSEL.COMPANY_ID,
            rightShipDatas ? rightShipDatas : [],
          );
          await this.rightShipRepository.vesselDocHolderIdUpdates(
            APP_CONFIG.ENV.CONFIG_SYNC_DATA_VESSEL.COMPANY_ID,
            rightShipDatas ? rightShipDatas : [],
          );
          await this.rightShipRepository.syncRestrictions(
            APP_CONFIG.ENV.CONFIG_SYNC_DATA_VESSEL.COMPANY_ID,
            rightShipDatas ? rightShipDatas : [],
          );
          const incidentsRecord = await this.rightShipRepository.syncRightShipIncidents(
            APP_CONFIG.ENV.CONFIG_SYNC_DATA_VESSEL.COMPANY_ID,
            rigthiShpsIncidentsDatas ? rigthiShpsIncidentsDatas : [],
          );
          await this.vesselLogSyncDataRepository.updateLog({
            id: logId,
            finishedAt: new Date(),
            numberRightShipImported: vesselsRecord?.noOfRightShipInserted
              ? vesselsRecord?.noOfRightShipInserted
              : 0,
            numberOfVesselInserted: vesselsRecord?.noOfVesselInserted
              ? vesselsRecord?.noOfVesselInserted
              : 0,
            numberOfVesselUpdated: vesselsRecord?.noOfVesselUpdated
              ? vesselsRecord?.noOfVesselUpdated
              : 0,
            // numberVesselImported: vesselDatas ? vesselDatas.length : 0,
            // numberVoyageImported: voyageDatas ? voyageDatas.length : 0,
            numberVoyageMasterDetailsImported: vesselsRecord?.noOfVoyageMasterInserted
              ? vesselsRecord?.noOfVoyageMasterInserted
              : 0,
            status: VesselLogSyncDataEnumStatus.SUCCESS,
          } as VesselLogSyncData);
          //New Service for Mail Notification
          // pushing mail
          const todayDate = moment(new Date()).format('DD-MM-YYYY');
          const company = await this.connection
            .getRepository(Company)
            .createQueryBuilder('company')
            .where('company.code = :code', { code: 'RT-SIN' }) // 'RT-SIN' Rio Tinto Company Code
            .select(['company.id'])
            .getOne();
          console.log(company.id);
          // get DLs
          const getDLs = await this.connection
            .getRepository(CompanyConfiguration)
            .createQueryBuilder('companyConfiguration')
            .where('companyConfiguration.companyId = :companyId', { companyId: company.id })
            .getOne();
          const receiver = getDLs.DLMails[0];
          const cc = getDLs.DLMails.slice(1);
          cc.push(DefaultDLs.I_NAUTIX);
          const user: IEmailEventModel[] = [];
          user.push({
            receiver: { email: receiver },
            cc: cc,
            subject: ` [${todayDate}] Rio Tinto - Data insertion status`,
            data: {
              vesselRaws_size: vesselRaws_size ? vesselRaws_size : 0,
              rightShip_size: rightShip_size ? rightShip_size : 0,
              // voyage_size: voyage_size ? voyage_size : 0,
              voyageMasterDetails_size: voyageMasterDetails_size ? voyageMasterDetails_size : 0,
              vessel_record_inserted: vesselsRecord?.noOfVesselInserted
                ? vesselsRecord?.noOfVesselInserted
                : 0,
              vessel_record_updated: vesselsRecord?.noOfVesselUpdated
                ? vesselsRecord?.noOfVesselUpdated
                : 0,
              rightShip_record: vesselsRecord?.noOfRightShipInserted
                ? vesselsRecord?.noOfRightShipInserted
                : 0,
              // voyage_record: voyage_record ? voyage_record : 0,
              voyageMasterDetails_record: vesselsRecord?.noOfVoyageMasterInserted
                ? vesselsRecord?.noOfVoyageMasterInserted
                : 0,
              logoImage: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.CDN_RESOURCE + AppConst.LOGO_IMAGE,
            },
            type: EmailTypeEnum.RT_DATA_INSERTION_STATUS,
            templateKey: MailTemplate.RT_DATA_INSERTION_STATUS,
          });
          await this.emailProducer.publishEmail(user);
          LoggerCommon.log('Rio Tinto Data insertion mail sent');
          // }
        } else {
          await this.vesselLogSyncDataRepository.updateLog({
            id: logId,
            finishedAt: new Date(),
            status: VesselLogSyncDataEnumStatus.ERROR,
            messageError: 'get token error',
          } as VesselLogSyncData);
        }
      } else {
        LoggerCommon.log('Non Productoion Environment');
        await this.vesselLogSyncDataRepository.updateLog({
          id: logId,
          finishedAt: new Date(),
        } as VesselLogSyncData);
      }
      LoggerCommon.log('syncRioTintoVesselData ok');
    } catch (ex) {
      await this.vesselLogSyncDataRepository.updateLog({
        id: logId,
        finishedAt: new Date(),
        status: VesselLogSyncDataEnumStatus.ERROR,
        messageError: ex.message || ex,
      } as VesselLogSyncData);
      LoggerCommon.error('syncRioTintoVesselData job error', ex.message || ex);
    }
  }

  // Disabled the NYK scheduler because that the company has no longer to use our application.
  // NYK Data insertion scheduler running every day at 02.45 AM SGT(Singapore). (IST - 12:15 AM)
  // @Cron(`0 45 18 * * *`, {
  //   name: 'syncNYKVesselData',
  // })
  async syncNykVesselData() {
    const logId = Utils.strings.generateUUID();
    try {
      LoggerCommon.log('syncNYKVesselData start');
      await this.vesselLogSyncDataRepository.createLog({
        id: logId,
        startedAt: new Date(),
        nameJob: 'syncNYKVesselData',
      } as VesselLogSyncData);
      if (APP_CONFIG.ENV.APP.ENV === 'uat') {
        const currentDate = new Date();
        currentDate.setHours(0, 0, 0, 0);
        const fromDate = currentDate.toISOString();
        currentDate.setHours(23, 59, 59, 999);
        const toDate = currentDate.toISOString();
        const login: any = await SyncDataVesselService.connectLoginService();
        if (login && login.status_code == 200) {
          const vessels: any = await SyncDataNykVesselService.getDataNykVesselMaster(
            login.result.token,
            fromDate,
            toDate,
          );
          let vesselDatas = undefined;
          if (vessels && vessels.data.status_code == 200) {
            vesselDatas = vessels.data.data;
          }
          const vesselsRecord = await this.nykVesselRepository.syncNykVesselUpdate(
            APP_CONFIG.ENV.CONFIG_SYNC_NYK_DATA_VESSEL.COMPANY_ID,
            vesselDatas ? vesselDatas : [],
          );
          // pushing mail
          const today = moment(new Date()).format('DD-MM-YYYY');
          const company = await this.connection
            .getRepository(Company)
            .createQueryBuilder('company')
            .where('company.code = :code', { code: '10001' }) // '10001' NYK Company Code
            .select(['company.id'])
            .getOne();
          console.log(company.id);
          const getDLs = await this.connection
            .getRepository(CompanyConfiguration)
            .createQueryBuilder('companyConfiguration')
            .where('companyConfiguration.companyId = :companyId', { companyId: company.id })
            .getOne();
          const receiver = getDLs.DLMails[0];
          const cc = getDLs.DLMails.slice(1);
          cc.push(DefaultDLs.I_NAUTIX);
          const user: IEmailEventModel[] = [];
          user.push({
            receiver: { email: receiver },
            cc: cc,
            subject: ` [${today}] NYK - Data insertion status`,
            data: {
              vesselRaws_size: vesselDatas?.length ? vesselDatas?.length : 0,
              vessel_record_inserted: vesselsRecord?.noOfVesselInserted
                ? vesselsRecord?.noOfVesselInserted
                : 0,
              vessel_record_updated: vesselsRecord?.noOfVesselUpdated
                ? vesselsRecord?.noOfVesselUpdated
                : 0,
              logoImage: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.CDN_RESOURCE + AppConst.LOGO_IMAGE,
            },
            type: EmailTypeEnum.NYK_DATA_INSERTION_STATUS,
            templateKey: MailTemplate.NYK_DATA_INSERTION_STATUS,
          });
          await this.emailProducer.publishEmail(user);
          LoggerCommon.log('NYK Data insertion mail sent');
          await this.vesselLogSyncDataRepository.updateLog({
            id: logId,
            finishedAt: new Date(),
            numberOfVesselInserted: vesselsRecord?.noOfVesselInserted
              ? vesselsRecord?.noOfVesselInserted
              : 0,
            numberOfVesselUpdated: vesselsRecord?.noOfVesselUpdated
              ? vesselsRecord?.noOfVesselUpdated
              : 0,
            status: VesselLogSyncDataEnumStatus.SUCCESS,
          } as VesselLogSyncData);
        }
      } else {
        LoggerCommon.log('Non UAT Environment');
        await this.vesselLogSyncDataRepository.updateLog({
          id: logId,
          finishedAt: new Date(),
        } as VesselLogSyncData);
      }
      LoggerCommon.log('syncNYKVesselData ok');
    } catch (ex) {
      await this.vesselLogSyncDataRepository.updateLog({
        id: logId,
        finishedAt: new Date(),
        status: VesselLogSyncDataEnumStatus.ERROR,
        messageError: ex.message || ex,
      } as VesselLogSyncData);
      LoggerCommon.error('syncNYKVesselData job error', ex.message || ex);
    }
  }

  @Cron(`0 30 18 * * *`, {
    name: 'syncDataZenithPotentialRisk',
  })
  async syncZenithPotentialRiskData() {
    const logId = Utils.strings.generateUUID();
    try {
      LoggerCommon.log('syncZenithPotentialRisk start');
      await this.vesselLogSyncDataRepository.createLog({
        id: logId,
        startedAt: new Date(),
        nameJob: 'syncZenithPotentialRiskData',
      } as VesselLogSyncData);
      if (APP_CONFIG.ENV.APP.ENV === Environment.PRODUCTION) {
        const currentDate = new Date();
        currentDate.setHours(0, 0, 0, 0);
        const login: any = await SyncZenithPotentialRiskData.connectLoginService();
        if (login && login.status == 'Success') {
          const ZenithPotentialRisk: any = await SyncZenithPotentialRiskData.getPotentialRiskData(
            login.AuthenticationToken,
          );
          let zenithPotentialRisksDatas = undefined;
          if (ZenithPotentialRisk && ZenithPotentialRisk.data.status == 'Success') {
            zenithPotentialRisksDatas = ZenithPotentialRisk.data.PotentialRiskInformationList;
          }
          const zenithPotentialRiskData = await this.voyageMasterDetailsRepository.syncZenithPotentialRiskData(
            APP_CONFIG.ENV.CONFIG_ZENITH_POTENTIAL_RISK_DATA.COMPANY_ID,
            zenithPotentialRisksDatas ? zenithPotentialRisksDatas : [],
          );
          await this.vesselLogSyncDataRepository.updateLog({
            id: logId,
            finishedAt: new Date(),
            numberOfZenithPotentialRiskInserted: zenithPotentialRiskData.noOfPotentialRiskInserted
              ? zenithPotentialRiskData.noOfPotentialRiskInserted
              : 0,
            numberOfZenithPotentialRiskUpdated: zenithPotentialRiskData.noOfPotentialriskUpdate
              ? zenithPotentialRiskData.noOfPotentialriskUpdate
              : 0,
            status: VesselLogSyncDataEnumStatus.SUCCESS,
          } as VesselLogSyncData);
        }
      } else {
        await this.vesselLogSyncDataRepository.updateLog({
          id: logId,
          finishedAt: new Date(),
        } as VesselLogSyncData);
      }
      LoggerCommon.log('syncDataZenithPotentialRisk ok');
    } catch (ex) {
      await this.vesselLogSyncDataRepository.updateLog({
        id: logId,
        finishedAt: new Date(),
        status: VesselLogSyncDataEnumStatus.ERROR,
        messageError: ex.message || ex,
      } as VesselLogSyncData);
      LoggerCommon.error('syncZenithPotentialRiskData job error', ex.message || ex);
    }
  }
}
