import { VesselScreeningRisk } from '../../../modules-qa/vessel-screening/entity/vessel-screening-risk.entity';
import { Column, Entity, ManyToOne } from 'typeorm';
import { Vessel } from './vessel.entity';

@Entity()
export class VesselRiskRequest extends VesselScreeningRisk {
  @Column({ type: 'uuid', nullable: true })
  public vesselId: string;

  @Column({ type: 'int4', default: 0 })
  public count: number

  @ManyToOne(() => Vessel, { onDelete: 'CASCADE' })
  vessel: Vessel;
}
