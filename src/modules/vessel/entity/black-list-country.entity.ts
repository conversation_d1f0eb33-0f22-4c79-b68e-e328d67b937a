import { IdentifyEntity } from 'svm-nest-lib-v3';
import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToOne } from 'typeorm';
import { Country } from '../../country/country.entity';

@Entity()
export class BlackListCountry extends IdentifyEntity {
  @Column({ type: 'text' })
  countryCode: string;

  @Column({ type: 'text' })
  countryName: string;

  @Column({ type: 'uuid' })
  countryId: string;

  @OneToOne(() => Country)
  @JoinColumn()
  country: Country;
}
