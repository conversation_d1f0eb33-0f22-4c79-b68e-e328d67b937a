import { IdentifyEntity } from 'svm-nest-lib-v3';
import { Col<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToOne } from 'typeorm';
import { Country } from '../../country/country.entity';

@Entity()
export class WhiteListCountry extends IdentifyEntity {
  @Column({ type: 'text' })
  countryCode: string;

  @Column({ type: 'text' })
  countryName: string;

  @Column({ type: 'uuid' })
  countryId: string;

  @OneToOne(() => Country, (country) => country.whiteListCountry)
  @JoinColumn()
  country: Country;
}
