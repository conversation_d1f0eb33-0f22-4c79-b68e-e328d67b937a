import { CommonStatus, IdentifyEntity } from 'svm-nest-lib-v3';
import {
  AfterLoad,
  Column,
  Entity,
  Index,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  OneToOne,
} from 'typeorm';
import { DBIndexes } from '../../../commons/consts/db.const';
import { ClassificationSociety } from '../../../modules-qa/classification-society/classification-society.entity';
import { CrewGrouping } from '../../../modules-qa/crew-grouping/crew-grouping.entity';
import { RightShip } from '../../../modules-qa/right-ship/right-ship.entity';
import { Company } from '../../company/company.entity';
import { DivisionMapping } from '../../division-mapping/division-mapping.entity';
import { User } from '../../user/user.entity';
import { VesselType } from '../../vessel-type/vessel-type.entity';
import { VesselCharterer } from './vessel-charterer.entity';
import { VesselDocHolder } from './vessel-doc-holder.entity';
import { VesselOwner } from './vessel-owner.entity';
import { Voyage } from '../../../modules-qa/voyage/voyage.entity';
// import { IncidentInvestigation } from 'src/modules-qa/incident-investigation/entity/incident-investigation.entity';
// import { VesselScreening } from 'src/modules-qa/vessel-screening/entity/vessel-screening.entity';
// import { AuditWorkspace } from 'src/modules/audit-workspace/entities/audit-workspace.entity';
// import { PlanningRequest } from 'src/modules/planning-request/entities/planning-request.entity';
import { hashImage } from '../../../commons/functions';
import { IncidentInvestigation } from '../../../modules-qa/incident-investigation/entity/incident-investigation.entity';
import { VesselScreening } from '../../../modules-qa/vessel-screening/entity/vessel-screening.entity';
import { AuditWorkspace } from '../../audit-workspace/entities/audit-workspace.entity';
import { PlanningRequest } from '../../planning-request/entities/planning-request.entity';
import { Injury } from '../../../modules-qa/injury/entity/injury.entity';
import { VesselGeneralHistory } from './vessel-history.entity';
import { Country } from '../../country/country.entity';
import { ZenithPotentialRiskData } from '../../../modules-qa/vessel-scheduler-module/entity/zenith-potential-risk-data.entity';
import { RightShipRestrictions } from '../../../modules-qa/right-ship/rightship-restrictions.entity';
import { VesselBlacklistMOU } from './vessel-blacklist-mou.entity';

@Entity()
// Remove the imo index for avoid duplicate contraints error
// @Index(DBIndexes.IDX_VESSEL_IMO_NUMBER, ['imoNumber', 'companyId'], {
//   unique: true,
//   where: 'deleted = false',
// })
@Index(DBIndexes.IDX_VESSEL_CODE_COMPANYID, ['code', 'companyId'], {
  unique: true,
  where: 'deleted = false',
})
// @Index(DBIndexes.IDX_VESSEL_NAME_COMPANYID, ['name', 'companyId'], {
//   unique: true,
//   where: 'deleted = false',
// })
export class Vessel extends IdentifyEntity {
  @Column({ nullable: true })
  public image?: string;

  @Column({ type: 'citext' })
  public imoNumber: string;

  @Column({ type: 'citext' })
  public name: string;

  @Column({ type: 'citext' })
  public code: string;

  // @Column({ nullable: true })
  // public countryFlag: string;

  @Column({ type: 'int4', nullable: true })
  public countryId: number;

  @Column({ type: 'uuid' })
  public vesselTypeId: string;

  @Column({ nullable: true })
  public callSign?: string;

  @Column({ type: 'timestamp' })
  public buildDate: string;

  @Column({ nullable: true })
  public shipyardName?: string;

  // @Column({ nullable: true })
  // public shipyardCountry?: string;

  @Column({ type: 'int4', nullable: true })
  public shipyardCountryId?: number;

  @Column({ nullable: true })
  public officialNumber?: string;

  @Column({ type: 'uuid', nullable: true })
  public classificationSocietyId?: string;

  @Column({ nullable: true })
  public vesselClass?: string;

  @Column({ nullable: true })
  public hullNumber?: string;

  @Column({ nullable: true })
  public fleetName?: string;

  // @Column({ type: 'uuid' })
  // public fleetId: string;

  // @Column({ type: 'uuid', nullable: true })
  // public divisionId: string;

  @Column({ type: 'enum', enum: CommonStatus })
  public status: string;

  @Column({ type: 'uuid', nullable: true })
  public docHolderId: string;

  @Column({ type: 'boolean', default: false, nullable: true })
  docResponsiblePartyInspection: boolean;

  @Column({ type: 'boolean', default: false, nullable: true })
  docResponsiblePartyQA: boolean;

  @Column({ type: 'numeric', precision: 12, scale: 2 })
  public deadWeightTonnage: number;

  @Column({ type: 'numeric', nullable: true })
  public grt?: number;

  @Column({ type: 'numeric', nullable: true })
  public nrt?: number;

  @Column({ nullable: true })
  public teuCapacity?: string;

  @Column({ type: 'numeric', nullable: true })
  public maxDraft?: number;

  @Column({ nullable: true })
  public lightShip?: string;

  @Column({ type: 'numeric', nullable: true })
  public loa?: number;

  @Column({ type: 'numeric', nullable: true })
  public lbp?: number;

  @Column({ type: 'numeric', nullable: true })
  public breath?: number;

  @Column({ type: 'numeric', nullable: true })
  public height?: number;

  @Column({ type: 'numeric', nullable: true })
  public depth?: number;

  @Column({ type: 'boolean', default: false })
  public customerRestricted: boolean;

  @Column({ type: 'boolean', default: false })
  public blacklistOnMOUWebsite: boolean;

  @Column({ type: 'boolean', default: false, nullable: true })
  isImported: boolean;

  @Column({
    type: 'text',
    array: true,
    default: [],
  })
  public officers: string[];

  @Column({
    type: 'text',
    array: true,
    default: [],
  })
  public rating: string[];

  @Column({ type: 'uuid', nullable: true })
  public crewGroupingId?: string;

  @Column({ type: 'uuid' })
  public companyId: string;

  @Column({ type: 'uuid', nullable: true })
  public createdUserId?: string;

  @Column({ type: 'uuid', nullable: true })
  public updatedUserId?: string;

  @Column({ type: 'boolean', default: false, nullable: true })
  isVesselRestricted: boolean;

  @Column({ type: 'boolean', default: false, nullable: true })
  isCompanyRestricted: boolean;

  @Column({ type: 'boolean', default: false, nullable: true })
  isITF: boolean;

  // Relationships
  @ManyToMany(() => User, (user) => user.vessels)
  @JoinTable({ name: 'vessel_user' })
  owners: User[];

  @ManyToOne(() => Company, (company) => company.vessel, { onDelete: 'CASCADE' })
  company: Company;

  @ManyToOne(() => Company, (company) => company.vessel, { onDelete: 'CASCADE' })
  docHolder: Company;

  @ManyToOne(() => VesselType, (vesselType) => vesselType.vessels, { onDelete: 'CASCADE' })
  vesselType: VesselType;

  @ManyToOne(() => CrewGrouping, (crewGrouping) => crewGrouping.vessels, { onDelete: 'CASCADE' })
  crewGrouping: CrewGrouping;

  // @ManyToOne(() => Division, (division) => division.vessels, { onDelete: 'CASCADE' })
  // division: Division;

  @ManyToOne(
    () => ClassificationSociety,
    (classificationSociety) => classificationSociety.vessels,
    { onDelete: 'CASCADE' },
  )
  classificationSociety: ClassificationSociety;

  // @ManyToOne(() => Fleet, (fleet) => fleet.vessels, { onDelete: 'CASCADE' })
  // fleet: Fleet;

  @ManyToOne(() => User, { onDelete: 'NO ACTION' })
  createdUser: User;

  @ManyToOne(() => User, { onDelete: 'NO ACTION' })
  updatedUser: User;

  @ManyToOne(() => Country, { onDelete: 'NO ACTION' })
  country: Country;

  @ManyToOne(() => Country, { onDelete: 'NO ACTION' })
  shipyardCountry: Country;

  @OneToMany(() => VesselDocHolder, (vesselDocHolder) => vesselDocHolder.vessel)
  vesselDocHolders: VesselDocHolder[];

  @OneToMany(() => VesselGeneralHistory, (vesselGeneralHistory) => vesselGeneralHistory.vessel)
  vesselGeneralHistory: VesselGeneralHistory[];

  @OneToMany(() => VesselCharterer, (vesselCharterer) => vesselCharterer.vessel)
  vesselCharterers: VesselCharterer[];

  @OneToMany(() => VesselOwner, (vesselOwner) => vesselOwner.vessel)
  vesselOwners: VesselOwner[];

  @OneToOne(() => DivisionMapping, (mapping) => mapping.vessel)
  divisionMapping: DivisionMapping;

  @OneToMany(() => RightShip, (rightShip) => rightShip.vessel)
  rightShips: RightShip[];

  @OneToMany(() => Voyage, (voyage) => voyage.vessel)
  voyages: Voyage[];

  @OneToMany(
    (type) => IncidentInvestigation,
    (incidentInvestigation) => incidentInvestigation.vessel,
  )
  incidentInvestigation: IncidentInvestigation[];

  @OneToMany((type) => Injury, (injury) => injury.vessel)
  injury: Injury[];

  @OneToMany((type) => VesselScreening, (VesselScreening) => VesselScreening.vessel)
  vesselScreening: VesselScreening[];

  @OneToMany((type) => AuditWorkspace, (auditWorkSpace) => auditWorkSpace.vessel)
  auditWorkspace: AuditWorkspace[];

  @OneToMany((type) => PlanningRequest, (planningRequest) => planningRequest.vessel)
  planningRequest: PlanningRequest[];

  @OneToMany(
    (type) => ZenithPotentialRiskData,
    (zenithPotentialRiskData) => zenithPotentialRiskData.vessel,
  )
  zenithPotentialRiskData: ZenithPotentialRiskData[];

  @OneToMany(
    (type) => RightShipRestrictions,
    (rightShipRestrictions) => rightShipRestrictions.vessel,
  )
  rightShipRestrictions: RightShipRestrictions[];

  @OneToMany((type) => VesselBlacklistMOU, (blacklistMOU) => blacklistMOU.vessel)
  blacklistMOU: VesselBlacklistMOU[];

  @AfterLoad()
  async transformAttachment() {
    if (this.image) {
      this.image = await hashImage(this.image);
    }
  }
}
