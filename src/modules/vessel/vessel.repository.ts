import { cloneDeep, omit } from 'lodash';
import moment from 'moment';
import {
  BaseError,
  BaseMultiErrors,
  CommonStatus,
  ConstLib,
  LoggerCommon,
  RoleScopeCheck,
  TokenPayloadModel,
  TypeORMRepository,
  Utils,
} from 'svm-nest-lib-v3';
import { Connection, EntityRepository, getConnection, getRepository, In } from 'typeorm';

import { DBErrorCode } from '../../commons/consts/db.const';
import {
  ApiOrManual,
  CompanyLevelEnum,
  GlobalStatusEnum,
  PlanningRequestStatus,
} from '../../commons/enums';
import { ClassDispensations } from '../../modules-qa/class-dispensations/entity/class-dispensations.entity';
import { DryDocking } from '../../modules-qa/dry-docking/entity/dry-docking.entity';
import { ExternalInspections } from '../../modules-qa/external-inspections/entity/external-inspections.entity';
import { IncidentInvestigation } from '../../modules-qa/incident-investigation/entity/incident-investigation.entity';
import { Injury } from '../../modules-qa/injury/entity/injury.entity';
import { InternalInspections } from '../../modules-qa/internal-inspections/entity/internal-inspections.entity';
import { MaintenancePerformance } from '../../modules-qa/maintenance-performance/entity/maintenance-performance.entity';
import { OtherSmsRecords } from '../../modules-qa/other-sms-records/entity/other-sms-records.entity';
import { OtherTechRecords } from '../../modules-qa/other-technical-records/entity/other-technical-records.entity';
import { PlansDrawings } from '../../modules-qa/plans-drawings/entity/plans-drawings.entity';
import { PortStateControl } from '../../modules-qa/port-state-control/entity/port-state-control.entity';
import { SurveyClassInfo } from '../../modules-qa/survey/survey-class-info/entity/survey-class-info.entity';
import { VesselScreening } from '../../modules-qa/vessel-screening/entity/vessel-screening.entity';
import { PlanedMaintenanceSystem } from '../../modules/pms/pms.entity';
import {
  Cols,
  convertFilterField,
  createSelectSql,
  createWhereSql,
  customSort,
  handleGetDataForAGGrid,
  handleResponse,
  isDoingGrouping,
  isEqualStringIC,
  MySet,
  PayloadAGGridDto,
  Soft,
} from '../../utils';
import { AuditWorkspaceRepository } from '../audit-workspace/repositories/audit-workspace.repository';
import { DivisionMapping } from '../division-mapping/division-mapping.entity';
import { PlanningRequest } from '../planning-request/entities/planning-request.entity';
import { PlanningRequestRepository } from '../planning-request/repositories/planning-request.repository';
import { User } from '../user/user.entity';
import { CreateVesselDto, ExportVesselQueryDTO, ListVesselDto, UpdateVesselDto } from './dto';
import { AuditTimeAndDueDateDto } from './dto/audit-time-and-due-date.dto';
import { VesselCharterer } from './entity/vessel-charterer.entity';
import { VesselDocHolder } from './entity/vessel-doc-holder.entity';
import { VesselGeneralHistory } from './entity/vessel-history.entity';
import { CommonConfiguration } from './entity/vessel-log-sync-data.entity';
import { VesselOwner } from './entity/vessel-owner.entity';
import { Vessel } from './entity/vessel.entity';
import { VesselDocHolderEnum, VesselModuleEnum } from './vessel.enum';
import { AuditWorkspace } from '../audit-workspace/entities/audit-workspace.entity';
import { AppConst } from '../../commons/consts/app.const';
import { AnalyticalReportInspectionPerformanceRepository } from '../analytical-report/repositories/analytical-report-inspection-performance.repository';
import { Column, SortColumn, VESSEL_FILTER_FIELDS } from './dto/filter-vessel.dto';
import { isEqual } from 'lodash';
import { InspectionMappingRepository } from '../inspection-mapping/repositories/inspection-mapping.repository';
import { VesselTypeRepository } from '../vessel-type/vessel-type.repository';
import { EntityType } from '../dashboard/enums';
import _ from 'lodash';
import { Company } from '../company/company.entity';
import { RightShipRestrictions } from 'src/modules-qa/right-ship/rightship-restrictions.entity';
import { VesselBlacklistMOU } from './entity/vessel-blacklist-mou.entity';
import { IARPlanningRequestRepository } from '../internal-audit-report/repositories/iar-caches.repository';
import {
  ROFPlanningRequestRepository,
  ROFUserRepository,
} from '../report-finding/repositories/rof-caches.repository';
import { SourceEnum } from 'src/modules-qa/right-ship/enums';
import { CompanyRepository } from '../company/company.repository';
import { Country } from '../country/country.entity';
import { WhiteListCountry } from './entity/white-list-country.entity';
import { BlackListCountry } from './entity/black-list-country.entity';
import { GreyListCountry } from './entity/grey-list-country.entity';
import { VesselRiskRequest } from './entity/vessel-risk-request.entity';
import { VesselScreeningSummaryReferenceEnum } from 'src/modules-qa/vessel-screening/entity/vessel-screening-summary.entity';
import { VesselScreeningSummaryRepository } from 'src/modules-qa/vessel-screening/repository/vessel-screening-summary.repository';
import { ClassificationSociety } from 'src/modules-qa/classification-society/classification-society.entity';

@EntityRepository(Vessel)
export class VesselRepository extends TypeORMRepository<Vessel> {
  constructor(private readonly connection: Connection) {
    super();
  }

  async createVessel(entityParam: CreateVesselDto, token: TokenPayloadModel) {
    try {
      // Validate active vesel type
      if (entityParam.vesselTypeId) {
        await this.manager
          .getCustomRepository(VesselTypeRepository)
          ._checkActiveVesselType([entityParam.vesselTypeId], token);
      }
      // Insert to vessel table
      const {
        vesselDocHolders,
        vesselCharterers,
        vesselOwners,
        vesselGeneralHistory,
        blacklistData,
      } = entityParam;
      const preparedVesselParams = omit(entityParam, [
        'ownerIds',
        'charterers',
        'owners',
        'divisionId',
        'blacklistData',
      ]);
      // return Object.assign(vesselParams, {
      //   companyId: token.companyId,
      //   createdUserId: token.id,
      //   owners: entityParam.ownerIds.map((ownerId) => ({ id: ownerId } as User)),
      // });
      await this.connection.transaction(async (manager) => {
        if (entityParam.ownerIds) {
          Object.assign(preparedVesselParams, {
            owners: entityParam.ownerIds.map((ownerId) => ({ id: ownerId } as User)),
          });
        }
        const newVessel = await manager.save(
          Vessel,
          Object.assign(preparedVesselParams, {
            companyId: token.companyId,
            createdUserId: token.id,
            docHolderId: vesselDocHolders[0].companyId,
          }) as Vessel,
        );
        // Create division mapping
        if (entityParam.divisionId) {
          const preparedDivisionMapping = {
            vesselId: newVessel.id,
            divisionId: entityParam.divisionId,
            createdUserId: token.id,
            companyId: token.companyId,
          };
          await manager.save(DivisionMapping, preparedDivisionMapping);
        }
        //  create exVesselName
        const preparedVesselGeneralHistory: VesselGeneralHistory[] = [];
        if (vesselGeneralHistory && vesselGeneralHistory.length > 0) {
          for (let i = 0; i < vesselGeneralHistory.length; i++) {
            preparedVesselGeneralHistory.push({
              name: vesselGeneralHistory[i].name,
              fromDate: vesselGeneralHistory[i].fromDate,
              toDate: vesselGeneralHistory[i].toDate,
              companyId: token.companyId,
              vesselId: newVessel.id,
            } as VesselGeneralHistory);
            await manager.save(VesselGeneralHistory, preparedVesselGeneralHistory);
          }
        }
        // Create doc holder
        const preparedDocHolder: VesselDocHolder[] = [];
        if (vesselDocHolders && vesselDocHolders.length > 0) {
          for (let i = 0; i < vesselDocHolders.length; i++) {
            const currentTime = new Date();
            let docHolderStatus = CommonStatus.IN_ACTIVE;
            if (
              new Date(vesselDocHolders[i].fromDate) <= currentTime &&
              (vesselDocHolders[i].toDate === null ||
                new Date(vesselDocHolders[i].toDate) >= currentTime)
            ) {
              docHolderStatus = CommonStatus.ACTIVE;
            }
            preparedDocHolder.push({
              companyId: vesselDocHolders[i].companyId,
              fromDate: vesselDocHolders[i].fromDate,
              toDate: vesselDocHolders[i]?.toDate || null,
              status: docHolderStatus,
              responsiblePartyQA: vesselDocHolders[i]?.responsiblePartyQA || false,
              responsiblePartyInspection: vesselDocHolders[i]?.responsiblePartyInspection || false,
              remark: vesselDocHolders[i]?.remark || null,
              createdAt: new Date(moment().unix() * 1000 - i),
              vesselId: newVessel.id,
            } as VesselDocHolder);
          }
          if (preparedDocHolder.length > 0) {
            await manager.save(VesselDocHolder, preparedDocHolder);
          }
        }

        // Create vessel charterer
        const preparedCharterers: VesselCharterer[] = [];
        if (vesselCharterers && vesselCharterers.length > 0) {
          for (let i = 0; i < vesselCharterers.length; i++) {
            const currentTime = new Date();
            let vesselCharterStatus = CommonStatus.IN_ACTIVE;
            if (
              new Date(vesselCharterers[i].fromDate) <= currentTime &&
              (vesselCharterers[i].toDate === null ||
                new Date(vesselCharterers[i].toDate) >= currentTime)
            ) {
              vesselCharterStatus = CommonStatus.ACTIVE;
            }
            preparedCharterers.push({
              companyId: vesselCharterers[i].companyId,
              fromDate: vesselCharterers[i].fromDate,
              toDate: vesselCharterers[i]?.toDate || null,
              status: vesselCharterStatus,
              remark: vesselCharterers[i]?.remark || null,
              type: vesselCharterers[i].type,
              responsiblePartyInspection: vesselCharterers[i].responsiblePartyInspection,
              responsiblePartyQA: vesselCharterers[i].responsiblePartyQA,
              createdAt: new Date(moment().unix() * 1000 - i),
              vesselId: newVessel.id,
            } as VesselCharterer);
          }
          if (preparedCharterers.length > 0) {
            await manager.save(VesselCharterer, preparedCharterers);
          }
        }

        // Create vessel owner
        const preparedOwners: VesselOwner[] = [];
        if (vesselOwners && vesselOwners.length > 0) {
          for (let j = 0; j < vesselOwners.length; j++) {
            const currentTime = new Date();
            let vesselOwnerStatus = CommonStatus.IN_ACTIVE;
            if (
              new Date(vesselOwners[j].fromDate) <= currentTime &&
              (vesselOwners[j].toDate === null || new Date(vesselOwners[j].toDate) >= currentTime)
            ) {
              vesselOwnerStatus = CommonStatus.ACTIVE;
            }
            preparedOwners.push({
              companyId: vesselOwners[j].companyId,
              fromDate: vesselOwners[j].fromDate,
              toDate: vesselOwners[j]?.toDate || null,
              status: vesselOwnerStatus,
              remark: vesselOwners[j]?.remark || null,
              responsiblePartyInspection: vesselOwners[j].responsiblePartyInspection,
              responsiblePartyQA: vesselOwners[j].responsiblePartyQA,
              createdAt: new Date(moment().unix() * 1000 - j),
              vesselId: newVessel.id,
            } as VesselOwner);
          }
          if (preparedOwners.length > 0) {
            await manager.save(VesselOwner, preparedOwners);
          }
        }

        // Create blacklist mou
        const preparedBlacklists = [];
        if (blacklistData && blacklistData.length > 0) {
          for (let j = 0; j < blacklistData.length; j++) {
            preparedBlacklists.push({
              blacklistType: blacklistData[j].blacklistType,
              companyId: blacklistData[j]?.companyId || null,
              vesselId: newVessel?.id,
              description: blacklistData[j]?.description || null,
              authorityMasterId: blacklistData[j]?.authorityMasterId || null,
              effectiveFrom: blacklistData[j]?.effectiveFrom,
              effectiveTo: blacklistData[j]?.effectiveTo || null,
            });
          }
          if (preparedOwners.length > 0) {
            await manager.save(VesselBlacklistMOU, preparedBlacklists);
          }
        }

        // update vessel risk request
        await this.updateVesselRiskRequest(newVessel?.id, entityParam);

        // await manager.increment(Company, { id: newVessel.companyId }, 'numVessels', 1);
        return newVessel;
      });
      return 1;
    } catch (ex) {
      LoggerCommon.error('[VesselRepository] createVessel error ', ex.message || ex);
      if (ex.code === DBErrorCode.UNIQUE_VIOLATION) {
        const foundEntities = await this.createQueryBuilder('vessel')
          .select(['vessel.imoNumber', 'vessel.code'])
          .where('(vessel.imoNumber = :imoNumber OR vessel.code = :code)', {
            imoNumber: entityParam.imoNumber,
            code: entityParam.code,
          })
          .andWhere('vessel.deleted = :deleted', { deleted: false })
          .getMany();

        // Handle unique constraints multiply
        const errorList = [];
        let imoSeen = false;
        let codeSeen = false;
        for (let i = 0; i < foundEntities.length; i++) {
          if (isEqualStringIC(foundEntities[i].imoNumber, entityParam.imoNumber) && !imoSeen) {
            errorList.push({ fieldName: 'imoNumber', message: 'vessel.VESSEL_IMO_NUMBER_EXISTED' });
            imoSeen = true;
          }
          if (isEqualStringIC(foundEntities[i].code, entityParam.code) && !codeSeen) {
            errorList.push({ fieldName: 'code', message: 'vessel.VESSEL_CODE_EXISTED' });
            codeSeen = true;
          }
        }
        throw new BaseMultiErrors({
          status: 400,
          errors: errorList,
        });
      }
      throw ex;
    }
  }

  customSort = (sortValue: Soft[]): string => {
    if (sortValue?.length) {
      const numberSort = sortValue[0]?.sort === 'ASC' ? 1 : -1;
      const textSoft = sortValue[0]?.colId;

      return `${textSoft}:${numberSort}`;
    }
  };

  genResponseGroupVessel(data: any[], keys: string[]) {
    let keyExpect;
    let key;

    if (isEqual(keys, ['vesselType_name', 'count'])) {
      keyExpect = 'type';
      key = 'vesselType_name';
    }

    if (isEqual(keys, ['vessel_imoNumber', 'count'])) {
      keyExpect = 'imoNumber';
      key = 'vessel_imoNumber';
    }

    if (isEqual(keys, ['vessel_name', 'count'])) {
      keyExpect = 'name';
      key = 'vessel_name';
    }

    if (isEqual(keys, ['vessel_code', 'count'])) {
      keyExpect = 'code';
      key = 'vessel_code';
    }

    if (isEqual(keys, ['company_name', 'count'])) {
      keyExpect = 'company';
      key = 'company_name';
    }

    if (isEqual(keys, ['division_name', 'count'])) {
      keyExpect = 'division';
      key = 'division_name';
    }

    if (isEqual(keys, ['companyVesselDocHolders_name', 'count'])) {
      keyExpect = 'docHolder';
      key = 'companyVesselDocHolders_name';
    }

    if (isEqual(keys, ['country_name', 'count'])) {
      keyExpect = 'flag';
      key = 'country_name';
    }

    if (isEqual(keys, ['status', 'count'])) {
      keyExpect = 'status';
      key = 'status';
    }

    const result = data.map((obj) => {
      const groupKey = Object.keys(obj);

      if (groupKey.includes(key) && key !== 'status') {
        return {
          ...obj,
          [keyExpect]: obj[key],
          [key]: undefined,
        };
      }
      return obj;
    });
    return result;
  }

  async listVessels(
    token: TokenPayloadModel,
    query: ListVesselDto,
    pilotTerminal?: boolean,
    body?: PayloadAGGridDto,
  ) {
    const queryBuilder = this.createQueryBuilder('vessel')
      // .distinct()
      .leftJoin('vessel.vesselType', 'vesselType')
      .leftJoin('vessel.company', 'company')
      .leftJoin('vessel.classificationSociety', 'classificationSociety')
      .leftJoin('vessel.divisionMapping', 'divisionMapping')
      .leftJoin('divisionMapping.division', 'division')
      .leftJoin('vessel.crewGrouping', 'crewGrouping')
      .leftJoin('vessel.owners', 'owners')
      .leftJoin('vessel.country', 'country')
      .leftJoin('division.users', 'users')
      .leftJoin('vessel.vesselDocHolders', 'vesselDocHolders', `vesselDocHolders.status = 'active'`)
      .leftJoin('vesselDocHolders.company', 'companyVesselDocHolders')
      .leftJoin('companyVesselDocHolders.companyTypes', 'companyTypes')
      .leftJoin('companyVesselDocHolders.country', 'companyCountry')
      .leftJoin('vessel.vesselOwners', 'vesselOwners')
      .leftJoin('vesselOwners.company', 'companyVesselOwnersPlans')
      .leftJoin('vessel.vesselCharterers', 'vesselCharterers')
      .leftJoin('vesselCharterers.company', 'companyVesselCharterers')
      .where(`vessel.companyId = '${token.companyId}'`);

    const fieldSelects = [
      'vesselType.name',
      'company.id',
      'company.name',
      'company.companyIMO',
      'divisionMapping.id',
      'division.id',
      'division.code',
      'division.name',
      'classificationSociety.id',
      'classificationSociety.code',
      'classificationSociety.name',
      'crewGrouping.id',
      'crewGrouping.code',
      'crewGrouping.name',
      'crewGrouping.officers',
      'crewGrouping.rating',
      'country.id',
      'country.name',
      'vesselDocHolders.id',
      'vesselDocHolders.companyId',
      'vesselDocHolders.fromDate',
      'vesselDocHolders.toDate',
      'vesselDocHolders.responsiblePartyInspection',
      'vesselDocHolders.responsiblePartyQA',
      'vesselDocHolders.status',
      'companyVesselDocHolders.id',
      'companyVesselDocHolders.name',
      'companyVesselDocHolders.code',
      'companyVesselDocHolders.companyIMO',
      'companyVesselDocHolders.abbreviation',
      'companyCountry.name',
      'companyCountry.nationality',
      'companyTypes.companyType',
    ];

    if (query.companyId) {
      queryBuilder.andWhere(`vessel.companyId = '${query.companyId}'`);
    }

    if (query.content) {
      queryBuilder.andWhere(
        `(vessel.code LIKE '%${query.content}%' OR vessel.name LIKE '%${query.content}%' OR vessel.imoNumber LIKE '%${query.content}%')`,
      );
    }

    if (query.vesselTypeId) {
      queryBuilder.andWhere(`vessel.vesselTypeId = '${query.vesselTypeId}'`);
    }

    if (query.status) {
      queryBuilder.andWhere(`vessel.status = '${query.status}'`);
    }
    if (query.ownerId) {
      queryBuilder.andWhere(`owners.id = '${query.ownerId}'`);
    }

    if (query.moduleName && query.moduleName == VesselModuleEnum.VESSEL_SCHEDULER) {
      queryBuilder
        .leftJoin('vessel.zenithPotentialRiskData', 'zenithPotentialRiskData')
        .addSelect([
          'zenithPotentialRiskData.id',
          'zenithPotentialRiskData.createdAt',
          'zenithPotentialRiskData.updatedAt',
          'zenithPotentialRiskData.potentialScore',
          'zenithPotentialRiskData.potentialWeightage',
          'zenithPotentialRiskData.potentialRiskPercentage',
        ])
        .getMany();

      fieldSelects.push(
        'zenithPotentialRiskData.id',
        'zenithPotentialRiskData.createdAt',
        'zenithPotentialRiskData.updatedAt',
        'zenithPotentialRiskData.potentialScore',
        'zenithPotentialRiskData.potentialWeightage',
        'zenithPotentialRiskData.potentialRiskPercentage',
      );
    }

    if (query.forCreateDivision) {
      queryBuilder.andWhere('divisionMapping.id IS NULL');
    }

    if (!pilotTerminal && !RoleScopeCheck.isAdmin(token)) {
      const {
        whereForExternal,
        whereForMainAndInternal,
      } = await this._supportWhereDOCChartererOwner(
        token.explicitCompanyId,
        query.moduleName ? query.moduleName : VesselModuleEnum.INSPECTION,
      );

      if (String(query.byUserCreated) === 'true') {
        if (
          token.companyLevel === CompanyLevelEnum.MAIN_COMPANY ||
          token.companyLevel === CompanyLevelEnum.INTERNAL_COMPANY
        ) {
          queryBuilder.andWhere(
            `( users.id = '${token.id}' ` +
            whereForMainAndInternal +
            ` or vessel.createdUserId = '${token.id}')`,
          );
        } else {
          queryBuilder.andWhere(
            `( users.id = '${token.id}' ` +
            whereForExternal +
            ` or vessel.createdUserId = '${token.id}')`,
          );
        }
      } else {
        if (
          token.companyLevel === CompanyLevelEnum.MAIN_COMPANY ||
          token.companyLevel === CompanyLevelEnum.INTERNAL_COMPANY
        ) {
          queryBuilder.andWhere(`( users.id = '${token.id}' ` + whereForMainAndInternal + ')');
        } else if (
          token.companyLevel === CompanyLevelEnum.EXTERNAL_COMPANY &&
          query.isHomepage &&
          (await this.connection
            .getCustomRepository(PlanningRequestRepository)
            .checkRoleAndCompanyType(token))
        ) {
        } else {
          queryBuilder.andWhere(`( users.id = '${token.id}' ` + whereForExternal + ')');
        }
      }
    }

    const connection = getConnection();
    const subQueryBuilder = connection.createQueryBuilder();
    this.buildSql(body, queryBuilder, subQueryBuilder, fieldSelects);

    //update after build sql ag-grid
    queryBuilder.addSelect([
      'owners.id',
      'owners.username',
      'owners.employeeId',
      'owners.userType',
      'owners.controlType',
      'owners.email',
      'vesselOwners.id',
      'companyVesselOwnersPlans.name',
    ]);

    let dataList = await handleGetDataForAGGrid(
      this,
      queryBuilder,
      query,
      body,
      subQueryBuilder,
      queryBuilder,
      'vessel',
      [`"vesselRestricted"`, `"companyRestricted"`, `"mouBlackList"`],
    );
    if (body && isDoingGrouping(body)) {
      return dataList;
    }

    const vesselData = cloneDeep(dataList.data);
    if (vesselData.length) {
      let vesselInspectionPerformanceScoreMap = new Map<string, number>();
      if (query.moduleName && query.moduleName == VesselModuleEnum.VESSEL_SCHEDULER) {
        const vesselIds = vesselData.map((vessel) => vessel.id);

        const [workspaces, plannings] = await Promise.all([
          this.manager
            .createQueryBuilder(AuditWorkspace, 'auditWorkspace')
            .leftJoin('auditWorkspace.planningRequest', 'planning')
            .where('auditWorkspace.vesselId IN (:...vesselId)', {
              vesselId: vesselIds,
            })
            .andWhere('planning.globalStatus IN (:...status)', {
              status: [
                GlobalStatusEnum.APPROVED_CAP_NO_VERIFICATION,
                GlobalStatusEnum.WAITING_VERIFICATION,
                GlobalStatusEnum.APPROVED_VERIFICATION,
              ],
            })
            .select([
              'auditWorkspace.id',
              'auditWorkspace.submittedDate',
              'auditWorkspace.vesselId',
              'auditWorkspace.createdAt',
            ])
            .orderBy('auditWorkspace.createdAt')
            .getMany(),
          this.manager
            .createQueryBuilder(PlanningRequest, 'planningRequest')
            .where(
              'planningRequest.vesselId IN (:...vesselId) AND planningRequest.status IN (:...status)',
              {
                vesselId: vesselIds,
                status: [
                  PlanningRequestStatus.SUBMITTED,
                  PlanningRequestStatus.APPROVED,
                  PlanningRequestStatus.REVIEWED_1,
                  PlanningRequestStatus.REVIEWED_2,
                  PlanningRequestStatus.REVIEWED_3,
                  PlanningRequestStatus.REVIEWED_4,
                  PlanningRequestStatus.REVIEWED_5,
                  PlanningRequestStatus.IN_PROGRESS,
                ],
              },
            )
            .orderBy('planningRequest.createdAt')
            .select([
              'planningRequest.id',
              'planningRequest.plannedFromDate',
              'planningRequest.vesselId',
              'planningRequest.createdAt',
            ])
            .getMany(),
        ]);

        let workSpaceIds = [];
        for (const data of vesselData) {
          data.auditWorkspace = [];
          data.planningRequest = [];

          for (const workspace of workspaces) {
            if (workspace.vesselId === data.id) {
              data.auditWorkspace.push(workspace);
            }
          }

          for (const planning of plannings) {
            if (planning.vesselId === data.id) {
              data.planningRequest.push(planning);
            }
          }

          data.auditWorkspace.splice(0, data.auditWorkspace.length - 1);
          data.planningRequest.splice(0, data.planningRequest.length - 1);
          workSpaceIds.push(data.auditWorkspace[0]?.id);
        }
        workSpaceIds = workSpaceIds.filter((item) => item !== undefined);

        if (workSpaceIds.length > 0) {
          vesselInspectionPerformanceScoreMap = await this.manager
            .getCustomRepository(AnalyticalReportInspectionPerformanceRepository)
            .getInfoInspectionPerformanceScore(workSpaceIds);
        }
      }

      for (const element of vesselData) {
        const vesselDocHolderArray = element?.vesselDocHolders;
        const uniqueVesselDocHolderArray = vesselDocHolderArray?.filter(
          (obj, index, self) => index === self.findIndex((t) => t.companyId === obj.companyId),
        );
        element.vesselDocHolders = uniqueVesselDocHolderArray;
        if (query.moduleName && query.moduleName == VesselModuleEnum.VESSEL_SCHEDULER) {
          let riskRating = undefined;
          // check for observed risk for a vessel
          const vesselScreeningRepository = this.manager.connection.getRepository(VesselScreening);
          const vesselScreeningData = await vesselScreeningRepository
            .createQueryBuilder('vesselScreening')
            .select(['vesselScreening.id', 'vesselScreening.updatedAt'])
            .where(
              // 'vesselScreening.vesselId = :vesselId AND vesselScreening.status in (:...status)',
              'vesselScreening.vesselId = :vesselId',
              {
                vesselId: element.id,
                // status: [VesselScreeningStatusEnum.CLEARED, VesselScreeningStatusEnum.DISAPPROVED],
              },
            )
            .orderBy({ 'vesselScreening.updatedAt': 'DESC' })
            .limit(1)
            .getOne();

          let flag = false; // to check all tab is null or contain some data
          if (vesselScreeningData) {
            const rawQuery = `SELECT
                          sum(vss."observedScore") as "sumObservedScore",
                          count(*) as "count"
                        FROM
                          vessel_screening_summary vss
                        WHERE
                          "vesselScreeningId" = $1 AND
                          ("reviewStatus" = 'Reject' or "reviewStatus" = 'Accept')
                        GROUP BY "tabName" ;`;
            const data = await this.connection.query(rawQuery, [vesselScreeningData.id]);
            let totalRiskRating = 0;
            for (let i = 0; i < data.length; i++) {
              let averageRiskByTab = 0;
              if (data[i].sumObservedScore !== null) {
                flag = true;
                averageRiskByTab = Number(data[i].sumObservedScore) / Number(data[i].count);
              }
              if (averageRiskByTab) {
                totalRiskRating += Number(averageRiskByTab);
              }
            }
            riskRating = totalRiskRating / data.length;

            // const riskRating = flag ? (totalRiskRating / data.length).toFixed(2) : null;
            if (flag && !Number.isInteger(riskRating)) {
              riskRating = Number((totalRiskRating / data.length).toFixed(2));
            }
            if (!flag) {
              riskRating = null;
            }
            if (riskRating) {
              element['observedRiskLastScreening'] = riskRating;
              element['lastScreeningDate'] = vesselScreeningData.updatedAt;
            }
          }

          const workspaces = element.auditWorkspace;
          const plannings = element.planningRequest;

          Object.assign(element, {
            lastInspectionDate: workspaces[0]?.submittedDate || null,
            periodFromLastInspection:
              Math.round(
                Math.abs(new Date().getTime() - workspaces[0]?.submittedDate?.getTime()) /
                AppConst.ONE_DAY_TIMESTAMP,
              ) || 0,
            plannedDateOfInspection: plannings[0]?.plannedFromDate || null,
            lastInspectionPerformanceScore: vesselInspectionPerformanceScoreMap.get(element.id)
              ? Number(vesselInspectionPerformanceScoreMap.get(element.id).toFixed(2))
              : 0,
          });
        }
      }
    }

    return {
      data: vesselData,
      page: dataList.page,
      pageSize: dataList.pageSize,
      totalPage: dataList.totalPage,
      totalItem: dataList.totalItem,
    };
  }

  buildSql(body, queryBuilder, subQueryBuilder, fieldSelects) {
    queryBuilder.select().addSelect(fieldSelects);

    if (body) {
      convertFilterField(body, VESSEL_FILTER_FIELDS);

      queryBuilder
        .addSelect([
          `(CASE WHEN "vessel"."isVesselRestricted" THEN 'Yes' ELSE 'No' END) AS "vesselRestricted"`,
          `(CASE WHEN "vessel"."isCompanyRestricted" THEN 'Yes' ELSE 'No' END) AS "companyRestricted"`,
          `(CASE WHEN "vessel"."blacklistOnMOUWebsite" THEN 'Yes' ELSE 'No' END) AS "mouBlackList"`,
        ])
        .groupBy(
          `
          vessel.id, 
          ${fieldSelects.join(', ')}
      `,
        )
        .andWhere(`vessel.deleted = false`)
        .distinctOn(['vessel.id']);

      subQueryBuilder
        .select(`DISTINCT "vessel_id" AS id`)
        .from(`(${queryBuilder.getQuery()})`, 'subquery');

      createWhereSql(body, subQueryBuilder);
      createSelectSql(body, subQueryBuilder, null, '"vessel_id"');

      queryBuilder.groupBy();
    }
  }

  async listVesselForGraphical(
    token: TokenPayloadModel,
    query: ListVesselDto,
    pilotTerminal?: boolean,
  ) {
    const queryBuilder = this.createQueryBuilder('vessel')
      .leftJoin('vessel.company', 'company')
      .leftJoin('vessel.divisionMapping', 'divisionMapping')
      .leftJoin('divisionMapping.division', 'division')
      .select(['vessel.id', 'vessel.name'])
      .where('(vessel.companyId = :companyId)', {
        companyId: token.companyId,
      });
    queryBuilder
      .leftJoin('division.users', 'users')
      .leftJoin('vessel.vesselDocHolders', 'vesselDocHolders')
      .leftJoin('vessel.vesselOwners', 'vesselOwners')
      .leftJoin('vessel.vesselCharterers', 'vesselCharterers');

    if (!pilotTerminal && !RoleScopeCheck.isAdmin(token)) {
      const {
        whereForExternal,
        whereForMainAndInternal,
      } = await this._supportWhereDOCChartererOwner(
        token.explicitCompanyId,
        query.moduleName ? query.moduleName : VesselModuleEnum.INSPECTION,
      );

      if (String(query.byUserCreated) === 'true') {
        if (
          token.companyLevel === CompanyLevelEnum.MAIN_COMPANY ||
          token.companyLevel === CompanyLevelEnum.INTERNAL_COMPANY
        ) {
          queryBuilder.andWhere(
            `( users.id = :userId ` +
            whereForMainAndInternal +
            ' or vessel.createdUserId = :userId)',
            {
              userId: token.id,
            },
          );
        } else {
          queryBuilder.andWhere(
            `( users.id = :userId ` + whereForExternal + ' or vessel.createdUserId = :userId)',
            {
              userId: token.id,
            },
          );
        }
      } else {
        if (
          token.companyLevel === CompanyLevelEnum.MAIN_COMPANY ||
          token.companyLevel === CompanyLevelEnum.INTERNAL_COMPANY
        ) {
          queryBuilder.andWhere(`( users.id = :userId ` + whereForMainAndInternal + ')', {
            userId: token.id,
          });
        } else if (
          token.companyLevel === CompanyLevelEnum.EXTERNAL_COMPANY &&
          query.isHomepage &&
          (await this.connection
            .getCustomRepository(PlanningRequestRepository)
            .checkRoleAndCompanyType(token))
        ) {
        } else {
          queryBuilder.andWhere(`( users.id = :userId ` + whereForExternal + ')', {
            userId: token.id,
          });
        }
      }
    }

    if (query.vesselName) {
      queryBuilder.andWhere('vessel.name ILIKE :vesselName', {
        vesselName: `%${query.vesselName}%`,
      });
    }
    return await this.getManyQB(queryBuilder);
  }

  async _supportWhereDOCChartererOwner(companyId: string, module: string) {
    const listDOCHolder = await this.manager.find(VesselDocHolder, {
      deleted: false,
      companyId: companyId,
      // status: CommonStatus.ACTIVE,
    });

    const listVesselCharterers = await this.manager.find(VesselCharterer, {
      deleted: false,
      companyId: companyId,
      // status: CommonStatus.ACTIVE,
    });

    const listVesselOwners = await this.manager.find(VesselOwner, {
      deleted: false,
      companyId: companyId,
      // status: CommonStatus.ACTIVE,
    });
    let whereForExternal = '';
    let whereForMainAndInternal = '';
    if (listDOCHolder.length > 0) {
      for (const item of listDOCHolder) {
        whereForExternal += ` or (vesselDocHolders.companyId = '${item.companyId}'  and vessel.id = '${item.vesselId}'
          and vesselDocHolders.status = '${CommonStatus.ACTIVE}'`;
        if (module === VesselModuleEnum.INSPECTION) {
          whereForExternal += ' and vesselDocHolders.responsiblePartyInspection = true) ';
        } else if (module === VesselModuleEnum.QA) {
          whereForExternal += ' and vesselDocHolders.responsiblePartyQA = true) ';
        } else {
          whereForExternal += ') ';
        }
        whereForMainAndInternal += ` or (vesselDocHolders.companyId = '${item.companyId}' and vessel.id = '${item.vesselId}')`;
      }
    }

    if (listVesselCharterers.length > 0) {
      for (const item of listVesselCharterers) {
        whereForExternal += ` or (vesselCharterers.companyId = '${item.companyId}' and vessel.id = '${item.vesselId}'
         and vesselCharterers.status = '${CommonStatus.ACTIVE}'`;
        if (module === VesselModuleEnum.INSPECTION) {
          whereForExternal += ' and vesselCharterers.responsiblePartyInspection = true) ';
        } else if (module === VesselModuleEnum.QA) {
          whereForExternal += ' and vesselCharterers.responsiblePartyQA = true) ';
        } else {
          whereForExternal += ') ';
        }
        whereForMainAndInternal += ` or (vesselCharterers.companyId = '${item.companyId}' and vessel.id = '${item.vesselId}')`;
      }
    }
    if (listVesselOwners.length > 0) {
      for (const item of listVesselOwners) {
        whereForExternal += ` or (vesselOwners.companyId = '${item.companyId}' and vessel.id = '${item.vesselId}' and vesselOwners.status = '${CommonStatus.ACTIVE}'`;

        if (module === VesselModuleEnum.INSPECTION) {
          whereForExternal += ' and vesselOwners.responsiblePartyInspection = true) ';
        } else if (module === VesselModuleEnum.QA) {
          whereForExternal += ' and vesselOwners.responsiblePartyQA = true) ';
        } else {
          whereForExternal += ') ';
        }
        whereForMainAndInternal += ` or (vesselOwners.companyId = '${item.companyId}' and vessel.id = '${item.vesselId}')`;
      }
    }

    return { whereForExternal, whereForMainAndInternal };
  }

  async getDetailVessel(companyId: string, vesselId: string) {
    const vessel = await this.getOneQB(
      this.createQueryBuilder('vessel')
        .leftJoin('vessel.vesselType', 'vesselType')
        .leftJoin('vessel.company', 'company')
        .leftJoin('vessel.owners', 'owners')
        .leftJoin('vessel.classificationSociety', 'classificationSociety')
        .leftJoin('vessel.divisionMapping', 'divisionMapping')
        .leftJoin('divisionMapping.division', 'division')
        .leftJoinAndSelect('vessel.vesselDocHolders', 'vesselDocHolders')
        .leftJoin('vesselDocHolders.company', 'vdhCompany')
        .leftJoin('vessel.crewGrouping', 'crewGrouping')
        .leftJoinAndSelect('owners.rank', 'rank')
        .leftJoinAndSelect('owners.departments', 'departments')
        .leftJoinAndSelect('owners.primaryDepartment', 'primaryDepartment')
        .leftJoinAndSelect('vessel.vesselCharterers', 'vesselCharterers')
        .leftJoin('vesselCharterers.company', 'vcCompany')
        .leftJoinAndSelect('vessel.vesselOwners', 'vesselOwners')
        .leftJoin('vesselOwners.company', 'voCompany')
        .leftJoin('vessel.vesselGeneralHistory', 'vesselGeneralHistory')
        .leftJoin('vessel.country', 'country')
        .leftJoin('vessel.shipyardCountry', 'shipyardCountry')
        .leftJoin('vessel.rightShipRestrictions', 'rightShipRestrictions')
        .leftJoin('vessel.blacklistMOU', 'blacklistMOU')
        .leftJoin('blacklistMOU.authorityMaster', 'authorityMaster')
        .select()
        .addSelect([
          'vesselType.name',
          'company.id',
          'company.name',
          'company.companyIMO',
          'owners.id',
          'owners.username',
          'owners.employeeId',
          'owners.userType',
          'owners.controlType',
          'owners.email',
          'vdhCompany.id',
          'vdhCompany.name',
          'vdhCompany.code',
          'vdhCompany.companyIMO',
          'vcCompany.id',
          'vcCompany.name',
          'vcCompany.code',
          'vcCompany.companyIMO',
          'voCompany.id',
          'voCompany.name',
          'voCompany.code',
          'voCompany.companyIMO',
          'divisionMapping.id',
          'division.id',
          'division.code',
          'division.name',
          'classificationSociety.id',
          'classificationSociety.code',
          'classificationSociety.name',
          'crewGrouping.id',
          'crewGrouping.code',
          'crewGrouping.name',
          'crewGrouping.officers',
          'crewGrouping.rating',
          'vesselGeneralHistory.id',
          'vesselGeneralHistory.fromDate',
          'vesselGeneralHistory.toDate',
          'vesselGeneralHistory.name',
          'vesselGeneralHistory.source',
          'country.id',
          'country.name',
          'shipyardCountry.id',
          'shipyardCountry.name',
          'rightShipRestrictions.id',
          'rightShipRestrictions.restrictionType',
          'rightShipRestrictions.description',
          'rightShipRestrictions.comment',
          'rightShipRestrictions.effectiveFrom',
          'rightShipRestrictions.effectiveTo',
          'rightShipRestrictions.source',
          'rightShipRestrictions.updatedAt',
          'rightShipRestrictions.order',
          'rightShipRestrictions.status',
          'blacklistMOU.id',
          'blacklistMOU.blacklistType',
          'blacklistMOU.vesselId',
          'blacklistMOU.companyId',
          'blacklistMOU.description',
          'blacklistMOU.effectiveFrom',
          'blacklistMOU.effectiveTo',
          'authorityMaster.id',
          'authorityMaster.name',
        ])
        .where(
          '(vessel.companyId = :companyId OR company.parentId = :companyId) AND vessel.id = :vesselId',
          { companyId, vesselId },
        )
        // .andWhere('vesselGeneralHistory.deleted = false')
        .orderBy('vdhCompany.createdAt', 'DESC')
        .addOrderBy('vdhCompany.updatedAt', 'DESC')
        .addOrderBy('vcCompany.createdAt', 'DESC')
        .addOrderBy('vcCompany.updatedAt', 'DESC')
        .addOrderBy('voCompany.createdAt', 'DESC')
        .addOrderBy('voCompany.updatedAt', 'DESC'),
    );

    if (vessel) {
      const companyDetail = await this.connection
        .createQueryBuilder(Company, 'company')
        .where('company.id IN (:...companyIds)', {
          companyIds: vessel.vesselDocHolders.map((x) => x.companyId),
        })
        .select('company.companyIMO')
        .getMany();
      const companyIMOs = companyDetail.map((x) => x.companyIMO);
      const restrictByCompany = await this.connection
        .createQueryBuilder(RightShipRestrictions, 'rightShipRestrictions')
        .where(
          `rightShipRestrictions.code IN (:...code) AND rightShipRestrictions.restrictionType = 'Company'`,
          {
            code: companyIMOs,
          },
        )
        .select([
          'rightShipRestrictions.id',
          'rightShipRestrictions.restrictionType',
          'rightShipRestrictions.description',
          'rightShipRestrictions.comment',
          'rightShipRestrictions.effectiveFrom',
          'rightShipRestrictions.effectiveTo',
          'rightShipRestrictions.source',
          'rightShipRestrictions.updatedAt',
          'rightShipRestrictions.order',
          'rightShipRestrictions.status',
        ])
        .getMany();

      if (vessel?.rightShipRestrictions?.length > 0 || restrictByCompany?.length > 0) {
        let vesselRestrictions = [];
        let companyRestrictions = [];
        // To push vessel restrictions only
        vessel?.rightShipRestrictions.forEach((data) => {
          if (data.restrictionType === 'Vessel') vesselRestrictions.push(data);
        });
        const apiSource = [];
        const manualSource = [];
        if (vesselRestrictions?.length > 0) {
          vesselRestrictions.map((restrictions) => {
            if (restrictions.source === SourceEnum.API) {
              apiSource.push(restrictions);
            } else {
              manualSource.push(restrictions);
            }
          });
          if (apiSource?.length > 0) {
            vesselRestrictions = _.uniqBy(
              apiSource,
              (obj) => `${obj.effectiveFrom}-${obj.effectiveTo}`,
            );
          } else {
            vesselRestrictions = [];
          }
        } else {
          vesselRestrictions = [];
        }
        if (manualSource?.length > 0) {
          vesselRestrictions = [...vesselRestrictions, ...manualSource];
        }
        // If the vessel had been restricted by the company then show the company restrictions details also
        if (restrictByCompany) companyRestrictions.push(...restrictByCompany);
        const companyApiSource = [];
        const companyManualSource = [];
        if (companyRestrictions?.length > 0) {
          companyRestrictions.map((restrictions) => {
            if (restrictions.source === SourceEnum.API) {
              companyApiSource.push(restrictions);
            } else {
              companyManualSource.push(restrictions);
            }
          });
          if (companyApiSource?.length > 0) {
            companyRestrictions = _.uniqBy(
              companyRestrictions,
              (obj) => `${obj.effectiveFrom}-${obj.effectiveTo}`,
            );
          } else {
            companyRestrictions = [];
          }
        } else {
          companyRestrictions = [];
        }
        if (companyManualSource?.length > 0) {
          companyRestrictions = [...companyRestrictions, ...companyManualSource];
        }
        Object.assign(vessel.rightShipRestrictions, [
          ...vesselRestrictions,
          ...companyRestrictions,
        ]);
      }
      return vessel;
    } else {
      throw new BaseError({ status: 404, message: 'vessel.VESSEL_NOT_FOUND' });
    }
  }

  // async getVesselHistoryByVesselId(companyId: string, vesselId: string) {
  //   const vesselHistory = await getRepository(VesselGeneralHistory)
  //     .createQueryBuilder('vesselGeneralHistory')
  //     .select([
  //       'vesselGeneralHistory.id',
  //       'vesselGeneralHistory.fromDate',
  //       'vesselGeneralHistory.toDate',
  //       'vesselGeneralHistory.name',
  //     ])
  //     .where(
  //       'vesselGeneralHistory.companyId = :companyId AND vesselGeneralHistory.vesselId = :vesselId AND vesselGeneralHistory.deleted = :deleted',
  //       { companyId: companyId, vesselId: vesselId, deleted: false },
  //     )
  //     .getMany();
  //   if (vesselHistory) {
  //     return vesselHistory;
  //   } else {
  //     throw new BaseError({ status: 404, message: 'vessel.VESSEL_HISTORY_NOT_FOUND' });
  //   }
  // }

  async getDetailVesselById(companyId: string, vesselId: string) {
    const vessel = await this.getOneQB(
      this.createQueryBuilder('vessel')
        .leftJoin('vessel.vesselType', 'vesselType')
        .leftJoin('vessel.company', 'company')
        .leftJoin('vessel.owners', 'owners')
        .leftJoin('vessel.classificationSociety', 'classificationSociety')
        .leftJoin('vessel.divisionMapping', 'divisionMapping')
        .leftJoin('divisionMapping.division', 'division')
        .leftJoinAndSelect(
          'vessel.vesselDocHolders',
          'vesselDocHolders',
          // 'vesselDocHolders.fromDate < now() and COALESCE(vesselDocHolders.toDate,now()) >= now()',
          'vesselDocHolders.status = :status',
          {
            status: CommonStatus.ACTIVE,
          },
        )
        .leftJoin('vessel.crewGrouping', 'crewGrouping')
        .leftJoinAndSelect(
          'vessel.vesselCharterers',
          'vesselCharterers',
          'vesselCharterers.status = :status',
          {
            status: CommonStatus.ACTIVE,
          },
        )
        .leftJoin('vesselCharterers.company', 'vesselCharterer')
        .leftJoinAndSelect('vessel.vesselOwners', 'vesselOwners', 'vesselOwners.status = :status', {
          status: CommonStatus.ACTIVE,
        })
        .leftJoin('vesselOwners.company', 'vesselOwner')
        .select()
        .where('vessel.id = :vesselId And vessel.deleted = false', {
          vesselId,
        })
        .addSelect([
          'vesselDocHolders.companyId',
          'vesselOwners.companyId',
          'vesselCharterers.companyId',
        ]),
    );

    return vessel;
  }

  async getDetailVesselByIdAll(companyId: string, vesselId: string) {
    const vessel = await this.getOneQB(
      this.createQueryBuilder('vessel')
        .leftJoin('vessel.vesselType', 'vesselType')
        .leftJoin('vessel.company', 'company')
        .leftJoin('vessel.owners', 'owners')
        .leftJoin('vessel.classificationSociety', 'classificationSociety')
        .leftJoin('vessel.divisionMapping', 'divisionMapping')
        .leftJoin('divisionMapping.division', 'division')
        .leftJoinAndSelect('vessel.vesselDocHolders', 'vesselDocHolders')
        .leftJoin('vessel.crewGrouping', 'crewGrouping')
        .leftJoinAndSelect('vessel.vesselCharterers', 'vesselCharterers')
        .leftJoin('vesselCharterers.company', 'vesselCharterer')
        .leftJoinAndSelect('vessel.vesselOwners', 'vesselOwners')
        .leftJoin('vesselOwners.company', 'vesselOwner')
        .select()
        .where('vessel.id = :vesselId And vessel.deleted = false', {
          vesselId,
        })
        .addSelect([
          'vesselDocHolders.companyId',
          'vesselOwners.companyId',
          'vesselCharterers.companyId',
        ]),
    );

    return vessel;
  }

  async getDetailVesselByIdRelate(companyId: string, vesselId: string) {
    const vessel = await this.getOneQB(
      this.createQueryBuilder('vessel')
        .leftJoin('vessel.vesselType', 'vesselType')
        .leftJoin('vessel.company', 'company')
        .leftJoin('vessel.owners', 'owners')
        .leftJoin('vessel.classificationSociety', 'classificationSociety')
        .leftJoin('vessel.divisionMapping', 'divisionMapping')
        .leftJoin('divisionMapping.division', 'division')
        .leftJoinAndSelect(
          'vessel.vesselDocHolders',
          'vesselDocHolders',
          // 'vesselDocHolders.fromDate < now() and COALESCE(vesselDocHolders.toDate,now()) >= now()',
          'vesselDocHolders.status = :status',
          {
            status: CommonStatus.ACTIVE,
          },
        )
        .leftJoin('vessel.crewGrouping', 'crewGrouping')
        .leftJoinAndSelect(
          'vessel.vesselCharterers',
          'vesselCharterers',
          'vesselCharterers.status = :status',
          {
            status: CommonStatus.ACTIVE,
          },
        )
        .leftJoin('vesselCharterers.company', 'vesselCharterer')
        .leftJoinAndSelect('vessel.vesselOwners', 'vesselOwners', 'vesselOwners.status = :status', {
          status: CommonStatus.ACTIVE,
        })
        .leftJoin('vesselOwners.company', 'vesselOwner')
        .select()
        .where('vessel.id = :vesselId And vessel.deleted = false', {
          vesselId,
        }),
    );

    return vessel;
  }

  async updateVessel(vesselId: string, body: UpdateVesselDto, token: TokenPayloadModel) {
    try {
      if (body.status === CommonStatus.IN_ACTIVE) {
        // Check record has been used in other modules
        // const hasRef = await this.hasRefInGivenTables(vesselId, 'vesselId', [
        //   { entity: PlanningRequest },
        //   { entity: PlanedMaintenanceSystem },
        //   { entity: ClassDispensations },
        //   { entity: DryDocking },
        //   { entity: ExternalInspections },
        //   { entity: IncidentInvestigation },
        //   { entity: Injury },
        //   { entity: InternalInspections },
        //   { entity: MaintenancePerformance },
        //   { entity: OtherSmsRecords },
        //   { entity: OtherTechRecords },
        //   { entity: PlansDrawings },
        //   { entity: PortStateControl },
        //   { entity: SurveyClassInfo },
        //   { entity: VesselScreening },
        //   { entity: DivisionMapping },
        // ]);
        // if (hasRef) {
        //   throw new BaseError({
        //     status: 400,
        //     message: 'common.CANNOT_UPDATE_TO_INACTIVE_DUE_TO_REF',
        //   });
        // }
      }

      const vesselFoundById = await this.getDetailVessel(token.companyId, vesselId);
      if (!vesselFoundById) {
        throw new BaseError({ status: 404, message: 'vessel.VESSEL_NOT_FOUND' });
      }

      const company = await this.manager
        .getCustomRepository(CompanyRepository)
        .createQueryBuilder('company')
        .where(
          'company.parentId = :parentId and company.isCompanyRestricted = :isCompanyRestricted',
          {
            parentId: vesselFoundById.companyId,
            isCompanyRestricted: true,
          },
        )
        .getMany();

      // Validate active vesel type
      if (body.vesselTypeId && body.vesselTypeId !== vesselFoundById.vesselTypeId) {
        await this.manager
          .getCustomRepository(VesselTypeRepository)
          ._checkActiveVesselType([body.vesselTypeId], token);
      }
      const preparedVessel = omit(body, [
        'ownerIds',
        'vesselDocHolders',
        'vesselCharterers',
        'vesselOwners',
        'divisionId',
        'blacklistData',
      ]);
      return await this.connection.transaction(async (manager) => {
        // Update vessel
        if (body.ownerIds) {
          Object.assign(preparedVessel, {
            owners: body.ownerIds.map((ownerId) => ({ id: ownerId } as User)),
          });
        }
        let activeDocHolder = undefined;
        let newDocHolderId = undefined;
        if (body.vesselDocHolders.length > 0) {
          activeDocHolder = body.vesselDocHolders.filter((obj) => {
            return obj.toDate === null;
          });
          if (activeDocHolder.length > 0) {
            newDocHolderId = activeDocHolder[0].companyId;
          }
        }
        console.log('preparedVessel: ', preparedVessel.blacklistOnMOUWebsite);
        await manager.save(
          Vessel,
          Object.assign(preparedVessel, {
            id: vesselId,
            companyId: token.companyId,
            updatedUserId: token.id,
            docHolderId: newDocHolderId,
          }),
        );
        //create or up division mapping
        if (body.divisionId) {
          const preparedDivisionMapping = {
            vesselId: vesselId,
            divisionId: body.divisionId,
            companyId: token.companyId,
          } as DivisionMapping;
          if (vesselFoundById.divisionMapping) {
            preparedDivisionMapping.id = vesselFoundById.divisionMapping.id;
            preparedDivisionMapping.updatedUserId = token.id;
          } else {
            preparedDivisionMapping.createdUserId = token.id;
          }
          await manager.save(DivisionMapping, preparedDivisionMapping);
        } else {
          if (vesselFoundById.divisionMapping) {
            // delete division mapping
            await manager.delete(DivisionMapping, {
              id: vesselFoundById.divisionMapping.id,
            });
          }
        }
        //  create or update exvesselnames
        const rawVesselGeneralHistory: VesselGeneralHistory[] = [];
        if (body.vesselGeneralHistory && body.vesselGeneralHistory.length > 0) {
          for (const vesselHistory of body.vesselGeneralHistory) {
            rawVesselGeneralHistory.push({
              ...vesselHistory,
              id: vesselHistory.id ? vesselHistory.id : Utils.strings.generateUUID(),
              companyId: token.companyId,
              vesselId: vesselFoundById.id,
              source: vesselHistory?.source,
            } as VesselGeneralHistory);
          }
          if (rawVesselGeneralHistory && rawVesselGeneralHistory.length > 0) {
            await manager.save(VesselGeneralHistory, rawVesselGeneralHistory);
          }
          // to update the vessel name in ROF/IAR/ROFUser
          await manager
            .getCustomRepository(ROFPlanningRequestRepository)
            ._updateVesselInfo(vesselId, (body as unknown) as Vessel);
          await manager
            .getCustomRepository(IARPlanningRequestRepository)
            ._updateVesselInfo(vesselId, (body as unknown) as Vessel);
          await manager
            .getCustomRepository(ROFUserRepository)
            ._updateVesselManagerRelationship(vesselId, []);
        }

        //#region Prepare data Doc Holder
        const rawDocHolders: VesselDocHolder[] = [];
        if (body.vesselDocHolders && body.vesselDocHolders.length > 0) {
          for (const docHolder of body.vesselDocHolders) {
            const currentTime = new Date();
            let vesselDocHolderStatus = CommonStatus.IN_ACTIVE;
            if (
              new Date(docHolder.fromDate) <= currentTime &&
              (docHolder.toDate === null || new Date(docHolder.toDate) >= currentTime)
            ) {
              vesselDocHolderStatus = CommonStatus.ACTIVE;
            }
            const findDocCompany = company?.find((x) => x.id === docHolder?.companyId);
            if (
              new Date(docHolder?.toDate) <= currentTime &&
              findDocCompany &&
              docHolder?.toDate != null
            ) {
              if (vesselFoundById?.isCompanyRestricted && vesselFoundById?.isVesselRestricted) {
                if (
                  vesselFoundById?.rightShipRestrictions.find((x) => {
                    return x.restrictionType === 'Vessel';
                  })
                ) {
                  await manager.update(
                    Vessel,
                    { id: vesselFoundById.id },
                    { isCompanyRestricted: false },
                  );
                }
                if (
                  vesselFoundById?.rightShipRestrictions?.length === 1 &&
                  vesselFoundById?.rightShipRestrictions[0]?.restrictionType === 'Company'
                ) {
                  await manager.update(
                    Vessel,
                    { id: vesselFoundById.id },
                    {
                      isVesselRestricted: false,
                      isCompanyRestricted: false,
                      customerRestricted: false,
                    },
                  );
                }
              }
            }
            rawDocHolders.push({
              ...docHolder,
              status: vesselDocHolderStatus,
              id: docHolder.id ? docHolder.id : Utils.strings.generateUUID(),
              vesselId: vesselFoundById.id,
            } as VesselDocHolder);
          }
        }

        if (body?.rightShipRestrictions?.length > 0) {
          let isCustomerRestricted = false;
          const currentDate = moment(new Date()).format('YYYY-MM-DD');
          for (const vesselRestrictions of body.rightShipRestrictions) {
            if (vesselRestrictions?.effectiveFrom && vesselRestrictions?.effectiveTo) {
              if (
                currentDate >= moment(vesselRestrictions?.effectiveFrom).format('YYYY-MM-DD') &&
                currentDate <= moment(vesselRestrictions?.effectiveTo).format('YYYY-MM-DD')
              ) {
                isCustomerRestricted = true;
                break;
              }
            } else if (vesselRestrictions?.effectiveFrom) {
              if (currentDate >= moment(vesselRestrictions?.effectiveFrom).format('YYYY-MM-DD')) {
                isCustomerRestricted = true;
                break;
              }
            }
          }

          if (isCustomerRestricted) {
            await manager.update(
              Vessel,
              { id: vesselFoundById?.id },
              {
                customerRestricted: true,
                isVesselRestricted: true,
              },
            );
          } else {
            await manager.update(
              Vessel,
              { id: vesselFoundById?.id },
              {
                customerRestricted: false,
                isVesselRestricted: false,
              },
            );
          }

          if (body?.rightShipRestrictions) {
            await manager.save(RightShipRestrictions, body?.rightShipRestrictions);
          }
        }

        const listCurrentDocHolders = vesselFoundById.vesselDocHolders;
        const listCurrentDocHolderIds: string[] = [];
        const listNewDocHolderIds: string[] = [];

        if (listCurrentDocHolders && listCurrentDocHolders.length > 0) {
          listCurrentDocHolders.forEach((item: VesselDocHolder) => {
            listCurrentDocHolderIds.push(item.id);
          });
        }

        if (rawDocHolders && rawDocHolders.length > 0) {
          rawDocHolders.forEach((item: VesselDocHolder) => {
            listNewDocHolderIds.push(item.id);
          });
        }

        const listDocHoldersUpdateIds = MySet.intersect(
          new Set(listCurrentDocHolderIds),
          new Set(listNewDocHolderIds),
        );

        const listDocHolderCreateIds = MySet.difference(
          new Set(listNewDocHolderIds),
          new Set(listCurrentDocHolderIds),
        );

        const listDocHoldersDeleteIds = MySet.difference(
          new Set(listCurrentDocHolderIds),
          new Set(listNewDocHolderIds),
        );

        const docHoldersCreate = rawDocHolders.filter((item) =>
          listDocHolderCreateIds.has(item.id),
        );

        const docHolderUpdate = rawDocHolders.filter((item) =>
          listDocHoldersUpdateIds.has(item.id),
        );
        //#endregion Prepare data Charterer

        //#region Prepare data Charterer
        const rawCharterers: VesselCharterer[] = [];
        if (body.vesselCharterers && body.vesselCharterers.length > 0) {
          for (let i = 0; i < body.vesselCharterers.length; i++) {
            const charterer = body.vesselCharterers[i];
            const currentTime = new Date();
            let vesselChartererStatus = CommonStatus.IN_ACTIVE;
            if (
              new Date(charterer.fromDate) <= currentTime &&
              (charterer.toDate === null || new Date(charterer.toDate) >= currentTime)
            ) {
              vesselChartererStatus = CommonStatus.ACTIVE;
            }
            rawCharterers.push({
              ...charterer,
              status: vesselChartererStatus,
              id: charterer.id ? charterer.id : Utils.strings.generateUUID(),
              vesselId: vesselFoundById.id,
              type: charterer.type,
              // vesselChartererId: charterer.vesselChartererId,
              // responsiblePartyInspection: charterer.responsiblePartyInspection,
              // responsiblePartyQA: charterer.responsiblePartyQA,
            } as VesselCharterer);
          }
        }

        const listCurrentCharterers = vesselFoundById.vesselCharterers;
        const listCurrentCharterersIds: string[] = [];
        const listNewCharterersIds: string[] = [];

        if (listCurrentCharterers && listCurrentCharterers.length > 0) {
          listCurrentCharterers.forEach((item: VesselCharterer) => {
            listCurrentCharterersIds.push(item.id);
          });
        }

        if (rawCharterers && rawCharterers.length > 0) {
          rawCharterers.forEach((item: VesselCharterer) => {
            listNewCharterersIds.push(item.id);
          });
        }

        const listCharterersUpdateIds = MySet.intersect(
          new Set(listCurrentCharterersIds),
          new Set(listNewCharterersIds),
        );

        const listCharterersCreateIds = MySet.difference(
          new Set(listNewCharterersIds),
          new Set(listCurrentCharterersIds),
        );

        const listCharterersDeleteIds = MySet.difference(
          new Set(listCurrentCharterersIds),
          new Set(listNewCharterersIds),
        );

        const charterersCreate = rawCharterers.filter((item) =>
          listCharterersCreateIds.has(item.id),
        );

        const charterersUpdate = rawCharterers.filter((item) =>
          listCharterersUpdateIds.has(item.id),
        );
        //#endregion Prepare data Charterer

        //#region Prepare data Owner
        const rawOwners: VesselOwner[] = [];
        if (body.vesselOwners && body.vesselOwners.length > 0) {
          for (let i = 0; i < body.vesselOwners.length; i++) {
            const owner = body.vesselOwners[i];
            const currentTime = new Date();
            let vesselOwnerStatus = CommonStatus.IN_ACTIVE;
            if (
              new Date(owner.fromDate) <= currentTime &&
              (owner.toDate === null || new Date(owner.toDate) >= currentTime)
            ) {
              vesselOwnerStatus = CommonStatus.ACTIVE;
            }
            rawOwners.push({
              ...owner,
              status: vesselOwnerStatus,
              id: owner.id ? owner.id : Utils.strings.generateUUID(),
              vesselId: vesselFoundById.id,
              // vesselOwnerId: owner.vesselOwnerId,
              // responsiblePartyInspection: owner.responsiblePartyInspection,
              // responsiblePartyQA: owner.responsiblePartyQA,
            } as VesselOwner);
          }
        }

        const listCurrentOwners = vesselFoundById.vesselOwners;
        const listCurrentOwnersIds: string[] = [];
        const listNewOwnersIds: string[] = [];

        if (listCurrentOwners && listCurrentOwners.length > 0) {
          listCurrentOwners.forEach((item: VesselOwner) => {
            listCurrentOwnersIds.push(item.id);
          });
        }

        if (rawOwners && rawOwners.length > 0) {
          rawOwners.forEach((item: VesselOwner) => {
            listNewOwnersIds.push(item.id);
          });
        }

        const listOwnersUpdateIds = MySet.intersect(
          new Set(listCurrentOwnersIds),
          new Set(listNewOwnersIds),
        );

        const listOwnersCreateIds = MySet.difference(
          new Set(listNewOwnersIds),
          new Set(listCurrentOwnersIds),
        );

        const listOwnersDeleteIds = MySet.difference(
          new Set(listCurrentOwnersIds),
          new Set(listNewOwnersIds),
        );

        const ownersCreate = rawOwners.filter((item) => listOwnersCreateIds.has(item.id));

        const ownersUpdate = rawOwners.filter((item) => listOwnersUpdateIds.has(item.id));
        //#endregion Prepare data Owner

        //#region Update DocHolder
        // Create doc holder
        if (docHoldersCreate.length > 0) {
          const preparedDocHoldersCreate: VesselDocHolder[] = [];
          for (const [index, item] of docHoldersCreate.entries()) {
            preparedDocHoldersCreate.push(
              Object.assign(item, {
                createdAt: new Date(moment().unix() * 1000 - index),
              }),
            );
          }
          await manager.save(VesselDocHolder, preparedDocHoldersCreate);
        }
        // Update charterer
        if (docHolderUpdate.length > 0) {
          await manager.save(VesselDocHolder, docHolderUpdate);
        }
        // Delete charterer
        if (Array.from(listDocHoldersDeleteIds).length > 0) {
          await manager.delete(VesselDocHolder, { id: In(Array.from(listDocHoldersDeleteIds)) });
        }
        //#end region Update DocHolder

        //#region Update Charterer Owner
        // Create charterer
        if (charterersCreate.length > 0) {
          const preparedChaterersCreate: VesselCharterer[] = [];
          for (const [index, item] of charterersCreate.entries()) {
            preparedChaterersCreate.push(
              Object.assign(item, {
                createdAt: new Date(moment().unix() * 1000 - index),
              }),
            );
          }
          await manager.save(VesselCharterer, preparedChaterersCreate);
        }
        // Update charterer
        if (charterersUpdate.length > 0) {
          await manager.save(VesselCharterer, charterersUpdate);
        }
        // Delete charterer
        if (Array.from(listCharterersDeleteIds).length > 0) {
          await manager.delete(VesselCharterer, { id: In(Array.from(listCharterersDeleteIds)) });
        }

        // Create owner
        if (ownersCreate.length > 0) {
          const preparedOwnersCreate: VesselOwner[] = [];
          for (const [index, item] of ownersCreate.entries()) {
            preparedOwnersCreate.push(
              Object.assign(item, {
                createdAt: new Date(moment().unix() * 1000 - index),
              }),
            );
          }
          await manager.save(VesselOwner, preparedOwnersCreate);
        }
        // Update owner
        if (ownersUpdate.length > 0) {
          await manager.save(VesselOwner, ownersUpdate);
        }
        // Delete owner
        if (Array.from(listOwnersDeleteIds).length > 0) {
          await manager.delete(VesselOwner, { id: In(Array.from(listOwnersDeleteIds)) });
        }
        //#endregion Update Charterer Owner

        // Create blacklist mou
        const preparedBlacklists = [];
        if (body?.blacklistData && body?.blacklistData.length > 0) {
          const blacklistData = body?.blacklistData;
          for (let j = 0; j < blacklistData.length; j++) {
            if (blacklistData[j]?.id) {
              preparedBlacklists.push({
                id: blacklistData[j]?.id,
                blacklistType: blacklistData[j].blacklistType,
                companyId: blacklistData[j]?.companyId || null,
                vesselId: vesselId,
                description: blacklistData[j]?.description || null,
                authorityMasterId: blacklistData[j]?.authorityMasterId || null,
                effectiveFrom: blacklistData[j]?.effectiveFrom,
                effectiveTo: blacklistData[j]?.effectiveTo || null,
              });
            } else {
              preparedBlacklists.push({
                blacklistType: blacklistData[j].blacklistType,
                companyId: blacklistData[j]?.companyId || null,
                vesselId: vesselId,
                description: blacklistData[j]?.description || null,
                authorityMasterId: blacklistData[j]?.authorityMasterId || null,
                effectiveFrom: blacklistData[j]?.effectiveFrom,
                effectiveTo: blacklistData[j]?.effectiveTo || null,
              });
            }
          }
          if (preparedBlacklists.length > 0) {
            await manager.save(VesselBlacklistMOU, preparedBlacklists);
          }
          if (preparedBlacklists?.length > 0) {
            let blacklistOnMOUWebsite = false;
            const currentDate = moment(new Date()).format('YYYY-MM-DD');
            for (const blacklistMou of preparedBlacklists) {
              if (blacklistMou?.effectiveFrom && blacklistMou?.effectiveTo) {
                if (
                  currentDate >= moment(blacklistMou?.effectiveFrom).format('YYYY-MM-DD') &&
                  currentDate <= moment(blacklistMou?.effectiveTo).format('YYYY-MM-DD')
                ) {
                  blacklistOnMOUWebsite = true;
                  break;
                }
              } else if (blacklistMou?.effectiveFrom) {
                if (currentDate >= moment(blacklistMou?.effectiveFrom).format('YYYY-MM-DD')) {
                  blacklistOnMOUWebsite = true;
                  break;
                }
              }
            }

            if (blacklistOnMOUWebsite) {
              await manager.update(
                Vessel,
                { id: vesselFoundById?.id },
                {
                  blacklistOnMOUWebsite: true,
                },
              );
            } else {
              await manager.update(
                Vessel,
                { id: vesselFoundById?.id },
                {
                  blacklistOnMOUWebsite: false,
                },
              );
            }
          }
        }

        // update vessel risk request
        await this.updateVesselRiskRequest(vesselId, body);
      });
    } catch (ex) {
      LoggerCommon.error('[VesselRepository] updateVessel error ', ex.message || ex);
      if (ex.code === DBErrorCode.UNIQUE_VIOLATION) {
        const foundEntities = await this.createQueryBuilder('vessel')
          .select(['vessel.imoNumber', 'vessel.code', 'vessel.companyId'])
          .where('(vessel.imoNumber = :imoNumber OR vessel.code = :code)', {
            imoNumber: body.imoNumber,
            code: body.code,
          })
          .andWhere('vessel.id != :vesselId', { vesselId })
          .andWhere('vessel.deleted = :deleted', { deleted: false })
          .getMany();

        // Handle unique constraints multiply
        const errorList = [];
        let imoSeen = false;
        let codeSeen = false;
        for (let i = 0; i < foundEntities.length; i++) {
          if (
            isEqualStringIC(foundEntities[i].imoNumber, body.imoNumber) &&
            foundEntities[i].companyId === token.companyId &&
            !imoSeen
          ) {
            errorList.push({ fieldName: 'imoNumber', message: 'vessel.VESSEL_IMO_NUMBER_EXISTED' });
            imoSeen = true;
          }
          if (
            isEqualStringIC(foundEntities[i].code, body.code) &&
            foundEntities[i].companyId === token.companyId &&
            !codeSeen
          ) {
            errorList.push({ fieldName: 'code', message: 'vessel.VESSEL_CODE_EXISTED' });
            codeSeen = true;
          }
        }

        throw new BaseMultiErrors({
          status: 400,
          errors: errorList,
        });
      }
      throw ex;
    }
  }

  async deleteVessel(companyId: string, vesselId: string) {
    // soft delete
    try {
      const hasRef = await this.hasRefInGivenTables(vesselId, 'vesselId', [
        { entity: PlanningRequest },
        { entity: PlanedMaintenanceSystem },
        { entity: ClassDispensations },
        { entity: DryDocking },
        { entity: ExternalInspections },
        { entity: IncidentInvestigation },
        { entity: Injury },
        { entity: InternalInspections },
        { entity: MaintenancePerformance },
        { entity: OtherSmsRecords },
        { entity: OtherTechRecords },
        { entity: PlansDrawings },
        { entity: PortStateControl },
        { entity: SurveyClassInfo },
        { entity: VesselScreening },
        { entity: DivisionMapping },
      ]);

      if (hasRef) {
        throw new BaseError({ status: 400, message: 'common.CANNOT_DELETE_DUE_TO_REF' });
      }

      const vesselFound = await this.findOne({
        where: {
          id: vesselId,
          companyId,
          deleted: false,
        },
        select: ['companyId'],
      });
      if (!vesselFound) {
        throw new BaseError({ status: 404, message: 'vessel.VESSEL_NOT_FOUND' });
      }
      await this.connection.transaction(async (manager) => {
        await manager.update(Vessel, { id: vesselId, deleted: false }, { deleted: true });
        // await manager.decrement(
        //   Company,
        //   { id: vesselFound.companyId, deleted: false },
        //   'numVessels',
        //   1,
        // );
      });
      return 1;
    } catch (error) {
      throw error;
    }
  }

  async listVesselsExport(companyId: string, query: ExportVesselQueryDTO) {
    const pageSize = Number(query.pageSize) || ConstLib.PAGE_SIZE;
    const fromPage = Number(query.fromPage);
    const toPage = Number(query.toPage);

    // const page = Number(query.page) || ConstLib.PAGE_NUMBER;
    const queryBuilder = this.createQueryBuilder('vessel')
      .leftJoin('vessel.vesselType', 'vesselType')
      .leftJoin('vessel.company', 'company')
      .select()
      .addSelect(['vesselType.name', 'company.name'])
      .where('vessel.companyId = :companyId', { companyId });

    if (query.content) {
      queryBuilder.andWhere('(vessel.code LIKE :content OR vessel.name LIKE :content)', {
        content: `%${query.content}%`,
      });
    }

    if (query.vesselTypeId) {
      queryBuilder.andWhere('vessel.vesselTypeId = :vesselTypeId', {
        vesselTypeId: query.vesselTypeId,
      });
    }

    if (query.status) {
      queryBuilder.andWhere('vessel.status = :status', {
        status: query.status,
      });
    }

    if (query.sort) {
      const sortValues: string[] = query.sort.split(';');
      sortValues.forEach((sv: string) => {
        const value: string[] = sv.split(':');
        if (value[0].includes('.')) {
          queryBuilder.addOrderBy(value[0], Number(value[1]) === 1 ? 'ASC' : 'DESC');
        } else {
          queryBuilder.addOrderBy(`vessel.${value[0]}`, Number(value[1]) === 1 ? 'ASC' : 'DESC');
        }
      });
    } else {
      queryBuilder.addOrderBy('vessel.createdAt', 'DESC');
    }
    if (query.fromPage) {
      if (fromPage == 1) {
        queryBuilder.offset(pageSize * fromPage);
      }
      queryBuilder.offset(pageSize * (fromPage - 1));

      if (query.toPage) {
        if (toPage >= fromPage) {
          queryBuilder.limit((toPage - fromPage + 1) * pageSize);
        } else {
          throw new BaseError({ message: 'error.TO_PAGE_GREATER_THAN_FROM_PAGE' });
        }
      }
    }

    // const dataExports = [];
    const listUserExport = await this.getManyQB(queryBuilder);
    // for (let i = 0; i < listUserExport.length; i++) {
    //   const item = Object(listUserExport[i]);
    //   item.fleetName = item.fleet.name;
    //   dataExports.push(item);
    // }
    return listUserExport;
  }

  async _getVesselCode(companyId: string) {
    const vessel = await this.createQueryBuilder('vessel')
      .select(['vessel.code'])
      .where('vessel.companyId = :companyId', { companyId })
      .andWhere('deleted = :deleted', { deleted: false })
      .getOne();

    if (vessel) {
      return vessel;
    } else {
      throw new BaseError({ status: 404, message: 'vessel.VESSEL_NOT_FOUND' });
    }
  }

  async dateOfLastInspectionAndDueDateByVesselId(
    vesselId: string,
    query: AuditTimeAndDueDateDto,
    user: TokenPayloadModel,
  ) {
    const planningRequestdata = await this.connection
      .getCustomRepository(PlanningRequestRepository)
      .createQueryBuilder('planningRequest')
      .leftJoinAndSelect('planningRequest.vessel', 'vessel')
      .leftJoinAndSelect('planningRequest.auditTypes', 'auditTypes')
      .select()
      .where(
        'vessel.id = :Id AND planningRequest.status=:status AND auditTypes.id IN (:...auditTypesId)',
        {
          Id: vesselId,
          auditTypesId: query.auditTypeIds,
          status: 'Completed',
        },
      )
      .orderBy('planningRequest.createdAt', 'DESC')
      .getMany();

    const auditId = planningRequestdata[0]?.auditNo;

    if (planningRequestdata && auditId) {
      const workSpacedata = await this.connection
        .getCustomRepository(AuditWorkspaceRepository)
        .createQueryBuilder('workSpace')
        .leftJoinAndSelect('workSpace.planningRequest', 'planningRequest')
        .select()
        .where('planningRequest.auditNo LIKE :id', {
          id: auditId,
        })
        .getOne();
      const getInspectionMapping = await this.connection
        .getCustomRepository(InspectionMappingRepository)
        .createQueryBuilder('inspectionmapping')
        .select()
        .where(
          'inspectionmapping.auditTypeId = :auditTypeId AND inspectionmapping.companyId=:companyId AND inspectionmapping.deleted = :deleted',
          {
            auditTypeId: planningRequestdata[0]?.auditTypes[0]?.id,
            companyId: user.companyId,
            deleted: false,
          },
        )
        .getOne();
      if (workSpacedata) {
        const currentDate = new Date(workSpacedata.submittedDate);
        if (getInspectionMapping?.auditPeriod) {
          currentDate.setDate(currentDate.getDate() + getInspectionMapping?.auditPeriod);
        }

        return {
          dateOfLastInspection: workSpacedata.submittedDate,
          dueDate: currentDate,
        };
      } else return { dateOfLastInspection: '', dueDate: '' };
    } else {
      return { dateOfLastInspection: '', dueDate: '' };
    }
  }
  async listVesselByUser(token: TokenPayloadModel, query: ListVesselDto) {
    const queryBuilder = this.createQueryBuilder('vessel')
      .leftJoin('vessel.vesselType', 'vesselType')
      .leftJoin('vessel.company', 'company')
      .leftJoin('vessel.classificationSociety', 'classificationSociety')
      .leftJoin('vessel.divisionMapping', 'divisionMapping')
      .leftJoin('vessel.vesselCharterers', 'vesselCharterers')
      .leftJoin('vessel.vesselOwners', 'vesselOwners')
      .leftJoin('vessel.vesselGeneralHistory', 'vesselGeneralHistory')
      .leftJoin('divisionMapping.division', 'division')
      .leftJoin('division.users', 'users')
      .leftJoin('vessel.docHolder', 'docHolder')
      .leftJoin('vessel.crewGrouping', 'crewGrouping')
      .leftJoin('vessel.owners', 'owners')
      .leftJoinAndSelect('owners.rank', 'rank')
      .leftJoinAndSelect('owners.departments', 'departments')
      .leftJoinAndSelect('owners.primaryDepartment', 'primaryDepartment')
      .leftJoin('vessel.country', 'country')
      .select()
      .addSelect([
        'vesselType.name',
        'company.id',
        'company.name',
        'company.companyIMO',
        'docHolder.id',
        'docHolder.name',
        'docHolder.code',
        'docHolder.companyIMO',
        'division.id',
        'division.code',
        'division.name',
        'classificationSociety.id',
        'classificationSociety.code',
        'classificationSociety.name',
        'crewGrouping.id',
        'crewGrouping.code',
        'crewGrouping.name',
        'crewGrouping.officers',
        'crewGrouping.rating',
        'owners.id',
        'owners.username',
        'owners.employeeId',
        'owners.userType',
        'owners.controlType',
        'owners.email',
        'country.id',
        'country.name',
      ])
      .where(
        `(vessel.companyId = :companyId and vessel.deleted = false) and 
        (users.id = :userId OR docHolder.id = :companyId OR vesselOwners.companyId = :companyId OR vesselCharterers.companyId = :companyId) and 
        vessel.isVesselRestricted = :isVesselRestricted and vessel.blacklistOnMOUWebsite = :blacklistOnMOUWebsite`,
        {
          companyId: token.companyId,
          userId: token.id,
          isVesselRestricted: false,
          blacklistOnMOUWebsite: false,
        },
      );
    return await this.list(
      { page: query.page, limit: query.pageSize },
      {
        queryBuilder,
        advanceConditions: {
          createdAtFrom: query.createdAtFrom,
          createdAtTo: query.createdAtTo,
        },
      },
    );
  }
  async updateStatusDOCHolderVesselCharacterOwner() {
    const listGetAllVesselForUpdateStatusActive = await this.createQueryBuilder('vessel')
      .leftJoinAndSelect(
        'vessel.vesselDocHolders',
        'vesselDocHolders',
        'vesselDocHolders.fromDate < now() and COALESCE(vesselDocHolders.toDate,now()) >= now() and vesselDocHolders.status = :status',
        { status: CommonStatus.IN_ACTIVE },
      )
      .leftJoinAndSelect(
        'vessel.vesselCharterers',
        'vesselCharterers',
        'vesselCharterers.fromDate < now() and COALESCE(vesselCharterers.toDate,now()) >= now() and vesselCharterers.status = :status',
        { status: CommonStatus.IN_ACTIVE },
      )
      .leftJoinAndSelect(
        'vessel.vesselOwners',
        'vesselOwners',
        'vesselOwners.fromDate < now() and COALESCE(vesselOwners.toDate,now()) >= now() and vesselOwners.status = :status',
        { status: CommonStatus.IN_ACTIVE },
      )
      .where('vessel.deleted = false')
      .getMany();
    const listIdVesselDocHoldersUpdateActive = [];
    const listVesselDocHoldersUpdateActive = [];
    const listIdVesselCharterersUpdateActive = [];
    const listVesselCharterersUpdateActive = [];
    const listIdVesselOwnersUpdateActive = [];
    const listVesselOwnersUpdateActive = [];
    for (const vessel of listGetAllVesselForUpdateStatusActive) {
      if (vessel.vesselDocHolders.length > 0) {
        for (const temp of vessel.vesselDocHolders) {
          if (listIdVesselDocHoldersUpdateActive.indexOf(temp.id) === -1) {
            listIdVesselDocHoldersUpdateActive.push(temp.id);
            listVesselDocHoldersUpdateActive.push({
              id: temp.id,
              status: CommonStatus.ACTIVE,
            });
          }
        }
      }
      if (vessel.vesselCharterers.length > 0) {
        for (const temp of vessel.vesselCharterers) {
          if (listIdVesselCharterersUpdateActive.indexOf(temp.id) === -1) {
            listIdVesselCharterersUpdateActive.push(temp.id);
            listVesselCharterersUpdateActive.push({
              id: temp.id,
              status: CommonStatus.ACTIVE,
            });
          }
        }
      }

      if (vessel.vesselOwners.length > 0) {
        for (const temp of vessel.vesselOwners) {
          if (listIdVesselOwnersUpdateActive.indexOf(temp.id) === -1) {
            listIdVesselOwnersUpdateActive.push(temp.id);
            listVesselOwnersUpdateActive.push({
              id: temp.id,
              status: CommonStatus.ACTIVE,
            });
          }
        }
      }
    }
    if (listVesselDocHoldersUpdateActive.length > 0) {
      await this.manager.save(VesselDocHolder, listVesselDocHoldersUpdateActive);
    }
    if (listVesselCharterersUpdateActive.length > 0) {
      await this.manager.save(VesselCharterer, listVesselCharterersUpdateActive);
    }

    if (listVesselOwnersUpdateActive.length > 0) {
      await this.manager.save(VesselOwner, listVesselOwnersUpdateActive);
    }

    const listGetAllVesselCurrentStatusActive = await this.createQueryBuilder('vessel')
      .leftJoinAndSelect(
        'vessel.vesselDocHolders',
        'vesselDocHolders',
        'vesselDocHolders.fromDate < now() and COALESCE(vesselDocHolders.toDate,now()) >= now() and vesselDocHolders.status = :status',
        { status: CommonStatus.ACTIVE },
      )
      .leftJoinAndSelect(
        'vessel.vesselCharterers',
        'vesselCharterers',
        'vesselCharterers.fromDate < now() and COALESCE(vesselCharterers.toDate,now()) >= now() and vesselCharterers.status = :status',
        { status: CommonStatus.ACTIVE },
      )
      .leftJoinAndSelect(
        'vessel.vesselOwners',
        'vesselOwners',
        'vesselOwners.fromDate < now() and COALESCE(vesselOwners.toDate,now()) >= now() and vesselOwners.status = :status',
        { status: CommonStatus.ACTIVE },
      )
      .where('vessel.deleted = false')
      .getMany();

    for (const vessel of listGetAllVesselCurrentStatusActive) {
      if (vessel.vesselDocHolders.length > 0) {
        for (const temp of vessel.vesselDocHolders) {
          if (listIdVesselDocHoldersUpdateActive.indexOf(temp.id) === -1) {
            listIdVesselDocHoldersUpdateActive.push(temp.id);
          }
        }
      }
      if (vessel.vesselCharterers.length > 0) {
        for (const temp of vessel.vesselCharterers) {
          if (listIdVesselCharterersUpdateActive.indexOf(temp.id) === -1) {
            listIdVesselCharterersUpdateActive.push(temp.id);
          }
        }
      }

      if (vessel.vesselOwners.length > 0) {
        for (const temp of vessel.vesselOwners) {
          if (listIdVesselOwnersUpdateActive.indexOf(temp.id) === -1) {
            listIdVesselOwnersUpdateActive.push(temp.id);
          }
        }
      }
    }

    let conditionVesselDocHolders = `vesselDocHolders.status = :status`;
    if (listIdVesselDocHoldersUpdateActive.length > 0) {
      conditionVesselDocHolders +=
        ' and vesselDocHolders.id Not in (:...listIdVesselDocHoldersUpdateActive)';
    }

    let conditionVesselCharterers = `vesselCharterers.status = :status`;
    if (listIdVesselCharterersUpdateActive.length > 0) {
      conditionVesselCharterers +=
        ' and vesselCharterers.id Not in (:...listIdVesselCharterersUpdateActive)';
    }

    let conditionVesselOwners = `vesselOwners.status = :status`;
    if (listIdVesselOwnersUpdateActive.length > 0) {
      conditionVesselOwners += ' and vesselOwners.id Not in (:...listIdVesselOwnersUpdateActive)';
    }

    const listGetAllVesselForUpdateStatusInActive = await this.createQueryBuilder('vessel')
      .leftJoinAndSelect('vessel.vesselDocHolders', 'vesselDocHolders', conditionVesselDocHolders, {
        status: CommonStatus.ACTIVE,
        listIdVesselDocHoldersUpdateActive,
      })
      .leftJoinAndSelect('vessel.vesselCharterers', 'vesselCharterers', conditionVesselCharterers, {
        status: CommonStatus.ACTIVE,
        listIdVesselCharterersUpdateActive,
      })
      .leftJoinAndSelect('vessel.vesselOwners', 'vesselOwners', conditionVesselOwners, {
        status: CommonStatus.ACTIVE,
        listIdVesselOwnersUpdateActive,
      })
      .where('vessel.deleted = false')
      .getMany();

    const listVesselDocHoldersUpdateInActive = [];
    const listVesselCharterersUpdateInActive = [];
    const listVesselOwnersUpdateInActive = [];
    for (const vessel of listGetAllVesselForUpdateStatusInActive) {
      if (vessel.vesselDocHolders.length > 0) {
        for (const temp of vessel.vesselDocHolders) {
          listVesselDocHoldersUpdateInActive.push({
            id: temp.id,
            status: CommonStatus.IN_ACTIVE,
          });
        }
      }
      if (vessel.vesselCharterers.length > 0) {
        for (const temp of vessel.vesselCharterers) {
          listVesselCharterersUpdateInActive.push({
            id: temp.id,
            status: CommonStatus.IN_ACTIVE,
          });
        }
      }

      if (vessel.vesselOwners.length > 0) {
        for (const temp of vessel.vesselOwners) {
          listVesselOwnersUpdateInActive.push({
            id: temp.id,
            status: CommonStatus.IN_ACTIVE,
          });
        }
      }
    }
    if (listVesselDocHoldersUpdateInActive.length > 0) {
      await this.manager.save(VesselDocHolder, listVesselDocHoldersUpdateInActive);
    }
    if (listVesselCharterersUpdateInActive.length > 0) {
      await this.manager.save(VesselCharterer, listVesselCharterersUpdateInActive);
    }

    if (listVesselOwnersUpdateInActive.length > 0) {
      await this.manager.save(VesselOwner, listVesselOwnersUpdateInActive);
    }
  }

  //  delete vessel general history by Id
  async deleteVesselGeneralHistoryById(token: TokenPayloadModel, vesselgeneralHistoryId: string) {
    try {
      const getVessel = await getRepository(VesselGeneralHistory)
        .createQueryBuilder('vesselGeneralHistory')
        .select(['vesselGeneralHistory.companyId'])
        .where('vesselGeneralHistory.id = :id', { id: vesselgeneralHistoryId })
        .getOne();
      if (!getVessel) {
        throw new BaseError({ status: 404, message: 'vessel.VESSEL_EX_NAME_NOT_FOUND' });
      }
      await this.connection.transaction(async (manager) => {
        await manager.update(
          VesselGeneralHistory,
          { id: vesselgeneralHistoryId, deleted: false },
          { deleted: true },
        );
      });
      return 1;
    } catch (e) {
      throw e;
    }
  }
  async _compareAndCheckActiveVessel(
    currentId: string,
    newVesselId: string,
    token: TokenPayloadModel,
  ) {
    if (newVesselId && currentId !== newVesselId) {
      await this._checkActiveVessel([newVesselId], token);
    }
  }

  async _checkActiveVessel(vesselIds: string[], token: TokenPayloadModel) {
    const vessel = await this.createQueryBuilder('vessel')
      .leftJoin('vessel.company', 'company')
      .select()
      .where('vessel.id IN (:...vesselIds) AND vessel.status = :status ', {
        vesselIds: vesselIds,
        status: CommonStatus.IN_ACTIVE,
      })
      .andWhere(
        '(vessel.companyId = :companyId OR company.parentId = :companyId) AND vessel.deleted = FALSE ',
        {
          companyId: token.companyId,
        },
      )
      .getOne();

    if (vessel) {
      throw new BaseError({ status: 400, message: 'vessel.VESSEL_INACTIVE' });
    }
  }

  async updateVesselRiskRequest(vesselId: string, body: any) {
    return await this.connection.transaction(async (manager) => {
      // vessel risk request
      let potentialScore = 0;
      let observedScore = 0;
      let count = 0;
      let tran
      // calculate age risk
      if (body?.buildDate) {
        const buildDate = new Date(body.buildDate);
        const currentDate = new Date();
        const age = currentDate.getFullYear() - buildDate.getFullYear();
        if (age >= 15.00) {
          potentialScore += 10;
          observedScore += 10;
        } else if (age >= 10.01 && age < 14.99) {
          potentialScore += 5;
          observedScore += 5;
        } else if (age >= 5.01 && age < 10.00) {
          potentialScore += 2;
          observedScore += 2;
        } else {
          potentialScore += 0;
          observedScore += 0;
        }
      }
      count++;

      // calculate flag risk
      if (body?.countryFlag || body?.countryId) {
        const countryId = body?.countryFlag || body?.countryId;
        const country = await manager.findOne(Country, {
          where: {
            id: countryId,
          },
        });
        if (country) {
          const whiteListCountries = await manager.find(WhiteListCountry, {
            where: {
              countryId: country.id,
            },
          });
          const greyListCountries = await manager.find(GreyListCountry, {
            where: {
              countryId: country.id,
            },
          });
          const blackListCountries = await manager.find(BlackListCountry, {
            where: {
              countryId: country.id,
            },
          });
          if (whiteListCountries.length > 0) {
            potentialScore += 2;
            observedScore += 2;
          } else if (greyListCountries.length > 0) {
            potentialScore += 10;
            observedScore += 10;
          } else if (blackListCountries.length > 0) {
            potentialScore += 10;
            observedScore += 10;
          } else {
            potentialScore += 0;
            observedScore += 0;
          }
        }
      }
      count++;

      // calculate classification society risk
      if (body?.classificationSocietyId) {
        const IACSMember = await manager.findOne(ClassificationSociety, {
          where: {
            id: body.classificationSocietyId,
            deleted: false,
            isIACSMember: true,
          },
        });
        if (!IACSMember) {
          potentialScore += 10;
          observedScore += 10;
        } else {
          potentialScore += 0;
          observedScore += 0;
        }
      }
      count++;

      // calculate shipyard risk
      if (body?.shipyardName == 'JSC United Shipbuilding Corporation' || body?.shipyardName == 'Kuzey Star Shipyard' || body?.shipyardName == 'Penglai Jutal Offshore Engineering Heavy Industry Co., Ltd.') {
        potentialScore += 10;
        observedScore += 10;
      } else {
        potentialScore += 0;
        observedScore += 0;
      }
      count++;

      // calculate ITF risk
      if (body?.isITF === false) {
        potentialScore += 2;
        observedScore += 2;
      } else if (body?.isITF === true) {
        potentialScore += 0;
        observedScore += 0;
      }
      count++;

      // calculate right ship restrictions risk
      if (body?.rightShipRestrictions && body?.rightShipRestrictions.length > 0) {
        const vesselRestrictions = body?.rightShipRestrictions.some((rightShipRestriction) => {
          return rightShipRestriction?.restrictionType === 'Vessel';
        });
        count++;
        const companyRestrictions = body?.rightShipRestrictions.some((rightShipRestriction) => {
          return rightShipRestriction?.restrictionType === 'Company';
        });
        count++;
        if (vesselRestrictions) {
          potentialScore += 10;
          observedScore += 10;
        } else if (companyRestrictions) {
          potentialScore += 10;
          observedScore += 10;
        }
      } else {
        potentialScore += 0;
        observedScore += 0;
        count++;
        count++;
      }

      // calculate blacklist on MOU website risk
      if (body?.blacklistOnMOUWebsite === true) {
        potentialScore += 10;
        observedScore += 10;
      } else {
        potentialScore += 0;
        observedScore += 0;
      }
      count++;

      let vesselRiskRequestParams = {};
      vesselRiskRequestParams = {
        potentialScore,
        observedScore,
        vesselId,
        count,
      };
      console.log('count: ', count);
      const existingVesselRiskRequest = await manager.find(VesselRiskRequest, {
        where: {
          vesselId,
        },
      });
      if (existingVesselRiskRequest.length > 0) {
        existingVesselRiskRequest.forEach(async (vesselRiskRequest) => {
          await manager.update(VesselRiskRequest, vesselRiskRequest.id, vesselRiskRequestParams);
        });
      }
      const existingVesselScreening = await manager.find(VesselScreening, {
        where: {
          vesselId,
        },
      });
      if (existingVesselScreening.length > 0) {
        existingVesselScreening.forEach(async (vesselScreening) => {
          try {
            await this.manager
              .getCustomRepository(VesselScreeningSummaryRepository)
              .updateVesselScreeningSummaryByRef(
                this.manager,
                VesselScreeningSummaryReferenceEnum.VESSEL_GENERAL_RISK,
                vesselScreening.id,
              );
          } catch (error) {
            console.log(error);
          }
        });
      }

      return 1;
    });
  }
}

@EntityRepository(CommonConfiguration)
export class ConfigRepository extends TypeORMRepository<CommonConfiguration> {
  constructor(private readonly connection: Connection) {
    super();
  }
  async getVesselConfig() {
    const queryBuilder = this.createQueryBuilder('common_configuration')
      .select(['common_configuration.configuration'])
      .where('common_configuration.configureName = :configName', {
        configName: 'Vessel Configuration',
      })
      .getOne();
    return queryBuilder;
  }
}
