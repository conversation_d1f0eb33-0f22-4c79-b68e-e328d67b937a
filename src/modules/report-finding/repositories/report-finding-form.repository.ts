import { cloneDeep, difference, omit, pick } from 'lodash';
import * as momentTZ from 'moment-timezone';
import {
  BaseError,
  ForbiddenError,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  RoleScopeCheck,
  TokenPayloadModel,
  TypeORMRepository,
  Utils,
} from 'svm-nest-lib-v3';
import { Connection, EntityManager, EntityRepository, In, Not } from 'typeorm';

import { AppConst } from '../../../commons/consts/app.const';
import { DBErrorCode } from '../../../commons/consts/db.const';
import {
  ActionValueChangeEnum,
  AuditEntity,
  AuditTimeTableStatus,
  CarStatusEnum,
  CarVerificationStatusEnum,
  CompanyLevelEnum,
  EmailTypeEnum,
  FollowUpStatusEnum,
  MailTemplate,
  ModuleEnum,
  ModulePathEnum,
  PeriodTypeEnum,
  PushTypeEnum,
  ReportFindingFormStatus,
  UserRelationship,
  WorkflowPermission,
} from '../../../commons/enums';
import { CreatedUserHistoryModel } from '../../../commons/models';
import { FeatureVersionConfig } from '../../commons/company-feature-version/feature-version.config';
import { PlanningRequestRepository } from '../../planning-request/repositories/planning-request.repository';
import {
  CloseOutReportFindingFormDto,
  CreateReportFindingFormDto,
  ListFindingItemOfFormDto,
  ListReportFindingFormDto,
  ReassignReportFindingFormDto,
  ReportFindingItem2Dto,
  ReportFindingItem2MobileDto,
  ReviewReportFindingFormDto,
  UpdateReportFindingFormDto,
} from '../dto';
import { UpdateROFOfficeCommentDTO } from '../dto/update-rof-office-comment.dto';
import { ReportFindingForm } from '../entities/report-finding-form.entity';

import { ReportFindingItemRepository } from '../../audit-workspace/repositories/report-finding-item.repository';
import { CompanyFeatureVersionRepository } from '../../commons/company-feature-version/company-feature-version.repository';
import { InternalAuditReportRepository } from '../../internal-audit-report/repositories/internal-audit-report.repository';
import { NatureFindingRepository } from '../../nature_finding/nature-finding.repository';
import { UserRepository } from '../../user/user.repository';

import { AuditTimeTableHistory } from '../../audit-time-table/entities/audit-time-table-history.entity';
import { AuditTimeTable } from '../../audit-time-table/entities/audit-time-table.entity';
import { ReportFindingItem } from '../../audit-workspace/entities/report-finding-item.entity';
import { Company } from '../../company/company.entity';
import { ReportFindingHistory } from '../entities/report-finding-history.entity';
import { ROFAuditType } from '../entities/rof-audit-type.entity';
import { ROFOfficeComment } from '../entities/rof-office-comment.entity';
import { ROFPlanningRequest } from '../entities/rof-planning-request.entity';
import { ROFUser } from '../entities/rof-user.entity';

import { InspectionMappingRepository } from 'src/modules/inspection-mapping/repositories/inspection-mapping.repository';
import {
  _supportCheckRoleScopeForGetList,
  _supportWhereDOCChartererOwner,
  decryptAttachmentValues,
} from '../../../commons/functions';
import APP_CONFIG from '../../../configs/app.config';
import { IEmailEventModel, IUserEmail } from '../../../micro-services/async/email.producer';
import {
  INotificationEventModel,
  IUser,
  NotificationProducer,
} from '../../../micro-services/async/notification.producer';
import { leadingZero, MySet } from '../../../utils';
import { AuditActivityEnum, AuditModuleEnum } from '../../audit-log/audit-log.entity';
import { AuditLogRepository } from '../../audit-log/audit-log.repository';
import { CompanyRepository } from '../../company/company.repository';
import { CorrectiveActionRequest } from '../../corrective-action-request/entities/car.entity';
import { FollowUp } from '../../corrective-action-request/entities/follow-up.entity';
import { CARRepository } from '../../corrective-action-request/repositories/car.repository';
import { Department } from '../../department-master/department.entity';
import { CreateUserAssignmentDTO } from '../../user-assignment/dto';
import { ModuleType } from '../../user-assignment/user-assignment.enum';
import { UserAssignmentRepository } from '../../user-assignment/user-assignment.repository';
import { ReviewFiveStepReportFindingFormDTO } from '../dto/review-five-step-report-finding-form.dto';
import { AuditWorkspaceRepository } from '../../audit-workspace/repositories/audit-workspace.repository';
import { commonCheckValueChange } from '../../../commons/functions/value-change-history';
import { ValueChangeHistory } from '../../value-change-history/value-change-history.entity';
import { MetaConfig } from 'src/modules-qa/catalog/entity/meta-config.entity';
import { CatalogConst } from '../../../modules-qa/catalog/catalog-key.const';
import { FillAuditChecklistRepository } from 'src/modules/audit-workspace/repositories/fill-audit-checklist.repository';
import { FillAuditChecklistQuestionRepository } from 'src/modules/audit-workspace/repositories/fill-audit-checklist-question.repository';
import { FillAuditChecklistQuestion } from 'src/modules/audit-workspace/entities/fill-audit-checklist-question.entity';
import { ChkQuestionAnswerRepository } from 'src/modules/audit-checklist/repository/chk-question-answer.repository';
import { MasterTableObj } from 'src/modules/commons/master-table/data/master-table.data';
import { PriorityMasterRepository } from 'src/modules/priority-master/priority-master.repository';
import { ObservedRiskRepository } from '../../risks/repositories/observed-risk.repository';
import { ROFUserRepository } from './rof-caches.repository';
import { SAFindingItem } from 'src/modules/audit-workspace/entities/sa-finding-items.entity';
import { SVMSupportService } from '../../../micro-services/sync/svm-support.service';
import { FillAuditChecklist } from 'src/modules/audit-workspace/entities/fill-audit-checklist.entity';
import { FillSAChecklistQuestion } from 'src/modules/audit-workspace/entities/fill-sa-checklist-question.entity';
import { AuditWorkspace } from 'src/modules/audit-workspace/entities/audit-workspace.entity';
import { AnalyticalReportInspectionPerformanceRepository } from 'src/modules/analytical-report/repositories/analytical-report-inspection-performance.repository';
import { ChartererInspectionRequest } from 'src/modules/corrective-action-request/entities/charterer-inspection-request.entity';
import { VesselScreeningSummaryRepository } from 'src/modules-qa/vessel-screening/repository/vessel-screening-summary.repository';
import { VesselScreeningSummaryReferenceEnum } from 'src/modules-qa/vessel-screening/entity/vessel-screening-summary.entity';
import { VesselScreening } from 'src/modules-qa/vessel-screening/entity/vessel-screening.entity';

@EntityRepository(ReportFindingForm)
export class ReportFindingFormRepository extends TypeORMRepository<ReportFindingForm> {
  constructor(
    private readonly connection: Connection,
    private readonly notificationProducer: NotificationProducer,
    private readonly planningRepository: PlanningRequestRepository,
    private readonly svmSupportService: SVMSupportService,
  ) {
    super();
  }

  private async _genReportFindingFormSNo(companyId: string, counter: number, currYear: number) {
    const version = leadingZero(counter, 4);
    const company = await this.manager.findOne(Company, {
      where: { id: companyId },
      select: ['code'],
    });
    return `ROF${company.code}${currYear}${version}`;
  }

  private async _genReportFindingFormRefNo(companyId: string, counter: number, currYear: number) {
    const version = leadingZero(counter, 4);
    const company = await this.manager.findOne(Company, {
      where: { id: companyId },
      select: ['id', 'code'],
    });

    return `${company.code}/ROF/${version}/${currYear}`;
  }

  private async _getDetailMasterDataByIds(masterDataIds: { tableName: string; ids: string[] }[]) {
    let sql = ``;
    for (let i = 0; i < masterDataIds.length; i++) {
      if (masterDataIds[i].ids.length > 0) {
        const tableName = masterDataIds[i].tableName;
        let field = 'name';
        if (masterDataIds[i].tableName === 'viq') {
          field = 'refNo';
        }

        const listIdsStr = masterDataIds[i].ids.map((id) => `'${id}'`).join(',');

        if (sql.length > 0) {
          sql += ' UNION ';
        }

        // Build sql
        sql += `(
          SELECT
            '${tableName}::' || "${tableName}"."id" AS "key",
            "${tableName}"."${field}" AS "value"
          FROM "${tableName}" as "${tableName}"
          WHERE "${tableName}"."id" IN (${listIdsStr})
        )`;
      }
    }

    const masterDataMap = {};
    if (sql.length > 0) {
      const rawData: { key: string; value: string }[] = await this.manager.query(sql);
      for (let i = 0; i < rawData.length; i++) {
        masterDataMap[rawData[i].key] = rawData[i].value;
      }
    }
    return masterDataMap;
  }

  public async _prepareFindingItems(
    user: TokenPayloadModel,
    formId: string,
    planningRequestId: string,
    findingItems: ReportFindingItem2Dto[],
    isClone: boolean,
  ) {
    return await this.prepareDataMapping(findingItems, planningRequestId, isClone, user, formId);
  }

  private async _prepareFindingItemsForMobile(
    user: TokenPayloadModel,
    formId: string,
    planningRequestId: string,
    findingItems: ReportFindingItem2Dto[],
    auditWorkspaceId: string,
    isClone: boolean,
  ) {
    return await this.prepareDataMapping(
      findingItems,
      planningRequestId,
      isClone,
      user,
      formId,
      auditWorkspaceId,
      true,
    );
  }

  private _prepareOfficeComments(
    rofId: string,
    userHistory: CreatedUserHistoryModel,
    newComments: UpdateROFOfficeCommentDTO[],
    currentComments: ROFOfficeComment[],
  ) {
    const preparedPROfficeComments: ROFOfficeComment[] = [];
    if (Array.isArray(newComments) && newComments.length > 0) {
      for (let j = 0; j < newComments.length; j++) {
        const officeComment = newComments[j];
        preparedPROfficeComments.push({
          id: officeComment.id ? officeComment.id : Utils.strings.generateUUID(),
          serialNumber: officeComment.serialNumber,
          reportFindingFormId: rofId,
          comment: officeComment.comment,
          createdUser: userHistory,
          createdAt: new Date(),
        } as ROFOfficeComment);
      }
    }

    const listNewOfficeCommentIds: string[] = [];
    const listCurrentOfficeCommentIds: string[] = [];
    if (Array.isArray(currentComments) && currentComments.length > 0) {
      currentComments.forEach((item: ROFOfficeComment) => {
        listCurrentOfficeCommentIds.push(item.id);
      });
    }
    if (preparedPROfficeComments.length > 0) {
      preparedPROfficeComments.forEach((item: ROFOfficeComment) => {
        listNewOfficeCommentIds.push(item.id);
      });
    }

    const listOfficeCommentUpdateIds = MySet.intersect(
      new Set(listCurrentOfficeCommentIds),
      new Set(listNewOfficeCommentIds),
    );
    // console.log('list Comment Update: ', Array.from(listOfficeCommentUpdateIds));

    const listOfficeCommentCreateIds = MySet.difference(
      new Set(listNewOfficeCommentIds),
      new Set(listCurrentOfficeCommentIds),
    );
    // console.log('list Comment Create: ', Array.from(listOfficeCommentCreateIds));

    const listOfficeCommentDeleteSetIds = MySet.difference(
      new Set(listCurrentOfficeCommentIds),
      new Set(listNewOfficeCommentIds),
    );
    // console.log('list comment Delete: ', Array.from(listOfficeCommentDeleteSetIds))

    const ROFOfficeCommentCreate = preparedPROfficeComments.filter((item) =>
      listOfficeCommentCreateIds.has(item.id),
    );

    const PROfficeCommentUpdate = preparedPROfficeComments.filter((item) =>
      listOfficeCommentUpdateIds.has(item.id),
    );

    return {
      ROFOfficeCommentCreate,
      PROfficeCommentUpdate,
      listOfficeCommentDeleteIds: Array.from(listOfficeCommentDeleteSetIds),
    };
  }

  async _prepareFindingItemsMobile(
    user: { id: string; companyId: string },
    workspaceId: string,
    auditChecklistId: string,
    planningRequestId: string,
    findingItems: ReportFindingItem2MobileDto[],
    isClone: boolean,
  ) {
    const preparedFindingItems = [];

    if (findingItems.length > 0) {
      const natureFindingMap: { [key: string]: { name: string; isPrimary: boolean } } = {};
      const natureFindingArr = await this.manager
        .getCustomRepository(NatureFindingRepository)
        ._listNatureFindingByIdsWithPR(
          findingItems.map((x) => x.natureFindingId),
          planningRequestId,
        );

      // prepare nature finding mapping
      natureFindingArr.forEach((natureFinding) => {
        const isPrimary = natureFinding.inspMapNatFinding?.some((m) => m.isPrimaryFinding);
        natureFindingMap[natureFinding.id] = { name: natureFinding.name, isPrimary: !!isPrimary };
      });

      for (let i = 0; i < findingItems.length; i++) {
        // Prepare finding items
        const item = { ...findingItems[i] };
        if (isClone) {
          delete item.id;
        }
        preparedFindingItems.push({
          ...item,
          auditWorkspaceId: workspaceId,
          auditChecklistId,
          isPrimaryFinding: (
            natureFindingMap[findingItems[i].natureFindingId] || { isPrimary: false }
          ).isPrimary,
          companyId: user.companyId,
          createdUserId: user.id,
          updatedUserId: user.id,
        });
      }
    }

    return preparedFindingItems;
  }

  async createReportFinding(body: CreateReportFindingFormDto, user: TokenPayloadModel) {
    return await this.connection.transaction(async (managerTrans) => {
      return this._createReportFindingFormHelper(managerTrans, body, user);
    });
  }

  async _createReportFindingFormHelper(
    manager: EntityManager,
    body: CreateReportFindingFormDto,
    user,
    createdUser?: CreatedUserHistoryModel,
    ignoreValidate?: boolean,
    fillChecklistId?: any,
  ) {
    try {
      // Check and get PR
      const pr = await this.connection
        .getCustomRepository(PlanningRequestRepository)
        ._checkAndGetPROnTrigger(body.planningRequestId, user);

      // Check if submit
      if (body.isSubmit && user.id !== pr.leadAuditorId) {
        throw new BaseError({
          status: 400,
          message: 'reportFindingForm.ONLY_LEADER_AUDITOR_CAN_SUBMIT',
        });
      }
      //Validate
      if (body.reportFindingItems?.length > 0 && !ignoreValidate) {
        await manager
          .getCustomRepository(ReportFindingItemRepository)
          ._validateROFDto(body.reportFindingItems, user);
      }
      const currYear = momentTZ.tz(body.timezone).year();
      const formId = Utils.strings.generateUUID();

      // Prepare finding items
      const {
        totalFindings,
        totalNonConformity,
        totalObservation,
        preparedFindingItems,
        preparedFindingItemsForFillChecklist,
      } = await this._prepareFindingItems(
        user,
        formId,
        body.planningRequestId,
        body.reportFindingItems,
        false,
      );

      // Prepare finding form
      const preparedFindingForm = await this._supportPrepareFindingForm(
        body,
        formId,
        user,
        pr,
        totalFindings,
        totalNonConformity,
        totalObservation,
      );

      // Prepare history
      if (!createdUser) {
        createdUser = await this.manager
          .getCustomRepository(UserRepository)
          ._getUserInfoForHistory(user.id);
      }

      const preparedFindingHistory = {
        reportFindingFormId: formId,
        officeComment: body.officeComment,
        workflowRemark: body.workflowRemark,
        //status: body.isSubmit ? ReportFindingFormStatus.SUBMITTED : ReportFindingFormStatus.DRAFT,
        status: ReportFindingFormStatus.IN_PROGRESS,
        createdUser,
        leadAuditorId: pr.leadAuditorId,
      };

      // Prepare caches: rof planning request
      const supportPrepareROFPR = await this._supportPrepareROFPlanningRequest(
        manager,
        formId,
        pr,
        user,
        currYear,
      );

      // insert into report finding form table
      const rof = await manager.save(ReportFindingForm, {
        ...preparedFindingForm,
        sNo: supportPrepareROFPR.sNo,
        refNo: supportPrepareROFPR.refNo,
      });
      //  insert into report finding item table
      await manager.save(ReportFindingItem, preparedFindingItems);
      //  update previous NC finding items
      await manager.save(ReportFindingItem, body.previousNCFindings);
      //  insert into report finding history and caches table

      await Promise.all([
        manager.insert(ReportFindingHistory, preparedFindingHistory),
        manager.insert(ROFPlanningRequest, supportPrepareROFPR.preparedROFPlanningRequest),
        manager.insert(ROFUser, supportPrepareROFPR.preparedROFUsers),
        manager.insert(ROFAuditType, supportPrepareROFPR.preparedAuditTypes),
      ]);

      // Handle add Audit log
      await manager.getCustomRepository(AuditLogRepository).createAuditLog(
        {
          module: AuditModuleEnum.REPORT_OF_FINDING,
          planningId: pr.id,
        },
        null,
        [
          //body.isSubmit ? AuditActivityEnum.SUBMITTED : AuditActivityEnum.CREATED
          AuditActivityEnum.CREATED,
        ],
        createdUser,
      );

      //Add rof id into internal audit report
      const iar = await manager.getCustomRepository(InternalAuditReportRepository).findOne({
        planningRequestId: rof.planningRequestId,
      });

      if (iar && !iar.reportFindingFormId) {
        await manager.getCustomRepository(InternalAuditReportRepository).update(
          {
            id: iar.id,
          },
          {
            reportFindingFormId: rof.id,
          },
        );
      }

      // Handle update iar into existed report finding items
      // List finding items haven't sync to IAR
      const listROFItems = await manager.find(ReportFindingItem, {
        reportFindingFormId: rof.id,
        isSyncToIAR: false,
      });
      // Decrypt attachments from listROFItems
      await Promise.all(
        listROFItems.map(async (item) => {
          if (item?.findingAttachments && item?.findingAttachments.length > 0) {
            const decryptedAttachments = decryptAttachmentValues(item.findingAttachments);
            Object.assign(item, { findingAttachments: decryptedAttachments });
          }
          return item;
        }),
      );
      const preparedFindingItemsIar = listROFItems.map((x) => {
        const item = { ...x };
        // Reflect.deleteProperty(item, 'id');
        // Reflect.deleteProperty(item, 'reportFindingFormId');

        return {
          ...item,
          internalAuditReportId: iar?.id || null,
          createdUserId: user.id,
        };
      });
      manager.save(ReportFindingItem, preparedFindingItemsIar);
      manager.update(
        ReportFindingItem,
        { id: In(listROFItems.map((item) => item.id)) },
        { isSyncToIAR: true },
      );

      for (const praparedItem of preparedFindingItemsForFillChecklist) {
        manager.update(
          FillAuditChecklistQuestion,
          {
            fillAuditChecklistId: fillChecklistId,
            chkQuestionId: praparedItem.chkQuestionId,
          },
          praparedItem,
        );
      }
      return rof;
    } catch (ex) {
      LoggerCommon.error('[ReportFindingFormRepository] createReportFindingForm error', ex);
      if (ex.code === DBErrorCode.UNIQUE_VIOLATION) {
        throw new BaseError({
          status: 400,
          message: 'reportFindingForm.PLANNING_REQUEST_IS_DUPLICATED',
        });
      }
      throw ex;
    }
  }

  async _createReportFindingFormHelperForMobile(
    manager: EntityManager,
    body: CreateReportFindingFormDto,
    user: TokenPayloadModel,
    auditWorkspaceId: string,
  ) {
    try {
      // Check and get PR
      const pr = await this.connection
        .getCustomRepository(PlanningRequestRepository)
        ._checkAndGetPROnTrigger(body.planningRequestId, user.id);

      // Check if submit
      if (body.isSubmit && user.id !== pr.leadAuditorId) {
        throw new BaseError({
          status: 400,
          message: 'reportFindingForm.ONLY_LEADER_AUDITOR_CAN_SUBMIT',
        });
      }

      const currYear = momentTZ.tz(body.timezone).year();
      const formId = Utils.strings.generateUUID();

      // Prepare finding items
      const {
        totalFindings,
        totalNonConformity,
        totalObservation,
        preparedFindingItems,
      } = await this._prepareFindingItemsForMobile(
        user,
        formId,
        body.planningRequestId,
        body.reportFindingItems,
        auditWorkspaceId,
        true,
      );

      // Prepare finding form
      const preparedFindingForm = await this._supportPrepareFindingForm(
        body,
        formId,
        user,
        pr,
        totalFindings,
        totalNonConformity,
        totalObservation,
      );

      // Prepare history
      const createdUser = await this.manager
        .getCustomRepository(UserRepository)
        ._getUserInfoForHistory(user.id);

      const preparedFindingHistory = {
        reportFindingFormId: formId,
        officeComment: body.officeComment,
        workflowRemark: body.workflowRemark,
        // status: body.isSubmit
        //   ? ReportFindingFormStatus.SUBMITTED
        //   : ReportFindingFormStatus.APPROVED,
        status: ReportFindingFormStatus.IN_PROGRESS,
        createdUser,
        leadAuditorId: pr.leadAuditorId,
      };

      // Prepare caches: rof planning request
      const supportPrepareROFPR = await this._supportPrepareROFPlanningRequest(
        manager,
        formId,
        pr,
        user,
        currYear,
      );

      // }

      // insert into report finding form table
      const rofForm = await manager.save(ReportFindingForm, {
        ...preparedFindingForm,
        sNo: supportPrepareROFPR.sNo,
        refNo: supportPrepareROFPR.refNo,
      });
      //  insert into report finding item table
      await manager.save(ReportFindingItem, preparedFindingItems);
      //  update previous NC finding items
      await manager.save(ReportFindingItem, body.previousNCFindings);
      //  insert into report finding history and caches table

      await Promise.all([
        manager.save(ReportFindingHistory, preparedFindingHistory),
        manager.save(ROFPlanningRequest, supportPrepareROFPR.preparedROFPlanningRequest),
        manager.save(ROFUser, supportPrepareROFPR.preparedROFUsers),
        manager.save(ROFAuditType, supportPrepareROFPR.preparedAuditTypes),
      ]);

      // Handle add Audit log
      await manager.getCustomRepository(AuditLogRepository).createAuditLogForMobile(
        {
          module: AuditModuleEnum.REPORT_OF_FINDING,
          planningId: pr.id,
        },
        null,
        [body.isSubmit ? AuditActivityEnum.SUBMITTED : AuditActivityEnum.CREATED],
        createdUser,
      );

      const rofIds: any[] = await manager.find(ReportFindingItem, {
        where: { reportFindingFormId: rofForm.id },
      });

      //Add rof id into internal audit report
      const iar = await manager.getCustomRepository(InternalAuditReportRepository).findOne({
        planningRequestId: rofForm.planningRequestId,
      });

      if (iar && !iar.reportFindingFormId) {
        await manager.getCustomRepository(InternalAuditReportRepository).update(
          {
            id: iar.id,
          },
          {
            reportFindingFormId: rofForm.id,
          },
        );
      }

      // Handle update iar into existed report finding items
      // List finding items haven't sync to IAR
      const listROFItems = await manager.find(ReportFindingItem, {
        reportFindingFormId: rofForm.id,
        isSyncToIAR: false,
      });

      const preparedFindingItemsIar = listROFItems.map((x) => {
        const item = { ...x };
        // Reflect.deleteProperty(item, 'id');
        // Reflect.deleteProperty(item, 'reportFindingFormId');

        return {
          ...item,
          internalAuditReportId: iar?.id || null,
          createdUserId: user.id,
        };
      });
      manager.save(ReportFindingItem, preparedFindingItemsIar);
      manager.update(
        ReportFindingItem,
        { id: In(listROFItems.map((item) => item.id)) },
        { isSyncToIAR: true },
      );

      return rofForm;
    } catch (ex) {
      LoggerCommon.error('[ReportFindingFormRepository] createReportFindingForm error', ex);
      if (ex.code === DBErrorCode.UNIQUE_VIOLATION) {
        throw new BaseError({
          status: 400,
          message: 'reportFindingForm.PLANNING_REQUEST_IS_DUPLICATED',
        });
      }
      throw ex;
    }
  }

  async _checkCanUpdateAndGet(
    reportFindingFormId: string,
    user: TokenPayloadModel,
    workflowPermissions: string[],
  ) {
    const qb = this.createQueryBuilder('reportFindingForm')
      .leftJoin(
        'reportFindingForm.reportFindingItems',
        'reportFindingItems',
        'reportFindingItems.deleted = false',
      )
      .leftJoin('reportFindingForm.SAFindingItem', 'SAFindingItem', 'SAFindingItem.deleted = false')
      .leftJoin('reportFindingForm.planningRequest', 'planningRequest')
      .leftJoin('reportFindingForm.reportFindingHistories', 'reportFindingHistories')
      .leftJoin('reportFindingForm.rofOfficeComments', 'rofOfficeComments')
      .where(
        'reportFindingForm.id = :reportFindingFormId AND reportFindingForm.status != :status AND reportFindingForm.companyId = :companyId',
        {
          reportFindingFormId,
          status: ReportFindingFormStatus.APPROVED,
          companyId: user.companyId,
        },
      )
      .select([
        'reportFindingForm.status',
        'reportFindingForm.refNo',
        'reportFindingForm.companyId',
        'reportFindingForm.isDuplicatedCAR',
      ])
      .addSelect([
        'planningRequest.id',
        'planningRequest.leadAuditorId',
        'planningRequest.vesselId',
        'planningRequest.isSA',
        'reportFindingItems.id',
        'reportFindingItems.findingAttachments',
        'reportFindingItems.locationId',
        'reportFindingItems.departmentId',
        'reportFindingItems.mainCategoryId',
        'reportFindingItems.secondCategoryId',
        'reportFindingItems.natureFindingId',
        'reportFindingItems.viqId',
        'SAFindingItem.id',
        'SAFindingItem.natureFindingId',
        'reportFindingHistories.id',
        'reportFindingHistories.status',
        'reportFindingHistories.createdAt',
        'rofOfficeComments.id',
      ])
      .addOrderBy('reportFindingHistories.createdAt', 'DESC');

    const record = await this.getOneQB(qb);

    return record;
    // if (record) {
    //   if (
    //     record.status === ReportFindingFormStatus.DRAFT &&
    //     workflowPermissions.includes(WorkflowPermission.CREATOR)
    //   ) {
    //     return record;
    //   } else if (
    //     record.status === ReportFindingFormStatus.SUBMITTED &&
    //     (workflowPermissions.includes(WorkflowPermission.REVIEWER1) ||
    //       workflowPermissions.includes(WorkflowPermission.REVIEWER2) ||
    //       workflowPermissions.includes(WorkflowPermission.REVIEWER3) ||
    //       workflowPermissions.includes(WorkflowPermission.REVIEWER4) ||
    //       workflowPermissions.includes(WorkflowPermission.REVIEWER5))
    //   ) {
    //     return record;
    //   } else if (
    //     record.status === ReportFindingFormStatus.REVIEWED_1 &&
    //     (workflowPermissions.includes(WorkflowPermission.REVIEWER2) ||
    //       workflowPermissions.includes(WorkflowPermission.REVIEWER3) ||
    //       workflowPermissions.includes(WorkflowPermission.REVIEWER4) ||
    //       workflowPermissions.includes(WorkflowPermission.REVIEWER5))
    //   ) {
    //     return record;
    //   } else if (
    //     record.status === ReportFindingFormStatus.REVIEWED_2 &&
    //     (workflowPermissions.includes(WorkflowPermission.REVIEWER3) ||
    //       workflowPermissions.includes(WorkflowPermission.REVIEWER4) ||
    //       workflowPermissions.includes(WorkflowPermission.REVIEWER5))
    //   ) {
    //     return record;
    //   } else if (
    //     record.status === ReportFindingFormStatus.REVIEWED_3 &&
    //     (workflowPermissions.includes(WorkflowPermission.REVIEWER4) ||
    //       workflowPermissions.includes(WorkflowPermission.REVIEWER5))
    //   ) {
    //     return record;
    //   } else if (
    //     record.status === ReportFindingFormStatus.REVIEWED_4 &&
    //     workflowPermissions.includes(WorkflowPermission.REVIEWER5)
    //   ) {
    //     return record;
    //   } else if (
    //     (record.status === ReportFindingFormStatus.REVIEWED_1 ||
    //       record.status === ReportFindingFormStatus.REVIEWED_2 ||
    //       record.status === ReportFindingFormStatus.REVIEWED_3 ||
    //       record.status === ReportFindingFormStatus.REVIEWED_4 ||
    //       record.status === ReportFindingFormStatus.REVIEWED_5) &&
    //     workflowPermissions.includes(WorkflowPermission.APPROVER)
    //   ) {
    //     return record;
    //   } else if (record.status === ReportFindingFormStatus.REASSIGNED) {
    //     if (
    //       // record.reportFindingHistories[1]?.status === ReportFindingFormStatus.SUBMITTED &&
    //       workflowPermissions.includes(WorkflowPermission.CREATOR)
    //     ) {
    //       return record;
    //     }
    //     // else if (
    //     //   record.reportFindingHistories[1]?.status === ReportFindingFormStatus.REVIEWED &&
    //     //   workflowPermissions.includes(WorkflowPermission.REVIEWER)
    //     // ) {
    //     //   return record;
    //     // }
    //   }
    // }

    // throw new BaseError({ status: 400, message: 'reportFindingForm.CANNOT_UPDATE' });
  }

  async _checkCanSubmitAndGet(reportFindingFormId: string, user: TokenPayloadModel) {
    const qb = this.createQueryBuilder('reportFindingForm')
      .leftJoin('reportFindingForm.planningRequest', 'planningRequest')
      .leftJoin('planningRequest.auditors', 'auditors')
      .leftJoin(
        'reportFindingForm.reportFindingItems',
        'reportFindingItems',
        'reportFindingItems.deleted = false',
      )
      .where('reportFindingForm.id = :reportFindingFormId AND reportFindingForm.status = :status', {
        reportFindingFormId,
        status: ReportFindingFormStatus.DRAFT,
        companyId: user.companyId,
      })
      .select([
        'reportFindingForm.id',
        'reportFindingForm.refNo',
        'reportFindingForm.companyId',
        'reportFindingForm.planningRequestId',
        'reportFindingForm.status',
      ])
      .addSelect([
        'planningRequest.id',
        'planningRequest.leadAuditorId',
        'planningRequest.vesselId',
        'reportFindingItems.id',
        'reportFindingItems.natureFindingName',
        'reportFindingItems.carId',
        'reportFindingItems.findingComment',
      ]);

    const record = await this.getOneQB(qb);
    if (record) {
      if (record.planningRequest.leadAuditorId !== user.id) {
        throw new BaseError({
          status: 400,
          message: 'reportFindingForm.ONLY_LEADER_AUDITOR_CAN_SUBMIT',
        });
      }
      return record;
    } else {
      throw new BaseError({ status: 400, message: 'reportFindingForm.CANNOT_SUBMIT' });
    }
  }

  async _checkCanUndoSubmitAndGet(reportFindingFormId: string, user: TokenPayloadModel) {
    const qb = this.createQueryBuilder('reportFindingForm')
      // .leftJoin('reportFindingForm.planningRequest', 'planningRequest')
      // .leftJoin('planningRequest.auditors', 'auditors')
      .where(
        'reportFindingForm.id = :reportFindingFormId AND reportFindingForm.status = :status AND reportFindingForm.companyId = :companyId',
        {
          reportFindingFormId,
          status: ReportFindingFormStatus.SUBMITTED,
          companyId: user.companyId,
        },
      )
      .select(['reportFindingForm.id']);

    const record = await this.getOneQB(qb);
    if (record) {
      return record;
    } else {
      throw new BaseError({ status: 400, message: 'reportFindingForm.CANNOT_REVIEW' });
    }
  }

  async _checkCanReviewAndGet(reportFindingFormId: string, user: TokenPayloadModel) {
    const qb = this.createQueryBuilder('reportFindingForm')
      .leftJoin('reportFindingForm.planningRequest', 'planningRequest')
      .leftJoin('planningRequest.auditors', 'auditors')
      .where(
        'reportFindingForm.id = :reportFindingFormId AND reportFindingForm.status IN (:...status) AND reportFindingForm.companyId = :companyId',
        {
          reportFindingFormId,
          status: [ReportFindingFormStatus.SUBMITTED, ReportFindingFormStatus.REASSIGNED],
          companyId: user.companyId,
        },
      )
      .select([
        'reportFindingForm.id',
        'reportFindingForm.planningRequestId',
        'reportFindingForm.status',
        'reportFindingForm.refNo',
        'planningRequest.id',
        'auditors.id',
      ]);

    const record = await this.getOneQB(qb);
    if (record) {
      return record;
    } else {
      throw new BaseError({ status: 400, message: 'reportFindingForm.CANNOT_REVIEW' });
    }
  }

  async _checkCanCloseOutAndGet(reportFindingFormId: string, user: TokenPayloadModel) {
    const qb = this.createQueryBuilder('reportFindingForm')
      .leftJoin('reportFindingForm.planningRequest', 'planningRequest')
      .leftJoin('planningRequest.auditors', 'auditors')
      .where(
        'reportFindingForm.id = :reportFindingFormId AND reportFindingForm.status IN (:...status) AND reportFindingForm.companyId = :companyId',
        {
          reportFindingFormId,
          status: [
            ReportFindingFormStatus.REVIEWED_1,
            ReportFindingFormStatus.REVIEWED_2,
            ReportFindingFormStatus.REVIEWED_3,
            ReportFindingFormStatus.REVIEWED_4,
            ReportFindingFormStatus.REVIEWED_5,
          ],
          companyId: user.companyId,
        },
      )
      .select([
        'reportFindingForm.id',
        'reportFindingForm.vesselId',
        'reportFindingForm.departmentId',
        'reportFindingForm.planningRequestId',
        'reportFindingForm.status',
        'reportFindingForm.refNo',
        'planningRequest.id',
        'auditors.id',
      ]);

    const record = await this.getOneQB(qb);
    if (record) {
      return record;
    } else {
      throw new BaseError({ status: 400, message: 'reportFindingForm.CANNOT_CLOSE_OUT' });
    }
  }

  async _checkCanReassignAndGet(reportFindingFormId: string, user: TokenPayloadModel) {
    const qb = this.createQueryBuilder('reportFindingForm')
      .leftJoin('reportFindingForm.planningRequest', 'planningRequest')
      .leftJoin('planningRequest.auditors', 'auditors')
      .where(
        'reportFindingForm.id = :reportFindingFormId AND reportFindingForm.status = :status AND reportFindingForm.companyId = :companyId',
        {
          reportFindingFormId,
          status: ReportFindingFormStatus.APPROVED,
          companyId: user.companyId,
        },
      )
      .select([
        'reportFindingForm.id',
        'reportFindingForm.planningRequestId',
        'reportFindingForm.status',
        'reportFindingForm.refNo',
        'planningRequest.id',
        'auditors.id',
      ]);

    const record = await this.getOneQB(qb);
    if (record) {
      return record;
    } else {
      throw new BaseError({ status: 400, message: 'reportFindingForm.CANNOT_REASSIGN' });
    }
  }

  async updateReportFindingForm(
    reportFindingFormId: string,
    body: UpdateReportFindingFormDto,
    user: TokenPayloadModel,
    workflowPermissions: string[],
    userAssignmentParams?: CreateUserAssignmentDTO,
  ) {
    return await this.connection.transaction(async (managerTrans) => {
      // validate planning request and get vesselId
      const currReportFindingForm = await this._checkCanUpdateAndGet(
        reportFindingFormId,
        user,
        workflowPermissions,
      );
      //validate inactive department
      if (body.reportFindingItems?.length > 0) {
        await this.manager
          .getCustomRepository(ReportFindingItemRepository)
          ._validateROFDto(body.reportFindingItems, user, currReportFindingForm.reportFindingItems);
      }

      const currentItemIds = body?.reportFindingItems?.map((item) => item.id) || [];
      const currentSAItemIds = body?.SAFindingItems?.map((item) => item.id) || [];

      const findingItemsUpdate = await this.manager
        .getCustomRepository(ReportFindingItemRepository)
        .findByIds(currentItemIds);

      // const saFindingItemsUpdate = await this.manager
      //   .getCustomRepository(SAFindingItem)
      //   .findByIds(currentSAItemIds);

      const newStatus = currReportFindingForm.status;
      // if (body.isSubmit) {
      //   // Check can submit status
      //   if (
      //     ![ReportFindingFormStatus.DRAFT, ReportFindingFormStatus.REASSIGNED].includes(
      //       currReportFindingForm.status as ReportFindingFormStatus,
      //     )
      //   ) {
      //     throw new BaseError({
      //       status: 400,
      //       message: 'reportFindingForm.CANNOT_SUBMIT',
      //     });
      //   }

      //   // Check leader can submit
      //   if (user.id !== currReportFindingForm.planningRequest.leadAuditorId) {
      //     throw new BaseError({
      //       status: 400,
      //       message: 'reportFindingForm.ONLY_LEADER_AUDITOR_CAN_SUBMIT',
      //     });
      //   }
      //   newStatus = ReportFindingFormStatus.SUBMITTED;
      // } else {
      //   if (currReportFindingForm.status === ReportFindingFormStatus.REASSIGNED) {
      //     newStatus = ReportFindingFormStatus.DRAFT;
      //   }
      // }
      const findingForms = [];
      for (const item of body.reportFindingItems) {
        if (item.id) {
          const filterROF = currReportFindingForm.reportFindingItems.filter(
            (itemFilter) => itemFilter.id === item.id,
          );
          Object.assign(item, {
            locationId: filterROF.length > 0 ? filterROF[0].locationId : undefined,
          });
        }

        findingForms.push(item);
      }

      // Process regular finding items
      const {
        totalFindings,
        totalNonConformity,
        totalObservation,
        preparedFindingItems: preparedFindings,
        preparedFindingItemsForFillChecklist,
      } = await this._prepareFindingItems(
        user,
        reportFindingFormId,
        currReportFindingForm.planningRequest.id,
        findingForms,
        false,
      );

      // Process SA finding items if present
      let saTotalFindings = 0;
      let saTotalNonConformity = 0;
      let saTotalObservation = 0;
      let preparedSAFindingItems = [];

      if (body.SAFindingItems?.length > 0) {
        const saResult = await this._prepareSAFindingItems(
          user,
          reportFindingFormId,
          currReportFindingForm.planningRequest.id,
          body.SAFindingItems,
          false,
        );

        saTotalFindings = saResult.totalFindings;
        saTotalNonConformity = saResult.totalNonConformity;
        saTotalObservation = saResult.totalObservation;
        preparedSAFindingItems.push(...saResult.preparedSAFindingItems);
      }

      // Update totals to include both regular findings and SA findings
      const updatedTotalFindings = totalFindings + saTotalFindings;
      const updatedTotalNonConformity = totalNonConformity + saTotalNonConformity;
      const updatedTotalObservation = totalObservation + saTotalObservation;

      let preparedInsertOrUpdateFindingItems = preparedFindings;
      // Add iarId into report finding item
      const iar = await this.connection.getCustomRepository(InternalAuditReportRepository).findOne({
        where: {
          planningRequestId: currReportFindingForm.planningRequest.id,
        },
        select: ['id'],
      });

      if (iar) {
        for (const findingItem of preparedInsertOrUpdateFindingItems) {
          Object.assign(findingItem, { internalAuditReportId: iar.id });
        }
        for (const findingItem of preparedSAFindingItems) {
          Object.assign(findingItem, { internalAuditReportId: iar.id });
        }
      }

      // Add workspaceId into report finding item
      const workspace = await this.connection
        .getCustomRepository(AuditWorkspaceRepository)
        .findOne({
          where: {
            planningRequestId: currReportFindingForm.planningRequest.id,
          },
          select: ['id'],
        });

      if (workspace) {
        for (const findingItem of preparedInsertOrUpdateFindingItems) {
          Object.assign(findingItem, { auditWorkspaceId: workspace.id });
        }
        for (const findingItem of preparedSAFindingItems) {
          Object.assign(findingItem, { auditWorkspaceId: workspace.id });
        }
      }

      const fillAuditChecklist = await this.connection
        .getRepository(FillAuditChecklist)
        .createQueryBuilder('fillAuditChecklist')
        .leftJoinAndSelect('fillAuditChecklist.selfAssessment', 'selfAssessment')
        .where('fillAuditChecklist.auditWorkspaceId = :auditWorkspaceId', {
          auditWorkspaceId: workspace.id,
        })
        .getOne();

      if (fillAuditChecklist) {
        for (const findingItem of preparedSAFindingItems) {
          Object.assign(findingItem, { selfAssessmentId: fillAuditChecklist.selfAssessment.id });
        }
      }

      const preparedDeleteFindingItemIds = [
        ...MySet.difference(
          new Set(currReportFindingForm.reportFindingItems.map((item) => item.id)),
          new Set(body.reportFindingItems.filter((item) => item.id).map((item) => item.id)),
        ),
      ];

      const prepareDeleteSAFindingItemIds = [
        ...MySet.difference(
          new Set(currReportFindingForm.SAFindingItem.map((item) => item.id)),
          new Set(body.SAFindingItems.filter((item) => item.id).map((item) => item.id)),
        ),
      ];

      let allIds = [];
      let carAttachments = [];
      if (preparedDeleteFindingItemIds?.length > 0) {
        const prepareDeleteFindingItem = {
          reportFindingItemId: null,
          createdUserId: null,
          updatedUserId: null,
          reportFindingFormId: null,
          internalAuditReportId: null,
          auditWorkspaceId: null,
          auditTypeId: null,
          auditTypeName: null,
          natureFindingId: null,
          natureFindingName: null,
          isPrimaryFinding: null,
          locationName: null,
          findingComment: null,
          reference: null,
          isSignificant: null,
          rectifiedOnBoard: null,
          mainCategoryId: null,
          mainCategoryName: null,
          secondCategoryId: null,
          secondCategoryName: null,
          thirdCategoryId: null,
          thirdCategoryName: null,
          remark: null,
          carId: null,
          isManualFinding: false,
          findingRemark: null,
          memo: null,
          answers: [],
          evidencePictures: [],
          attachments: [],
        };
        await this.manager.update(
          FillAuditChecklistQuestion,
          { reportFindingItemId: In(preparedDeleteFindingItemIds) }, // Use IN condition
          prepareDeleteFindingItem,
        );
        const attachments = await this.connection
          .getCustomRepository(FillAuditChecklistQuestionRepository)
          .createQueryBuilder('fillAuditChecklistQuestion')
          .select([
            'fillAuditChecklistQuestion.evidencePictures',
            'fillAuditChecklistQuestion.attachments',
          ])
          .where(
            'fillAuditChecklistQuestion.reportFindingItemId IN (:...ids) and fillAuditChecklistQuestion.deleted=:deleted',
            {
              ids: preparedDeleteFindingItemIds,
              deleted: false,
            },
          )
          .getMany();

        const findingItems = await this.connection
          .getCustomRepository(ReportFindingItemRepository)
          .createQueryBuilder('reportFindingItem')
          .select(['reportFindingItem.carId'])
          .where('reportFindingItem.id IN (:...ids) and reportFindingItem.deleted=:deleted', {
            ids: preparedDeleteFindingItemIds,
            deleted: false,
          })
          .getMany();

        if (findingItems?.length > 0) {
          const carIds = findingItems?.filter((item) => item?.carId)?.map((item) => item?.carId);
          if (carIds?.length > 0) {
            const cars = await this.connection
              .getCustomRepository(CARRepository)
              .createQueryBuilder('car')
              .select(['car.attachments'])
              .where('car.id IN (:...ids) and car.deleted=:deleted', {
                ids: carIds,
                deleted: false,
              })
              .getMany();

            await this.connection
              .getCustomRepository(CARRepository)
              .delete({ id: In(cars?.map((item) => item?.id)) });

            cars
              ?.filter((item) => item?.attachments?.length)
              ?.map((item) => (carAttachments = [...carAttachments, ...item?.attachments]));
          }
        }

        allIds = attachments?.reduce((acc: any[], item) => {
          return acc.concat(item?.evidencePictures || [], item?.attachments || []);
        }, []);
        console.log('allIds', allIds);
        console.log('carAttachments', carAttachments);
        if (carAttachments?.length > 0) {
          allIds = [...allIds, ...carAttachments];
          console.log('allIds', allIds);
        }
      } else if (prepareDeleteSAFindingItemIds.length > 0) {
        // Handle CAR deletion for SA finding items
        const preparedDeletedSACarIds = await this.getCarIdsNeedToDeleted(
          prepareDeleteSAFindingItemIds,
          managerTrans,
          true, // isSA = true for self-assessment
        );

        await managerTrans.delete(SAFindingItem, { id: In(prepareDeleteSAFindingItemIds) });
        if (preparedDeletedSACarIds.length != 0) {
          await managerTrans.delete(CorrectiveActionRequest, { id: In(preparedDeletedSACarIds) });
        }
      }

      for (const preparedItem of preparedFindingItemsForFillChecklist) {
        await this.manager.update(
          FillAuditChecklistQuestion,
          { reportFindingItemId: preparedItem.reportFindingItemId },
          preparedItem,
        );
      }

      // if findings have chkQuestionId the below code will be exexuted || Manual finding.
      const newFindings = [];
      preparedInsertOrUpdateFindingItems = preparedInsertOrUpdateFindingItems?.filter((item) => {
        if (item?.chkQuestionId) {
          newFindings?.push({ ...item, isManualFinding: true });
          return false;
        }
        return true;
      });
      preparedSAFindingItems = preparedSAFindingItems?.filter((item) => {
        if (item?.elementMasterId) {
          newFindings?.push({ ...item, isManualFinding: true });
          return false;
        }
        return true;
      });
      if (newFindings?.length) {
        for (const item of newFindings) {
          if (item?.elementMasterId) {
            // Save the SA finding item
            await this.manager.save(SAFindingItem, item);

            // Get the saved data
            const saFindingData = await this.manager
              .createQueryBuilder(SAFindingItem, 'saFindingItem')
              .where(
                'saFindingItem.id = :id AND saFindingItem.reportFindingFormId = :reportFindingFormId',
                {
                  id: item?.id,
                  reportFindingFormId,
                },
              )
              .getOne();

            // Check if this item has been updated and handle attachments if needed
            // if (currentSAItemIds.includes(item.id)) {
            //   const itemDTO = body.SAFindingItems.find((it) => it.id === item.id);

            //   // Process findingAttachments if any
            //   if (itemDTO?.findingAttachments) {
            //     const currentAtt = saFindingData?.findingAttachments || [];
            //     const newAtt = itemDTO?.findingAttachments || [];

            //     // Find attachments to delete and add
            //     const attDelete = Array.from(
            //       MySet.difference(new Set(currentAtt), new Set(newAtt)),
            //     );
            //     const attAdd = Array.from(MySet.difference(new Set(newAtt), new Set(currentAtt)));

            //     // Create updated attachment list
            //     const updateAttachment = currentAtt
            //       ? currentAtt.filter((it) => !attDelete.includes(it))
            //       : newAtt;
            //     const updatedAttachments = Array.from(new Set([...updateAttachment, ...attAdd]));

            //     // Update findingAttachments on the SA finding item
            //     await this.manager.update(
            //       SAFindingItem,
            //       { id: item.id },
            //       { findingAttachments: updatedAttachments },
            //     );
            //   }
            // }

            // Update fill-sa-checklist-question with the SAFindingItem ID
            if (saFindingData.elementMasterId) {
              // Find related fill-sa-checklist-question records
              const fillSAQuestions = await this.manager
                .getRepository(FillSAChecklistQuestion)
                .createQueryBuilder('fillSAChecklistQuestion')
                .leftJoin('fillSAChecklistQuestion.fillAuditChecklist', 'fillAuditChecklist')
                .leftJoin('fillAuditChecklist.auditWorkspace', 'auditWorkspace')
                .where('fillSAChecklistQuestion.elementMasterId = :elementMasterId', {
                  elementMasterId: saFindingData.elementMasterId,
                })
                .andWhere('fillAuditChecklist.auditWorkspaceId = :auditWorkspaceId', {
                  auditWorkspaceId: saFindingData.auditWorkspaceId,
                })
                .getMany();

              // Update the fill-sa-checklist-question records with the SAFindingItem ID
              if (fillSAQuestions && fillSAQuestions.length > 0) {
                for (const question of fillSAQuestions) {
                  await this.manager.update(
                    FillSAChecklistQuestion,
                    { id: question.id },
                    {
                      sAFindingItemId: saFindingData.id,
                      remarks: saFindingData.findingRemark,
                      auditorComplianceId: saFindingData.complianceId,
                      auditorCompliance: saFindingData.auditorCompliance,
                    },
                  );
                }
              }
            }
          } else {
            await this.manager.getCustomRepository(ReportFindingItemRepository).save(item);
            const reportFindingData = await this.manager
              .getCustomRepository(ReportFindingItemRepository)
              .createQueryBuilder('reportFindingItem')
              .where(
                'reportFindingItem.id = :id AND reportFindingItem.reportFindingFormId = :reportFindingFormId',
                {
                  id: item?.id,
                  reportFindingFormId,
                },
              )
              .getOne();

            const fillAuditChecklistData = await this.manager
              .getCustomRepository(FillAuditChecklistRepository)
              .createQueryBuilder('fillAuditChecklist')
              .where('fillAuditChecklist.auditWorkspaceId = :auditWorkspaceId', {
                auditWorkspaceId: reportFindingData?.auditWorkspaceId,
              })
              .getMany();

            const fillChecklistIds = fillAuditChecklistData?.length
              ? fillAuditChecklistData.map((item) => item.id)
              : [];

            const fillQuestionFound = await this.manager
              .getCustomRepository(FillAuditChecklistQuestionRepository)
              .createQueryBuilder('fillQuestion')
              .select()
              .where('fillQuestion.chkQuestionId = :chkQuestionId', {
                chkQuestionId: item?.chkQuestionId,
              })
              .andWhere('fillQuestion.fillAuditChecklistId IN (:...fillChecklistIds)', {
                fillChecklistIds,
              })
              .orderBy('fillQuestion.updatedAt', 'DESC')
              .getOne();
            const fillAuditChecklistFound = fillAuditChecklistData.filter(
              (item) => item.id === fillQuestionFound.fillAuditChecklistId,
            )[0];

            const chkQuestionAnswerData = await this.manager
              .getCustomRepository(ChkQuestionAnswerRepository)
              .createQueryBuilder('chkQuestionAnswer')
              .leftJoin('chkQuestionAnswer.value', 'valuemanagement')
              .where(
                'chkQuestionAnswer.chkQuestionId = :chkQuestionId and valuemanagement.number < :number',
                {
                  chkQuestionId: item?.chkQuestionId,
                  number: 0,
                },
              )
              .getOne();

            const fillAuditChecklistQuestion = {
              reportFindingItemId: item?.id,
              findingRemark: reportFindingData?.findingComment,
              answers: [chkQuestionAnswerData?.id],
              locationId: reportFindingData?.locationId,
              chkQuestionId: reportFindingData?.chkQuestionId,
              reportFindingFormId: reportFindingData?.reportFindingFormId,
              internalAuditReportId: reportFindingData?.internalAuditReportId,
              auditWorkspaceId: reportFindingData?.auditWorkspaceId,
              auditTypeId: reportFindingData?.auditTypeId,
              auditTypeName: reportFindingData?.auditTypeName,
              natureFindingId: reportFindingData?.natureFindingId,
              natureFindingName: reportFindingData?.natureFindingName,
              isPrimaryFinding: reportFindingData?.isPrimaryFinding,
              locationName: reportFindingData?.locationName,
              findingComment: reportFindingData?.findingComment,
              reference: reportFindingData?.reference,
              isSignificant: reportFindingData?.isSignificant,
              rectifiedOnBoard: reportFindingData?.rectifiedOnBoard,
              mainCategoryId: reportFindingData?.mainCategoryId,
              mainCategoryName: reportFindingData?.mainCategoryName,
              secondCategoryId: reportFindingData?.secondCategoryId,
              secondCategoryName: reportFindingData?.secondCategoryName,
              thirdCategoryId: reportFindingData?.thirdCategoryId,
              thirdCategoryName: reportFindingData?.thirdCategoryName,
              remark: reportFindingData?.remark,
              findingAttachments: decryptAttachmentValues(reportFindingData?.findingAttachments),
              carId: reportFindingData?.carId,
              isManualFinding: reportFindingData?.isManualFinding,
            };

            await this.manager.getCustomRepository(FillAuditChecklistQuestionRepository).update(
              {
                id: fillQuestionFound?.id,
                chkQuestionId: item?.chkQuestionId,
              },
              {
                ...fillAuditChecklistQuestion,
              },
            );
            await this.manager.getCustomRepository(ReportFindingItemRepository).update(
              {
                id: item?.id,
                chkQuestionId: item?.chkQuestionId,
                reportFindingFormId,
              },
              {
                auditChecklistId: fillAuditChecklistFound?.auditChecklistId,
              },
            );

            if (!currentItemIds.includes(item.id)) {
              await this.manager
                .getCustomRepository(FillAuditChecklistQuestionRepository)
                .syncDataToFindingItem(item?.id, user);
            }
            if (currentItemIds.includes(item.id)) {
              const currentItem = findingItemsUpdate.find((it) => it.id === item.id);
              const itemDTO = body.reportFindingItems.find((it) => it.id === item.id);
              const currentAtt = decryptAttachmentValues(currentItem?.findingAttachments) || [];
              const newAtt = itemDTO?.findingAttachments || [];
              const attDelete = Array.from(MySet.difference(new Set(currentAtt), new Set(newAtt)));
              const attAdd = Array.from(MySet.difference(new Set(newAtt), new Set(currentAtt)));
              const updateAttachment = currentAtt
                ? currentAtt.filter((it) => !attDelete.includes(it))
                : newAtt;
              const updateFindingAtt = Array.from(new Set([...updateAttachment, ...attAdd]));

              await this.manager
                .getCustomRepository(FillAuditChecklistQuestionRepository)
                .updateReportFinding(item.id, updateFindingAtt);

              if (reportFindingData?.carId) {
                const carFound = await this.manager.findOne(CorrectiveActionRequest, {
                  where: { id: reportFindingData.carId },
                });
                const carAttachment = carFound.attachments?.length
                  ? decryptAttachmentValues(carFound?.attachments)
                  : [];
                const syncCarAtt = carAttachment?.length
                  ? carAttachment.filter((item) => !attDelete.includes(item))
                  : newAtt;

                const attSync = Array.from(new Set([...syncCarAtt, ...attAdd]));
                await this.manager
                  .getCustomRepository(CARRepository)
                  .updateCarAttachment(reportFindingData.carId, attSync);
              }
            }
          }
        }
      }
      // Handle delete report finding item
      const preparedDeleteFindingItemAttachmentIds = [];
      for (const itemReportFinding of body.reportFindingItems) {
        if (itemReportFinding.id) {
          const preparedDeleteItemReportFindingAttachmentId = [
            ...MySet.difference(
              new Set(
                currReportFindingForm.reportFindingItems.filter(
                  (item) => item.id === itemReportFinding.id,
                )[0]?.findingAttachments,
              ),
              new Set(itemReportFinding.findingAttachments),
            ),
          ];
          if (preparedDeleteItemReportFindingAttachmentId.length > 0) {
            for (const item of preparedDeleteItemReportFindingAttachmentId) {
              if (preparedDeleteFindingItemAttachmentIds.indexOf(item) === -1) {
                preparedDeleteFindingItemAttachmentIds.push(item);
              }
            }
          }
        }
      }

      // Prepare data to update report finding form
      const preparedFindingForm = {
        ...omit(body, [
          'reportFindingItems',
          'SAFindingItems',
          'previousNCFindings',
          'comments',
          'workflowRemark',
          'isSubmit',
          'timezone',
          'officeComments',
          'userAssignment',
        ]),
        updatedUserId: user.id,
        status: newStatus,
        totalFindings: updatedTotalFindings,
        totalNonConformity: updatedTotalNonConformity,
        totalObservation: updatedTotalObservation,
      };

      // Get user info
      const createdUser = await this.manager
        .getCustomRepository(UserRepository)
        ._getUserInfoForHistory(user.id);

      const preparedFindingHistory = {
        reportFindingFormId,
        workflowRemark: body.workflowRemark,
        status: newStatus,
        createdUser,
      };

      // Prepare Office comment
      const {
        ROFOfficeCommentCreate,
        PROfficeCommentUpdate,
        listOfficeCommentDeleteIds,
      } = this._prepareOfficeComments(
        reportFindingFormId,
        createdUser,
        body.officeComments,
        currReportFindingForm.rofOfficeComments,
      );

      const currYear = momentTZ.tz(body.timezone).year();
      const dataNoti: INotificationEventModel[] = [];
      const dataSendMail: IEmailEventModel[] = [];

      // General refNo when submit
      const refNoObj: { refNo?: string } = {};
      if (body.isSubmit && !currReportFindingForm.refNo) {
        const counter = await this.manager
          .getCustomRepository(CompanyFeatureVersionRepository)
          .getNextVersion({
            manager: managerTrans,
            companyId: user.companyId,
            feature: FeatureVersionConfig.REPORT_FINDING_FORM_REFNO,
            year: Number(currYear),
          });
        refNoObj.refNo = await this._genReportFindingFormRefNo(
          currReportFindingForm.companyId,
          counter,
          Number(currYear),
        );
      }

      // insert into report finding form table
      await managerTrans.update(
        ReportFindingForm,
        { id: reportFindingFormId },
        { ...preparedFindingForm, ...refNoObj },
      );
      // delete report finding items record
      if (preparedDeleteFindingItemIds.length > 0) {
        //find Car with total items = 0

        const preparedDeletedCarIds = await this.getCarIdsNeedToDeleted(
          preparedDeleteFindingItemIds,
          managerTrans,
          currReportFindingForm.planningRequest.isSA,
        );

        await managerTrans.delete(ReportFindingItem, { id: In(preparedDeleteFindingItemIds) });
        if (preparedDeletedCarIds.length != 0) {
          await managerTrans.delete(CorrectiveActionRequest, { id: In(preparedDeletedCarIds) });
        }
      }

      // Prepare to handle finding item value change history
      const updateItemId = [];
      for (const itemBody of preparedInsertOrUpdateFindingItems) {
        if (itemBody.id) {
          updateItemId.push(itemBody.id);
        }
      }

      const currentItems = await managerTrans.find(ReportFindingItem, {
        where: {
          id: In(updateItemId),
        },
      });

      // insert or update into report finding item table
      const items = await managerTrans.save(ReportFindingItem, preparedInsertOrUpdateFindingItems);

      // Save SA finding items if they exist
      if (preparedSAFindingItems.length > 0) {
        await managerTrans.save(SAFindingItem, preparedSAFindingItems);
      }
      // Handle finding item value change history
      const userHistory = await this.manager
        .getCustomRepository(UserRepository)
        ._getUserInfoForHistory(user.id);

      const preparedValueChanges = [];
      for (const item of items) {
        for (const itemBody of preparedInsertOrUpdateFindingItems) {
          const newItemBody = pick(itemBody, [
            'findingRemark',
            'findingComment',
            'reference',
            'natureFindingName',
            'auditTypeName',
            'mainCategoryName',
            'secondCategoryName',
            'thirdCategoryName',
            'viqRefNo',
            'departmentName',
          ]);

          if (item.id === itemBody.id && !updateItemId.includes(item.id)) {
            const valueChanges = commonCheckValueChange(item, newItemBody, true);

            if (valueChanges) {
              for (let i = 0; i < valueChanges.length; i++) {
                preparedValueChanges.push({
                  module: ModuleEnum.FINDING_ITEM,
                  action: ActionValueChangeEnum.CREATE,
                  createdUser: userHistory,
                  companyId: user.companyId,
                  recordId: item.id,
                  key: valueChanges[i].key,
                  fieldLabel: valueChanges[i].fieldLabel,
                  oldValue: null,
                  newValue: valueChanges[i].newValue,
                });
              }
            }
          }
        }
      }

      for (const item of currentItems) {
        for (const itemBody of preparedInsertOrUpdateFindingItems) {
          const newItemBody = pick(itemBody, [
            'findingRemark',
            'findingComment',
            'reference',
            'natureFindingName',
            'auditTypeName',
            'mainCategoryName',
            'secondCategoryName',
            'thirdCategoryName',
            'viqRefNo',
            'departmentName',
          ]);
          if (item.id === itemBody.id && updateItemId.includes(item.id)) {
            const valueChanges = commonCheckValueChange(item, newItemBody);

            if (valueChanges) {
              for (let i = 0; i < valueChanges.length; i++) {
                preparedValueChanges.push({
                  module: ModuleEnum.FINDING_ITEM,
                  action: ActionValueChangeEnum.UPDATE,
                  createdUser: userHistory,
                  companyId: user.companyId,
                  recordId: item.id,
                  key: valueChanges[i].key,
                  fieldLabel: valueChanges[i].fieldLabel,
                  oldValue: valueChanges[i].oldValue,
                  newValue: valueChanges[i].newValue,
                });
              }
            }
          }
        }
      }

      await this.manager.save(ValueChangeHistory, preparedValueChanges);

      // delete car without items (handle both regular and SA finding items)
      if (currReportFindingForm.planningRequest.isSA) {
        // For self-assessment, check SAFindingItems
        const cars = await managerTrans
          .getCustomRepository(CARRepository)
          .createQueryBuilder('car')
          .leftJoin('car.saFindingItems', 'saFindingItems', 'saFindingItems.deleted = false')
          .select(['car', 'saFindingItems.id', 'saFindingItems.findingAttachments'])
          .where('car.planningRequestId = :planningRequestId', {
            planningRequestId: currReportFindingForm.planningRequest.id,
          })
          .getMany();

        const noItemCarIds = cars
          .filter((car) => car.saFindingItems.length == 0)
          .map((item) => item.id);

        if (noItemCarIds.length != 0) {
          await managerTrans.delete(CorrectiveActionRequest, { id: In(noItemCarIds) });
        }
      } else {
        // For regular audit, check ReportFindingItems
        const cars = await managerTrans
          .getCustomRepository(CARRepository)
          .createQueryBuilder('car')
          .leftJoin(
            'car.reportFindingItems',
            'reportFindingItems',
            'reportFindingItems.deleted = false',
          )
          .select(['car', 'reportFindingItems.id', 'reportFindingItems.findingAttachments'])
          .where('car.planningRequestId = :planningRequestId', {
            planningRequestId: currReportFindingForm.planningRequest.id,
          })
          .getMany();

        const noItemCarIds = cars
          .filter((car) => car.reportFindingItems.length == 0)
          .map((item) => item.id);

        if (noItemCarIds.length != 0) {
          await managerTrans.delete(CorrectiveActionRequest, { id: In(noItemCarIds) });
        }
      }
      // trigger update attachment from rof to car
      // const listCarForUpdateAttachment = [];
      // for (const car of cars) {
      //   if (noItemCarIds.indexOf(car.id) === -1) {
      //     if (car.status === CarStatusEnum.OPEN) {
      //       const listAttachmentReportFinding = [];
      //       for (const item of car.reportFindingItems) {
      //         if (item.findingAttachments.length > 0) {
      //           for (const itemAttachment of item.findingAttachments) {
      //             if (listAttachmentReportFinding.indexOf(itemAttachment) === -1) {
      //               listAttachmentReportFinding.push(decryptImage(itemAttachment));
      //             }
      //           }
      //         }
      //       }
      //       const carCurrentAttachments = differenceWith(
      //         car.attachments ? car.attachments : [],
      //         preparedDeleteFindingItemAttachmentIds,
      //       );
      //       const attachmentDifference = differenceWith(
      //         listAttachmentReportFinding,
      //         carCurrentAttachments,
      //       );
      //       // console.log('carCurrentAttachments', carCurrentAttachments);
      //       // console.log('listAttachmentReportFinding', listAttachmentReportFinding);
      //       // console.log('car.attachments', car.attachments);
      //       // console.log('attachmentDifference', attachmentDifference);
      //       let attachmentForUpdate = differenceWith(
      //         car.attachments ? car.attachments : [],
      //         preparedDeleteFindingItemAttachmentIds,
      //       );
      //       attachmentForUpdate = decryptAttachmentValues(attachmentForUpdate);

      //       if (
      //         attachmentDifference.length > 0 ||
      //         preparedDeleteFindingItemAttachmentIds.length > 0
      //       ) {
      //         for (const item of attachmentDifference) {
      //           attachmentForUpdate.push(item);
      //         }
      //         listCarForUpdateAttachment.push({
      //           id: car.id,
      //           attachments: attachmentForUpdate,
      //         });
      //       }
      //     }
      //   }
      // }
      // if (listCarForUpdateAttachment.length > 0) {
      //   await managerTrans.save(CorrectiveActionRequest, listCarForUpdateAttachment);
      // }
      // update previous NC finding items
      await managerTrans.save(ReportFindingItem, body.previousNCFindings);

      // insert into report finding history table if change status
      if (preparedFindingHistory.status !== currReportFindingForm.status) {
        await managerTrans.insert(ReportFindingHistory, preparedFindingHistory);
      }

      // insert into report finding comments if has data
      if (Array.isArray(ROFOfficeCommentCreate) && ROFOfficeCommentCreate.length > 0) {
        await managerTrans.save(ROFOfficeComment, ROFOfficeCommentCreate);
      }
      if (Array.isArray(PROfficeCommentUpdate) && PROfficeCommentUpdate.length > 0) {
        await managerTrans.save(ROFOfficeComment, PROfficeCommentUpdate);
      }
      if (Array.isArray(listOfficeCommentDeleteIds) && listOfficeCommentDeleteIds.length > 0) {
        await managerTrans.delete(ROFOfficeComment, { id: In(listOfficeCommentDeleteIds) });
      }

      // Handle add Audit log
      await managerTrans.getCustomRepository(AuditLogRepository).createAuditLog(
        {
          module: AuditModuleEnum.REPORT_OF_FINDING,
          planningId: currReportFindingForm.planningRequest.id,
        },
        null,
        [
          // preparedFindingForm.status == ReportFindingFormStatus.SUBMITTED
          //   ? AuditActivityEnum.SUBMITTED
          //   : AuditActivityEnum.UPDATED_INFO,
          AuditActivityEnum.UPDATED_INFO,
        ],
        createdUser,
      );

      //duplicate CAR only once
      // if (!currReportFindingForm.isDuplicatedCAR) {
      //   const dupCAR = await this._triggerCreateCar(managerTrans, user, reportFindingFormId);
      //   if (dupCAR.length > 0) {
      //     await managerTrans.update(
      //       ReportFindingForm,
      //       { id: reportFindingFormId },
      //       { isDuplicatedCAR: true },
      //     );
      //   }
      // }

      if (preparedFindingForm.status == ReportFindingFormStatus.SUBMITTED) {
        // Update global status
        // await managerTrans.update(
        //   PlanningRequest,
        //   {
        //     id: currReportFindingForm.planningRequest.id,
        //   },
        //   {
        //     globalStatus: GlobalStatusEnum.SENT_CAR_UNDER_CAP_PREPARATION,
        //   },
        // );

        //  reassigned ROF means existed UserAssignment ==> update
        const existUserAssignment = currReportFindingForm.reportFindingHistories.some(
          (historyROF) => historyROF.status == ReportFindingFormStatus.REASSIGNED,
        );

        if (body.isSubmit && !existUserAssignment) {
          await managerTrans
            .getCustomRepository(UserAssignmentRepository)
            .createUserAssignment({ ...userAssignmentParams, reportFindingFormId });
        }

        if (body.isSubmit && existUserAssignment) {
          await managerTrans
            .getCustomRepository(UserAssignmentRepository)
            .updateUserAssignment(
              managerTrans,
              ModuleType.REPORT_FINDING,
              reportFindingFormId,
              userAssignmentParams.usersPermissions,
            );
        }
        if (body.isSubmit) {
          //#region Handle push noti to Creator, Approver, Acceptor, Owner/Manager
          if (
            currReportFindingForm.status === ReportFindingFormStatus.DRAFT ||
            currReportFindingForm.status === ReportFindingFormStatus.REASSIGNED
          ) {
            // list User Assignment
            const listUserAssignment = await managerTrans
              .getCustomRepository(UserAssignmentRepository)
              .listByModule(ModuleType.REPORT_FINDING, reportFindingFormId);

            let listReceiverNoti = [];
            listReceiverNoti = listReceiverNoti.concat(
              listUserAssignment[WorkflowPermission.REVIEWER],
            );
            listReceiverNoti = listReceiverNoti.concat(
              listUserAssignment[WorkflowPermission.REVIEWER1],
            );
            const performer = await managerTrans
              .getCustomRepository(UserRepository)
              .getUserDetailAndSelect(user.id, ['id', 'username', 'jobTitle']);
            dataNoti.push({
              receivers: listReceiverNoti as IUser[],
              module: ModuleType.REPORT_FINDING,
              recordId: reportFindingFormId,
              recordRef: refNoObj.refNo ? refNoObj.refNo : currReportFindingForm.refNo,
              type: PushTypeEnum.CHANGE_RECORD_STATUS,
              currentStatus: ReportFindingFormStatus.SUBMITTED,
              previousStatus: currReportFindingForm.status,
              performer: performer,
              executedAt: new Date(),
            });

            for (const receiver of listReceiverNoti) {
              dataSendMail.push({
                receiver: receiver as IUserEmail,
                type: EmailTypeEnum.CHANGE_RECORD_STATUS,
                templateKey: MailTemplate.CHANGE_RECORD_STATUS,
                subject: '[Notification] Change status in a record',
                data: {
                  username: receiver.username,
                  baseURL: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.ADMIN_PAGE,
                  recordRef: refNoObj.refNo ? refNoObj.refNo : currReportFindingForm.refNo,
                  recordId: reportFindingFormId,
                  path: ModulePathEnum.REPORT_OF_FINDING,
                  currentStatus: ReportFindingFormStatus.SUBMITTED,
                  previousStatus: currReportFindingForm.status,
                  performer: performer,
                  executedAt: new Date(),
                },
              });
            }
          }

          //#endregion Push noti
        }
      }
      return { dataNoti, dataSendMail, allIds };
    });
  }

  async submitReportFindingForm(
    reportFindingFormId: string,
    timezone: string,
    user: TokenPayloadModel,
    userAssignmentParams: CreateUserAssignmentDTO,
  ) {
    const currReportFindingForm = await this._checkCanSubmitAndGet(reportFindingFormId, user);
    const currYear = momentTZ.tz(timezone).year();

    // Get user info
    const createdUser = await this.manager
      .getCustomRepository(UserRepository)
      ._getUserInfoForHistory(user.id);

    const preparedFindingHistory = {
      reportFindingFormId,
      status: ReportFindingFormStatus.SUBMITTED,
      createdUser,
    };
    const dataNoti: INotificationEventModel[] = [];
    const dataSendMail: IEmailEventModel[] = [];
    return await this.connection.transaction(async (managerTrans) => {
      // General refNo when submit
      let refNo: string;
      if (!currReportFindingForm.refNo) {
        const counter = await this.manager
          .getCustomRepository(CompanyFeatureVersionRepository)
          .getNextVersion({
            manager: managerTrans,
            companyId: user.companyId,
            feature: FeatureVersionConfig.REPORT_FINDING_FORM_REFNO,
            year: Number(currYear),
          });
        refNo = await this._genReportFindingFormRefNo(
          currReportFindingForm.companyId,
          counter,
          Number(currYear),
        );
      }
      // insert into report finding form table
      await managerTrans.update(
        ReportFindingForm,
        { id: reportFindingFormId },
        { refNo, status: ReportFindingFormStatus.SUBMITTED, updatedUserId: user.id },
      );

      // insert into report finding history table
      await managerTrans.insert(ReportFindingHistory, preparedFindingHistory);

      // Update global status
      // await managerTrans.update(
      //   PlanningRequest,
      //   {
      //     id: currReportFindingForm.planningRequest.id,
      //   },
      //   {
      //     globalStatus: GlobalStatusEnum.SENT_CAR_UNDER_CAP_PREPARATION,
      //   },
      // );

      // Handle add Audit log
      await managerTrans.getCustomRepository(AuditLogRepository).createAuditLog(
        {
          module: AuditModuleEnum.REPORT_OF_FINDING,
          planningId: currReportFindingForm.planningRequest.id,
        },
        null,
        [AuditActivityEnum.SUBMITTED],
        createdUser,
      );

      await this._triggerCreateCar(managerTrans, user, currReportFindingForm.id);
      await this._triggerCreateFollowUp(
        managerTrans,
        user,
        currReportFindingForm.id,
        timezone,
        createdUser,
      );

      await managerTrans
        .getCustomRepository(UserAssignmentRepository)
        .createUserAssignment(userAssignmentParams);

      //#region Handle push noti to Creator, Approver, Acceptor, Owner/Manager
      if (currReportFindingForm.status === ReportFindingFormStatus.DRAFT) {
        // list User Assignment
        const listUserAssignment = await managerTrans
          .getCustomRepository(UserAssignmentRepository)
          .listByModule(ModuleType.REPORT_FINDING, currReportFindingForm.id);

        let listReceiverNoti = [];
        listReceiverNoti = listReceiverNoti.concat(listUserAssignment[WorkflowPermission.REVIEWER]);
        const performer = await managerTrans
          .getCustomRepository(UserRepository)
          .getUserDetailAndSelect(user.id, ['id', 'username', 'jobTitle']);
        dataNoti.push({
          receivers: listReceiverNoti,
          module: ModuleType.REPORT_FINDING,
          recordId: currReportFindingForm.id,
          recordRef: refNo ? refNo : currReportFindingForm.refNo,
          type: PushTypeEnum.CHANGE_RECORD_STATUS,
          currentStatus: ReportFindingFormStatus.SUBMITTED,
          previousStatus: ReportFindingFormStatus.DRAFT,
          performer: performer,
          executedAt: new Date(),
        });

        for (const receiver of listReceiverNoti) {
          dataSendMail.push({
            receiver: receiver as IUserEmail,
            type: EmailTypeEnum.CHANGE_RECORD_STATUS,
            templateKey: MailTemplate.CHANGE_RECORD_STATUS,
            subject: '[Notification] Change status in a record',
            data: {
              username: receiver.username,
              baseURL: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.ADMIN_PAGE,
              recordRef: refNo ? refNo : currReportFindingForm.refNo,
              recordId: currReportFindingForm.id,
              path: ModulePathEnum.REPORT_OF_FINDING,
              currentStatus: ReportFindingFormStatus.SUBMITTED,
              previousStatus: ReportFindingFormStatus.DRAFT,
              performer: performer,
              executedAt: new Date(),
            },
          });
        }
      }

      //#endregion Push noti
      return { dataNoti, dataSendMail };
    });
  }

  async undoSubmitReportFindingForm(reportFindingFormId: string, user: TokenPayloadModel) {
    const recordFound = await this._checkCanUndoSubmitAndGet(reportFindingFormId, user);

    // Get user info
    const createdUser = await this.manager
      .getCustomRepository(UserRepository)
      ._getUserInfoForHistory(user.id);

    const preparedFindingHistory = {
      reportFindingFormId,
      status: ReportFindingFormStatus.DRAFT,
      createdUser,
    };
    await this.connection.transaction(async (managerTrans) => {
      // insert into report finding form table
      await managerTrans.update(
        ReportFindingForm,
        { id: reportFindingFormId },
        { status: ReportFindingFormStatus.DRAFT, updatedUserId: user.id, reviewInProgress: null },
      );
      // insert into report finding history table
      await managerTrans.insert(ReportFindingHistory, preparedFindingHistory);
    });
    return recordFound;
  }

  async reviewFiveStepReportFindingForm(
    reportFindingFormId: string,
    body: ReviewFiveStepReportFindingFormDTO,
    user: TokenPayloadModel,
    workflowPermissions: string[],
  ) {
    const dataNoti: INotificationEventModel[] = [];
    const dataSendMail: IEmailEventModel[] = [];
    let status: string, previousStatus: string;
    let qb;
    if (body.isReassigned) {
      qb = this.createQueryBuilder('reportFindingForm')
        .leftJoin('reportFindingForm.planningRequest', 'planningRequest')
        .leftJoin('planningRequest.auditors', 'auditors')
        .where(
          'reportFindingForm.id = :reportFindingFormId AND reportFindingForm.status IN (:...status) AND reportFindingForm.companyId = :companyId',
          {
            reportFindingFormId,
            status: [
              ReportFindingFormStatus.SUBMITTED,
              ReportFindingFormStatus.REVIEWED_1,
              ReportFindingFormStatus.REVIEWED_2,
              ReportFindingFormStatus.REVIEWED_3,
              ReportFindingFormStatus.REVIEWED_4,
              ReportFindingFormStatus.REVIEWED_5,
            ],
            companyId: user.companyId,
          },
        )
        .select([
          'reportFindingForm.id',
          'reportFindingForm.planningRequestId',
          'reportFindingForm.status',
          'reportFindingForm.refNo',
          'planningRequest.id',
          'auditors.id',
        ]);
    } else {
      qb = this.createQueryBuilder('reportFindingForm')
        .leftJoin('reportFindingForm.planningRequest', 'planningRequest')
        .leftJoin('planningRequest.auditors', 'auditors')
        .where(
          'reportFindingForm.id = :reportFindingFormId AND reportFindingForm.status IN (:...status) AND reportFindingForm.companyId = :companyId',
          {
            reportFindingFormId,
            status: [
              ReportFindingFormStatus.SUBMITTED,
              ReportFindingFormStatus.REASSIGNED,
              ReportFindingFormStatus.REVIEWED_1,
              ReportFindingFormStatus.REVIEWED_2,
              ReportFindingFormStatus.REVIEWED_3,
              ReportFindingFormStatus.REVIEWED_4,
            ],
            companyId: user.companyId,
          },
        )
        .select([
          'reportFindingForm.id',
          'reportFindingForm.planningRequestId',
          'reportFindingForm.status',
          'reportFindingForm.refNo',
          'planningRequest.id',
          'auditors.id',
        ]);
    }

    const RFFFound = await this.getOneQB(qb);
    if (!RFFFound) {
      if (body.isReassigned) {
        throw new BaseError({ status: 400, message: 'reportFindingForm.CANNOT_REASSIGN' });
      } else {
        throw new BaseError({ status: 400, message: 'reportFindingForm.CANNOT_REVIEW' });
      }
    }
    if (RFFFound.status === ReportFindingFormStatus.SUBMITTED) {
      if (!workflowPermissions.includes(WorkflowPermission.REVIEWER1)) {
        throw new ForbiddenError({});
      }
      status = ReportFindingFormStatus.REVIEWED_1;
      previousStatus = ReportFindingFormStatus.SUBMITTED;
    } else if (RFFFound.status.startsWith('reviewed_')) {
      const lastReviewedStep = Number(RFFFound.status.substring(RFFFound.status.length - 1));
      const currentReviewedStep = lastReviewedStep + 1;
      if (currentReviewedStep > 5) {
        throw new BaseError({ message: 'reportFindingForm.CANNOT_REVIEWED_5' });
      }
      // console.log('lastReviewedStep', lastReviewedStep);
      // console.log('currentReviewedStep', currentReviewedStep);

      // get max workflow role of user exam: review_5
      const maxRoleOfCurrentUser = workflowPermissions.reduce((currentMaxRole, role) => {
        if (role.startsWith('reviewer') && role > currentMaxRole) {
          return role;
        } else {
          return currentMaxRole;
        }
      }, WorkflowPermission.REVIEWER1);

      // get last char Number to compared 2 role, if less than current PR role then throw err
      const lastChar = maxRoleOfCurrentUser.slice(-1);
      // console.log('lastChar', lastChar);
      if (lastChar <= RFFFound.status.slice(-1)) {
        throw new BaseError({
          status: 404,
          message: 'reportFindingForm.PERMISSION_REQUIRED',
        });
      }
      // set status for PR and format type of enum string. exp: reviewer1 -> review_1
      let selectedStep: number;
      for (let i = currentReviewedStep; i <= Number(lastChar); i++) {
        if (workflowPermissions.includes(`reviewer${i}`)) {
          selectedStep = i;
          break;
        }
      }

      previousStatus = RFFFound.status;
      // console.log('workflowPermissions', workflowPermissions);
      // console.log('selectedStep', selectedStep);

      status = [
        ReportFindingFormStatus.REVIEWED_2,
        ReportFindingFormStatus.REVIEWED_3,
        ReportFindingFormStatus.REVIEWED_4,
        ReportFindingFormStatus.REVIEWED_5,
      ][selectedStep - 2];
      // console.log('status', status);
    }
    // Get user info
    const createdUser = await this.manager
      .getCustomRepository(UserRepository)
      ._getUserInfoForHistory(user.id);
    const preparedFindingHistory = {
      reportFindingFormId,
      status: body.isReassigned ? ReportFindingFormStatus.REASSIGNED : status,
      workflowRemark: body.workflowRemark,
      createdUser,
    };
    return await this.connection.transaction(async (managerTrans) => {
      await managerTrans.update(
        ReportFindingForm,
        { id: reportFindingFormId },
        {
          status: body.isReassigned ? ReportFindingFormStatus.REASSIGNED : status,
          updatedUserId: user.id,
        },
      );
      // insert into report finding history table
      await managerTrans.insert(ReportFindingHistory, preparedFindingHistory);

      // Handle add Audit log
      await managerTrans.getCustomRepository(AuditLogRepository).createAuditLog(
        {
          module: AuditModuleEnum.REPORT_OF_FINDING,
          planningId: RFFFound.planningRequestId,
        },
        user,
        [(body.isReassigned ? AuditActivityEnum.REASSIGNED : status) as AuditActivityEnum],
        createdUser,
      );
      if (body.userAssignment) {
        await managerTrans
          .getCustomRepository(UserAssignmentRepository)
          .updateUserAssignment(
            managerTrans,
            ModuleType.REPORT_FINDING,
            reportFindingFormId,
            body.userAssignment.usersPermissions,
          );
      }

      if (body.isReassigned === true) {
        if (RFFFound.planningRequest.auditors.length > 0) {
          const auditors = RFFFound.planningRequest.auditors.map((user) => user.id);
          const listReceiverNoti = await managerTrans
            .getCustomRepository(UserRepository)
            .listByIds(auditors);

          // list User Assignment

          const performer = await managerTrans
            .getCustomRepository(UserRepository)
            .getUserDetailAndSelect(user.id, ['id', 'username', 'jobTitle']);
          dataNoti.push({
            receivers: listReceiverNoti as IUser[],
            module: ModuleType.REPORT_FINDING,
            recordId: RFFFound.id,
            recordRef: RFFFound.refNo,
            type: PushTypeEnum.CHANGE_RECORD_STATUS,
            currentStatus: ReportFindingFormStatus.REASSIGNED,
            previousStatus: RFFFound.status,
            performer: performer,
            executedAt: new Date(),
          });
          for (const receiver of listReceiverNoti) {
            dataSendMail.push({
              receiver: receiver as IUserEmail,
              type: EmailTypeEnum.CHANGE_RECORD_STATUS,
              templateKey: MailTemplate.CHANGE_RECORD_STATUS,
              subject: '[Notification] Change status in a record',
              data: {
                username: receiver.username,
                baseURL: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.ADMIN_PAGE,
                recordRef: RFFFound.refNo,
                recordId: RFFFound.id,
                path: ModulePathEnum.REPORT_OF_FINDING,
                currentStatus: ReportFindingFormStatus.REASSIGNED,
                previousStatus: RFFFound.status,
                performer: performer,
                executedAt: new Date(),
              },
            });
          }
        }
      } else {
        // list User Assignment
        const listUserAssignment = await managerTrans
          .getCustomRepository(UserAssignmentRepository)
          .listByModule(ModuleType.REPORT_FINDING, reportFindingFormId);
        // console.log('listUserAssignment', listUserAssignment);
        const {
          step1,
          step2,
          step3,
          step4,
          step5,
          stepApprove,
        } = this.planningRepository.getStepsForAssigments(listUserAssignment);
        let listReceiverNoti = [];
        let nextStep;
        // console.log('step2', step2);
        // console.log('step3', step3);
        // console.log('step4', step4);
        // console.log('step5', step5);
        // console.log('stepApprove', stepCloseOut);
        // console.log('PRFound.status', RFFFound.status);
        switch (RFFFound.status) {
          case ReportFindingFormStatus.SUBMITTED:
            nextStep = step2 || step3 || step4 || step5;

            if (nextStep) {
              listReceiverNoti = listReceiverNoti.concat(listUserAssignment[nextStep]);
            }
            break;
          case ReportFindingFormStatus.REVIEWED_1:
            if (status === ReportFindingFormStatus.REVIEWED_2) {
              nextStep = step3 || step4 || step5 || stepApprove;
            }
            if (status === ReportFindingFormStatus.REVIEWED_3) {
              nextStep = step4 || step5 || stepApprove;
            }

            if (status === ReportFindingFormStatus.REVIEWED_4) {
              nextStep = step5 || stepApprove;
            }

            if (status === ReportFindingFormStatus.REVIEWED_5) {
              nextStep = stepApprove;
            }

            if (nextStep) {
              listReceiverNoti = listReceiverNoti.concat(listUserAssignment[nextStep]);
            }
            break;
          case ReportFindingFormStatus.REVIEWED_2:
            if (status === ReportFindingFormStatus.REVIEWED_3) {
              nextStep = step4 || step5 || stepApprove;
            }

            if (status === ReportFindingFormStatus.REVIEWED_4) {
              nextStep = step5 || stepApprove;
            }

            if (status === ReportFindingFormStatus.REVIEWED_5) {
              nextStep = stepApprove;
            }
            if (nextStep) {
              listReceiverNoti = listReceiverNoti.concat(listUserAssignment[nextStep]);
            }
            break;
          case ReportFindingFormStatus.REVIEWED_3:
            if (status === ReportFindingFormStatus.REVIEWED_4) {
              nextStep = step5 || stepApprove;
            }

            if (status === ReportFindingFormStatus.REVIEWED_5) {
              nextStep = stepApprove;
            }
            if (nextStep) {
              listReceiverNoti = listReceiverNoti.concat(listUserAssignment[nextStep]);
            }
            break;
          case ReportFindingFormStatus.REVIEWED_4:
            nextStep = stepApprove;
            if (nextStep) {
              listReceiverNoti = listReceiverNoti.concat(listUserAssignment[nextStep]);
            }
            break;
          case ReportFindingFormStatus.REVIEWED_5:
            listReceiverNoti = listReceiverNoti.concat(
              listUserAssignment[WorkflowPermission.APPROVER],
            );
            break;
        }
        // console.log('listReceiverNoti', listReceiverNoti);
        const performer = await managerTrans
          .getCustomRepository(UserRepository)
          .getUserDetailAndSelect(user.id, ['id', 'username', 'jobTitle', 'email']);
        dataNoti.push({
          receivers: listReceiverNoti,
          module: ModuleType.REPORT_FINDING,
          recordId: RFFFound.id,
          recordRef: RFFFound.refNo,
          type: PushTypeEnum.CHANGE_RECORD_STATUS,
          currentStatus: status,
          previousStatus,
          performer: performer,
          executedAt: new Date(),
        });

        for (const receiver of listReceiverNoti) {
          dataSendMail.push({
            receiver: receiver as IUserEmail,
            type: EmailTypeEnum.CHANGE_RECORD_STATUS,
            templateKey: MailTemplate.CHANGE_RECORD_STATUS,
            subject: '[Notification] Change status in a record',
            data: {
              username: receiver.username,
              baseURL: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.ADMIN_PAGE,
              recordRef: RFFFound.refNo,
              recordId: RFFFound.id,
              path: ModulePathEnum.REPORT_OF_FINDING,
              currentStatus: status,
              previousStatus,
              performer: performer,
              executedAt: new Date(),
            },
          });
        }
      }
      return { dataNoti, dataSendMail };
    });
  }

  async reviewReportFindingForm(
    reportFindingFormId: string,
    body: ReviewReportFindingFormDto,
    user: TokenPayloadModel,
  ) {
    const rof = await this._checkCanReviewAndGet(reportFindingFormId, user);

    // Get user info
    const createdUser = await this.manager
      .getCustomRepository(UserRepository)
      ._getUserInfoForHistory(user.id);

    const preparedFindingHistory = {
      reportFindingFormId,
      status: body.status,
      workflowRemark: body.workflowRemark,
      createdUser,
    };
    const dataSendMail: IEmailEventModel[] = [];
    return await this.connection.transaction(async (managerTrans) => {
      const dataNoti: INotificationEventModel[] = [];
      // insert into report finding form table
      await managerTrans.update(
        ReportFindingForm,
        { id: reportFindingFormId },
        { status: body.status, updatedUserId: user.id },
      );
      // insert into report finding history table
      await managerTrans.insert(ReportFindingHistory, preparedFindingHistory);

      // Handle add Audit log
      await managerTrans.getCustomRepository(AuditLogRepository).createAuditLog(
        {
          module: AuditModuleEnum.REPORT_OF_FINDING,
          planningId: rof.planningRequestId,
        },
        user,
        [
          body.status === ReportFindingFormStatus.REVIEWED
            ? AuditActivityEnum.REVIEWED
            : AuditActivityEnum.REASSIGNED,
        ],
        createdUser,
      );
      if (body.userAssignment) {
        await managerTrans
          .getCustomRepository(UserAssignmentRepository)
          .updateUserAssignment(
            managerTrans,
            ModuleType.REPORT_FINDING,
            reportFindingFormId,
            body.userAssignment.usersPermissions,
          );
      }

      //#region Handle push noti to Creator, Approver, Acceptor, Owner/Manager
      if (
        rof.status === ReportFindingFormStatus.SUBMITTED &&
        body.status === ReportFindingFormStatus.REVIEWED
      ) {
        // list User Assignment
        const listUserAssignment = await managerTrans
          .getCustomRepository(UserAssignmentRepository)
          .listByModule(ModuleType.REPORT_FINDING, rof.id);

        let listReceiverNoti = [];
        listReceiverNoti = listReceiverNoti.concat(
          listUserAssignment[WorkflowPermission.CLOSE_OUT],
        );
        const performer = await managerTrans
          .getCustomRepository(UserRepository)
          .getUserDetailAndSelect(user.id, ['id', 'username', 'jobTitle']);
        dataNoti.push({
          receivers: listReceiverNoti as IUser[],
          module: ModuleType.REPORT_FINDING,
          recordId: rof.id,
          recordRef: rof.refNo,
          type: PushTypeEnum.CHANGE_RECORD_STATUS,
          currentStatus: ReportFindingFormStatus.REVIEWED,
          previousStatus: ReportFindingFormStatus.SUBMITTED,
          performer: performer,
          executedAt: new Date(),
        });
        for (const receiver of listReceiverNoti) {
          dataSendMail.push({
            receiver: receiver as IUserEmail,
            type: EmailTypeEnum.CHANGE_RECORD_STATUS,
            templateKey: MailTemplate.CHANGE_RECORD_STATUS,
            subject: '[Notification] Change status in a record',
            data: {
              username: receiver.username,
              baseURL: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.ADMIN_PAGE,
              recordRef: rof.refNo,
              recordId: rof.id,
              path: ModulePathEnum.REPORT_OF_FINDING,
              previousStatus: ReportFindingFormStatus.SUBMITTED,
              currentStatus: ReportFindingFormStatus.REVIEWED,
              performer: performer,
              executedAt: new Date(),
            },
          });
        }
      }

      //#endregion Push noti

      //#region Handle push noti to Creator, Approver, Acceptor, Owner/Manager, inspector
      if (body.status === ReportFindingFormStatus.REASSIGNED) {
        if (rof.planningRequest.auditors.length > 0) {
          const auditors = rof.planningRequest.auditors.map((user) => user.id);
          const listReceiverNoti = await managerTrans
            .getCustomRepository(UserRepository)
            .listByIds(auditors);

          // list User Assignment

          const performer = await managerTrans
            .getCustomRepository(UserRepository)
            .getUserDetailAndSelect(user.id, ['id', 'username', 'jobTitle']);
          if (rof.status === ReportFindingFormStatus.SUBMITTED) {
            dataNoti.push({
              receivers: listReceiverNoti as IUser[],
              module: ModuleType.REPORT_FINDING,
              recordId: rof.id,
              recordRef: rof.refNo,
              type: PushTypeEnum.CHANGE_RECORD_STATUS,
              currentStatus: ReportFindingFormStatus.REASSIGNED,
              previousStatus: ReportFindingFormStatus.SUBMITTED,
              performer: performer,
              executedAt: new Date(),
            });
            for (const receiver of listReceiverNoti) {
              dataSendMail.push({
                receiver: receiver as IUserEmail,
                type: EmailTypeEnum.CHANGE_RECORD_STATUS,
                templateKey: MailTemplate.CHANGE_RECORD_STATUS,
                subject: '[Notification] Change status in a record',
                data: {
                  username: receiver.username,
                  baseURL: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.ADMIN_PAGE,
                  recordRef: rof.refNo,
                  recordId: rof.id,
                  path: ModulePathEnum.REPORT_OF_FINDING,
                  currentStatus: ReportFindingFormStatus.REASSIGNED,
                  previousStatus: ReportFindingFormStatus.SUBMITTED,
                  performer: performer,
                  executedAt: new Date(),
                },
              });
            }
          }
          if (rof.status === ReportFindingFormStatus.REVIEWED) {
            dataNoti.push({
              receivers: listReceiverNoti as IUser[],
              module: ModuleType.REPORT_FINDING,
              recordId: rof.id,
              recordRef: rof.refNo,
              type: PushTypeEnum.CHANGE_RECORD_STATUS,
              currentStatus: ReportFindingFormStatus.REASSIGNED,
              previousStatus: ReportFindingFormStatus.REVIEWED,
              performer: performer,
              executedAt: new Date(),
            });
            for (const receiver of listReceiverNoti) {
              dataSendMail.push({
                receiver: receiver as IUserEmail,
                type: EmailTypeEnum.CHANGE_RECORD_STATUS,
                templateKey: MailTemplate.CHANGE_RECORD_STATUS,
                subject: '[Notification] Change status in a record',
                data: {
                  username: receiver.username,
                  baseURL: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.ADMIN_PAGE,
                  recordRef: rof.refNo,
                  recordId: rof.id,
                  path: ModulePathEnum.REPORT_OF_FINDING,
                  currentStatus: ReportFindingFormStatus.REASSIGNED,
                  previousStatus: ReportFindingFormStatus.REVIEWED,
                  performer: performer,
                  executedAt: new Date(),
                },
              });
            }
          }
        }
      }
      //#endregion Push noti
      return { dataNoti, dataSendMail };
    });
  }

  async closeOutReportFindingForm(
    reportFindingFormId: string,
    body: CloseOutReportFindingFormDto,
    user: TokenPayloadModel,
  ) {
    const currentROF = await this._checkCanCloseOutAndGet(reportFindingFormId, user);

    // Get user info
    const createdUser = await this.manager
      .getCustomRepository(UserRepository)
      ._getUserInfoForHistory(user.id);

    const preparedFindingHistory = {
      reportFindingFormId,
      status: body.status,
      workflowRemark: body.workflowRemark,
      createdUser,
    };
    const dataNoti: INotificationEventModel[] = [];
    const dataSendMail: IEmailEventModel[] = [];
    return await this.connection.transaction(async (managerTrans) => {
      // insert into report finding form table
      await managerTrans.update(
        ReportFindingForm,
        { id: reportFindingFormId },
        { status: body.status, updatedUserId: user.id },
      );
      // insert into report finding history table
      await managerTrans.insert(ReportFindingHistory, preparedFindingHistory);

      /** Close out Audit time table */
      // Get audit time table by planning request id
      const auditTimeTable = await managerTrans.findOne(AuditTimeTable, {
        where: { planningRequestId: currentROF.planningRequestId },
        select: ['id'],
      });
      if (auditTimeTable) {
        // update status to Submitted
        const updatedATTResult = await managerTrans.update(
          AuditTimeTable,
          { id: auditTimeTable.id, status: Not(AuditTimeTableStatus.CLOSE_OUT) },
          { status: AuditTimeTableStatus.CLOSE_OUT },
        );
        // insert to  audit time table history table only if ATT has not close out
        if (updatedATTResult.affected === 1) {
          await managerTrans.insert(AuditTimeTableHistory, {
            auditTimeTableId: auditTimeTable.id,
            status: AuditTimeTableStatus.CLOSE_OUT,
            createdUser,
          });
        }
      }

      /** End close out Audit time table */

      // Handle add Audit log
      await managerTrans.getCustomRepository(AuditLogRepository).createAuditLog(
        {
          module: AuditModuleEnum.REPORT_OF_FINDING,
          planningId: currentROF.planningRequestId,
        },
        user,
        [
          body.status === ReportFindingFormStatus.APPROVED
            ? AuditActivityEnum.APPROVED
            : AuditActivityEnum.REASSIGNED,
        ],
        createdUser,
      );

      /* Trigger create NC CAR   */
      await this._triggerCreateCar(managerTrans, user, reportFindingFormId);
      await this._triggerCreateFollowUp(
        managerTrans,
        user,
        reportFindingFormId,
        body.timezone,
        createdUser,
      );

      // Check if IAR is created before
      // const iar = await managerTrans.findOne(InternalAuditReport, {
      //   where: { reportFindingFormId },
      //   select: ['id', 'status'],
      // });
      // if (iar) {
      //   // Update IAR
      //   await managerTrans
      //     .getCustomRepository(InternalAuditReportRepository)
      //     ._triggerUpdateInternalAuditReport(
      //       managerTrans,
      //       iar,
      //       reportFindingFormId,
      //       currentROF.planningRequestId,
      //       user,
      //       createdUser,
      //     );
      // } else {
      //   // Create new IAR
      //   await managerTrans
      //     .getCustomRepository(InternalAuditReportRepository)
      //     ._triggerCreateInternalAuditReport(
      //       managerTrans,
      //       {
      //         reportFindingFormId,
      //         planningRequestId: currentROF.planningRequestId,
      //         vesselId: currentROF.vesselId,
      //         departmentId: currentROF.departmentId,
      //         timezone: body.timezone,
      //       },
      //       user,
      //       createdUser,
      //     );
      // }
      //#region Handle push noti to Creator, Approver, Acceptor, Owner/Manager
      if (
        currentROF.status !== ReportFindingFormStatus.APPROVED &&
        body.status === ReportFindingFormStatus.APPROVED
      ) {
        // list User Assignment
        const listUserAssignment = await managerTrans
          .getCustomRepository(UserAssignmentRepository)
          .listByModule(ModuleType.REPORT_FINDING, currentROF.id);

        let listReceiverNoti = [];
        listReceiverNoti = listReceiverNoti.concat(listUserAssignment[WorkflowPermission.APPROVER]);
        switch (currentROF.status) {
          case ReportFindingFormStatus.REVIEWED_1:
            listReceiverNoti = listReceiverNoti.concat(
              listUserAssignment[WorkflowPermission.REVIEWER1],
            );
            break;
          case ReportFindingFormStatus.REVIEWED_2:
            listReceiverNoti = listReceiverNoti.concat(
              listUserAssignment[WorkflowPermission.REVIEWER2],
            );
            break;
          case ReportFindingFormStatus.REVIEWED_3:
            listReceiverNoti = listReceiverNoti.concat(
              listUserAssignment[WorkflowPermission.REVIEWER3],
            );
            break;
          case ReportFindingFormStatus.REVIEWED_4:
            listReceiverNoti = listReceiverNoti.concat(
              listUserAssignment[WorkflowPermission.REVIEWER4],
            );
            break;
          case ReportFindingFormStatus.REVIEWED_5:
            listReceiverNoti = listReceiverNoti.concat(
              listUserAssignment[WorkflowPermission.REVIEWER5],
            );
            break;
          case ReportFindingFormStatus.REVIEWED:
            listReceiverNoti = listReceiverNoti.concat(
              listUserAssignment[WorkflowPermission.REVIEWER],
            );
            break;
        }
        // listReceiverNoti = listReceiverNoti.concat(listUserAssignment[WorkflowPermission.REVIEWER]);
        const performer = await managerTrans
          .getCustomRepository(UserRepository)
          .getUserDetailAndSelect(user.id, ['id', 'username', 'jobTitle']);
        dataNoti.push({
          receivers: listReceiverNoti as IUser[],
          module: ModuleType.REPORT_FINDING,
          recordId: currentROF.id,
          recordRef: currentROF.refNo,
          type: PushTypeEnum.CHANGE_RECORD_STATUS,
          currentStatus: ReportFindingFormStatus.APPROVED,
          previousStatus: currentROF.status,
          performer: performer,
          executedAt: new Date(),
        });
        for (const receiver of listReceiverNoti) {
          dataSendMail.push({
            receiver: receiver as IUserEmail,
            type: EmailTypeEnum.CHANGE_RECORD_STATUS,
            templateKey: MailTemplate.CHANGE_RECORD_STATUS,
            subject: '[Notification] Change status in a record',
            data: {
              username: receiver.username,
              baseURL: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.ADMIN_PAGE,
              recordRef: currentROF.refNo,
              recordId: currentROF.id,
              path: ModulePathEnum.REPORT_OF_FINDING,
              currentStatus: ReportFindingFormStatus.APPROVED,
              previousStatus: currentROF.status,
              performer: performer,
              executedAt: new Date(),
            },
          });
        }
      }
      //#region Handle push noti to Creator, Approver, Acceptor, Owner/Manager, inspector
      if (body.status === ReportFindingFormStatus.REASSIGNED) {
        if (currentROF.planningRequest.auditors.length > 0) {
          const auditors = currentROF.planningRequest.auditors.map((user) => user.id);
          const listReceiverNoti = await managerTrans
            .getCustomRepository(UserRepository)
            .listByIds(auditors);

          // list User Assignment

          const performer = await managerTrans
            .getCustomRepository(UserRepository)
            .getUserDetailAndSelect(user.id, ['id', 'username', 'jobTitle']);
          dataNoti.push({
            receivers: listReceiverNoti as IUser[],
            module: ModuleType.REPORT_FINDING,
            recordId: currentROF.id,
            recordRef: currentROF.refNo,
            type: PushTypeEnum.CHANGE_RECORD_STATUS,
            currentStatus: ReportFindingFormStatus.REASSIGNED,
            previousStatus: currentROF.status,
            performer: performer,
            executedAt: new Date(),
          });
          for (const receiver of listReceiverNoti) {
            dataSendMail.push({
              receiver: receiver as IUserEmail,
              type: EmailTypeEnum.CHANGE_RECORD_STATUS,
              templateKey: MailTemplate.CHANGE_RECORD_STATUS,
              subject: '[Notification] Change status in a record',
              data: {
                username: receiver.username,
                baseURL: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.ADMIN_PAGE,
                recordRef: currentROF.refNo,
                recordId: currentROF.id,
                path: ModulePathEnum.REPORT_OF_FINDING,
                currentStatus: ReportFindingFormStatus.REASSIGNED,
                previousStatus: currentROF.status,
                performer: performer,
                executedAt: new Date(),
              },
            });
          }
        }
      }

      //#endregion Push noti
      return { dataNoti, dataSendMail };
    });
  }

  /** Reassign report of finding form after close out. Change to Draft*/
  async reassignReportFindingForm(
    reportFindingFormId: string,
    body: ReassignReportFindingFormDto,
    user: TokenPayloadModel,
  ) {
    const currentROF = await this._checkCanReassignAndGet(reportFindingFormId, user);
    // Get user info
    const createdUser = await this.manager
      .getCustomRepository(UserRepository)
      ._getUserInfoForHistory(user.id);

    const preparedFindingHistory = {
      reportFindingFormId,
      status: body.status,
      workflowRemark: body.workflowRemark,
      createdUser,
    };
    const dataNoti: INotificationEventModel[] = [];
    const dataSendMail: IEmailEventModel[] = [];
    return await this.connection.transaction(async (managerTrans) => {
      // insert into report finding form table
      await managerTrans.update(
        ReportFindingForm,
        { id: reportFindingFormId },
        { status: body.status, updatedUserId: user.id },
      );
      // insert into report finding history table
      await managerTrans.insert(ReportFindingHistory, preparedFindingHistory);

      // Handle add Audit log
      await managerTrans.getCustomRepository(AuditLogRepository).createAuditLog(
        {
          module: AuditModuleEnum.REPORT_OF_FINDING,
          planningId: currentROF.planningRequestId,
        },
        user,
        [AuditActivityEnum.REASSIGNED],
        createdUser,
      );
      //#region Handle push noti to Creator, Approver, Acceptor, Owner/Manager, inspecteor
      if (currentROF.planningRequest.auditors.length > 0) {
        const auditors = currentROF.planningRequest.auditors.map((user) => user.id);
        const listReceiverNoti = await managerTrans
          .getCustomRepository(UserRepository)
          .listByIds(auditors);
        if (body.status === ReportFindingFormStatus.REASSIGNED) {
          // list User Assignment

          const performer = await managerTrans
            .getCustomRepository(UserRepository)
            .getUserDetailAndSelect(user.id, ['id', 'username', 'jobTitle']);
          if (currentROF.status === ReportFindingFormStatus.REVIEWED) {
            dataNoti.push({
              receivers: listReceiverNoti as IUser[],
              module: ModuleType.REPORT_FINDING,
              recordId: currentROF.id,
              recordRef: currentROF.refNo,
              type: PushTypeEnum.CHANGE_RECORD_STATUS,
              currentStatus: ReportFindingFormStatus.REASSIGNED,
              previousStatus: ReportFindingFormStatus.REVIEWED,
              performer: performer,
              executedAt: new Date(),
            });
            for (const receiver of listReceiverNoti) {
              dataSendMail.push({
                receiver: receiver as IUserEmail,
                type: EmailTypeEnum.CHANGE_RECORD_STATUS,
                templateKey: MailTemplate.CHANGE_RECORD_STATUS,
                subject: '[Notification] Change status in a record',
                data: {
                  username: receiver.username,
                  baseURL: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.ADMIN_PAGE,
                  recordRef: currentROF.refNo,
                  recordId: currentROF.id,
                  path: ModulePathEnum.REPORT_OF_FINDING,
                  currentStatus: ReportFindingFormStatus.REASSIGNED,
                  previousStatus: ReportFindingFormStatus.REVIEWED,
                  performer: performer,
                  executedAt: new Date(),
                },
              });
            }
          }
          if (currentROF.status === ReportFindingFormStatus.SUBMITTED) {
            dataNoti.push({
              receivers: listReceiverNoti as IUser[],
              module: ModuleType.REPORT_FINDING,
              recordId: currentROF.id,
              recordRef: currentROF.refNo,
              type: PushTypeEnum.CHANGE_RECORD_STATUS,
              currentStatus: ReportFindingFormStatus.REASSIGNED,
              previousStatus: ReportFindingFormStatus.SUBMITTED,
              performer: performer,
              executedAt: new Date(),
            });
            for (const receiver of listReceiverNoti) {
              dataSendMail.push({
                receiver: receiver as IUserEmail,
                type: EmailTypeEnum.CHANGE_RECORD_STATUS,
                templateKey: MailTemplate.CHANGE_RECORD_STATUS,
                subject: '[Notification] Change status in a record',
                data: {
                  username: receiver.username,
                  baseURL: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.ADMIN_PAGE,
                  recordRef: currentROF.refNo,
                  recordId: currentROF.id,
                  path: ModulePathEnum.REPORT_OF_FINDING,
                  currentStatus: ReportFindingFormStatus.REASSIGNED,
                  previousStatus: ReportFindingFormStatus.SUBMITTED,
                  performer: performer,
                  executedAt: new Date(),
                },
              });
            }
          }
        }
      }
      //#endregion Push noti
      return { dataNoti, dataSendMail };
    });
  }

  async listReportFindingForm(
    query: ListReportFindingFormDto,
    user: TokenPayloadModel,
    workflowPermissions: string[],
  ) {
    const queryBuilder = this.createQueryBuilder('reportFindingForm')
      .leftJoin('reportFindingForm.planningRequest', 'planningRequest')
      .leftJoin('planningRequest.auditors', 'auditors')
      .leftJoin('planningRequest.vessel', 'vesselPlan')
      .leftJoin('vesselPlan.vesselDocHolders', 'vesselDocHolders')
      .leftJoin('vesselDocHolders.company', 'companyVesselDocHolders')
      // .leftJoinAndSelect('reportFindingForm.reportFindingHistories', 'reportFindingHistories')
      // .leftJoinAndSelect('reportFindingForm.rofPlanningRequest', 'rofPlanningRequest')
      // .leftJoinAndSelect('reportFindingForm.rofAuditTypes', 'rofAuditTypes')
      // .leftJoinAndSelect('reportFindingForm.rofUsers', 'rofUsers')
      .leftJoin('reportFindingForm.company', 'company')
      .leftJoin('reportFindingForm.userAssignments', 'userAssignments')
      .leftJoin('userAssignments.user', 'user')
      .select()
      .addSelect([
        'planningRequest.id',
        'planningRequest.refId',
        'planningRequest.auditNo',
        'planningRequest.globalStatus',
        'planningRequest.customerRef',
        'company.id',
        'company.name',
        'userAssignments.id',
        'userAssignments.permission',
        'user.id',
        'user.username',
        'vesselPlan.id',
        'vesselDocHolders.id',
        'vesselDocHolders.companyId',
        'vesselDocHolders.fromDate',
        'vesselDocHolders.toDate',
        'vesselDocHolders.responsiblePartyInspection',
        'vesselDocHolders.responsiblePartyQA',
        'vesselDocHolders.status',
        'companyVesselDocHolders.name',
      ])
      .where('(reportFindingForm.companyId = :companyId OR  company.parentId = :companyId)', {
        companyId: user.companyId,
      });

    // if (workflowPermissions.length === 1 && workflowPermissions[0] === WorkflowPermission.CREATOR) {
    //   queryBuilder.andWhere(
    //     'rofUsers.userId = :userId AND (rofUsers.relationship = :auditor OR rofUsers.relationship = :leadAuditor )',
    //     {
    //       userId: user.id,
    //       auditor: ROFUserRelationship.AUDITOR,
    //       leadAuditor: ROFUserRelationship.LEAD_AUDITOR,
    //     },
    //   );
    // }

    if (query.planningRequestId) {
      queryBuilder.andWhere('reportFindingForm.planningRequestId = :planningRequestId', {
        planningRequestId: query.planningRequestId,
      });
    }

    if (query.status) {
      queryBuilder.andWhere('reportFindingForm.status = :status', {
        status: query.status,
      });
    }

    if (query.fromDate) {
      queryBuilder.andWhere(`reportFindingForm.createdAt >= :fromDate`, {
        fromDate: new Date(query.fromDate),
      });
    }

    if (query.toDate) {
      queryBuilder.andWhere(`reportFindingForm.createdAt <= :toDate`, {
        toDate: new Date(query.toDate),
      });
    }

    if (!RoleScopeCheck.isAdmin(user)) {
      const { whereForExternal, whereForMainAndInternal } = await _supportWhereDOCChartererOwner(
        this.manager,
        user.explicitCompanyId,
        'reportFindingForm',
      );

      queryBuilder
        .leftJoin('vesselPlan.divisionMapping', 'divisionMapping')
        //.leftJoin('vesselPlan.docHolder', 'docHolder')
        .leftJoin('divisionMapping.division', 'division')
        .leftJoin('division.users', 'users')
        .leftJoin('vesselPlan.vesselCharterers', 'vesselCharterers')
        .leftJoin('vesselPlan.vesselOwners', 'vesselOwners')
        // .leftJoin('vesselPlan.vesselDocHolders', 'vesselDocHolders')
        // .leftJoin('vesselDocHolders.company', 'companyVesselDocHolders')
        .addSelect([
          'vesselPlan.id',
          'vesselPlan.name',
          'vesselCharterers.id',
          'vesselCharterers.companyId',
          'vesselCharterers.fromDate',
          'vesselCharterers.toDate',
          'vesselCharterers.responsiblePartyInspection',
          'vesselCharterers.responsiblePartyQA',
          'vesselCharterers.status',
          'vesselOwners.id',
          'vesselOwners.companyId',
          'vesselOwners.fromDate',
          'vesselOwners.toDate',
          'vesselOwners.responsiblePartyInspection',
          'vesselOwners.responsiblePartyQA',
          'vesselOwners.status',
          // 'vesselDocHolders.id',
          // 'vesselDocHolders.companyId',
          // 'vesselDocHolders.fromDate',
          // 'vesselDocHolders.toDate',
          // 'vesselDocHolders.responsiblePartyInspection',
          // 'vesselDocHolders.responsiblePartyQA',
          // 'vesselDocHolders.status',
          // 'companyVesselDocHolders.name',
        ]);
      // .andWhere(
      //   '(users.id = :userId OR vesselDocHolders.id = :explicitCompanyId OR vesselOwners.companyId = :explicitCompanyId OR vesselCharterers.companyId = :explicitCompanyId OR (reportFindingForm.entityType = :type))',
      //   {
      //     explicitCompanyId: user.explicitCompanyId,
      //     userId: user.id,
      //     type: AuditEntity.OFFICE,
      //   },
      // );
      await _supportCheckRoleScopeForGetList(
        this.manager,
        queryBuilder,
        user,
        whereForMainAndInternal,
        whereForExternal,
        'reportFindingForm',
      );
    }
    // Removed the advanceConditions(date filter) because we changed the default ag grid date range to 3 months
    const result = await this.list(
      {
        page: query.page,
        limit: query.pageSize,
      },
      {
        queryBuilder,
        sort: query.sort || 'reportFindingForm.createdAt:-1',
      },
    );
    if (result.data.length === 0) {
      return result;
    }
    //#region Manipulate rof list
    const rofIds: string[] = [];
    const planningRequestIds: string[] = [];
    const rofData = cloneDeep(result.data);

    for (const data of result.data) {
      rofIds.push(data.id);
      planningRequestIds.push(data.planningRequestId);
    }
    const queryBuilderCar = this.manager
      .getCustomRepository(CARRepository)
      .createQueryBuilder('car')
      .leftJoin('car.cap', 'cap')
      .leftJoin('car.cARVerification', 'cARVerification')
      .where('car.planningRequestId IN (:...planningRequestIds)', {
        planningRequestIds,
      })
      .select([
        'car.id',
        'car.planningRequestId',
        'car.status',
        'cap.id',
        'cap.status',
        'cARVerification.id',
        'cARVerification.status',
      ]);
    //Promise all get related tables
    const [
      reportFindingHistories,
      rofPlanningRequests,
      rofAuditTypes,
      rofUsers,
      rofCars,
    ] = await Promise.all([
      this.manager.find(ReportFindingHistory, {
        where: { reportFindingFormId: In(rofIds) },
      }),
      this.manager.find(ROFPlanningRequest, {
        where: { reportFindingFormId: In(rofIds) },
        relations: ['country'],
      }),
      this.manager.find(ROFAuditType, {
        where: { reportFindingFormId: In(rofIds) },
      }),
      this.manager.find(ROFUser, {
        where: { reportFindingFormId: In(rofIds) },
      }),
      queryBuilderCar.getMany(),
    ]);

    // console.log('rofCars', rofCars);
    //Manipulate data
    for (const data of rofData) {
      data.reportFindingHistories = [];
      data.rofAuditTypes = [];
      data.rofUsers = [];
      let cars = [];

      for (const rofPlanningReq of rofPlanningRequests) {
        if (data.id === rofPlanningReq.reportFindingFormId) {
          data.rofPlanningRequest = rofPlanningReq;
        }
      }

      data.reportFindingHistories = reportFindingHistories.filter(
        (rofHistory) => data.id === rofHistory.reportFindingFormId,
      );

      data.rofAuditTypes = rofAuditTypes.filter(
        (rofAuditType) => data.id === rofAuditType.reportFindingFormId,
      );

      data.rofUsers = rofUsers.filter((rofUser) => data.id === rofUser.reportFindingFormId);
      cars = rofCars.filter((rofCar) => data.planningRequestId === rofCar.planningRequestId);
      // console.log('cars', cars);
      Object.assign(data, { cars });
    }
    //#endregion

    return {
      data: rofData,
      page: result.page,
      pageSize: result.pageSize,
      totalPage: result.totalPage,
      totalItem: result.totalItem,
    };
  }

  async listReassignedROF(
    query: ListReportFindingFormDto,
    user: TokenPayloadModel,
    workflowPermissions: string[],
  ) {
    const queryBuilder = this.createQueryBuilder('reportFindingForm')
      .leftJoin('reportFindingForm.planningRequest', 'planningRequest')
      .leftJoin('planningRequest.auditors', 'auditors')
      .leftJoin('reportFindingForm.reportFindingHistories', 'reportFindingHistories')
      .leftJoin('reportFindingForm.vessel', 'vessel')
      .leftJoin('vessel.vesselDocHolders', 'vesselDocHolders')
      .leftJoin('vesselDocHolders.company', 'companyVesselDocHolders')
      .leftJoin('reportFindingForm.userAssignments', 'userAssignments')
      .leftJoin('userAssignments.user', 'user')
      .leftJoin('reportFindingForm.company', 'company')
      .leftJoin('planningRequest.auditCompany', 'auditCompany')
      .select([
        'reportFindingForm.id',
        'reportFindingForm.refNo',
        'reportFindingForm.sNo',
        'reportFindingForm.status',
        'reportFindingForm.createdAt',
        'reportFindingForm.updatedAt',
        'reportFindingForm.companyId',
        'userAssignments.id',
        'userAssignments.permission',
        'user.id',
        'user.username',
        'company.name',
        'vesselDocHolders.id',
        'vesselDocHolders.companyId',
        'vesselDocHolders.fromDate',
        'vesselDocHolders.toDate',
        'vesselDocHolders.responsiblePartyInspection',
        'vesselDocHolders.responsiblePartyQA',
        'vesselDocHolders.status',
        'companyVesselDocHolders.name',
      ])
      .addSelect([
        'planningRequest.id',
        'auditCompany.name',
        'vessel.id',
        'vessel.name',
        'reportFindingHistories.id',
        'reportFindingHistories.createdAt',
        'reportFindingHistories.status',
        'reportFindingHistories.workflowRemark',
      ])
      .where('reportFindingForm.companyId = :companyId', {
        companyId: user.companyId,
      });

    // if (workflowPermissions.length === 1 && workflowPermissions[0] === WorkflowPermission.CREATOR) {
    //   queryBuilder
    //     .leftJoin('planningRequest.auditors', 'auditors')
    //     .andWhere('auditors.id = :userId', { userId: user.id });
    // }

    if (query.status) {
      queryBuilder.andWhere('reportFindingForm.status = :status', {
        status: query.status,
      });
    } else {
      queryBuilder.andWhere('reportFindingForm.status = :status', {
        //status: ReportFindingFormStatus.REASSIGNED,
        status: ReportFindingFormStatus.IN_PROGRESS,
      });
    }

    if (query.fromDate) {
      queryBuilder.andWhere(`reportFindingForm.createdAt >= :fromDate`, {
        fromDate: new Date(query.fromDate),
      });
    }

    if (query.toDate) {
      queryBuilder.andWhere(`reportFindingForm.createdAt <= :toDate`, {
        toDate: new Date(query.toDate),
      });
    }

    if (query.entityType) {
      queryBuilder.andWhere('reportFindingForm.entityType = :entityType', {
        entityType: query.entityType,
      });
    }

    if (!RoleScopeCheck.isAdmin(user)) {
      const { whereForExternal, whereForMainAndInternal } = await _supportWhereDOCChartererOwner(
        this.manager,
        user.explicitCompanyId,
        'reportFindingForm',
      );
      queryBuilder
        .leftJoin('vessel.divisionMapping', 'divisionMapping')
        .leftJoin('divisionMapping.division', 'division')
        .leftJoin('division.users', 'users')
        // .leftJoin('vessel.vesselDocHolders', 'vesselDocHolders')
        // .leftJoin('vesselDocHolders.company', 'companyVesselDocHolders')
        // .addSelect([
        //   'vesselDocHolders.id',
        //   'vesselDocHolders.companyId',
        //   'vesselDocHolders.fromDate',
        //   'vesselDocHolders.toDate',
        //   'vesselDocHolders.responsiblePartyInspection',
        //   'vesselDocHolders.responsiblePartyQA',
        //   'vesselDocHolders.status',
        //   'companyVesselDocHolders.name',
        // ])
        .leftJoin('vessel.vesselOwners', 'vesselOwners')
        .addSelect([
          'vesselOwners.id',
          'vesselOwners.companyId',
          'vesselOwners.fromDate',
          'vesselOwners.toDate',
          'vesselOwners.responsiblePartyInspection',
          'vesselOwners.responsiblePartyQA',
          'vesselOwners.status',
        ])
        .leftJoin('vessel.vesselCharterers', 'vesselCharterers')
        .addSelect([
          'vesselCharterers.id',
          'vesselCharterers.companyId',
          'vesselCharterers.fromDate',
          'vesselCharterers.toDate',
          'vesselCharterers.responsiblePartyInspection',
          'vesselCharterers.responsiblePartyQA',
          'vesselCharterers.status',
        ]);
      await _supportCheckRoleScopeForGetList(
        this.manager,
        queryBuilder,
        user,
        whereForMainAndInternal,
        whereForExternal,
        'reportFindingForm',
      );
    }

    return this.list(
      {
        page: query.page,
        limit: query.pageSize,
      },
      {
        queryBuilder,
        sort: query.sort || 'reportFindingForm.createdAt:-1;reportFindingHistories.createdAt:1',
      },
    );
  }

  async listReportFindingFormByVessel(query: ListReportFindingFormDto, user: TokenPayloadModel) {
    const queryBuilder = this.createQueryBuilder('reportFindingForm')
      .leftJoin('reportFindingForm.planningRequest', 'planningRequest')
      .leftJoin('reportFindingForm.userAssignments', 'userAssignments')
      .leftJoin('userAssignments.user', 'user')
      .leftJoin('planningRequest.vessel', 'vessel')
      .leftJoin('vessel.vesselDocHolders', 'vesselDocHolders')
      .leftJoin('vesselDocHolders.company', 'companyVesselDocHolders')
      .select()
      .addSelect([
        'planningRequest.id',
        'planningRequest.refId',
        'planningRequest.status',
        'planningRequest.globalStatus',
        'userAssignments.id',
        'userAssignments.permission',
        'user.id',
        'user.username',
        'vesselDocHolders.id',
        'vesselDocHolders.companyId',
        'vesselDocHolders.fromDate',
        'vesselDocHolders.toDate',
        'vesselDocHolders.responsiblePartyInspection',
        'vesselDocHolders.responsiblePartyQA',
        'vesselDocHolders.status',
        'companyVesselDocHolders.name',
      ])
      .where(
        'reportFindingForm.companyId = :companyId AND reportFindingForm.vesselId = :vesselId',
        {
          companyId: user.companyId,
          vesselId: query.vesselId,
        },
      )
      .addOrderBy('reportFindingForm.createdAt', 'DESC');

    if (!RoleScopeCheck.isAdmin(user)) {
      const { whereForExternal, whereForMainAndInternal } = await _supportWhereDOCChartererOwner(
        this.manager,
        user.explicitCompanyId,
        'reportFindingForm',
      );
      queryBuilder
        .leftJoin('planningRequest.vessel', 'vessel')
        .leftJoin('vessel.divisionMapping', 'divisionMapping')
        .leftJoin('divisionMapping.division', 'division')
        .leftJoin('division.users', 'users')
        // .leftJoin('vessel.vesselDocHolders', 'vesselDocHolders')
        // .leftJoin('vesselDocHolders.company', 'companyVesselDocHolders')
        // .addSelect([
        //   'vesselDocHolders.id',
        //   'vesselDocHolders.companyId',
        //   'vesselDocHolders.fromDate',
        //   'vesselDocHolders.toDate',
        //   'vesselDocHolders.responsiblePartyInspection',
        //   'vesselDocHolders.responsiblePartyQA',
        //   'vesselDocHolders.status',
        //   'companyVesselDocHolders.name',
        // ])
        .leftJoin('vessel.vesselOwners', 'vesselOwners')
        .addSelect([
          'vesselOwners.id',
          'vesselOwners.companyId',
          'vesselOwners.fromDate',
          'vesselOwners.toDate',
          'vesselOwners.responsiblePartyInspection',
          'vesselOwners.responsiblePartyQA',
          'vesselOwners.status',
          'companyVesselOwnersPlans.name',
        ])
        .leftJoin('vessel.vesselCharterers', 'vesselCharterers')
        .addSelect([
          'vesselCharterers.id',
          'vesselCharterers.companyId',
          'vesselCharterers.fromDate',
          'vesselCharterers.toDate',
          'vesselCharterers.responsiblePartyInspection',
          'vesselCharterers.responsiblePartyQA',
          'vesselCharterers.status',
          'companyVesselCharterers.name',
        ]);
      if (
        user.companyLevel === CompanyLevelEnum.EXTERNAL_COMPANY &&
        (await this.connection
          .getCustomRepository(PlanningRequestRepository)
          .checkRoleAndCompanyType(user))
      ) {
        queryBuilder.andWhere(
          `(reportFindingForm.entityType = :entityTypeVessel and (auditors.id = :userId or user.id = :userId))`,
          {
            userId: user.id,
            entityTypeVessel: AuditEntity.VESSEL,
          },
        );
      } else {
        if (
          user.companyLevel === CompanyLevelEnum.MAIN_COMPANY ||
          user.companyLevel === CompanyLevelEnum.INTERNAL_COMPANY
        ) {
          queryBuilder.andWhere(`( users.id = :userId ` + whereForMainAndInternal + ')', {
            userId: user.id,
          });
        } else {
          queryBuilder.andWhere(`( users.id = :userId ` + whereForExternal + ')', {
            userId: user.id,
          });
        }
      }
    }

    return this.list(
      {
        page: query.page,
        limit: query.pageSize,
      },
      {
        queryBuilder,
        sort: query.sort || 'reportFindingForm.createdAt:-1',
      },
    );
  }

  async _getReportFindingFormByIAR(iarId: string) {
    const queryBuilder = this.createQueryBuilder('reportFindingForm')
      .leftJoin('reportFindingForm.planningRequest', 'planningRequest')
      .leftJoin('reportFindingForm.internalAuditReport', 'internalAuditReport')
      .select()
      .addSelect(['planningRequest.id', 'planningRequest.refId', 'planningRequest.status'])
      .where('internalAuditReport.id = :iarId', {
        iarId,
      })
      .addOrderBy('reportFindingForm.createdAt', 'DESC');

    return this.getOneQB(queryBuilder);
  }

  async getDetailReportFindingForm(
    reportFindingFormId: string,
    user: TokenPayloadModel,
    workflowPermissions: string[],
  ) {
    // First, check if the planning request is for self assessment
    const planningRequestCheck = await this.createQueryBuilder('reportFindingForm')
      .leftJoin('reportFindingForm.planningRequest', 'planningRequest')
      .select(['reportFindingForm.id', 'planningRequest.id', 'planningRequest.isSA'])
      .where('reportFindingForm.id = :reportFindingFormId', { reportFindingFormId })
      .getOne();

    const isSA = planningRequestCheck?.planningRequest?.isSA || false;
    console.log('Planning request isSA:', isSA);

    let whereVesselDocHolder = '(1=1)';
    if (user.companyLevel === CompanyLevelEnum.EXTERNAL_COMPANY) {
      whereVesselDocHolder = '(vesselDocHolders.responsiblePartyInspection = true)';
    }
    let whereVesselCharterer = '(1=1)';
    if (user.companyLevel === CompanyLevelEnum.EXTERNAL_COMPANY) {
      whereVesselCharterer = '(vesselCharterers.responsiblePartyInspection = true)';
    }
    let whereVesselOwner = '(1=1)';
    if (user.companyLevel === CompanyLevelEnum.EXTERNAL_COMPANY) {
      whereVesselOwner = '(vesselOwnersPlans.responsiblePartyInspection = true)';
    }
    const queryBuilder = this.createQueryBuilder('reportFindingForm')
      .leftJoin('reportFindingForm.planningRequest', 'planningRequest')
      .leftJoin('planningRequest.voyageType', 'voyageType')
      .leftJoin('planningRequest.auditWorkspace', 'auditWorkspace')
      .leftJoin('planningRequest.auditCompany', 'auditCompany')
      .leftJoin('planningRequest.location', 'location')
      .leftJoin('reportFindingForm.company', 'company')
      .leftJoin('reportFindingForm.userAssignments', 'userAssignments')
      .leftJoin('userAssignments.user', 'user')
      .leftJoin('user.company', 'userCompany')
      .leftJoin('user.divisions', 'userDivisions')
      .leftJoinAndSelect(
        'reportFindingForm.reportFindingItems',
        'reportFindingItems',
        'reportFindingItems.deleted = false',
      )
      .leftJoinAndSelect(
        'reportFindingForm.SAFindingItem',
        'SAFindingItem',
        'SAFindingItem.deleted = false',
      )
      .leftJoin('SAFindingItem.elementMaster', 'elementMaster')
      .leftJoinAndSelect('SAFindingItem.fillSAChecklistQuestion', 'fillSAChecklistQuestion')
      .leftJoin('reportFindingItems.chkQuestion', 'chkQuestion')
      .leftJoin(
        'chkQuestion.referencesCategoryData',
        'referencesCategoryData',
        '(referencesCategoryData.masterTableId IN (:...categoryName) AND referencesCategoryData.chkQuestionId =  chkQuestion.id)',
        {
          categoryName: [
            MasterTableObj.VIQ,
            MasterTableObj.CRITICALITY,
            MasterTableObj.POTENTIAL_RISK,
          ],
        },
      )
      .leftJoin('reportFindingItems.car', 'rofCar')
      .leftJoin('reportFindingItems.iarItem', 'iarItem')
      .leftJoin('iarItem.car', 'iarCar')
      // .leftJoinAndSelect('reportFindingForm.reportFindingHistories', 'reportFindingHistories')
      // .leftJoinAndSelect('reportFindingForm.rofPlanningRequest', 'rofPlanningRequest')
      // .leftJoinAndSelect('reportFindingForm.rofAuditTypes', 'rofAuditTypes')
      // .leftJoinAndSelect('reportFindingForm.rofUsers', 'rofUsers')
      // .leftJoinAndSelect('reportFindingForm.rofComments', 'rofComments')
      .leftJoinAndSelect('reportFindingForm.rofOfficeComments', 'rofOfficeComments')
      .leftJoin('planningRequest.vessel', 'vessel')
      .leftJoin('vessel.vesselDocHolders', 'vesselDocHolders', whereVesselDocHolder)
      .leftJoin('vesselDocHolders.company', 'companyVesselDocHolders')
      .leftJoin('vessel.vesselOwners', 'vesselOwnersPlans', whereVesselOwner)
      .leftJoin('vesselOwnersPlans.company', 'companyVesselOwnersPlans')
      .leftJoin('vessel.vesselCharterers', 'vesselCharterers', whereVesselCharterer)
      .leftJoin('vesselCharterers.company', 'companyVesselCharterers')
      .leftJoin('auditWorkspace.fillAuditChecklists', 'fillAuditChecklists')
      .leftJoin('fillAuditChecklists.fillQuestions', 'fillQuestions')
      .leftJoin('fillQuestions.chkQuestion', 'fillQuestionschkQuestion')
      .leftJoin('fillAuditChecklists.fillSAQuestions', 'fillSAQuestions')
      .leftJoin('fillSAQuestions.SAQuestions', 'SAQuestions')
      // .leftJoin('fillQuestionschkQuestion.auditChecklist', 'auditChecklist')
      .leftJoin('reportFindingItems.fillQuestion', 'fillQuestion')
      .leftJoinAndSelect('reportFindingItems.observedRisk', 'observedRisk')
      .leftJoin('planningRequest.auditTimeTable', 'auditTimeTable')
      .select()
      .addSelect([
        'planningRequest.id',
        'planningRequest.refId',
        'planningRequest.auditNo',
        'planningRequest.typeOfAudit',
        'planningRequest.plannedFromDate',
        'planningRequest.plannedToDate',
        'planningRequest.memo',
        'planningRequest.globalStatus',
        'planningRequest.customerRef',
        'planningRequest.isSA',
        'auditCompany.name',
        'auditCompany.isCompanyRestricted',
        'auditWorkspace.id',
        'auditWorkspace.isGenerateROF',
        'auditWorkspace.mobileInspectionStartDate',
        'auditWorkspace.mobileInspectionEndDate',
        'auditWorkspace.attachments',
        'auditWorkspace.inspectionMappingIds',
        'location.id',
        'location.name',
        'vessel.id',
        'vessel.name',
        'vessel.imoNumber',
        'vessel.isVesselRestricted',
        'vessel.isCompanyRestricted',
        'iarItem.id',
        'iarCar.id',
        'rofCar.id',
        'chkQuestion.id',
        'chkQuestion.code',
        'referencesCategoryData.valueId',
        'referencesCategoryData.value',
        'referencesCategoryData.masterTableId',
        'userAssignments.id',
        'userAssignments.permission',
        'user.id',
        'user.username',
        'user.jobTitle',
        'userCompany.name',
        'userCompany.code',
        'userDivisions.name',
        'userDivisions.code',
        'vesselDocHolders.id',
        'vesselDocHolders.companyId',
        'vesselDocHolders.fromDate',
        'vesselDocHolders.toDate',
        'vesselDocHolders.responsiblePartyInspection',
        'vesselDocHolders.responsiblePartyQA',
        'vesselDocHolders.status',
        'companyVesselDocHolders.name',
        'vesselOwnersPlans.id',
        'vesselOwnersPlans.companyId',
        'vesselOwnersPlans.fromDate',
        'vesselOwnersPlans.toDate',
        'vesselOwnersPlans.responsiblePartyInspection',
        'vesselOwnersPlans.responsiblePartyQA',
        'vesselOwnersPlans.status',
        'companyVesselOwnersPlans.name',
        'vesselCharterers.id',
        'vesselCharterers.companyId',
        'vesselCharterers.fromDate',
        'vesselCharterers.toDate',
        'vesselCharterers.responsiblePartyInspection',
        'vesselCharterers.responsiblePartyQA',
        'vesselCharterers.status',
        'companyVesselCharterers.name',
        'fillAuditChecklists.id',
        'fillQuestions.id',
        'fillQuestions.attachments',
        'fillQuestionschkQuestion.id',
        'fillQuestionschkQuestion.code',
        'fillSAQuestions.id',
        'fillSAQuestions.attachments',
        'SAQuestions.id',
        'SAQuestions.code',
        'SAQuestions.elementStageQ',
        'SAQuestions.stage',
        'SAQuestions.keyPerformanceIndicator',
        // 'auditChecklist.id',
        // 'auditChecklist.name',
        'fillQuestion.id',
        'auditTimeTable.actualFrom',
        'auditTimeTable.actualTo',
        'auditTimeTable.actualFrom_Year',
        'auditTimeTable.actualTo_Year',
        'auditTimeTable.actualFrom_Month',
        'auditTimeTable.actualTo_Month',
        'elementMaster.id',
        'elementMaster.name',
        'elementMaster.code',
        'elementMaster.elementStageQ',
        'voyageType.id',
        'voyageType.name',
      ])
      .where(
        'reportFindingForm.id = :reportFindingFormId AND (reportFindingForm.companyId = :companyId OR company.parentId = :companyId)',
        { reportFindingFormId, companyId: user.companyId },
      )
      .addOrderBy('reportFindingItems.createdAt', 'DESC')
      .addOrderBy('fillQuestionschkQuestion.code', 'ASC');

    // if (workflowPermissions.length === 1 && workflowPermissions[0] === WorkflowPermission.CREATOR) {
    //   queryBuilder.andWhere(
    //     'rofUsers.userId = :userId AND (rofUsers.relationship = :auditor OR rofUsers.relationship = :leadAuditor )',
    //     {
    //       userId: user.id,
    //       auditor: ROFUserRelationship.AUDITOR,
    //       leadAuditor: ROFUserRelationship.LEAD_AUDITOR,
    //     },
    //   );
    // }

    // Modify the query based on whether this is a self-assessment or not
    if (isSA) {
      console.log('Using self-assessment specific query');
      // For self-assessment, we need to focus on SAFindingItem instead of reportFindingItems
      queryBuilder.addOrderBy('SAFindingItem.createdAt', 'DESC');
    }

    const detail = await this.getOneQB(queryBuilder);

    // Process finding items based on whether this is a self-assessment or not
    let findingItems = [];
    if (isSA) {
      console.log('Processing self-assessment finding items');
      findingItems = detail?.SAFindingItem || [];
    } else {
      console.log('Processing regular finding items');
      findingItems = detail?.reportFindingItems || [];
    }

    const natureFindingIds = findingItems.map((natureFinding) => natureFinding?.natureFindingId);
    const natureFindings = await this.manager
      .getCustomRepository(NatureFindingRepository)
      .findByIds(natureFindingIds || []);
    const observedRisks = await this.manager.getCustomRepository(ObservedRiskRepository).find();
    if (findingItems?.length) {
      for (const item of findingItems) {
        if (!item.natureFindingName && item?.natureFindingId) {
          item.natureFindingName = natureFindings?.find(
            (x) => x?.id === item?.natureFindingId,
          )?.name;
        }

        // Process differently based on whether this is a self-assessment or not
        if (!isSA) {
          // For regular items, process chkQuestion data
          const referencesCategoryData = item?.chkQuestion?.referencesCategoryData || [];
          if (!referencesCategoryData?.length) {
            continue;
          }
          let newCategoryData = referencesCategoryData;
          const critical = referencesCategoryData.find(
            (x) => x.masterTableId === MasterTableObj.CRITICALITY,
          );
          const potentialRisk = referencesCategoryData.find(
            (x) => x.masterTableId === MasterTableObj.POTENTIAL_RISK,
          );

          if (critical) {
            newCategoryData = newCategoryData.filter(
              (x) => x.masterTableId !== critical.masterTableId,
            );
            Object.assign(item.chkQuestion, { criticality: critical.value });
          }
          if (potentialRisk) {
            newCategoryData = newCategoryData.filter(
              (x) => x.masterTableId !== potentialRisk.masterTableId,
            );
            const potential = await this.manager
              .getCustomRepository(PriorityMasterRepository)
              .findOne(potentialRisk.valueId);
            Object.assign(item.chkQuestion, { potentialRisk: potential });

            if (!item.observedRiskId && potential) {
              const observedRiskMapping = observedRisks.find((ob) => ob.type === potential?.risk);
              item.observedRiskId = observedRiskMapping?.id;
              item.observedRisk = observedRiskMapping;
            }
          }
          item.chkQuestion.referencesCategoryData = newCategoryData;
        }
      }
    }

    if (detail) {
      if (
        !detail.reviewInProgress &&
        detail.status === ReportFindingFormStatus.SUBMITTED &&
        workflowPermissions.includes(WorkflowPermission.REVIEWER)
      ) {
        await this.update({ id: reportFindingFormId }, { reviewInProgress: true });
      }

      const rofDetail = cloneDeep(detail);

      // Check if this is a self-assessment
      const isSA = rofDetail?.planningRequest?.isSA || false;

      // fetching the inspection mapping data
      if (rofDetail?.planningRequest?.auditWorkspace?.inspectionMappingIds?.length) {
        const inspectionMapping = [];
        const { inspectionMappingIds } = rofDetail?.planningRequest?.auditWorkspace;
        for (const inspectionMappingId of inspectionMappingIds) {
          const detailInspectionMapping = await this.connection
            .getCustomRepository(InspectionMappingRepository)
            .createQueryBuilder('inspectionMapping')
            .leftJoinAndSelect('inspectionMapping.standardMasters', 'standardMasters')
            .leftJoinAndSelect('standardMasters.complianceAnswers', 'complianceAnswers')
            .where('inspectionMapping.id = :inspectionMappingId', {
              inspectionMappingId,
            })
            .getOne();
          inspectionMapping.push(detailInspectionMapping);
        }
        if (inspectionMapping?.length) {
          rofDetail.planningRequest.auditWorkspace['inspectionMapping'] = inspectionMapping;

          // // If this is a self-assessment, fetch compliance answers
          if (isSA) {
            console.log('Fetching compliance answers for self-assessment');

            // Get self assessments from inspection mappings
            const standardMasterIds = [];
            inspectionMapping.forEach((im: any) => {
              if (im.standardMasters && im.standardMasters.length > 0) {
                im.standardMasters.forEach((sa: any) => {
                  standardMasterIds.push(sa.id);
                });
              }
            });

            if (standardMasterIds.length > 0) {
              console.log('Found self assessment IDs:', standardMasterIds);

              // Get compliance answers for these standard masters
              const complianceAnswers: any[] = await this.manager
                .createQueryBuilder('ComplianceAnswer', 'ca')
                .where('ca.standardMasterId IN (:...standardMasterIds)', {
                  standardMasterIds,
                })
                .andWhere('ca.deleted = false')
                .getMany();

              if (complianceAnswers && complianceAnswers.length > 0) {
                console.log('Found compliance answers:', complianceAnswers.length);

                // Add compliance answers to the rofDetail
                if (!rofDetail['complianceAnswers']) {
                  rofDetail['complianceAnswers'] = complianceAnswers;
                }
              }
            }
          }
        }
      }
      //Promise all get related tables
      const [
        reportFindingHistories,
        rofPlanningRequests,
        rofAuditTypes,
        rofUsers,
      ] = await Promise.all([
        this.manager.find(ReportFindingHistory, {
          where: { reportFindingFormId: detail.id },
        }),
        this.manager.find(ROFPlanningRequest, {
          where: { reportFindingFormId: detail.id },
        }),
        this.manager.find(ROFAuditType, {
          where: { reportFindingFormId: detail.id },
        }),
        // this.manager.find(ROFUser, {
        //   where: { reportFindingFormId: detail.id },
        //   relations: ['user'],
        // }),
        this.manager.getCustomRepository(ROFUserRepository).getByRofId(detail.id),
      ]);

      //Manipulate data
      rofDetail.reportFindingHistories = [];
      rofDetail.rofAuditTypes = [];
      rofDetail.rofUsers = [];

      for (const rofPlanningReq of rofPlanningRequests) {
        if (rofDetail.id === rofPlanningReq.reportFindingFormId) {
          rofDetail.rofPlanningRequest = rofPlanningReq;
        }
      }

      rofDetail.reportFindingHistories = reportFindingHistories.filter(
        (rofHistory) => rofDetail.id === rofHistory.reportFindingFormId,
      );

      rofDetail.rofAuditTypes = rofAuditTypes.filter(
        (rofAuditType) => rofDetail.id === rofAuditType.reportFindingFormId,
      );

      rofDetail.rofUsers = rofUsers.filter(
        (rofUser) => rofDetail.id === rofUser.reportFindingFormId,
      );
      // const checkQuestionId = rofDetail?.reportFindingItems?.map((item) => {
      //   return item.chkQuestionId;
      // });
      // const checkListQuestions = [];
      // rofDetail.planningRequest.auditWorkspace['fillAuditChecklists'].map((item) => {
      //   item.fillQuestions.map((item) => {
      //     checkListQuestions.push(item.chkQuestion);
      //   });
      // });

      // delete rofDetail.planningRequest.auditWorkspace.fillAuditChecklists;

      // const groupedArrays = {};
      // checkListQuestions.map((item) => {
      //   const name = item?.auditChecklist.name;
      //   if (!groupedArrays[name]) {
      //     groupedArrays[name] = {
      //       checkListId: item?.auditChecklist?.id,
      //       checksListName: name,
      //       questions: [],
      //     };
      //   }
      //   if (!checkQuestionId.includes(item?.id)) {
      //     groupedArrays[name]?.questions?.push({
      //       id: item?.id,
      //       code: item?.code,
      //     });
      //   }
      // });
      // rofDetail.planningRequest.auditWorkspace['auditChecklist'] = Object.values(groupedArrays);
      return rofDetail;
    } else {
      throw new BaseError({ status: 404, message: 'reportFindingForm.NOT_FOUND' });
    }
  }

  async listFindingItemOfForm(
    reportFindingFormId: string,
    query: ListFindingItemOfFormDto,
    user: TokenPayloadModel,
  ) {
    return this.connection
      .getCustomRepository(ReportFindingItemRepository)
      .listFindingItemOfForm(reportFindingFormId, query, user);
  }

  async getReportFindingFormDataForCreatePdf(reportFindingFormId: string) {
    try {
      const rawQuery = `
        SELECT
        rff."refNo" AS "refNo",
        rff."sNo" AS "serialNo",
        rff."totalFindings" AS "totalNoOfFinding",
        rff."totalNonConformity" AS "totalNoOfConformity",
        rff."totalObservation" AS "totalNoOfObservation",
        rff."entityType" AS "entityType",
        rff."vesselId",
        (
        SELECT
          JSON_AGG(tem)
        FROM
          (
          SELECT
            pr."auditNo" AS "auditNumber",
            (
            SELECT
              STRING_AGG(at2."name", ',')
            FROM
              (
              SELECT
                prat."auditTypeId"
              FROM
                planning_request_audit_type prat
              WHERE
                prat."planningRequestId" = rff."planningRequestId") AS tem
            JOIN audit_type at2
                                        ON
              at2.id = tem."auditTypeId") AS "auditType",
            pr."plannedFromDate" AS "auditFromDate",
            pr."plannedToDate" AS "auditToDate",
            (SELECT string_agg(u.username,',')  FROM vessel_user vu
            LEFT  JOIN "user" u
            ON u.id = vu."userId"
            WHERE vu."vesselId" = v.id) AS "vesselManager",
            (
            SELECT
              leadauditor.username
            FROM
              "user" leadauditor
            WHERE
              leadauditor."id" = pr."leadAuditorId") AS "leadAuditor",
            (
            SELECT
              STRING_AGG(auditor."username", ',')
            FROM
              (
              SELECT
                pra."userId"
              FROM
                planning_request_auditor pra
              WHERE
                pra."planningRequestId" = rff."planningRequestId"
                                ) AS tem
              LEFT JOIN "user" auditor
                                        ON
              auditor.id = tem."userId"
                          ) AS "auditors"
          FROM
            planning_request pr
        LEFT JOIN vessel v
                                ON
            v.id = pr."vesselId"
          WHERE
            pr.id = rff."planningRequestId") AS tem) AS "planingRequestData",
        (
        SELECT
          JSON_AGG(tem)
        FROM
          (
          SELECT
            rfi."viqRefNo" AS "referenceNumber",
            rfi."auditTypeName" AS "auditType",
            rfi."natureFindingName" AS "natureFindingType",
            rfi."findingComment" AS "findingComment",
            rfi."findingRemark" AS "findingRemark",
            "isSignificant" AS "isSignificant",
            rfi.reference,
            cq.code as "questionCode"
          FROM
            report_finding_item rfi
          JOIN chk_question cq
          ON rfi."chkQuestionId" = cq."id"
          WHERE
            rfi."reportFindingFormId" = $1) AS tem) AS "reportOfFindings",
        (
        SELECT
          JSON_AGG(tem)
        FROM
          (
          SELECT
            roc."comment" AS "comment",
            roc."createdUser":: json -> 'username' AS "commentBy",
            roc."createdUser" :: json -> 'jobTitle' AS "jobTitle",
            roc."createdAt" AS "commentedDate"
          FROM
            rof_office_comment roc
          WHERE
            roc."reportFindingFormId" = $1) AS tem) AS "officeComments",
        (
        SELECT
          JSON_AGG(tem)
        FROM
          (
          SELECT
            rfh.status AS "status",
            rfh."workflowRemark" AS "comment",
            rfh."createdUser":: json -> 'username' AS "commentBy",
            rfh."createdUser" :: json -> 'jobTitle' AS "jobTitle",
            rfh."createdAt" AS "commentedDate"
          FROM
            report_finding_history rfh
          WHERE
            rfh."reportFindingFormId" = $1) AS tem) AS "userHistorySection",
          (
              SELECT
                JSON_AGG(tem)
              FROM
                (
                SELECT
                  car.status AS "status",
                  car."actionRequest"  AS "actionRequest",
                  car."capTargetEndDate"  AS "targetDate"
                FROM
                  corrective_action_request car
                WHERE
                  car."planningRequestId" = rff."planningRequestId") AS tem) AS "cars",
            (
              SELECT
                STRING_AGG ( tem."name", ',' )
              FROM
                (SELECT rfd."departmentId", department."name" FROM report_finding_department rfd JOIN "department" department ON department.ID = rfd."departmentId" WHERE rfd."reportFindingFormId" = $1 ) AS tem
            ) AS "departmentName"
          FROM
            report_finding_form rff
          WHERE
            rff.id = $1;
      `;
      return await this.query(rawQuery, [reportFindingFormId]);
    } catch (ex) {
      LoggerCommon.error(
        '[ReportFindingFormRepository] getReportFindingFormDataForCreatePdf error ',
        ex.message || ex,
      );
    }
  }

  async _triggerCreateCar(
    manager: EntityManager,
    user: TokenPayloadModel,
    formId: string,
    isDup?: boolean,
  ) {
    try {
      // get NC items without car and PARId
      const [listInspectionMappings, form] = await Promise.all([
        manager
          .getCustomRepository(InspectionMappingRepository)
          .listInspectionMapping({ pageSize: -1 }, user),
        manager
          .createQueryBuilder(ReportFindingForm, 'form')
          .leftJoin(
            'form.reportFindingItems',
            'items',
            `(items.carId IS NULL
                    AND items.reportFindingFormId = :formId)
                  `,
            {
              formId,
            },
          )
          .where('form.id = :formId', { formId })
          .select([
            'form.id',
            'form.planningRequestId',
            'items.findingComment',
            'items.natureFindingId',
            'items.natureFindingName',
            'items.auditTypeId',
            'items.carId',
            'items.id',
            'items.findingAttachments',
            'items.chkQuestionId',
          ])
          .getOne(),
      ]);

      const inspectionMappingMapped = listInspectionMappings.data.map((inspectionMap) => {
        return {
          id: inspectionMap.id,
          auditTypeId: inspectionMap.auditTypeId,
          natureFindingName: inspectionMap.natureFindings[0]?.natureFinding.name || null,
          natureFindingId: inspectionMap.natureFindings[0]?.natureFinding.id || null,
        };
      });

      const nonConformityItems = form.reportFindingItems.filter((findingItem) => {
        return inspectionMappingMapped.some(
          (inspectionMap) =>
            inspectionMap.auditTypeId === findingItem.auditTypeId &&
            (inspectionMap.natureFindingName === findingItem.natureFindingName ||
              inspectionMap.natureFindingId === findingItem.natureFindingId),
        );
      });

      // prepared each car for each item
      const preparedCars = nonConformityItems.map((item) => {
        const capTargetEndDate = new Date();
        capTargetEndDate.setDate(capTargetEndDate.getDate() + 30);
        manager.delete(CorrectiveActionRequest, {
          planningRequestId: form.planningRequestId,
          chkQuestionId: item.chkQuestionId,
        });
        return {
          id: Utils.strings.generateUUID(),
          reportFindingItems: [item],
          actionRequest: item.findingComment || '',
          capTargetPeriod: 30,
          periodType: 'Day',
          capTargetEndDate,
          attachments: decryptAttachmentValues(item.findingAttachments),
          planningRequestId: form.planningRequestId,
          createdUserId: user.id,
          companyId: user.companyId,
          isDup: isDup ? isDup : false,
          chkQuestionId: item.chkQuestionId,
        } as CorrectiveActionRequest;
      });

      // create new CAR for each non-conformity-item
      return await manager.save(CorrectiveActionRequest, preparedCars);
    } catch (ex) {
      LoggerCommon.error(
        '[ReportFindingFormRepository] _triggerCreateCarForNonConformityItems error ',
        ex.message || ex,
      );

      throw ex;
    }
  }

  /**
   * Create Corrective Action Requests (CARs) for Self Assessment findings
   * @param manager EntityManager
   * @param user User information
   * @param formId Report Finding Form ID
   * @param isDup Flag to indicate if this is a duplicate CAR
   * @returns Array of created CARs
   */
  async _triggerCreateSACar(
    manager: EntityManager,
    user: TokenPayloadModel,
    auditWorkspaceId: string,
    isDup?: boolean,
  ) {
    try {
      // get NC items without car and PARId
      const [listInspectionMappings, auditWorkspace] = await Promise.all([
        manager
          .getCustomRepository(InspectionMappingRepository)
          .listInspectionMapping({ pageSize: -1 }, user),
        manager
          .createQueryBuilder(AuditWorkspace, 'auditWorkspace')
          .leftJoin(
            'auditWorkspace.saFindingItems',
            'items',
            `(items.carId IS NULL
                    AND items.auditWorkspaceId = :auditWorkspaceId)
                  `,
            {
              auditWorkspaceId,
            },
          )
          .where('auditWorkspace.id = :auditWorkspaceId', { auditWorkspaceId })
          .select([
            'auditWorkspace.id',
            'auditWorkspace.planningRequestId',
            'items.findingRemark',
            'items.findingComment',
            'items.natureFindingId',
            'items.natureFindingName',
            'items.auditTypeId',
            'items.carId',
            'items.id',
            'items.findingAttachments',
            'items.elementMasterId',
          ])
          .getOne(),
      ]);

      const inspectionMappingMapped = listInspectionMappings.data.map((inspectionMap) => {
        return {
          id: inspectionMap.id,
          auditTypeId: inspectionMap.auditTypeId,
          natureFindingName: inspectionMap.natureFindings[0]?.natureFinding.name || null,
          natureFindingId: inspectionMap.natureFindings[0]?.natureFinding.id || null,
          isPrimaryFinding: inspectionMap.natureFindings[0]?.isPrimaryFinding || false,
        };
      });

      const filterPrimaryFindings = auditWorkspace.saFindingItems.filter((findingItem) => {
        return inspectionMappingMapped.some(
          (inspectionMap) =>
            inspectionMap.auditTypeId === findingItem.auditTypeId &&
            (inspectionMap.natureFindingName === findingItem.natureFindingName ||
              inspectionMap.natureFindingId === findingItem.natureFindingId) &&
            inspectionMap?.isPrimaryFinding === true,
        );
      });

      if (filterPrimaryFindings.length === 0) {
        return [];
      }

      // Prepare each CAR for each item
      const preparedCars = filterPrimaryFindings.map((item) => {
        const capTargetEndDate = new Date();
        capTargetEndDate.setDate(capTargetEndDate.getDate() + 30);

        // Create a new CAR with the required fields
        const newCar = new CorrectiveActionRequest();
        newCar.id = Utils.strings.generateUUID();
        newCar.actionRequest = item?.findingRemark || item?.findingComment || '';
        newCar.capTargetPeriod = 30;
        newCar.periodType = PeriodTypeEnum.DAY;
        newCar.capTargetEndDate = capTargetEndDate;
        newCar.attachments = item.findingAttachments
          ? decryptAttachmentValues(item.findingAttachments)
          : [];
        newCar.planningRequestId = auditWorkspace.planningRequestId;
        newCar.createdUserId = user.id;
        newCar.companyId = user.companyId;
        newCar.isDup = isDup ? isDup : false;
        newCar.status = CarStatusEnum.OPEN;
        newCar.chkQuestionId = item.elementMasterId;

        return newCar;
      });

      // Create new CAR for each non-conformity item
      const savedCars = await manager.save(CorrectiveActionRequest, preparedCars);
      console.log('savedCars', savedCars);

      // Update the carId in the SAFindingItems
      for (let i = 0; i < savedCars.length; i++) {
        const car = savedCars[i];
        const item = filterPrimaryFindings[i];

        await manager.update(SAFindingItem, { id: item.id }, { carId: car.id });
      }

      return savedCars;
    } catch (ex) {
      LoggerCommon.error(
        '[ReportFindingFormRepository] _triggerCreateSACar error ',
        ex.message || ex,
      );

      throw ex;
    }
  }

  async _triggerCreateFollowUp(
    manager: EntityManager,
    user: TokenPayloadModel,
    formId: string,
    timezone: string,
    createdUser?: CreatedUserHistoryModel,
  ) {
    try {
      const form = await manager
        .createQueryBuilder(ReportFindingForm, 'form')
        .leftJoin(
          'form.reportFindingItems',
          'items',
          `(items.carId IS NULL
          AND items.reportFindingFormId = :formId)
        `,
          {
            formId,
          },
        )
        .where('form.id = :formId', { formId })
        .select([
          'form.id',
          'form.planningRequestId',
          'items.findingComment',
          'items.natureFindingName',
          'items.auditTypeId',
          'items.carId',
          'items.id',
          'items.findingAttachments',
        ])
        .getOne();

      // create FlowUp when submit ROF
      const followUpFound = await manager.findOne(FollowUp, { reportFindingFormId: form.id });
      if (!followUpFound) {
        const currYear = momentTZ.tz(timezone).year();

        const counter = await manager
          .getCustomRepository(CompanyFeatureVersionRepository)
          .getNextVersion({
            manager,
            companyId: user.companyId,
            feature: FeatureVersionConfig.FOLLOW_UP,
            year: Number(currYear),
          });

        const refId = await this._genFollowUpRefId(manager, user, counter, Number(currYear));

        const cars = await manager
          .createQueryBuilder(CorrectiveActionRequest, 'car')
          .leftJoinAndSelect('car.cap', 'cap')
          .leftJoinAndSelect('car.cARVerification', 'cARVerification')
          .where(`car.planningRequestId = :id`, {
            id: form.planningRequestId,
          })
          .select()
          .getMany();

        const totalCars = cars.length;
        const totalsCloseCars = cars.filter(
          (car) =>
            car.cARVerification?.status == CarVerificationStatusEnum.VERIFIED_AND_CLOSE ||
            car.cARVerification?.status == CarVerificationStatusEnum.OVERRIDING_CLOSURE,
        ).length;

        await manager.insert(FollowUp, {
          status:
            totalCars == totalsCloseCars ? FollowUpStatusEnum.CLOSE_OUT : FollowUpStatusEnum.DRAFT,
          planningRequestId: form.planningRequestId,
          reportFindingFormId: form.id,
          refId,
        });

        // Handle add Audit log
        await manager.getCustomRepository(AuditLogRepository).createAuditLog(
          {
            module: AuditModuleEnum.INSPECTION_FOLLOW_UP,
            planningId: form.planningRequestId,
          },
          user,
          [AuditActivityEnum.CREATED],
          createdUser,
        );
      }
    } catch (ex) {
      LoggerCommon.error(
        '[ReportFindingFormRepository] _triggerCreateFollowUp error ',
        ex.message || ex,
      );

      throw ex;
    }
  }

  /**
   * Update the observedScore and potentialScore of the vessel screening
   * @param vesselId - The vessel ID to update the score for
   * @param user - The user making the request
   */
  async updateChartererInspectionScore(vesselId: string, user: TokenPayloadModel) {
    // get observedScore and potentialScore from follow-ups
    const { observedScore, potentialScore } = await this.getAllFollowUpsByVesselAndCompany(
      vesselId,
      user.companyId,
      user,
    );

    const vesselScreening = await this.manager.find(VesselScreening, {
      where: {
        vesselId: vesselId,
        companyId: user.companyId,
      },
    });

    await this.manager.update(
      ChartererInspectionRequest,
      {
        vesselId: vesselId,
      },
      {
        observedScore: observedScore || 0,
        potentialScore: potentialScore || 0,
      },
    );
    for (const item of vesselScreening) {
      await this.manager
        .getCustomRepository(VesselScreeningSummaryRepository)
        .updateVesselScreeningSummaryByRef(
          this.manager,
          VesselScreeningSummaryReferenceEnum.CHARTERER_INSPECTION,
          item?.id,
        );
    }
  }

  /**
   * Get all follow-ups based on vessel ID and company ID
   * @param vesselId - The vessel ID to filter by
   * @param companyId - The company ID to filter by
   */
  async getAllFollowUpsByVesselAndCompany(
    vesselId: string,
    companyId: string,
    user: TokenPayloadModel,
  ) {
    try {
      const followUps = await this.connection
        .getRepository(FollowUp)
        .createQueryBuilder('followUp')
        .leftJoin('followUp.planningRequest', 'planningRequest')
        .leftJoin('planningRequest.auditWorkspace', 'auditWorkspace')
        .leftJoin('planningRequest.vessel', 'vessel')
        .select([
          'followUp.id',
          'followUp.createdAt',
          'planningRequest.id',
          'planningRequest.companyId',
          'planningRequest.vesselId',
          'auditWorkspace.id',
          'vessel.id',
        ])
        .where('planningRequest.vesselId = :vesselId', { vesselId })
        .andWhere('planningRequest.companyId = :companyId', { companyId })
        .andWhere('followUp.deleted = false')
        .andWhere('planningRequest.deleted = false')
        .orderBy('followUp.createdAt', 'DESC')
        .getMany();
      let totalObservedScore = 0;
      let totalPotentialScore = 0;
      for (const item of followUps) {
        const analyticalReport = await this.manager
          .getCustomRepository(AnalyticalReportInspectionPerformanceRepository)
          .getInfoAnalyticalReportInspectionPerformance(
            item?.planningRequest?.auditWorkspace?.id,
            user,
          );

        // Calculate scoring logic
        let observedRiskScore = 0;
        let potentialRiskScore = 0;

        if (
          analyticalReport &&
          'maxValueVesselObservedRisk' in analyticalReport &&
          'totalObservedRisk' in analyticalReport &&
          typeof (analyticalReport as any).maxValueVesselObservedRisk === 'number' &&
          typeof (analyticalReport as any).totalObservedRisk === 'number' &&
          (analyticalReport as any).maxValueVesselObservedRisk > 0
        ) {
          observedRiskScore = Math.round(
            (Math.abs((analyticalReport as any).totalObservedRisk) /
              (analyticalReport as any).maxValueVesselObservedRisk) *
            100,
          );

          if (observedRiskScore === 0) {
            observedRiskScore = 0;
            totalObservedScore += observedRiskScore;
          } else if (observedRiskScore >= 1 && observedRiskScore <= 19) {
            observedRiskScore = 2;
            totalObservedScore += observedRiskScore;
          } else if (observedRiskScore >= 20 && observedRiskScore <= 49) {
            observedRiskScore = 5;
            totalObservedScore += observedRiskScore;
          } else if (observedRiskScore >= 50) {
            observedRiskScore = 10;
            totalObservedScore += observedRiskScore;
          }

          LoggerCommon.log(`[FollowUpRepository] Observed Risk Score: ${observedRiskScore}`);
        }

        if (
          analyticalReport &&
          'maxValueVesselPotentialRisk' in analyticalReport &&
          'totalPotentialRisk' in analyticalReport &&
          typeof (analyticalReport as any).maxValueVesselPotentialRisk === 'number' &&
          typeof (analyticalReport as any).totalPotentialRisk === 'number' &&
          (analyticalReport as any).maxValueVesselPotentialRisk > 0
        ) {
          potentialRiskScore = Math.round(
            (Math.abs((analyticalReport as any).totalPotentialRisk) /
              (analyticalReport as any).maxValueVesselPotentialRisk) *
            100,
          );

          if (potentialRiskScore === 0) {
            potentialRiskScore = 0;
            totalPotentialScore += potentialRiskScore;
          } else if (potentialRiskScore >= 1 && potentialRiskScore <= 19) {
            potentialRiskScore = 2;
            totalPotentialScore += potentialRiskScore;
          } else if (potentialRiskScore >= 20 && potentialRiskScore <= 49) {
            potentialRiskScore = 5;
            totalPotentialScore += potentialRiskScore;
          } else if (potentialRiskScore >= 50) {
            potentialRiskScore = 10;
            totalPotentialScore += potentialRiskScore;
          }
        }
      }
      LoggerCommon.log(
        `[ReportFindingFormRepository] getAllFollowUpsByVesselAndCompany: Found ${followUps.length} follow-ups for vesselId: ${vesselId}, companyId: ${companyId}`,
      );

      return {
        observedScore: Math.round(totalObservedScore / followUps?.length),
        potentialScore: Math.round(totalPotentialScore / followUps?.length),
      };
    } catch (ex) {
      LoggerCommon.error(
        '[ReportFindingFormRepository] getAllFollowUpsByVesselAndCompany error ',
        ex.message || ex,
      );
      throw ex;
    }
  }

  async _triggerFindingManagementStatus(
    reportFindingFormId: string,
    findingStatus: ReportFindingFormStatus,
  ) {
    await this.update(
      {
        id: reportFindingFormId,
      },
      {
        status: findingStatus,
      },
    );
    return 1;
  }

  private async _genFollowUpRefId(
    manager: EntityManager,
    token: TokenPayloadModel,
    counter: number,
    year: number,
  ) {
    const company = await manager.findOne(Company, {
      where: { id: token.companyId },
      select: ['code'],
    });
    const version = leadingZero(counter, 4);
    return `${company.code}/IFU/${version}/${year}`;
  }
  private async getCarIdsNeedToDeleted(
    preparedDeleteFindingItemIds: string[],
    managerTrans: EntityManager,
    isSA = false,
  ) {
    let cars;

    if (isSA) {
      // For self-assessment, check SAFindingItems
      cars = await managerTrans
        .createQueryBuilder(CorrectiveActionRequest, 'car')
        .innerJoin('car.saFindingItems', 'items', 'items.deleted = false')
        .where('(items.id IN(:...preparedDeleteFindingItemIds))', {
          preparedDeleteFindingItemIds,
        })
        .select(['car.id', 'items.id'])
        .getMany();

      const preparedDeletedCarIds = cars
        .filter(
          (car) =>
            difference(
              car.saFindingItems?.map((item) => item.id),
              preparedDeleteFindingItemIds,
            ).length == 0,
        )
        .map((car) => car.id);

      return preparedDeletedCarIds;
    } else {
      // For regular audit, check ReportFindingItems
      cars = await managerTrans
        .createQueryBuilder(CorrectiveActionRequest, 'car')
        .innerJoin('car.reportFindingItems', 'items', 'items.deleted = false')
        .where('(items.id IN(:...preparedDeleteFindingItemIds))', {
          preparedDeleteFindingItemIds,
        })
        .select(['car.id', 'items.id'])
        .getMany();

      const preparedDeletedCarIds = cars
        .filter(
          (car) =>
            difference(
              car.reportFindingItems?.map((item) => item.id),
              preparedDeleteFindingItemIds,
            ).length == 0,
        )
        .map((car) => car.id);

      return preparedDeletedCarIds;
    }
  }
  async prepareDataMapping(
    findingItems: ReportFindingItem2Dto[],
    planningRequestId: string,
    isClone: boolean,
    user: TokenPayloadModel,
    formId: string,
    auditWorkspaceId?: string,
    isForMobile?: boolean,
  ) {
    let totalFindings = 0;
    let totalNonConformity = 0;
    let totalObservation = 0;
    const preparedFindingItems = [];
    const preparedFindingItemsForFillChecklist = [];
    if (findingItems.length > 0) {
      /** Prepared master data mapping (id-info) */
      // prepare nature finding mapping
      const natureFindingMap: { [key: string]: { name: string; isPrimary: boolean } } = {};
      const natureFindingArr = await this.manager
        .getCustomRepository(NatureFindingRepository)
        ._listNatureFindingByIdsWithPR(
          findingItems.map((x) => x.natureFindingId),
          planningRequestId,
        );

      natureFindingArr.forEach((natureFinding) => {
        const isPrimary = natureFinding.inspMapNatFinding?.some((m) => m.isPrimaryFinding);
        natureFindingMap[natureFinding.id] = { name: natureFinding.name, isPrimary: !!isPrimary };
      });
      // prepare other master data
      const otherMasterDataMap = await this._getDetailMasterDataByIds([
        {
          tableName: 'audit_type',
          ids: findingItems.map((x) => x.auditTypeId).filter((item) => !!item),
        },
        {
          tableName: 'main_category',
          ids: findingItems.map((x) => x.mainCategoryId).filter((item) => !!item),
        },
        {
          tableName: 'second_category',
          ids: findingItems.map((x) => x.secondCategoryId).filter((item) => !!item),
        },
        {
          tableName: 'third_category',
          ids: findingItems.map((x) => x.thirdCategoryId).filter((item) => !!item),
        },
        { tableName: 'viq', ids: findingItems.map((x) => x.viqId).filter((item) => !!item) },
        {
          tableName: 'department',
          ids: findingItems.map((x) => x.departmentId).filter((item) => !!item),
        },
        {
          tableName: 'location',
          ids: findingItems.map((x) => x.locationId).filter((item) => !!item),
        },
      ]);

      for (let i = 0; i < findingItems.length; i++) {
        totalFindings += 1;

        if (
          findingItems[i].natureFindingId &&
          natureFindingMap[findingItems[i].natureFindingId] &&
          natureFindingMap[findingItems[i].natureFindingId].name ===
            AppConst.NATURE_FINDING_DEFAULT.NON_CONFORMITY
        ) {
          totalNonConformity += 1;
        }

        // Check is Observation
        if (
          findingItems[i].natureFindingId &&
          natureFindingMap[findingItems[i].natureFindingId] &&
          natureFindingMap[findingItems[i].natureFindingId].name ===
            AppConst.NATURE_FINDING_DEFAULT.OBSERVATION
        ) {
          totalObservation += 1;
        }

        // Prepare finding items
        const item = { ...findingItems[i] };
        if (isClone) {
          delete item.id;
        }
        const findingAttachments = item.findingAttachments;
        const prepareFindingItem = {
          reportFindingFormId: formId,
          isPrimaryFinding: (
            natureFindingMap[findingItems[i].natureFindingId] || { isPrimary: false }
          ).isPrimary,
          natureFindingName: (natureFindingMap[item.natureFindingId] || {}).name || null,
          auditTypeName: otherMasterDataMap[`audit_type::${item.auditTypeId}`] || null,
          locationName: otherMasterDataMap[`location::${item.locationId}`] || null,
          mainCategoryName: otherMasterDataMap[`main_category::${item.mainCategoryId}`] || null,
          secondCategoryName:
            otherMasterDataMap[`second_category::${item.secondCategoryId}`] || null,
          thirdCategoryName: otherMasterDataMap[`third_category::${item.thirdCategoryId}`] || null,
          viqRefNo: otherMasterDataMap[`viq::${item.viqId}`] || null,
          departmentName: otherMasterDataMap[`department::${item.departmentId}`] || null,
          companyId: user.companyId,
          createdUserId: user.id,
          updatedUserId: user.id,
          findingAttachments: findingAttachments,
        };
        if (isForMobile) {
          preparedFindingItems.push({
            id: findingItems[i].id,
            auditWorkspaceId: auditWorkspaceId,
            ...prepareFindingItem,
          });
        } else {
          preparedFindingItems.push({
            ...item,
            ...prepareFindingItem,
          });
          delete item['observedRiskId'];
          preparedFindingItemsForFillChecklist.push({
            reportFindingItemId: item.id,
            findingAttachments,
            ...omit(item, [
              'id',
              'createdAt',
              'updatedAt',
              'companyId',
              'createdUserId',
              'updatedUserId',
              'auditChecklistId',
              'isSyncToIAR',
              'viqId',
              'viqRefNo',
              'departmentId',
              'departmentName',
              'isVerify',
              'isOpen',
              'isUpdatedFinding',
              'picId',
              'picUsername',
              'verifiedInPRId',
              'closedAndVerifiedByROFId',
              'verifiedDate',
              'verifiedUserId',
              'picRemark',
              'findingStatus',
              'workflowStatus',
              'remark',
              'planedCompletionDate',
              'actualCompletionDate',
              'immediateAction',
              'preventiveAction',
              'correctiveAction',
              'iarItemId',
              'auditType',
              'natureFinding',
              'findingAttachments',
            ]),
            findingRemark: item.findingComment,
            reportFindingFormId: formId,
            isPrimaryFinding: (
              natureFindingMap[findingItems[i].natureFindingId] || { isPrimary: false }
            ).isPrimary,
            natureFindingName: (natureFindingMap[item.natureFindingId] || {}).name || null,
            auditTypeName: otherMasterDataMap[`audit_type::${item.auditTypeId}`] || null,
            locationName: otherMasterDataMap[`location::${item.locationId}`] || null,
            mainCategoryName: otherMasterDataMap[`main_category::${item.mainCategoryId}`] || null,
            secondCategoryName:
              otherMasterDataMap[`second_category::${item.secondCategoryId}`] || null,
            thirdCategoryName:
              otherMasterDataMap[`third_category::${item.thirdCategoryId}`] || null,
          });
        }
      }
    }

    return {
      totalFindings,
      totalNonConformity,
      totalObservation,
      preparedFindingItems,
      preparedFindingItemsForFillChecklist,
    };
  }
  async _supportPrepareROFPlanningRequest(manager, formId, pr, user, currYear) {
    const preparedROFPlanningRequest: ROFPlanningRequest = {
      reportFindingFormId: formId,
      planningRequestId: pr.id,
      vesselId: pr.vesselId,
      vesselName: pr.vessel?.name,
      countryId: pr.vessel?.countryId,
      vesselTypeId: pr.vessel?.vesselTypeId,
      vesselTypeName: pr.vessel?.vesselType.name,
      fleetName: pr.vessel?.fleetName,
      departmentId: pr.department?.id,
      departmentName: pr.department?.name,
      auditCompanyId: pr.auditCompany?.id,
      auditCompanyName: pr.auditCompany?.name,
      fromPortId: pr.fromPortId,
      fromPortName: pr.fromPort?.name,
      toPortId: pr.toPortId,
      toPortName: pr.toPort?.name,
    } as ROFPlanningRequest;

    if (pr.departments.length > 0) {
      Object.assign(preparedROFPlanningRequest, {
        departments: pr.departments.map(
          (department) => ({ id: department.id, name: department.name } as Department),
        ),
      });
    }

    // Prepare caches: rof users
    const preparedROFUsers: ROFUser[] = [];
    for (let i = 0; i < pr.auditors.length; i++) {
      preparedROFUsers.push({
        reportFindingFormId: formId,
        userId: pr.auditors[i].id,
        username: pr.auditors[i].username,
        relationship:
          pr.auditors[i].id === pr.leadAuditorId
            ? UserRelationship.LEAD_AUDITOR
            : UserRelationship.AUDITOR,
      } as ROFUser);
    }

    for (let i = 0; i < pr.vessel?.owners.length; i++) {
      preparedROFUsers.push({
        reportFindingFormId: formId,
        userId: pr.vessel.owners[i].id,
        username: pr.vessel.owners[i].username,
        relationship: UserRelationship.VESSEL_MANAGER,
      } as ROFUser);
    }

    // Prepare caches: rof audit type
    const preparedAuditTypes: ROFAuditType[] = [];
    for (let i = 0; i < pr.auditTypes.length; i++) {
      preparedAuditTypes.push({
        reportFindingFormId: formId,
        auditTypeId: pr.auditTypes[i].id,
        auditTypeName: pr.auditTypes[i].name,
      } as ROFAuditType);
    }

    const counterSNo = await manager
      .getCustomRepository(CompanyFeatureVersionRepository)
      .getNextVersion({
        manager,
        companyId: user.companyId,
        feature: FeatureVersionConfig.REPORT_FINDING_FORM_SNO,
        year: Number(currYear),
      });

    const sNo = await this._genReportFindingFormSNo(user.companyId, counterSNo, Number(currYear));

    // General refNo when submit
    const counterRefNo = await manager
      .getCustomRepository(CompanyFeatureVersionRepository)
      .getNextVersion({
        manager,
        companyId: user.companyId,
        feature: FeatureVersionConfig.REPORT_FINDING_FORM_REFNO,
        year: Number(currYear),
      });
    const refNo = await this._genReportFindingFormRefNo(
      user.companyId,
      counterRefNo,
      Number(currYear),
    );
    return {
      preparedROFPlanningRequest,
      preparedROFUsers,
      preparedAuditTypes,
      counterSNo,
      sNo,
      counterRefNo,
      refNo,
    };
  }
  async _supportPrepareFindingForm(
    body,
    formId,
    user,
    pr,
    totalFindings,
    totalNonConformity,
    totalObservation,
  ) {
    const preparedFindingForm = {
      ...omit(body, [
        'reportFindingItems',
        'previousNCFindings',
        'comments',
        'workflowRemark',
        'isSubmit',
        'timezone',
      ]),
      id: formId,
      vesselId: pr.vesselId,
      companyId: user.companyId,
      auditCompanyId: pr.auditCompany?.id,
      departmentId: pr.departmentId,
      entityType: pr.entityType,
      createdUserId: user.id,
      //status: body.isSubmit ? ReportFindingFormStatus.SUBMITTED : ReportFindingFormStatus.DRAFT,
      status: ReportFindingFormStatus.IN_PROGRESS,
      totalFindings,
      totalNonConformity,
      totalObservation,
      leadAuditorId: pr.leadAuditorId,
    };

    if (pr.departments.length > 0) {
      Object.assign(preparedFindingForm, {
        departments: pr.departments.map((department) => ({ id: department.id } as Department)),
      });
    }
    return preparedFindingForm;
  }
  async _migrateReportFindingForm() {
    const metaConfig = await this.connection
      .getRepository(MetaConfig)
      .createQueryBuilder('meta_config')
      .where('meta_config.key = :key', {
        key: CatalogConst.MIGRATE_OLD_FINDING_FORM,
      })
      .getOne();
    // RUNNING ONLY ONE
    if (!metaConfig) {
      return await this.connection.transaction(async (manager) => {
        const queryRaw = `SELECT DISTINCT A.id, A."createdUserId", A."planningRequestId", A."companyId"
        FROM audit_workspace A
        LEFT JOIN report_finding_form B ON A."planningRequestId" = B."planningRequestId"
        LEFT JOIN fill_audit_checklist fac on A.id = fac."auditWorkspaceId"
        LEFT JOIN  fill_audit_checklist_question q on q."fillAuditChecklistId" = fac.id
        WHERE B."id" IS NULL AND A."deleted" = false AND q."createdAt" < q."updatedAt";`;
        const findingWorkspaceItems = await this.query(queryRaw);
        const insertReportFindingForm = [];

        for (const workspace of findingWorkspaceItems) {
          const createdUser = await manager
            .getCustomRepository(UserRepository)
            ._getUserInfoForHistory(workspace.createdUserId);

          const rofItems = await manager
            .getCustomRepository(ReportFindingItemRepository)
            .listFindingItemsByPR(workspace.planningRequestId, createdUser, workspace.companyId);

          for (let i = 0; i < rofItems.length; i++) {
            const rofItem = rofItems[i];
            delete rofItem.auditWorkspaceId;
            delete rofItem.auditType;
            delete rofItem.natureFinding;
          }

          const rofForm: CreateReportFindingFormDto = {
            planningRequestId: workspace.planningRequestId,
            timezone: 'Asia/Ho_Chi_Minh',
            reportFindingItems: rofItems,
            previousNCFindings: [],
            officeComment: '',
            comments: [],
            workflowRemark: '',
            isSubmit: false,
          };

          insertReportFindingForm.push(
            await manager
              .getCustomRepository(ReportFindingFormRepository)
              ._createReportFindingFormHelper(manager, rofForm, createdUser),
          );
          console.log('migrate data processing...');
        }
        await Promise.all(insertReportFindingForm);
        await manager.save(MetaConfig, {
          key: CatalogConst.MIGRATE_OLD_FINDING_FORM,
          lastTimeSync: '2023-07-25T04:20:00.000z',
        });
      });
    }
    return 1;
  }

  async _prepareSAFindingItems(
    user: TokenPayloadModel,
    formId: string,
    planningRequestId: string,
    saFindingItems: any[],
    isClone: boolean,
  ) {
    let totalFindings = 0;
    let totalNonConformity = 0;
    let totalObservation = 0;
    const preparedSAFindingItems = [];

    if (saFindingItems.length > 0) {
      /** Prepared master data mapping (id-info) */
      // prepare nature finding mapping
      const natureFindingMap: { [key: string]: { name: string; isPrimary: boolean } } = {};
      const natureFindingArr = await this.manager
        .getCustomRepository(NatureFindingRepository)
        ._listNatureFindingByIdsWithPR(
          saFindingItems.map((x) => x.natureFindingId),
          planningRequestId,
        );

      natureFindingArr.forEach((natureFinding) => {
        const isPrimary = natureFinding.inspMapNatFinding?.some((m) => m.isPrimaryFinding);
        natureFindingMap[natureFinding.id] = { name: natureFinding.name, isPrimary: !!isPrimary };
      });

      // prepare other master data
      const otherMasterDataMap = await this._getDetailMasterDataByIds([
        {
          tableName: 'audit_type',
          ids: saFindingItems.map((x) => x.auditTypeId).filter((item) => !!item),
        },
      ]);

      for (let i = 0; i < saFindingItems.length; i++) {
        totalFindings += 1;

        if (
          saFindingItems[i].natureFindingId &&
          natureFindingMap[saFindingItems[i].natureFindingId] &&
          natureFindingMap[saFindingItems[i].natureFindingId].name ===
            AppConst.NATURE_FINDING_DEFAULT.NON_CONFORMITY
        ) {
          totalNonConformity += 1;
        }

        // Check is Observation
        if (
          saFindingItems[i].natureFindingId &&
          natureFindingMap[saFindingItems[i].natureFindingId] &&
          natureFindingMap[saFindingItems[i].natureFindingId].name ===
            AppConst.NATURE_FINDING_DEFAULT.OBSERVATION
        ) {
          totalObservation += 1;
        }

        // Prepare finding items
        const item = { ...saFindingItems[i] };
        if (isClone) {
          delete item.id;
        }
        const prepareSAFindingItem = {
          reportFindingFormId: formId,
          selfAssessmentId: item?.selfAssessmentId,
          isPrimaryFinding: (
            natureFindingMap[saFindingItems[i]?.natureFindingId] || { isPrimary: false }
          )?.isPrimary,
          natureFindingId: item?.natureFindingId,
          elementMasterId: item?.elementMasterId,
          natureFindingName: (natureFindingMap[item?.natureFindingId] || {}).name || null,
          auditTypeId: item?.auditTypeId,
          auditTypeName: otherMasterDataMap[`audit_type::${item?.auditTypeId}`] || null,
          auditorComplianceId: item?.complianceId,
          auditorCompliance: item?.auditorCompliance,
          isUpdatedFinding: item?.isUpdatedFinding || false,
          findingRemark: item?.findingRemark,
          companyId: user?.companyId,
          createdUserId: user?.id,
          updatedUserId: user?.id,
        };

        preparedSAFindingItems.push({
          ...item,
          ...prepareSAFindingItem,
        });
      }
    }

    return {
      totalFindings,
      totalNonConformity,
      totalObservation,
      preparedSAFindingItems,
    };
  }

  async _createSAReportFindingFormHelper(
    manager: EntityManager,
    body: CreateReportFindingFormDto,
    user,
    createdUser?: CreatedUserHistoryModel,
    ignoreValidate?: boolean,
    fillChecklistId?: any,
    audtiWorkspaceId?: string,
  ) {
    try {
      // Check and get PR
      const pr = await this.connection
        .getCustomRepository(PlanningRequestRepository)
        ._checkAndGetPROnTrigger(body.planningRequestId, user);

      // Check if submit
      if (body.isSubmit && user.id !== pr.leadAuditorId) {
        throw new BaseError({
          status: 400,
          message: 'reportFindingForm.ONLY_LEADER_AUDITOR_CAN_SUBMIT',
        });
      }
      const currYear = momentTZ.tz(body.timezone).year();
      const formId = Utils.strings.generateUUID();

      // Prepare finding items - use SA specific function if saFindingItems exist
      let totalFindings = 0;
      let totalNonConformity = 0;
      let totalObservation = 0;
      let preparedSAFindingItems = [];

      if (body.saFindingItems && body.saFindingItems.length > 0) {
        // Use SA specific function for SA findings
        const saResult = await this._prepareSAFindingItems(
          user,
          formId,
          body.planningRequestId,
          body.saFindingItems,
          false,
        );

        totalFindings = saResult.totalFindings;
        totalNonConformity = saResult.totalNonConformity;
        totalObservation = saResult.totalObservation;
        // preparedSAFindingItems = saResult.preparedSAFindingItems;
      }

      // Prepare finding form
      const preparedFindingForm = await this._supportPrepareFindingForm(
        body,
        formId,
        user,
        pr,
        totalFindings,
        totalNonConformity,
        totalObservation,
      );

      // Prepare history
      if (!createdUser) {
        createdUser = await this.manager
          .getCustomRepository(UserRepository)
          ._getUserInfoForHistory(user.id);
      }

      const preparedFindingHistory = {
        reportFindingFormId: formId,
        officeComment: body.officeComment,
        workflowRemark: body.workflowRemark,
        status: ReportFindingFormStatus.IN_PROGRESS,
        createdUser,
        leadAuditorId: pr.leadAuditorId,
      };

      // Prepare caches: rof planning request
      const supportPrepareROFPR = await this._supportPrepareROFPlanningRequest(
        manager,
        formId,
        pr,
        user,
        currYear,
      );

      // insert into report finding form table
      const rof = await manager.save(ReportFindingForm, {
        ...preparedFindingForm,
        sNo: supportPrepareROFPR.sNo,
        refNo: supportPrepareROFPR.refNo,
      });

      // if (preparedSAFindingItems.length > 0) {
      //   await manager.save(SAFindingItem, preparedSAFindingItems);
      // }

      // Update previous NC finding items
      if (body.previousNCFindings && body.previousNCFindings.length > 0) {
        await manager.save(SAFindingItem, body.previousNCFindings);
      }

      // Insert into report finding history and caches table
      await Promise.all([
        manager.insert(ReportFindingHistory, preparedFindingHistory),
        manager.insert(ROFPlanningRequest, supportPrepareROFPR.preparedROFPlanningRequest),
        manager.insert(ROFUser, supportPrepareROFPR.preparedROFUsers),
        manager.insert(ROFAuditType, supportPrepareROFPR.preparedAuditTypes),
      ]);

      // Handle add Audit log
      await manager.getCustomRepository(AuditLogRepository).createAuditLog(
        {
          module: AuditModuleEnum.REPORT_OF_FINDING,
          planningId: pr.id,
        },
        null,
        [AuditActivityEnum.CREATED],
        createdUser,
      );

      // Add rof id into internal audit report
      const iar = await manager.getCustomRepository(InternalAuditReportRepository).findOne({
        planningRequestId: rof.planningRequestId,
      });

      if (iar && !iar.reportFindingFormId) {
        await manager.getCustomRepository(InternalAuditReportRepository).update(
          {
            id: iar.id,
          },
          {
            reportFindingFormId: rof.id,
          },
        );
      }

      // Create CARs for self assessment findings if needed
      // if (preparedSAFindingItems.length > 0) {
      await this._triggerCreateSACar(manager, user, audtiWorkspaceId, false);
      // }

      return rof;
    } catch (ex) {
      LoggerCommon.error('[ReportFindingFormRepository] createSAReportFindingForm error', ex);
      if (ex.code === DBErrorCode.UNIQUE_VIOLATION) {
        throw new BaseError({
          status: 400,
          message: 'reportFindingForm.PLANNING_REQUEST_IS_DUPLICATED',
        });
      }
      throw ex;
    }
  }
}
