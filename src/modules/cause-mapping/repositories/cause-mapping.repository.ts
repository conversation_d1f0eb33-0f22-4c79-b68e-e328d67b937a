import { EntityRepository } from 'typeorm';
import { TypeORMRepository, CommonStatus } from 'svm-nest-lib-v3';
import { CauseMapping } from '../entities/cause-mapping.entity';
import { CauseOneMapping } from '../entities/cause-one-mapping.entity';
import { CauseTwoMapping } from '../entities/cause-two-mapping.entity';
import { CreateCauseMappingDto } from '../dto/create-cause-mapping.dto';
import { UpdateCauseMappingDto } from '../dto/update-cause-mapping.dto';

@EntityRepository(CauseMapping)
export class CauseMappingRepository extends TypeORMRepository<CauseMapping> {
  async createCauseMapping(
    dto: CreateCauseMappingDto,
    companyId: string,
    userId: string,
  ): Promise<CauseMapping> {
    return this.manager.transaction(async (manager) => {
      // Find the latest version for this company and mark all existing versions as inactive
      const latestMapping = await manager
        .createQueryBuilder(CauseMapping, 'causeMapping')
        .where('causeMapping.companyId = :companyId', { companyId })
        .andWhere('causeMapping.deleted = :deleted', { deleted: false })
        .orderBy('causeMapping.version', 'DESC')
        .getOne();

      // Set all existing mappings to inactive
      if (latestMapping) {
        await manager
          .createQueryBuilder()
          .update(CauseMapping)
          .set({ status: CommonStatus.IN_ACTIVE })
          .where('companyId = :companyId', { companyId })
          .andWhere('deleted = :deleted', { deleted: false })
          .execute();
      }

      // Create new cause mapping with incremented version
      const newVersion = latestMapping ? latestMapping.version + 1 : 1;
      const causeMapping = manager.create(CauseMapping, {
        version: newVersion,
        status: CommonStatus.ACTIVE,
        companyId,
        createdUserId: userId,
      });

      const savedCauseMapping = await manager.save(causeMapping);

      // Create cause one mappings
      for (const causeOneDto of dto.causeOneMappings) {
        const causeOneMapping = manager.create(CauseOneMapping, {
          cause1Type: causeOneDto.cause1Type,
          mainCategoryId: causeOneDto.mainCategoryId,
          mainCategoryName: causeOneDto.mainCategoryName,
          subCategoryId: causeOneDto.subCategoryId,
          subcategoryName: causeOneDto.subcategoryName,
          causeMappingId: savedCauseMapping.id,
          companyId,
          createdUserId: userId,
        });

        const savedCauseOneMapping = await manager.save(causeOneMapping);

        // Create cause two mappings
        for (const causeTwoDto of causeOneDto.causeTwoMappings) {
          const causeTwoMapping = manager.create(CauseTwoMapping, {
            cause2Type: causeTwoDto.cause2Type,
            mainCategoryId: causeTwoDto.mainCategoryId,
            mainCategoryName: causeTwoDto.mainCategoryName,
            subCategoryId: causeTwoDto.subCategoryId,
            subCategoryName: causeTwoDto.subCategoryName,
            causeOneMappingId: savedCauseOneMapping.id,
            companyId,
            createdUserId: userId,
          });

          await manager.save(causeTwoMapping);
        }
      }

      return this.findOne({
        where: { id: savedCauseMapping.id },
        relations: ['causeOneMappings', 'causeOneMappings.causeTwoMappings'],
      });
    });
  }

  async updateCauseMapping(
    id: string,
    dto: UpdateCauseMappingDto,
    companyId: string,
    userId: string,
  ): Promise<CauseMapping> {
    return this.manager.transaction(async (manager) => {
      const causeMapping = await this.findOne({
        where: { id, companyId, deleted: false },
      });

      if (!causeMapping) {
        return null;
      }

      // Update status
      const { status } = dto;
      causeMapping.status = status;
      causeMapping.updatedUserId = userId;

      // If setting a mapping to ACTIVE, set all others to INACTIVE
      if (status === CommonStatus.ACTIVE) {
        await manager
          .createQueryBuilder()
          .update(CauseMapping)
          .set({ status: CommonStatus.IN_ACTIVE })
          .where('id != :id', { id })
          .andWhere('companyId = :companyId', { companyId })
          .andWhere('deleted = :deleted', { deleted: false })
          .execute();
      }

      await manager.save(causeMapping);

      // Get all existing CauseOneMapping IDs for this CauseMapping
      const existingCauseOneMappings = await manager.find(CauseOneMapping, {
        where: { causeMappingId: id, companyId, deleted: false },
        select: ['id'],
      });
      const existingCauseOneMappingIds = existingCauseOneMappings.map((mapping) => mapping.id);

      // Get all CauseOneMapping IDs from the DTO
      const dtoCauseOneMappingIds = dto.causeOneMappings.map((mapping) => mapping.id);

      // Soft delete CauseOneMapping records that are no longer in the DTO
      const causeOneMappingsToDelete = existingCauseOneMappingIds.filter(
        (id) => !dtoCauseOneMappingIds.includes(id),
      );

      if (causeOneMappingsToDelete.length > 0) {
        await manager
          .createQueryBuilder()
          .update(CauseOneMapping)
          .set({ deleted: true, updatedUserId: userId })
          .where('id IN (:...ids)', { ids: causeOneMappingsToDelete })
          .andWhere('companyId = :companyId', { companyId })
          .execute();

        // Also soft delete associated CauseTwoMapping records
        await manager
          .createQueryBuilder()
          .update(CauseTwoMapping)
          .set({ deleted: true, updatedUserId: userId })
          .where('causeOneMappingId IN (:...ids)', { ids: causeOneMappingsToDelete })
          .andWhere('companyId = :companyId', { companyId })
          .execute();
      }

      // Update cause one mappings
      for (const causeOneDto of dto.causeOneMappings) {
        // Find or create cause one mapping
        let causeOneMapping = await manager.findOne(CauseOneMapping, {
          where: { id: causeOneDto.id, companyId, deleted: false },
        });

        if (causeOneMapping) {
          // Update existing cause one mapping
          causeOneMapping.cause1Type = causeOneDto.cause1Type;
          causeOneMapping.mainCategoryId = causeOneDto.mainCategoryId;
          causeOneMapping.mainCategoryName = causeOneDto.mainCategoryName;
          causeOneMapping.subCategoryId = causeOneDto.subCategoryId;
          causeOneMapping.subcategoryName = causeOneDto.subcategoryName;
          causeOneMapping.updatedUserId = userId;
          await manager.save(causeOneMapping);
        } else {
          // Create new cause one mapping
          causeOneMapping = manager.create(CauseOneMapping, {
            id: causeOneDto.id,
            cause1Type: causeOneDto.cause1Type,
            mainCategoryId: causeOneDto.mainCategoryId,
            mainCategoryName: causeOneDto.mainCategoryName,
            subCategoryId: causeOneDto.subCategoryId,
            subcategoryName: causeOneDto.subcategoryName,
            causeMappingId: id,
            companyId,
            createdUserId: userId,
          });
          await manager.save(causeOneMapping);
        }

        // Get all existing CauseTwoMapping IDs for this CauseOneMapping
        const existingCauseTwoMappings = await manager.find(CauseTwoMapping, {
          where: { causeOneMappingId: causeOneMapping.id, companyId, deleted: false },
          select: ['id'],
        });
        const existingCauseTwoMappingIds = existingCauseTwoMappings.map((mapping) => mapping.id);

        // Get all CauseTwoMapping IDs from the DTO
        const dtoCauseTwoMappingIds = causeOneDto.causeTwoMappings.map((mapping) => mapping.id);

        // Soft delete CauseTwoMapping records that are no longer in the DTO
        const causeTwoMappingsToDelete = existingCauseTwoMappingIds.filter(
          (id) => !dtoCauseTwoMappingIds.includes(id),
        );

        if (causeTwoMappingsToDelete.length > 0) {
          await manager
            .createQueryBuilder()
            .update(CauseTwoMapping)
            .set({ deleted: true, updatedUserId: userId })
            .where('id IN (:...ids)', { ids: causeTwoMappingsToDelete })
            .andWhere('companyId = :companyId', { companyId })
            .execute();
        }

        // Update cause two mappings
        for (const causeTwoDto of causeOneDto.causeTwoMappings) {
          // Find or create cause two mapping
          let causeTwoMapping = await manager.findOne(CauseTwoMapping, {
            where: { id: causeTwoDto.id, companyId, deleted: false },
          });

          if (causeTwoMapping) {
            // Update existing cause two mapping
            causeTwoMapping.cause2Type = causeTwoDto.cause2Type;
            causeTwoMapping.mainCategoryId = causeTwoDto.mainCategoryId;
            causeTwoMapping.mainCategoryName = causeTwoDto.mainCategoryName;
            causeTwoMapping.subCategoryId = causeTwoDto.subCategoryId;
            causeTwoMapping.subCategoryName = causeTwoDto.subCategoryName;
            causeTwoMapping.updatedUserId = userId;
            await manager.save(causeTwoMapping);
          } else {
            // Create new cause two mapping
            causeTwoMapping = manager.create(CauseTwoMapping, {
              id: causeTwoDto.id,
              cause2Type: causeTwoDto.cause2Type,
              mainCategoryId: causeTwoDto.mainCategoryId,
              mainCategoryName: causeTwoDto.mainCategoryName,
              subCategoryId: causeTwoDto.subCategoryId,
              subCategoryName: causeTwoDto.subCategoryName,
              causeOneMappingId: causeOneMapping.id,
              companyId,
              createdUserId: userId,
            });
            await manager.save(causeTwoMapping);
          }
        }
      }

      return this.findOne({
        where: { id },
        relations: ['causeOneMappings', 'causeOneMappings.causeTwoMappings'],
      });
    });
  }

  async findAllCauseMappings(companyId: string, status?: CommonStatus): Promise<CauseMapping[]> {
    const queryBuilder = this.createQueryBuilder('causeMapping')
      .leftJoinAndSelect(
        'causeMapping.causeOneMappings',
        'causeOneMapping',
        'causeOneMapping.deleted = :deleted',
        { deleted: false },
      )
      .leftJoinAndSelect(
        'causeOneMapping.causeTwoMappings',
        'causeTwoMapping',
        'causeTwoMapping.deleted = :deleted',
        { deleted: false },
      )
      .where('causeMapping.companyId = :companyId', { companyId })
      .andWhere('causeMapping.deleted = :deleted', { deleted: false });

    if (status) {
      queryBuilder.andWhere('causeMapping.status = :status', { status });
    }

    return queryBuilder.orderBy('causeMapping.version', 'DESC').getMany();
  }

  async findCauseMappingById(id: string, companyId: string): Promise<CauseMapping> {
    return this.createQueryBuilder('causeMapping')
      .leftJoinAndSelect(
        'causeMapping.causeOneMappings',
        'causeOneMapping',
        'causeOneMapping.deleted = :deleted',
        { deleted: false },
      )
      .leftJoinAndSelect(
        'causeOneMapping.causeTwoMappings',
        'causeTwoMapping',
        'causeTwoMapping.deleted = :deleted',
        { deleted: false },
      )
      .where('causeMapping.id = :id', { id })
      .andWhere('causeMapping.companyId = :companyId', { companyId })
      .andWhere('causeMapping.deleted = :deleted', { deleted: false })
      .getOne();
  }

  async updateCauseMappingStatus(
    id: string,
    status: CommonStatus,
    userId: string,
    companyId: string,
  ): Promise<CauseMapping> {
    return this.manager.transaction(async (manager) => {
      const causeMapping = await this.findOne({
        where: { id, companyId, deleted: false },
      });

      if (!causeMapping) {
        return null;
      }

      // If setting a mapping to ACTIVE, set all others to INACTIVE
      if (status === CommonStatus.ACTIVE) {
        await manager
          .createQueryBuilder()
          .update(CauseMapping)
          .set({ status: CommonStatus.IN_ACTIVE })
          .where('companyId = :companyId', { companyId })
          .andWhere('id != :id', { id })
          .andWhere('deleted = :deleted', { deleted: false })
          .execute();
      }

      causeMapping.status = status;
      causeMapping.updatedUserId = userId;

      return this.save(causeMapping);
    });
  }

  async deleteCauseMapping(id: string, companyId: string): Promise<boolean> {
    return this.manager.transaction(async (manager) => {
      const causeMapping = await manager.findOne(CauseMapping, {
        where: { id, companyId, deleted: false },
      });

      if (!causeMapping) {
        return false;
      }

      causeMapping.deleted = true;
      await manager.save(causeMapping);
      return true;
    });
  }
}
