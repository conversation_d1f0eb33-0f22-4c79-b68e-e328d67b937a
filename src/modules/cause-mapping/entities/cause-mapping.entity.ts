import { <PERSON>tity, Column, OneToMany, ManyToOne, OneToOne } from 'typeorm';
import { CauseOneMapping } from './cause-one-mapping.entity';
import { CommonStatus, IdentifyEntity } from 'svm-nest-lib-v3';
import { User } from '../../user/user.entity';
import { Company } from '../../company/company.entity';
import { IncidentMainCategory } from '../../../modules-qa/incident-investigation/entity/incident-main-category.entity';
import { IncidentInvestigation } from '../../../modules-qa/incident-investigation/entity/incident-investigation.entity';

@Entity('cause_mapping')
export class CauseMapping extends IdentifyEntity {
  @Column({ type: 'int' })
  version: number;

  @Column({ type: 'enum', enum: CommonStatus, default: CommonStatus.ACTIVE })
  status: CommonStatus;

  @Column({ type: 'uuid' })
  public companyId: string;

  @Column({ type: 'uuid' })
  public createdUserId: string;

  @Column({ type: 'uuid', nullable: true })
  public updatedUserId?: string;

  @ManyToOne(() => Company, { onDelete: 'CASCADE' })
  company: Company;

  @ManyToOne(() => User, { onDelete: 'NO ACTION' })
  createdUser: User;

  @ManyToOne(() => User, { onDelete: 'NO ACTION' })
  updatedUser?: User;

  @OneToMany(() => CauseOneMapping, (causeOne) => causeOne.causeMapping)
  causeOneMappings: CauseOneMapping[];

  @OneToMany(
    () => IncidentMainCategory,
    (incidentMainCategory) => incidentMainCategory.causeMapping,
  )
  incidentMainCategory: IncidentMainCategory[];

  @OneToMany(
    () => IncidentInvestigation,
    (incidentInvestigation) => incidentInvestigation.causeMapping,
  )
  incidentInvestigation: IncidentInvestigation[];
}
