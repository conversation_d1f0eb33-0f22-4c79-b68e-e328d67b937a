import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsEnum, IsNotEmpty, IsOptional, IsString, IsUUID, ValidateNested } from 'class-validator';
import { CommonStatus } from 'svm-nest-lib-v3';

class UpdateCauseTwoMappingDto {
  @ApiProperty()
  @IsOptional()
  @IsString()
  id: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  cause2Type: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  mainCategoryId: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  mainCategoryName: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  subCategoryId: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  subCategoryName: string;
}

class UpdateCauseOneMappingDto {
  @ApiProperty()
  @IsOptional()
  @IsString()
  id: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  cause1Type: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  mainCategoryId: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  mainCategoryName: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  subCategoryId: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  subcategoryName: string;

  @ApiProperty({ type: [UpdateCauseTwoMappingDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateCauseTwoMappingDto)
  causeTwoMappings: UpdateCauseTwoMappingDto[];
}

export class UpdateCauseMappingDto {
  @ApiProperty({ enum: CommonStatus })
  @IsNotEmpty()
  @IsEnum(CommonStatus)
  status: CommonStatus;

  @ApiProperty({ type: [UpdateCauseOneMappingDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateCauseOneMappingDto)
  causeOneMappings: UpdateCauseOneMappingDto[];
}
