import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CreateCauseMappingDto } from './dto/create-cause-mapping.dto';
import { CauseMappingRepository } from './repositories/cause-mapping.repository';
import { CauseMapping } from './entities/cause-mapping.entity';
import { TokenPayloadModel, CommonStatus } from 'svm-nest-lib-v3';
import { UpdateCauseMappingStatusDto } from './dto/update-cause-mapping-status.dto';
import { UpdateCauseMappingDto } from './dto/update-cause-mapping.dto';

@Injectable()
export class CauseMappingService {
  constructor(
    @InjectRepository(CauseMappingRepository)
    private readonly causeMappingRepository: CauseMappingRepository,
  ) {}

  async create(
    createCauseMappingDto: CreateCauseMappingDto,
    user: TokenPayloadModel,
  ): Promise<CauseMapping> {
    return this.causeMappingRepository.createCauseMapping(
      createCauseMappingDto,
      user.companyId,
      user.id,
    );
  }

  async findAll(user: TokenPayloadModel, status?: CommonStatus): Promise<CauseMapping[]> {
    return this.causeMappingRepository.findAllCauseMappings(user.companyId, status);
  }

  async findOne(id: string, user: TokenPayloadModel): Promise<CauseMapping> {
    const causeMapping = await this.causeMappingRepository.findCauseMappingById(id, user.companyId);

    if (!causeMapping) {
      throw new NotFoundException(`Cause mapping with ID ${id} not found`);
    }

    return causeMapping;
  }

  async update(
    id: string,
    updateCauseMappingDto: UpdateCauseMappingDto,
    user: TokenPayloadModel,
  ): Promise<CauseMapping> {
    const updatedCauseMapping = await this.causeMappingRepository.updateCauseMapping(
      id,
      updateCauseMappingDto,
      user.companyId,
      user.id,
    );

    if (!updatedCauseMapping) {
      throw new NotFoundException(`Cause mapping with ID ${id} not found`);
    }

    return updatedCauseMapping;
  }

  async updateStatus(
    id: string,
    updateCauseMappingStatusDto: UpdateCauseMappingStatusDto,
    user: TokenPayloadModel,
  ): Promise<CauseMapping> {
    const causeMapping = await this.causeMappingRepository.updateCauseMappingStatus(
      id,
      updateCauseMappingStatusDto.status,
      user.id,
      user.companyId,
    );

    if (!causeMapping) {
      throw new NotFoundException(`Cause mapping with ID ${id} not found`);
    }

    return causeMapping;
  }

  async remove(id: string, user: TokenPayloadModel): Promise<void> {
    const deleted = await this.causeMappingRepository.deleteCauseMapping(id, user.companyId);

    if (!deleted) {
      throw new NotFoundException(`Cause mapping with ID ${id} not found`);
    }
  }
}
