import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsEnum, IsOptional } from 'class-validator';
import { IsGreaterThanOrEqual } from 'svm-nest-lib-v3';
import { ListQueryDto } from '../../../commons/dtos';
import { AuditEntity, AuditTimeTableStatus } from '../../../commons/enums';
import { DataType, FilterField } from 'src/utils';

export class ListAuditTimeTableDto extends ListQueryDto {
  @ApiProperty({
    enum: AuditTimeTableStatus,
    required: false,
    description: 'Status of audit time table',
  })
  @IsOptional()
  @IsEnum(AuditTimeTableStatus)
  status?: string;

  @ApiProperty({
    type: 'string',
    description: 'ISO string',
    example: '2020-10-05T14:48:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsDateString({ strict: true })
  planningFrom?: string;

  @ApiProperty({
    type: 'string',
    description: 'ISO string',
    example: '2023-10-05T14:48:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsDateString({ strict: true })
  @IsGreaterThanOrEqual('planningFrom', { message: 'common.INVALID_DATE_RANGE' })
  planningTo?: string;

  @ApiProperty({ type: String, enum: AuditEntity })
  @IsOptional()
  @IsEnum(AuditEntity)
  entityType?: string;
}

export enum AuditTimeTableFields {
  ENTITY_TYPE = 'entityType',
  VESSEL_NAME = 'vesselName',
  VESSEL_TYPE = 'vesselType',
  COMPANY = 'company',
  OFFICE_NAME = 'officeName',
  AUDIT_TYPE = 'auditType',
  LEAD_AUDITOR_NAME = 'leadAuditorName',
  STATUS = 'status',
  PLANNED_FROM_DATE = 'plannedFromDate',
  PLANNED_TO_DATE = 'plannedToDate',
  VESSEL_FLAG = 'vesselFlag',
  DEPARTMENT = 'department',
  CREATE_COMPANY_NAME = 'createCompanyName',
  REVOKE = 'revoke',
  AUDITS_NO = 'auditSno',
  SNO = 'sNo',
  AUDIT_REF_ID = 'auditRefId',
  REF_NO = 'refNo',
  PLANNED_FROM_DATE_YEAR = 'plannedFromDate_Year',
  PLANNED_FROM_DATE_MONTH = 'plannedFromDate_Month',
  PLANNED_TO_DATE_YEAR = 'plannedToDate_Year',
  PLANNED_TO_DATE_MONTH = 'plannedToDate_Month',
  DOOS = 'doos',
}

export const AUDIT_TIME_TABLE_FILTER_FIELDS: FilterField[] = [
  {
    field: AuditTimeTableFields.ENTITY_TYPE,
    column: '"planningRequest_entityType"',
    type: DataType.TEXT,
  },
  {
    field: AuditTimeTableFields.VESSEL_NAME,
    column: '"vessel_name"',
    type: DataType.TEXT,
  },
  {
    field: AuditTimeTableFields.VESSEL_TYPE,
    column: '"vesselType_name"',
    type: DataType.TEXT,
  },
  {
    field: AuditTimeTableFields.COMPANY,
    column: '"companyVesselDocHolders_name"',
    type: DataType.TEXT,
  },
  {
    field: AuditTimeTableFields.OFFICE_NAME,
    column: '"auditCompany_name"',
    type: DataType.TEXT,
  },
  {
    field: AuditTimeTableFields.AUDIT_TYPE,
    column: '"planningRequest_auditTypesName"',
    type: DataType.TEXT,
  },
  {
    field: AuditTimeTableFields.LEAD_AUDITOR_NAME,
    column: '"leadAuditor_username"',
    type: DataType.TEXT,
  },
  {
    field: AuditTimeTableFields.STATUS,
    column: '"auditTimeTable_status"',
    type: DataType.TEXT,
  },
  {
    field: AuditTimeTableFields.PLANNED_FROM_DATE,
    column: '"planningRequest_plannedFromDate"',
    type: DataType.DATE,
  },
  {
    field: AuditTimeTableFields.PLANNED_TO_DATE,
    column: '"planningRequest_plannedToDate"',
    type: DataType.DATE,
  },
  {
    field: AuditTimeTableFields.VESSEL_FLAG,
    column: '"country_name"',
    type: DataType.TEXT,
  },
  {
    field: AuditTimeTableFields.DEPARTMENT,
    column: '"departmentsName"',
    type: DataType.TEXT,
  },
  {
    field: AuditTimeTableFields.CREATE_COMPANY_NAME,
    column: '"company_name"',
    type: DataType.TEXT,
  },
  {
    field: AuditTimeTableFields.REVOKE,
    column: '"auditTimeTable_revoke"',
    type: DataType.TEXT,
  },
  {
    field: AuditTimeTableFields.AUDITS_NO,
    column: '"planningRequest_auditNo"',
    type: DataType.TEXT,
  },
  {
    field: AuditTimeTableFields.SNO,
    column: '"auditTimeTable_sNo"',
    type: DataType.TEXT,
  },
  {
    field: AuditTimeTableFields.AUDIT_REF_ID,
    column: '"planningRequest_refId"',
    type: DataType.TEXT,
  },
  {
    field: AuditTimeTableFields.REF_NO,
    column: '"auditTimeTable_refNo"',
    type: DataType.TEXT,
  },
  {
    field: AuditTimeTableFields.PLANNED_FROM_DATE_YEAR,
    column: '"planningRequest_plannedFromDate_Year"',
    type: DataType.TEXT,
  },
  {
    field: AuditTimeTableFields.PLANNED_FROM_DATE_MONTH,
    column: '"planningRequest_plannedFromDate_Month"',
    type: DataType.TEXT,
  },
  {
    field: AuditTimeTableFields.PLANNED_TO_DATE_YEAR,
    column: '"planningRequest_plannedToDate_Year"',
    type: DataType.TEXT,
  },
  {
    field: AuditTimeTableFields.PLANNED_TO_DATE_MONTH,
    column: '"planningRequest_plannedToDate_Month"',
    type: DataType.TEXT,
  },
  {
    field: 'voyageType',
    column: '"voyageType_name"',
    type: DataType.TEXT,
  },
  {
    field: AuditTimeTableFields.DOOS,
    column: '"planningRequest_doos"',
    type: DataType.TEXT,
  },
];

export enum TitleAuditTimeTable {
  ENTITY = 'Entity',
  VESSEL_NAME = 'Vessel Name',
  VESSEL_TYPE = 'Vessel Type',
  DOC_HOLDER_COMPANY = 'DOC Holder Company',
  OFFICE_NAME = 'Office Name',
  INSPECTION_TYPE = 'Inspection Type',
  LEAD_INSPECTOR_NAME = 'Lead Inspector Name',
  STATUS = 'Status',
  INSPECTION_PLANNED_FROM = 'Inspection Planned From Date',
  INSPECTION_PLANNED_TO = 'Inspection Planned To Date',
  FLAG = 'Flag',
  DEPARTMENT = 'Department',
  CREATED_BY_COMPANY = 'Created By Company',
  REVOKE = 'Revoke',
  INSPECTION_S_NO = 'Inspection S.No',
  S_NO = 'S.No',
  REF_ID = 'Inspection Ref.ID',
  REF_NO = 'Reference Number',
  INSPECTION_PLANNDED_FROM_YEAR = 'Inspection Planned From - Year',
  INSPECTION_PLANNDED_FROM_MONTH = 'Inspection Planned From - Month',
  INSPECTION_PLANNDED_TO_YEAR = 'Inspection Planned To - Year',
  INSPECTION_PLANNDED_TO_MONTH = 'Inspection Planned To - Month',
}
export const EXPORT_FIELDS_AUDIT_TIME_TABLE = [
  {
    label: TitleAuditTimeTable.ENTITY,
    value: TitleAuditTimeTable.ENTITY,
    default: '-',
  },
  {
    label: TitleAuditTimeTable.VESSEL_NAME,
    value: TitleAuditTimeTable.VESSEL_NAME,
    default: '-',
  },
  {
    label: TitleAuditTimeTable.VESSEL_TYPE,
    value: TitleAuditTimeTable.VESSEL_TYPE,
    default: '-',
  },
  {
    label: TitleAuditTimeTable.DOC_HOLDER_COMPANY,
    value: TitleAuditTimeTable.DOC_HOLDER_COMPANY,
    default: '-',
  },
  {
    label: TitleAuditTimeTable.OFFICE_NAME,
    value: TitleAuditTimeTable.OFFICE_NAME,
    default: '-',
  },
  {
    label: TitleAuditTimeTable.INSPECTION_TYPE,
    value: TitleAuditTimeTable.INSPECTION_TYPE,
    default: '-',
  },
  {
    label: TitleAuditTimeTable.LEAD_INSPECTOR_NAME,
    value: TitleAuditTimeTable.LEAD_INSPECTOR_NAME,
    default: '-',
  },
  {
    label: TitleAuditTimeTable.STATUS,
    value: TitleAuditTimeTable.STATUS,
    default: '-',
  },
  {
    label: TitleAuditTimeTable.INSPECTION_PLANNED_FROM,
    value: TitleAuditTimeTable.INSPECTION_PLANNED_FROM,
    default: '-',
  },
  {
    label: TitleAuditTimeTable.INSPECTION_PLANNED_TO,
    value: TitleAuditTimeTable.INSPECTION_PLANNED_TO,
    default: '-',
  },
  {
    label: TitleAuditTimeTable.FLAG,
    value: TitleAuditTimeTable.FLAG,
    default: '-',
  },
  {
    label: TitleAuditTimeTable.DEPARTMENT,
    value: TitleAuditTimeTable.DEPARTMENT,
    default: '-',
  },
  {
    label: TitleAuditTimeTable.CREATED_BY_COMPANY,
    value: TitleAuditTimeTable.CREATED_BY_COMPANY,
    default: '-',
  },
  {
    label: TitleAuditTimeTable.REVOKE,
    value: TitleAuditTimeTable.REVOKE,
    default: '-',
  },
  {
    label: TitleAuditTimeTable.INSPECTION_S_NO,
    value: TitleAuditTimeTable.INSPECTION_S_NO,
    default: '-',
  },
  {
    label: TitleAuditTimeTable.S_NO,
    value: TitleAuditTimeTable.S_NO,
    default: '-',
  },
  {
    label: TitleAuditTimeTable.REF_ID,
    value: TitleAuditTimeTable.REF_ID,
    default: '-',
  },
  {
    label: TitleAuditTimeTable.REF_NO,
    value: TitleAuditTimeTable.REF_NO,
    default: '-',
  },
  {
    label: TitleAuditTimeTable.INSPECTION_PLANNDED_FROM_YEAR,
    value: TitleAuditTimeTable.INSPECTION_PLANNDED_FROM_MONTH,
    default: '-',
  },
  {
    label: TitleAuditTimeTable.INSPECTION_PLANNDED_TO_YEAR,
    value: TitleAuditTimeTable.INSPECTION_PLANNDED_TO_YEAR,
    default: '-',
  },
  {
    label: TitleAuditTimeTable.INSPECTION_PLANNDED_TO_MONTH,
    value: TitleAuditTimeTable.INSPECTION_PLANNDED_TO_MONTH,
    default: '-',
  },
];
