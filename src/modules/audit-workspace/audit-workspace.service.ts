import { BadRequestException, Injectable } from '@nestjs/common';
import ExcelJS from 'exceljs';
import pdf from 'html-pdf';
import { uniqBy } from 'lodash';
import { BaseError, TokenPayloadModel } from 'svm-nest-lib-v3';
import { TimezoneDTO } from '../../commons/dtos';
import { UploadDTO } from '../../commons/dtos/upload.dto';
import { Parser } from 'json2csv';
import {
  AuditTimeTableStatus,
  CapStatusEnum,
  CarStatusEnum,
  CarVerificationStatusEnum,
  DeviceUploadEnum,
  FillAuditChecklistStatus,
  InternalAuditReportStatus,
  ReportFindingFormStatus,
  WorkflowType,
} from '../../commons/enums';
import { EmailProducer } from '../../micro-services/async/email.producer';
import { NotificationProducer } from '../../micro-services/async/notification.producer';
import { SVMSupportService } from '../../micro-services/sync/svm-support.service';
import { compile, createPdf, createPdfROF, makePDF } from '../../utils/generate-pdf/create-pdf';
import { ChkQuestionRepository } from '../audit-checklist/repository/chk-question.repository';
import { CompanyRepository } from '../company/company.repository';
import { CAPRepository } from '../corrective-action-request/repositories/cap.repository';
import { UpdatedFileHistory } from '../rof-file-history/entities/updated-file-history.entity';
import { SignedFileHistoryRepository } from '../rof-file-history/repositories/signed-file-history.repository';
import { UpdatedFileHistoryRepository } from '../rof-file-history/repositories/updated-file-history.repository';
import {
  CreateAuditWorkspaceDto,
  ListFillChecklistDetailDto,
  UpdateShowPopupAnalyticalReportDto,
} from './dto';
import {
  EXPORT_FIELDS_AUDIT_WORKSPACE,
  ListAuditWorkspaceDto,
  TitleAuditWorkspace,
} from './dto/list-audit-workspace.dto';
import { UpdateMasterOrChiefOfEngineerDto } from './dto/master-or-chief-of-engineer.dto';
import { SignSignatureDto } from './dto/sign-signature.dto';
import { FillChecklistQuickAnswersDto } from './dto/update-fill-checklist-quick-answer.dto';
import { UpdateFillAuditChecklistDto } from './dto/update-fill-question.dto';
import { UpdateReportFindingDto } from './dto/update-report-finding.dto';
import { AuditWorkspaceRepository } from './repositories/audit-workspace.repository';
import { FillAuditChecklistQuestionRepository } from './repositories/fill-audit-checklist-question.repository';
import { FillAuditChecklistRepository } from './repositories/fill-audit-checklist.repository';
import {
  OnboardFindingItemRepository,
  ReportFindingItemRepository,
} from './repositories/report-finding-item.repository';
import { SVMIAMService } from '../../micro-services/sync/svm-iam.service';
import { PayloadAGGridDto } from 'src/utils';
import { Response } from 'express';
import { AppConst } from 'src/commons/consts/app.const';
import { Readable } from 'stream';
import puppeteer from 'puppeteer';
import fsExtra from 'fs-extra';
import path from 'path';
import fs from 'fs';
import axios from 'axios';
import archiver from 'archiver';
import { AuditTimeTableRepository } from '../audit-time-table/repositories/audit-time-table.repository';
import { tz } from 'moment-timezone';
import { AuditTimeTableService } from '../audit-time-table/audit-time-table.service';

@Injectable()
export class AuditWorkspaceService {
  constructor(
    private readonly auditWorkRepo: AuditWorkspaceRepository,
    private readonly fillAuditChecklistRepo: FillAuditChecklistRepository,
    private readonly fillAuditChecklistQuestionRepo: FillAuditChecklistQuestionRepository,
    private readonly reportFindingItemRepository: ReportFindingItemRepository,
    private readonly onboardFindingRepository: OnboardFindingItemRepository,
    private readonly svmSupportService: SVMSupportService,
    private readonly companyRepository: CompanyRepository,
    private readonly chkQuestionRepository: ChkQuestionRepository,
    private readonly notificationProducer: NotificationProducer,
    private readonly emailProducer: EmailProducer,
    private readonly signedFileHistoryRepository: SignedFileHistoryRepository,
    private readonly updatedFileHistoryRepository: UpdatedFileHistoryRepository,
    private readonly capRepository: CAPRepository,
    private readonly svmIAMService: SVMIAMService,
    private readonly auditTimeTableRepository: AuditTimeTableRepository,
    private readonly auditTimeTableService: AuditTimeTableService,
  ) {
    this.auditWorkRepo._migrateSubmittedDateAuditWorspace();
    // this.fillAuditChecklistQuestionRepo._migrateSyncAttachmentToFindingAndCar();
  }

  async listAuditWorkspace(
    query: ListAuditWorkspaceDto,
    user: TokenPayloadModel,
    body?: PayloadAGGridDto,
  ) {
    return await this.auditWorkRepo.listAuditWorkspace(query, user, body);
  }

  async getDetailAuditWorkspaceGeneral(workspaceId: string, user: TokenPayloadModel) {
    const workSpace = await this.auditWorkRepo.getWorkspaceGeneral(workspaceId, user);

    if (workSpace.attachments.length > 0) {
      const attachments = await this.svmSupportService.getDetailImage(workSpace.attachments);
      Object.assign(workSpace.attachments, attachments);
    }

    return workSpace;
  }

  async addSignedROf(body: UploadDTO, workspaceId: string, user: TokenPayloadModel) {
    try {
      const detailWorkspace = await this.auditWorkRepo.findOne({
        where: {
          id: workspaceId,
          companyId: user.companyId,
        },
        select: ['id', 'isGenerateROF'],
      });

      if (!detailWorkspace) {
        throw new BaseError({ status: 400, message: 'workspace.NOT_FOUND' });
      }
      const preparedSignedFile = {
        signedFile: body.signedFileId,
        deviceUpload: body?.deviceUpload || DeviceUploadEnum.WEB,
        auditWorkspaceId: detailWorkspace.id,
        afterGenerated: detailWorkspace.isGenerateROF,
      };

      if (body.deviceUpload === DeviceUploadEnum.MOBILE) {
        await this.auditWorkRepo.update(
          {
            id: workspaceId,
          },
          {
            isGenerateROF: true,
            submittedDate: new Date(),
            submittedDate_Month: new Date().toLocaleString('en-US', {
              month: 'short',
            }),
            submittedDate_Year: new Date().getFullYear(),
          },
        );
      }

      // await this.auditWorkRepo.update(
      //   {
      //     id: detailWorkspace.id,
      //   },
      //   {
      //     signedFile: body.signedFileId,
      //     originSignedFile: body.signedFileId,
      //     deviceUpload: DeviceUploadEnum.WEB,
      //   },
      // );
      await this.signedFileHistoryRepository.save(preparedSignedFile);

      return 'success';
    } catch (ex) {
      throw ex;
    }
  }

  async listSignedFiles(workspaceId: string, token: TokenPayloadModel) {
    const detailWorkspace = await this.auditWorkRepo.findOne({
      where: {
        id: workspaceId,
        companyId: token.companyId,
      },
    });
    if (!detailWorkspace) {
      throw new BaseError({ status: 400, message: 'workspace.NOT_FOUND' });
    }
    const [signedFileHistories, updatedFileHistories] = await Promise.all([
      this.signedFileHistoryRepository.find({
        where: {
          auditWorkspaceId: detailWorkspace.id,
          deleted: false,
        },
      }),
      this.updatedFileHistoryRepository.find({
        where: {
          auditWorkspaceId: detailWorkspace.id,
          deleted: false,
        },
      }),
    ]);

    const signedFileIds = signedFileHistories.map((file) => file.signedFile);
    const updatedFileIds = updatedFileHistories.map((file) => file.updatedFile);
    let rsSignedFile = null;
    let rsUpdatedFile = null;
    let mapDataSignedFile = null;
    let mapDataUpdatedFile = null;
    if (signedFileIds.length > 0) {
      rsSignedFile = await this.svmSupportService.getDetailImage(signedFileIds);
      mapDataSignedFile = rsSignedFile.map((data, index) => {
        return {
          ...data,
          afterGenerated: signedFileHistories[index].afterGenerated,
          deviceUpload: signedFileHistories[index].deviceUpload,
        };
      });
      mapDataSignedFile.sort(
        (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
      );
    }

    if (updatedFileIds.length > 0) {
      rsUpdatedFile = await this.svmSupportService.getDetailImage(updatedFileIds);
      mapDataUpdatedFile = rsUpdatedFile.map((data, index) => {
        return {
          ...data,
          afterGenerated: updatedFileHistories[index].afterGenerated,
          deviceUpload: updatedFileHistories[index].deviceUpload,
        };
      });
      mapDataUpdatedFile.sort(
        (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
      );
    }

    return {
      dataSignedFile: rsSignedFile ? mapDataSignedFile : [],
      dataUpdatedFile: rsUpdatedFile ? mapDataUpdatedFile : [],
    };
  }

  async createUpdatedFilePdf(workspaceId: string, user: TokenPayloadModel) {
    const rs = await this.fillAuditChecklistQuestionRepo.getFillCkListDataForUpdatedROFRender(
      workspaceId,
      user,
    );
    const logoId = await this.companyRepository.getCompanyLogoId(user.companyId);
    let logoLink = '';
    if (logoId) {
      const data: any = await this.svmSupportService.getDetailImage([logoId]);
      logoLink = data[0].link;
    }

    const { nameInspectors, isGenerateROF } = rs.generalInfor;
    const jobTittles = ['Master', 'Chief Engineer', 'Chief Officer', 'First Assistant Engineer'];
    for (let i = 0; i < nameInspectors.length; i++) {
      jobTittles.push(`Inspector: ${nameInspectors[i]}`);
    }
    const dataTransfer = {
      ...rs,
      logoLink,
      jobTittles,
    };

    const htmlContent = await compile('generated-rof', dataTransfer);

    // Add CSS styles to the page
    const contentStyle = `
        @page {
          margin: 0; /* Remove default margin */
          margin-top: 50px;
          margin-bottom: 30px;
          border: 0;
          footer: {
            height: 10mm; /* Reduced footer height */
            content: element(footer)
          }
        }
        
        @page:first { margin-top: 0; }

        footer {
          text-align: center;
          margin-bottom: 1rem;
          font-size: 12px;
        }
      `;

    const buffer = await makePDF(htmlContent, contentStyle);

    // Example usage
    // const buffer = await createPdfBufferPromise();
    const base64Pdf = [];
    const base64String = Buffer.from(buffer as Buffer).toString('base64');
    base64Pdf.push(`data:application/pdf;base64,` + base64String);

    // //upload file to s3
    const uploadPdfFiles: any = await this.svmSupportService.uploadImageBase64(base64Pdf, user.id, [
      `Finding Management ${rs.generalInfor.auditNo}`,
    ]);

    await this.updatedFileHistoryRepository.save({
      updatedFile: uploadPdfFiles[0].id,
      deviceUpload: DeviceUploadEnum.WEB,
      auditWorkspaceId: workspaceId,
      afterGenerated: isGenerateROF,
    } as UpdatedFileHistory);

    return uploadPdfFiles[0];
  }

  async createGenerateROFFilePdf(workspaceId: string, user: TokenPayloadModel, res, body) {
    const rs = await this.fillAuditChecklistQuestionRepo.getFillCkListDataForUpdatedROFRender(
      workspaceId,
      user,
    );
    if (!rs?.data?.length) throw new BadRequestException('No Findings Found');
    // for difference color with default when generate rof
    // const preparedFindingItem = [];
    // for (let i = 0; i < rs.data.length; i++) {
    //   if (rs.data[i].chkQuestion === null) {
    //     preparedFindingItem.push({ id: rs.data[i].findingId, isUpdatedFinding: true });
    //   }
    // }
    // await this.reportFindingItemRepository.save(preparedFindingItem);

    const logoId = await this.companyRepository.getCompanyLogoId(user.companyId);
    let logoLink = '';
    if (logoId) {
      const data: any = await this.svmSupportService.getDetailImage([logoId]);
      logoLink = data[0].link;
    }
    const { nameInspectors } = rs.generalInfor;
    const jobTittles = ['Master', 'Chief Engineer', 'Chief Officer', 'First Assistant Engineer'];
    for (let i = 0; i < nameInspectors.length; i++) {
      jobTittles.push(`Inspector: ${nameInspectors[i]}`);
    }

    const dataTransfer = {
      ...rs,
      logoLink,
      jobTittles,
    };
    await this.fillAuditChecklistQuestionRepo.carGeneration(workspaceId, user); // for car generation
    if (body?.actualFrom && body?.actualTo) {
      const haveTimeTable = await this.auditTimeTableRepository.getDetailAuditTimeTableByPlanId(
        rs?.workspace?.planningRequest?.id,
      );
      if (!haveTimeTable) {
        const preparedTimeTable = {
          planningRequestId: rs?.workspace?.planningRequest?.id,
          actualFrom: body?.actualFrom,
          actualTo: body?.actualTo,
          timezone: tz.guess(),
          isSubmit: true,
          calendars: [],
        };
        await this.auditTimeTableService.createAuditTimeTable(user, preparedTimeTable);
      } else {
        await this;
        const preparedTimeTable = {
          planningRequestId: rs?.workspace?.planningRequest?.id,
          actualFrom: body?.actualFrom,
          actualTo: body?.actualTo,
          timezone: tz.guess(),
          calendars: [],
          status:
            haveTimeTable?.status === AuditTimeTableStatus.DRAFT
              ? AuditTimeTableStatus.SUBMITTED
              : haveTimeTable?.status,
        };
        await this.auditTimeTableService.updateAuditTimeTable(
          user,
          haveTimeTable?.id,
          preparedTimeTable,
        );
      }
    }
    await this.auditWorkRepo.update(
      {
        id: workspaceId,
      },
      {
        isGenerateROF: true,
        submittedDate: new Date(),
        submittedDate_Month: new Date().toLocaleString('en-US', {
          month: 'short',
        }),
        submittedDate_Year: new Date().getFullYear(),
      },
    );

    return await createPdfROF(
      'generated-rof',
      `Finding Management ${rs.generalInfor.auditNo}`,
      dataTransfer,
      res,
    );
  }

  async handleSignSignature(workspaceId: string, user: TokenPayloadModel, body: SignSignatureDto) {
    const signatureFiles = await this.svmSupportService.getDetailImage(body.ids);

    const rs = await this.fillAuditChecklistQuestionRepo.getFillCkListDataForUpdatedROFRender(
      workspaceId,
      user,
    );

    const logoId = await this.companyRepository.getCompanyLogoId(user.companyId);
    let logoLink = '';
    if (logoId) {
      const data: any = await this.svmSupportService.getDetailImage([logoId]);
      logoLink = data[0].link;
    }
    const dataTransfer = {
      ...rs,
      logoLink,
      signatureFiles,
      jobTittles: body.jobTittles,
    };

    if (body?.actualFrom && body?.actualTo) {
      const haveTimeTable = await this.auditTimeTableRepository.getDetailAuditTimeTableByPlanId(
        rs?.workspace?.planningRequest?.id,
      );
      if (!haveTimeTable) {
        const preparedTimeTable = {
          planningRequestId: rs?.workspace?.planningRequest?.id,
          actualFrom: body?.actualFrom,
          actualTo: body?.actualTo,
          timezone: tz.guess(),
          isSubmit: true,
          calenders: [],
        };
        await this.auditTimeTableService.createAuditTimeTable(user, preparedTimeTable);
      } else {
        await this;
        const preparedTimeTable = {
          planningRequestId: rs?.workspace?.planningRequest?.id,
          actualFrom: body?.actualFrom,
          actualTo: body?.actualTo,
          timezone: tz.guess(),
          calendars: [],
          status:
            haveTimeTable?.status === AuditTimeTableStatus.DRAFT
              ? AuditTimeTableStatus.SUBMITTED
              : haveTimeTable?.status,
        };
        await this.auditTimeTableService.updateAuditTimeTable(
          user,
          haveTimeTable?.id,
          preparedTimeTable,
        );
      }
    }

    const htmlContent = await compile('updated-rof-template', dataTransfer);

    // Add CSS styles to the page
    const contentStyle = `
        @page {
          margin: 0; /* Remove default margin */
          margin-top: 50px;
          margin-bottom: 30px;
          border: 0;
          footer: {
            height: 30mm;
            content: element(footer)
          }
        }
        
        @page:first { margin-top: 0; }

        footer {
          text-align: center;
          margin-bottom: 1rem;
          font-size: 12px;
        }
      `;

    const buffer = await makePDF(htmlContent, contentStyle, null, dataTransfer, true);

    // Example usage
    // const buffer = await createPdfBufferPromise();
    const base64Pdf = [];
    const base64String = Buffer.from(buffer as Buffer).toString('base64');
    base64Pdf.push(`data:application/pdf;base64,` + base64String);

    const uploadPdfFiles: any = await this.svmSupportService.uploadAdvanceImageBase64(
      base64Pdf,
      user.id,
      [`Finding Management ${rs.generalInfor.auditNo}`],
    );

    await this.auditWorkRepo.update(
      {
        id: workspaceId,
      },
      {
        isGenerateROF: true,
        submittedDate: new Date(),
        submittedDate_Month: new Date().toLocaleString('en-US', {
          month: 'short',
        }),
        submittedDate_Year: new Date().getFullYear(),
      },
    );
    await this.fillAuditChecklistQuestionRepo.carGeneration(workspaceId, user); // for car generation
    await this.signedFileHistoryRepository.save({
      signedFile: uploadPdfFiles[0].id,
      auditWorkspaceId: workspaceId,
      deviceUpload: DeviceUploadEnum.WEB,
    });
    return uploadPdfFiles[0];
  }

  async getSummaryData(workspaceId: string, user: TokenPayloadModel) {
    const data = await this.auditWorkRepo.getSummaryData(workspaceId, user);
    const entityInfo = {
      vesselId: data.vessel?.id,
      vesselName: data.vessel?.name,
      vesselType: data.vessel?.vesselType?.name,
      vesselBuildOn: data.vessel?.buildDate,
      // shipManagerName: data.vessel?.shipManagerName,
      departmentName: data.planningRequest.department?.name,
      departmentId: data.planningRequest.department?.id,
      companyId: data.planningRequest.companyId,
    };

    const schedulerInfo = {
      planningRequestId: data.planningRequest.id,
      workingType: data.planningRequest.workingType,
      visitType: data.planningRequest.typeOfAudit,
      inspectorName: data.planningRequest.auditors.map((user) => user.username),
      plannedFrom: data.planningRequest.plannedFromDate,
      plannedTo: data.planningRequest.plannedToDate,
      portFrom: data.planningRequest.fromPort?.name,
      portTo: data.planningRequest.toPort?.name,
      memo: data.planningRequest.memo,
    };

    const iarHistories =
      data?.planningRequest?.internalAuditReport?.internalAuditReportHistories || [];
    const rofHistories = data?.planningRequest?.reportFindingForm?.reportFindingHistories || [];

    const dateOf1stSubmission = iarHistories.find(
      (history) => history.status == InternalAuditReportStatus.SUBMITTED,
    )?.createdAt;

    const dateOfReportApproval = iarHistories.find(
      (history) => history.status == InternalAuditReportStatus.APPROVED,
    )?.createdAt;

    const dateOfReportDispatched = rofHistories.find(
      (history) => history.status == ReportFindingFormStatus.SUBMITTED,
    )?.createdAt;

    const inspectionReport = {
      iarId: data?.planningRequest?.internalAuditReport?.id,
      status: data?.planningRequest?.internalAuditReport?.status,
      dateOfInitiatingReport: data.planningRequest.auditTimeTable?.actualFrom,
      dateOf1stSubmission,
      dateOfReportApproval,
      dateOfReportDispatched,
    };

    const carIds = data.planningRequest.cars?.map((car) => car.id);
    const capReceivedDate = !carIds.length
      ? null
      : await this.capRepository.getSubmitedDateByCarIds(carIds);
    const auditTypes = uniqBy(
      data.planningRequest.reportFindingForm?.reportFindingItems,
      function (item) {
        return item.auditType?.name;
      },
    )
      .filter((i) => i.auditType)
      .map((i) => i.auditType.name);

    const workflowSteps: any = await this.svmIAMService.listStepWorkflowTypePermission(
      user.companyId,
      {
        workflowType: WorkflowType.CORRECTIVE_ACTION_REQUEST,
      },
    );

    const capAcceptedStatuses: string[] = [
      CapStatusEnum.REVIEWED_1,
      CapStatusEnum.REVIEWED_2,
      CapStatusEnum.REVIEWED_3,
      CapStatusEnum.REVIEWED_4,
      CapStatusEnum.REVIEWED_5,
    ];

    const allSteps = [];
    workflowSteps
      .map((wf) => wf.wfrPermission)
      .forEach((step) => {
        if (step && step.startsWith('reviewer')) {
          allSteps.push(Number(step.slice(-1)));
        }
      });
    const lastStep = allSteps.slice(-1);
    const acceptedStatus = capAcceptedStatuses.filter((status) => status.slice(-1) == lastStep[0]);

    const reportOfFinding = {
      followUpId: data.planningRequest.followUp?.id,
      rofId: data.planningRequest.reportFindingForm?.id,
      status: data.planningRequest.reportFindingForm?.status,
      totalNoOfFindings: data.planningRequest.reportFindingForm?.reportFindingItems?.length,
      totalCar: data.planningRequest.cars?.length,
      totalCap: data.planningRequest.cars?.filter((car) => car.cap).length,
      totalOpenCar: data.planningRequest.cars?.filter((car) => car.status == CarStatusEnum.OPEN)
        .length,
      totalCloseCar: data.planningRequest.cars?.filter((car) => car.status == CarStatusEnum.CLOSED)
        .length,
      totalDeniedCar: data.planningRequest.cars?.filter(
        (car) => car.cap?.status == CapStatusEnum.DENIED,
      ).length,
      totalAcceptCar: data.planningRequest.cars?.filter(
        (car) => car.cap?.status == acceptedStatus[0],
      ).length,
      verificationNeededCar: data.planningRequest.cars?.filter(
        (car) => car.cARVerification?.isNeeded == true,
      ).length,
      pendingVerificationCar: data.planningRequest.cars?.filter(
        (car) => car.cARVerification?.status == CarVerificationStatusEnum.PENDING,
      ).length,
      caIssuedDate: data.planningRequest.cars[0]?.createdAt,
      capReceivedDate,
      auditTypes,
    };

    return { vesselInfo: entityInfo, schedulerInfo, inspectionReport, reportOfFinding };
  }
  async listFillChecklist(workspaceId: string, user: TokenPayloadModel) {
    return await this.fillAuditChecklistRepo.listFillChecklistByWorkspace(workspaceId, user);
  }

  _getContentAnswer(item) {
    return item.value?.number;
  }
  async exportAuditWorkSpace(
    query: ListAuditWorkspaceDto,
    token: TokenPayloadModel,
    body: PayloadAGGridDto,
    res: Response,
  ) {
    const { planningFrom, planningTo } = query;
    if ((!planningFrom && planningTo) || (planningFrom && !planningTo)) {
      throw new BadRequestException('planningFrom and planningTo must exist or not');
    }
    const listAWs = await this.auditWorkRepo.listAuditWorkspace(query, token, body);

    const fileType = query?.fileType;
    const defaultValue = [];
    if (listAWs?.data?.length) {
      listAWs?.data.forEach((value) => {
        const {
          vessel,
          company,
          refNo,
          departments,
          auditTypesName,
          inspectionCarriedIn,
          mobileInspectionStartDate,
          mobileInspectionEndDate,
          switchStatus,
          createdAt,
          createdUser,
          updatedAt,
          updatedUser,
          submittedDate,
          submittedDateMonth,
          submittedDateYear,
          planningRequest,
        } = value;
        defaultValue.push({
          [TitleAuditWorkspace.VESSEL_NAME]: vessel?.name || '',
          [TitleAuditWorkspace.VESSEL_TYPE]: vessel?.vesselType?.name || '',
          [TitleAuditWorkspace.COMPANY]: vessel?.vesselDocHolders?.length
            ? vessel.vesselDocHolders[0].company.name
            : '',
          [TitleAuditWorkspace.INSPECTION_TYPE]: auditTypesName || '',
          [TitleAuditWorkspace.INSPECTION_CARRIED_IN]: inspectionCarriedIn || '',
          [TitleAuditWorkspace.INSPECTION_PLANNED_FROM]: planningRequest?.plannedFromDate || '',
          [TitleAuditWorkspace.INSPECTION_PLANNED_TO]: planningRequest?.plannedToDate || '',
          [TitleAuditWorkspace.GLOBAL_STATUS]: planningRequest?.globalStatus || '',
          [TitleAuditWorkspace.ENTITY]: planningRequest?.entityType || '',
          [TitleAuditWorkspace.FLAG]: vessel?.country?.name || '',
          [TitleAuditWorkspace.CREATED_DATE]: createdAt || '',
          [TitleAuditWorkspace.CREATED_BY_USER]: createdUser?.username || '',
          [TitleAuditWorkspace.UPDATED_DATE]: updatedAt || '',
          [TitleAuditWorkspace.UPDATED_BY_USER]: updatedUser?.username || '',
          [TitleAuditWorkspace.SUBMISSION_DATE]: submittedDate || '',
          [TitleAuditWorkspace.SWICH_STATUS]: switchStatus || '',
          [TitleAuditWorkspace.DEPARTMENT]: departments?.name || '',
          [TitleAuditWorkspace.CREATED_BY_COMPANY]: company?.name || '',
          [TitleAuditWorkspace.INSPECTION_START_DATE]: mobileInspectionStartDate || '',
          [TitleAuditWorkspace.INSPECTION_END_DATE]: mobileInspectionEndDate || '',
          [TitleAuditWorkspace.INSPECTION_S_NO]: planningRequest?.auditNo || '',
          [TitleAuditWorkspace.REF_ID]: refNo || '',
          [TitleAuditWorkspace.SUBMITTED_DATE_MONTH]: submittedDateMonth || '',
          [TitleAuditWorkspace.SUBMITTED_DATE_YEAR]: submittedDateYear || '',
        });
      });
    }
    if (fileType === 'xlsx') {
      const rs = this.downloadResourceExcel(res, 'Audit-Workspace', defaultValue);
      return rs.xlsx.write(res).then(function () {
        res.status(200).end();
      });
    } else {
      return this.downloadResource(
        res,
        'Audit-Workspace.csv',
        EXPORT_FIELDS_AUDIT_WORKSPACE,
        defaultValue,
      );
    }
  }

  private downloadResource(res: Response, fileName: string, fields, data) {
    const json2csv = new Parser({ fields });
    const csv = json2csv.parse(data);
    res.header('Content-Type', 'text/csv');
    res.attachment(fileName);
    const stream = this.bufferToStream(csv); // convert buffer to stream
    stream.pipe(res);
  }

  private bufferToStream(binary) {
    const readableInstanceStream = new Readable({
      read() {
        this.push(binary);
        this.push(null);
      },
    });
    return readableInstanceStream;
  }

  private downloadResourceExcel(res, fileName: string, data: any[]) {
    try {
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Sheet1');

      worksheet.addRow([
        TitleAuditWorkspace.VESSEL_NAME,
        TitleAuditWorkspace.VESSEL_TYPE,
        TitleAuditWorkspace.COMPANY,
        TitleAuditWorkspace.INSPECTION_TYPE,
        TitleAuditWorkspace.INSPECTION_CARRIED_IN,
        TitleAuditWorkspace.INSPECTION_PLANNED_FROM,
        TitleAuditWorkspace.INSPECTION_PLANNED_TO,
        TitleAuditWorkspace.GLOBAL_STATUS,
        TitleAuditWorkspace.ENTITY,
        TitleAuditWorkspace.FLAG,
        TitleAuditWorkspace.CREATED_DATE,
        TitleAuditWorkspace.CREATED_BY_USER,
        TitleAuditWorkspace.UPDATED_DATE,
        TitleAuditWorkspace.UPDATED_BY_USER,
        TitleAuditWorkspace.SUBMISSION_DATE,
        TitleAuditWorkspace.SWICH_STATUS,
        TitleAuditWorkspace.DEPARTMENT,
        TitleAuditWorkspace.CREATED_BY_COMPANY,
        TitleAuditWorkspace.INSPECTION_START_DATE,
        TitleAuditWorkspace.INSPECTION_END_DATE,
        TitleAuditWorkspace.INSPECTION_S_NO,
        TitleAuditWorkspace.REF_ID,
        TitleAuditWorkspace.SUBMITTED_DATE_MONTH,
        TitleAuditWorkspace.SUBMITTED_DATE_YEAR,
      ]);
      data.forEach((value) => {
        worksheet.addRow(Object.values(value));
      });

      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
      res.setHeader('Content-Disposition', 'attachment; filename=' + `${fileName}.xlsx`);
      return workbook;
    } catch (error) {
      throw new BadRequestException(error);
    }
  }

  async exportExcel(workspaceId: string, user: TokenPayloadModel) {
    const auditCheckLists = await this.fillAuditChecklistRepo.listFillChecklistForExport(
      workspaceId,
    );

    const auditWorkSpace = await this.getDetailAuditWorkspaceGeneral(workspaceId, user);

    const planAuditNo = auditWorkSpace.planningRequest.auditNo;

    const fileName = `Analysis Report ${planAuditNo}`;
    // init excel
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Audit CheckList', {
      properties: { defaultColWidth: 25, defaultRowHeight: 30 },
    });
    // Add row using key mapping to columns
    const arrItemData = [];
    let sNo = 1;
    for (let i = 0; i < auditCheckLists.length; i++) {
      for (let j = 0; j < auditCheckLists[i].fillQuestions.length; j++) {
        const fillQuestion = auditCheckLists[i].fillQuestions[j];
        const reportFindingItem: any = fillQuestion?.reportFindingItem;
        const answers = auditCheckLists[i].fillQuestions[j].answers;

        let refCategory;
        //get ref category
        if (fillQuestion) {
          refCategory = await this.chkQuestionRepository.detailQuestionRefCategory(
            fillQuestion.chkQuestion.id,
          );
        }

        const itemData = [sNo, fillQuestion.chkQuestion.code, fillQuestion.chkQuestion.question];

        //get list answers
        if (!answers || answers.length === 0) {
          itemData.push(null);
        } else {
          for (let k = 0; k < auditCheckLists[i].fillQuestions[j].answers.length; k++) {
            itemData.push(this._getContentAnswer(answers[k]));
          }
        }

        if (reportFindingItem) {
          itemData.push(
            fillQuestion.findingRemark,
            null,
            null,
            reportFindingItem.location?.location || refCategory?.location?.location,
            reportFindingItem.location?.acronym || refCategory?.location?.acronym || null,
            reportFindingItem.main_category?.mainCategory ||
              refCategory?.main_category?.mainCategory,
            reportFindingItem.main_category?.acronym || refCategory?.main_category?.acronym || null,
            reportFindingItem.secondCategory?.name ||
              refCategory?.second_category?.secondCategory ||
              null,
            reportFindingItem.secondCategory?.acronym ||
              refCategory?.second_category?.acronym ||
              null,
            reportFindingItem.thirdCategory?.name || refCategory?.third_category || null,
            reportFindingItem?.potential_risk || refCategory.potential_risk || null,
            reportFindingItem?.criticality || refCategory.criticality || null,
          );
        } else {
          itemData.push(
            fillQuestion.findingRemark,
            null,
            null,
            refCategory.location.location,
            refCategory.location.acronym,
            refCategory.main_category.mainCategory,
            refCategory.main_category.acronym,
            refCategory.second_category.secondCategory || null,
            refCategory.second_category.acronym || null,
            refCategory.third_category,
            refCategory.potential_risk,
            refCategory.criticality,
          );
        }
        sNo++;
        arrItemData.push(itemData);
      }
    }

    // add a table to a sheet
    worksheet.addTable({
      name: 'MyTable',
      ref: 'A1',
      headerRow: true,
      columns: [
        { name: 'S.NO', filterButton: true },
        { name: 'QuestionCode', filterButton: true },
        { name: 'QuestionName', filterButton: true },
        { name: 'Answer', filterButton: true },
        { name: 'FindingRemarks', filterButton: true },
        { name: 'Rectification / Verified', filterButton: true },
        { name: 'RectificationVerifiedRemarks', filterButton: true },
        { name: 'Location', filterButton: true },
        { name: 'locAcronym', filterButton: true },
        { name: 'mainCategory', filterButton: true },
        { name: 'mainAcronym', filterButton: true },
        { name: 'subCategoryOne', filterButton: true },
        { name: 'subAcronym', filterButton: true },
        { name: 'subCategoryTwo', filterButton: true },
        { name: 'potentialRisk', filterButton: true },
        { name: 'Criticality', filterButton: true },
      ],
      rows: arrItemData,
    });

    worksheet.columns = [
      { width: 10 },
      { width: 20 },
      { width: 50 },
      { width: 20 },
      { width: 50 },
      { width: 30 },
      { width: 30 },
      { width: 20 },
      { width: 20 },
      { width: 30 },
      { width: 20 },
      { width: 30 },
      { width: 20 },
      { width: 30 },
      { width: 20 },
      { width: 20 },
    ];

    const totalColumns = 16;
    // add config header
    worksheet.eachRow({ includeEmpty: true }, function (row, rowNumber) {
      row.eachCell({ includeEmpty: true }, function (cell, colNumber) {
        cell.alignment = {
          vertical: 'middle',
          horizontal: 'center',
        };
        cell.border = {
          top: { style: 'thin', color: { argb: '000000' } },
          left: { style: 'thin', color: { argb: '000000' } },
          bottom: { style: 'thin', color: { argb: '000000' } },
          right: { style: 'thin', color: { argb: '000000' } },
        };

        if (rowNumber === 1 && colNumber <= totalColumns) {
          row.height = 50;

          cell.font = {
            name: 'Arial',
            family: 2,
            bold: true,
            size: 11,
          };

          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '32CD32' },
          };
        } else {
          cell.font = {
            name: 'Arial',
            family: 2,
            bold: false,
            size: 11,
          };
        }
      });
    });

    return { workbook, fileName };
  }

  async listFindingSummaries(workSpaceId: string) {
    return await this.auditWorkRepo.listFindingSummaries(workSpaceId);
  }

  async listOnboardFindingItem(workSpaceId: string) {
    return await this.onboardFindingRepository.listOnboardFindingItem(workSpaceId);
  }

  async checkCompleteCheckList(
    workSpaceId: string,
    fillCheckListId: string,
    user: TokenPayloadModel,
  ) {
    return this.fillAuditChecklistQuestionRepo.checkCompleteChecklist(
      fillCheckListId,
      user,
      workSpaceId,
    );
  }

  async getFillCheckListDetail(
    query: ListFillChecklistDetailDto,
    workSpaceId: string,
    fillCheckListId: string,
    user: TokenPayloadModel,
  ) {
    return this.fillAuditChecklistQuestionRepo.listFillQuestionsOfFillChecklistGetReferenceData(
      query,
      fillCheckListId,
      user,
      workSpaceId,
    );

    // Group fill questions by topic
    // const topicMap = {};
    // for (let i = 0; i < fillQuestions.length; i++) {
    //   const quest = fillQuestions[i];
    //   const topic = quest.chkQuestion.topic.name;
    //   if (!topicMap[topic]) {
    //     topicMap[topic] = [];
    //   }
    //
    //   topicMap[topic].push(quest);
    // }
    //
    // const res = [];
    // // Transform map to Array
    // const topics = Object.keys(topicMap);
    // for (let i = 0; i < topics.length; i++) {
    //   res.push({
    //     topic: topics[i],
    //     questions: topicMap[topics[i]],
    //   });
    // }
    // return res;
  }

  async getGroupQuestions(
    query: ListFillChecklistDetailDto,
    workSpaceId: string,
    fillCheckListId: string,
    user: TokenPayloadModel,
  ) {
    return this.fillAuditChecklistQuestionRepo.getGroupQuestions(
      query,
      fillCheckListId,
      user,
      workSpaceId,
    );
  }

  async updatedFillChecklistDetail(
    fillChecklistId: string,
    params: UpdateFillAuditChecklistDto,
    user: TokenPayloadModel,
    auditWorkspaceId: string,
  ) {
    const {
      dataNoti,
      dataSendMail,
    } = await this.fillAuditChecklistQuestionRepo.updatedCheckListDetail(
      fillChecklistId,
      params,
      user,
      auditWorkspaceId,
      FillAuditChecklistStatus.IN_PROGRESS,
    );
    for (const data of dataNoti) {
      await this.notificationProducer.publishNotification(data);
    }
    this.emailProducer.publishEmail(dataSendMail);
    return 1;
  }

  async updatedAnswers(
    fillChecklistId: string,
    params: FillChecklistQuickAnswersDto,
    user: TokenPayloadModel,
    auditWorkspaceId: string,
  ) {
    return await this.fillAuditChecklistQuestionRepo.updatedAnswers(
      fillChecklistId,
      params,
      user,
      auditWorkspaceId,
    );
  }

  async submitFinalChecklist(
    fillChecklistId: string,
    // params: UpdateFillAuditChecklistDto,
    user: TokenPayloadModel,
    auditWorkspaceId: string,
  ) {
    const {
      dataNoti,
      dataSendMail,
    } = await this.fillAuditChecklistQuestionRepo.submitFinalChecklist(
      fillChecklistId,
      // params,
      user,
      auditWorkspaceId,
    );
    for (const data of dataNoti) {
      await this.notificationProducer.publishNotification(data);
    }
    this.emailProducer.publishEmail(dataSendMail);
    return 1;
  }
  async submitFinalWorkspace(user: TokenPayloadModel, auditWorkspaceId: string, body: TimezoneDTO) {
    const { dataNoti, dataSendMail } = await this.auditWorkRepo.submitFinalWorkspace(
      user,
      auditWorkspaceId,
      body,
    );
    for (const data of dataNoti) {
      await this.notificationProducer.publishNotification(data);
    }
    this.emailProducer.publishEmail(dataSendMail);
    return 1;
  }

  async updateShowPopupAnalyticalReport(
    user: TokenPayloadModel,
    auditWorkspaceId: string,
    body: UpdateShowPopupAnalyticalReportDto,
  ) {
    return this.auditWorkRepo.updateShowPopupAnalyticalReport(user, auditWorkspaceId, body);
  }

  async updateMasterOrChiefOfEngineer(
    user: TokenPayloadModel,
    auditWorkspaceId: string,
    body: UpdateMasterOrChiefOfEngineerDto,
  ) {
    return this.auditWorkRepo.updateMasterOrChiefOfEngineer(user, auditWorkspaceId, body);
  }

  async updateFindingItem(
    auditWorkspaceId: string,
    findingSummaryId: string,
    body: UpdateReportFindingDto,
    user: TokenPayloadModel,
  ) {
    await this.reportFindingItemRepository.updateFindingItem(
      auditWorkspaceId,
      findingSummaryId,
      body,
      user,
    );
  }

  async triggerAwsAndFillChkList(
    auditWorkspaceId: string,
    fillChecklistId: string,
    user: TokenPayloadModel,
    body: CreateAuditWorkspaceDto,
  ) {
    const { dataNoti, dataSendMail } = await this.fillAuditChecklistRepo.triggerAwsAndFillChkList(
      auditWorkspaceId,
      fillChecklistId,
      user,
      body,
    );
    for (const data of dataNoti) {
      await this.notificationProducer.publishNotification(data);
    }
    this.emailProducer.publishEmail(dataSendMail);
    return 1;
  }

  async createAuditWorkspacePdf(
    workspaceId: string,
    checklistId: string,
    user: TokenPayloadModel,
    res,
  ) {
    const chkList = await this.fillAuditChecklistQuestionRepo.getFillCkListDataForPdfRender(
      workspaceId,
      checklistId,
      user,
    );
    const logoId = await this.companyRepository.getCompanyLogoId(user.companyId);
    let logoLink = '';
    if (logoId) {
      const data: any = await this.svmSupportService.getDetailImage([logoId]);
      logoLink = data[0].link;
    }
    return await createPdf(
      'inspection-workspace-template',
      'checklistTemplate',
      { ...chkList, logoLink },
      res,
    );
  }

  async checkFillChecklistAndFindings(user: TokenPayloadModel, auditWorkspaceId: string) {
    return await this.fillAuditChecklistQuestionRepo.checkFillChecklistAndFindings(
      user,
      auditWorkspaceId,
    );
  }

  async exportZipFilesChecklist(
    workspaceId: string,
    checklistId: string,
    user: TokenPayloadModel,
    res: Response,
  ) {
    const fillQuestions = await this.fillAuditChecklistQuestionRepo.listFillQuestionsOfFillChecklistGetReferenceData(
      { pageSize: -1 },
      checklistId,
      user,
      workspaceId,
    );
    const questions: any[] = [];
    fillQuestions.forEach((fq) =>
      questions.push(
        ...fq.questions.filter((q) => q.attachments?.length || q.evidencePictures?.length),
      ),
    );
    //return message: No images
    if (!questions.length) {
      throw new BaseError({ status: 400, message: 'workspace.NO_PHOTO_ATTACHED' });
    }
    const attachments: string[] = [];
    questions.forEach((q) => {
      if (q.attachments?.length) {
        attachments.push(...q.attachments);
      }
      if (q.evidencePictures?.length) {
        attachments.push(...q.evidencePictures);
      }
    });

    const files: any = await this.svmSupportService.listFilesByIdsShort(attachments, true);
    const filesMap = {};
    files.forEach((file) => (filesMap[file.id] = file));

    const outputDir = path.join(__dirname, 'uploads');
    fs.mkdirSync(outputDir, { recursive: true });

    const zipFileName = 'result.zip';
    const outputPath = path.join(outputDir, zipFileName);

    const output = fs.createWriteStream(outputPath);
    const archive = archiver('zip', {
      zlib: { level: 9 },
    });

    output.on('close', () => {
      console.log(archive.pointer() + ' total bytes');
      console.log('archiver has been finalized and the output file descriptor has closed.');
    });

    archive.on('error', (err) => {
      throw new Error(err);
    });

    for (const q of questions) {
      for (const att of [...q.attachments, ...q.evidencePictures]) {
        const file = filesMap[att];
        const response = await axios.get(file.link, { responseType: 'arraybuffer' });
        const bufferData = Buffer.from(response.data, 'binary');
        const questionName = `QC${q.chkQuestion.code}`;
        const fileName = q.attachments.includes(att)
          ? `${questionName}_${q.attachments.indexOf(att) + 1}.${file.originName.split('.').pop()}`
          : file.originName;
        archive.append(bufferData, { name: `/${questionName}/` + fileName });
      }
    }

    archive.pipe(output);
    await archive.finalize();

    const zipBuffer = fs.createReadStream(outputPath);

    res.set({
      'Content-Type': 'application/zip',
      'Content-Disposition': 'attachment; filename="checklist-images.zip"',
    });
    zipBuffer.on('close', () => {
      fs.unlinkSync(outputPath);
      res.end();
    });
    zipBuffer.pipe(res);
  }

  /**
   * Import self-assessment compliance data from Excel file
   *
   * @param file Uploaded Excel file
   * @param user Current user information
   * @param auditWorkspaceId ID of the audit workspace
   * @param fillAuditChecklistId Optional ID of the fill audit checklist to filter by
   * @returns Object with success status and summary
   * @throws BadRequestException When file format is invalid or data cannot be processed
   * @throws UnauthorizedException When user doesn't have permission to access this workspace
   */
  async importSACompliance(
    file: Express.Multer.File,
    user: TokenPayloadModel,
    auditWorkspaceId: string,
    fillAuditChecklistId?: string,
  ) {
    try {
      if (!file) {
        throw new BadRequestException('No file uploaded');
      }

      // Verify file is Excel format
      if (
        !file.mimetype.includes('spreadsheetml') &&
        !file.mimetype.includes('excel') &&
        !file.mimetype.includes('openxmlformats')
      ) {
        throw new BadRequestException('File must be an Excel spreadsheet');
      }

      // Load workbook from file
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.readFile(file.path);

      // Get first worksheet
      const worksheet = workbook.getWorksheet(1);
      if (!worksheet) {
        throw new BadRequestException('Excel file has no worksheets');
      }

      // Extract column headers to validate structure
      const headerRow = worksheet.getRow(1);
      const headers = [];
      headerRow.eachCell((cell) => {
        headers.push(cell.value?.toString().trim());
      });

      // Validate required columns exist - customize for self-assessment data
      const requiredColumns = [
        'Element Code',
        'Element Name',
        'Element Number',
        'Stage',
        'Self-Assessment Compliance',
        'Auditor Compliance',
        'Remarks',
        'Is Finding',
        'Finding Type',
        'Finding Comments',
        'ID',
      ];
      const missingColumns = requiredColumns.filter((col) => !headers.includes(col));
      if (missingColumns.length > 0) {
        throw new BadRequestException(`Missing required columns: ${missingColumns.join(', ')}`);
      }

      // Extract data rows
      const dataExcels = [];
      const rowErrors = [];

      worksheet.eachRow({ includeEmpty: false }, (row, rowNumber) => {
        if (rowNumber === 1) return; // Skip header
        try {
          const rowValues = row.values;
          // Map columns by header index for flexibility
          const getCell = (header: string) => {
            const idx = headers.findIndex((h) => h === header);
            return idx >= 0 ? row.getCell(idx + 1).value : '';
          };
          const id = getCell('ID');
          if (!id) {
            rowErrors.push(`Row ${rowNumber}: Missing ID value`);
            return;
          }
          const isFinding = (getCell('Is Finding') || '').toString().trim();
          const findingType = (getCell('Finding Type') || '').toString().trim();
          const findingComments = (getCell('Finding Comments') || '').toString().trim();
          // Validate dropdowns
          const allowedIsFinding = ['Yes', 'No'];
          if (isFinding && !allowedIsFinding.includes(isFinding)) {
            rowErrors.push(`Row ${rowNumber}: Invalid Is Finding value '${isFinding}'`);
          }
          // If Is Finding is not Yes, ignore findingType/comments
          let finalFindingType = '';
          let finalFindingComments = '';
          if (isFinding === 'Yes') {
            finalFindingType = findingType;
            finalFindingComments = findingComments;
          }
          dataExcels.push({
            elementCode: getCell('Element Code') || '',
            elementName: getCell('Element Name') || '',
            elementNumber: getCell('Element Number') || '',
            stage: getCell('Stage') || '',
            assessmentCompliance: getCell('Self-Assessment Compliance') || '',
            auditorCompliance: getCell('Auditor Compliance') || '',
            remarks: getCell('Remarks') || '',
            isFinding: isFinding || 'No',
            findingType: finalFindingType,
            findingComments: finalFindingComments,
            id: getCell('ID'),
          });
        } catch (err) {
          rowErrors.push(`Row ${rowNumber}: ${err.message}`);
        }
      });

      if (dataExcels.length === 0) {
        throw new BadRequestException('No valid data rows found in the file');
      }

      // Process in batches for better performance
      const batchSize = 100;
      const results = { updated: 0, errors: rowErrors };

      for (let i = 0; i < dataExcels.length; i += batchSize) {
        const batch = dataExcels.slice(i, i + batchSize);
        const batchResult = await this.auditWorkRepo.importSACompliance(
          batch,
          user,
          auditWorkspaceId,
          fillAuditChecklistId,
        );
        results.updated += batchResult ? batch.length : 0;
      }

      return {
        summary: `Successfully updated ${results.updated} self-assessment compliance items`,
        errors: results.errors.length > 0 ? results.errors : undefined,
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to import self-assessment compliance data: ${error.message}`,
      );
    }
  }

  /**
   * Download self-assessment compliance data as Excel file
   *
   * @param auditWorkspaceId ID of the audit workspace
   * @param user Current user information
   * @param fillAuditChecklistId Optional ID of the fill audit checklist to filter by
   * @returns Object containing workbook and filename for download
   * @throws BadRequestException When workspace not found or data cannot be retrieved
   * @throws UnauthorizedException When user doesn't have permission to access this workspace
   */
  async downloadSACompliance(
    auditWorkspaceId: string,
    user: TokenPayloadModel,
    fillAuditChecklistId?: string,
  ) {
    try {
      // Fetch compliance data from repository
      const {
        data,
        answerList,
        answerListsByStandardMaster,
        findingTypes,
        fileName,
      } = await this.auditWorkRepo.getSAComplianceDataForExport(
        auditWorkspaceId,
        user,
        fillAuditChecklistId,
      );

      if (!data || data.length === 0) {
        throw new BadRequestException(
          'No self-assessment compliance data available for this workspace',
        );
      }

      // Create Excel workbook
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Self-Assessment Compliance');

      // Set column properties for better formatting
      worksheet.columns = [
        { header: 'Element Code', key: 'elementCode', width: 15 },
        { header: 'Element Name', key: 'elementName', width: 30 },
        { header: 'Element Number', key: 'elementNumber', width: 15 },
        { header: 'Stage', key: 'stage', width: 15 },
        { header: 'Self-Assessment Compliance', key: 'assessmentCompliance', width: 25 },
        { header: 'Auditor Compliance', key: 'auditorCompliance', width: 20 }, // dropdown
        { header: 'Remarks', key: 'remarks', width: 30 },
        { header: 'Is Finding', key: 'isFinding', width: 15 }, // dropdown
        { header: 'Finding Type', key: 'findingType', width: 20 }, // dropdown
        { header: 'Finding Comments', key: 'findingComments', width: 30 },
        { header: 'ID', key: 'id', width: 36 },
      ];
      // Hide the ID column (column K) - commented out to make visible
      worksheet.getColumn('K').hidden = true;

      // Add header styling
      const headerRow = worksheet.getRow(1);
      headerRow.eachCell((cell) => {
        cell.font = { bold: true };
        cell.alignment = { vertical: 'middle', horizontal: 'center' };
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFE0E0E0' },
        };
      });
      worksheet.protect('audit-sa-protect', { selectLockedCells: true, selectUnlockedCells: true });

      // Add data rows
      for (const row of data) {
        worksheet.addRow({
          elementCode: row.elementCode,
          elementName: row.elementName,
          elementNumber: row.elementNumber,
          stage: row.stage,
          assessmentCompliance: row.assessmentCompliance,
          auditorCompliance: row.auditorCompliance,
          remarks: row.remarks,
          isFinding: row.isFinding === true || row.isFinding === 'Yes' ? 'Yes' : 'No',
          findingType: row.findingType || '',
          findingComments: row.findingComment || '',
          id: row.id,
        });
      }
      // Add dropdowns for Auditor Compliance, Is Finding, Finding Type
      const auditorComplianceOptions =
        answerList && answerList.length > 0
          ? answerList
          : ['Compliant', 'Non-Compliant', 'Not Applicable'];
      const isFindingOptions = ['Yes', 'No'];
      // Build findingTypeOptions from unique values in data, or fetch from master if available
      let findingTypeOptions: string[] = [];
      if (findingTypes && Array.isArray(findingTypes)) {
        const set = new Set<string>();
        findingTypes.forEach((type) => {
          set.add(type);
        });
        findingTypeOptions = Array.from(set); // Need to check the finding type fetch data
      }
      if (!findingTypeOptions.length) {
        findingTypeOptions = ['Type1', 'Type2']; // fallback, replace with actual master fetch if needed
      }
      for (let i = 2; i <= worksheet.rowCount; i++) {
        worksheet.getCell(`F${i}`).dataValidation = {
          type: 'list',
          allowBlank: true,
          formulae: [`"${auditorComplianceOptions.join(',')}"`],
        };
        worksheet.getCell(`H${i}`).dataValidation = {
          type: 'list',
          allowBlank: true,
          formulae: [`"${isFindingOptions.join(',')}"`],
        };
        worksheet.getCell(`I${i}`).dataValidation = {
          type: 'list',
          allowBlank: true,
          formulae: [`"${findingTypeOptions.join(',')}"`],
        };
        // Lock/unlock cells for conditional editing
        worksheet.getCell(`F${i}`).protection = { locked: false };
        worksheet.getCell(`G${i}`).protection = { locked: false };
        worksheet.getCell(`H${i}`).protection = { locked: false };
        // Add hover messages (notes) to editable fields
        worksheet.getCell(`H${i}`).note =
          'Select "Yes" to indicate a finding. If "Yes", please fill in Finding Type and Comments.';
        worksheet.getCell(`I${i}`).protection = { locked: false };
        worksheet.getCell(`J${i}`).protection = { locked: false };
      }

      // Group rows by standardMasterId for applying different dropdowns
      const rowsByStandardMaster: Record<string, number[]> = {};

      // Organize rows by standardMasterId
      for (let i = 2; i <= worksheet.rowCount; i++) {
        const cell = worksheet.getCell(`I${i}`);
        const standardMasterId = cell.value?.toString();

        if (standardMasterId) {
          if (!rowsByStandardMaster[standardMasterId]) {
            rowsByStandardMaster[standardMasterId] = [];
          }
          rowsByStandardMaster[standardMasterId].push(i);
        }
      }

      // Apply dropdown validation for each group of rows based on their standardMasterId
      if (answerListsByStandardMaster) {
        Object.entries(rowsByStandardMaster).forEach(([standardMasterId, rowIndices]) => {
          const standardAnswers = answerListsByStandardMaster[standardMasterId];

          if (standardAnswers && standardAnswers.length > 0) {
            rowIndices.forEach((rowIndex: number) => {
              worksheet.getCell(`F${rowIndex}`).dataValidation = {
                type: 'list',
                allowBlank: true,
                formulae: [`"${standardAnswers.join(',')}"`],
              };
            });
          }
        });
      }

      // If no standard-specific answers were found, use the general answer list
      if (answerList && answerList.length > 0 && Object.keys(rowsByStandardMaster).length === 0) {
        for (let i = 2; i <= worksheet.rowCount; i++) {
          worksheet.getCell(`F${i}`).dataValidation = {
            type: 'list',
            allowBlank: true,
            formulae: [`"${answerList.join(',')}"`],
          };
        }
      }

      // Generate filename with workspace name and date
      const date = new Date().toISOString().split('T')[0];
      const generatedFileName = fileName
        ? `${fileName}-${date}.xlsx`
        : `self-assessment-compliance-${auditWorkspaceId.substring(0, 8)}-${date}.xlsx`;

      return { workbook, fileName: generatedFileName };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to download self-assessment compliance data: ${error.message}`,
      );
    }
  }
}
