import { Injectable } from '@nestjs/common';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { Connection, EntityManager, EntityRepository, In, IsNull, Not, Repository } from 'typeorm';
import {
  <PERSON><PERSON>rror,
  Logger<PERSON>ommon,
  TokenPayloadModel,
  TypeORMRepository,
  Utils,
} from 'svm-nest-lib-v3';
import { FillSAChecklistQuestion } from '../entities/fill-sa-checklist-question.entity';
import { SAFindingItem } from '../entities/sa-finding-items.entity';
import { OnboardFindingItem } from '../entities/onboard-finding-item.entity';
import { ReportFindingItem } from '../entities/report-finding-item.entity';
import {
  CreateSAReportFindingItemDto,
  UpdateFillSAChecklistQuestionDto,
} from '../dto/fill-sa-checklist-question.dto';
import { FillAuditChecklistQuestionRepository } from './fill-audit-checklist-question.repository';
import { FillAuditChecklist } from '../entities/fill-audit-checklist.entity';
import { AuditWorkspace } from '../entities/audit-workspace.entity';
import { InternalAuditReportRepository } from 'src/modules/internal-audit-report/repositories/internal-audit-report.repository';
import { omit, pick } from 'lodash';
import {
  ActionValueChangeEnum,
  AuditWorkspaceStatus,
  EmailTypeEnum,
  FillAuditChecklistStatus,
  MailTemplate,
  ModuleEnum,
  ModulePathEnum,
  PushTypeEnum,
} from 'src/commons/enums';
import { AppConst } from 'src/commons/consts/app.const';
import { decryptAttachmentValues } from 'src/commons/functions';
import { commonCheckValueChange } from 'src/commons/functions/value-change-history';
import { IUserEmail } from 'src/micro-services/async/email.producer';
import { IUser } from 'src/micro-services/async/notification.producer';
import { AuditModuleEnum, AuditActivityEnum } from 'src/modules/audit-log/audit-log.entity';
import { AuditLogRepository } from 'src/modules/audit-log/audit-log.repository';
import { CorrectiveActionRequest } from 'src/modules/corrective-action-request/entities/car.entity';
import { CARRepository } from 'src/modules/corrective-action-request/repositories/car.repository';
import { PlanningRequestRepository } from 'src/modules/planning-request/repositories/planning-request.repository';
import { CreateReportFindingFormDto } from 'src/modules/report-finding/dto';
import { ReportFindingForm } from 'src/modules/report-finding/entities/report-finding-form.entity';
import { ReportFindingFormRepository } from 'src/modules/report-finding/repositories/report-finding-form.repository';
import { ModuleType } from 'src/modules/user-assignment/user-assignment.enum';
import { UserRepository } from 'src/modules/user/user.repository';
import { ValueChangeHistory } from 'src/modules/value-change-history/value-change-history.entity';
import { MySet } from 'src/utils';
import { FillAuditChecklistQuestion } from '../entities/fill-audit-checklist-question.entity';
import { AuditWorkspaceRepository } from './audit-workspace.repository';
import { CorrectiveActionPlan } from '../../corrective-action-request/entities/cap.entity';
import { CarVerification } from '../../corrective-action-request/entities/car-verification.entity';
import { NatureFindingRepository } from '../../nature_finding/nature-finding.repository';

@EntityRepository(FillSAChecklistQuestion)
export class FillSAChecklistQuestionRepository extends TypeORMRepository<FillSAChecklistQuestion> {
  constructor(
    private readonly connection: Connection,
    private readonly fillAduitChecklistRepository: FillAuditChecklistQuestionRepository,
  ) {
    super();
  }

  async getFillSAChecklistQuestions(fillChecklistId: string, query?: any) {
    const elementName = query?.elementName;

    // Get inspection mapping data
    const inspectionMappingQuery = this.connection
      .getRepository(FillAuditChecklist)
      .createQueryBuilder('fillAuditChecklist')
      .leftJoinAndSelect('fillAuditChecklist.auditWorkspace', 'auditWorkspace')
      .leftJoinAndSelect('fillAuditChecklist.auditType', 'auditType')
      .leftJoinAndSelect('auditType.inspectionMapping', 'inspectionMapping')
      .leftJoinAndSelect('inspectionMapping.natureFindings', 'natureFindingList')
      .leftJoin('natureFindingList.natureFinding', 'natureFinding')
      .where('fillAuditChecklist.id = :fillChecklistId AND fillAuditChecklist.deleted = false', {
        fillChecklistId,
      })
      .addSelect(['natureFinding.id', 'natureFinding.name']);

    // Get fill SA checklist questions data
    const fillSAChecklistQuestionsQuery = this.createQueryBuilder('fillSAChecklistQuestion')
      .leftJoinAndSelect('fillSAChecklistQuestion.fillAuditChecklist', 'fillAuditChecklist')
      .leftJoinAndSelect('fillSAChecklistQuestion.SAQuestions', 'SAQuestions')
      .leftJoinAndSelect('fillSAChecklistQuestion.SAFindingItem', 'SAFindingItem')
      .where(
        'fillAuditChecklist.id = :fillChecklistId AND fillSAChecklistQuestion.deleted = false',
        {
          fillChecklistId,
        },
      )
      .orderBy('SAQuestions.elementStageQ', 'ASC');

    if (elementName) {
      fillSAChecklistQuestionsQuery.andWhere('SAQuestions.name = :elementName', {
        elementName,
      });
    }

    const [inspectionMapping, fillSAChecklistQuestions] = await Promise.all([
      inspectionMappingQuery.getOne(),
      fillSAChecklistQuestionsQuery.getMany(),
    ]);

    return {
      fillSAChecklistQuestions,
      fillAuditChecklist: inspectionMapping,
    };
  }

  /**
   * Get element/statistics data for a fill checklist
   */
  async getFillSAChecklistElementCounts(fillChecklistId: string) {
    // Get all elements with compliance statistics
    const elementStatsQuery = this.createQueryBuilder('fillSAChecklistQuestion')
      .leftJoin('fillSAChecklistQuestion.fillAuditChecklist', 'fillAuditChecklist')
      .leftJoin('fillSAChecklistQuestion.SAQuestions', 'SAQuestions')
      .select([
        'SAQuestions.group as "group"',
        'SAQuestions.stage as "stage"',
        'SAQuestions.name as "elementName"',
        'SAQuestions.code as "elementCode"',
        'COUNT(*) as "totalElements"',
        'COUNT(fillSAChecklistQuestion.auditorComplianceId) as "savedElements"',
        'COUNT(*) - COUNT(fillSAChecklistQuestion.auditorComplianceId) as "pendingElements"',
        'COUNT(fillSAChecklistQuestion.auditorComplianceId) * 100.0 / COUNT(*) as "fillPercentage"',
      ])
      .where(
        'fillAuditChecklist.id = :fillChecklistId AND fillSAChecklistQuestion.deleted = false',
        {
          fillChecklistId,
        },
      )
      .groupBy('SAQuestions.group, SAQuestions.stage, SAQuestions.name, SAQuestions.code')
      .orderBy('SAQuestions.group', 'ASC')
      .addOrderBy('SAQuestions.stage', 'ASC')
      .addOrderBy('SAQuestions.code', 'ASC');

    // Get overall compliance statistics
    const overallStatsQuery = this.createQueryBuilder('fillSAChecklistQuestion')
      .leftJoin('fillSAChecklistQuestion.fillAuditChecklist', 'fillAuditChecklist')
      .select([
        'COUNT(*) as "totalElements"',
        'COUNT(fillSAChecklistQuestion.auditorComplianceId) as "savedElements"',
        'COUNT(*) - COUNT(fillSAChecklistQuestion.auditorComplianceId) as "pendingElements"',
        'COUNT(fillSAChecklistQuestion.auditorComplianceId) * 100.0 / COUNT(*) as "overallFillPercentage"',
      ])
      .where(
        'fillAuditChecklist.id = :fillChecklistId AND fillSAChecklistQuestion.deleted = false',
        {
          fillChecklistId,
        },
      );

    // Get group-wise statistics
    const groupStatsQuery = this.createQueryBuilder('fillSAChecklistQuestion')
      .leftJoin('fillSAChecklistQuestion.fillAuditChecklist', 'fillAuditChecklist')
      .leftJoin('fillSAChecklistQuestion.SAQuestions', 'SAQuestions')
      .select([
        'SAQuestions.group as "group"',
        'COUNT(*) as "totalElements"',
        'COUNT(fillSAChecklistQuestion.auditorComplianceId) as "savedElements"',
        'COUNT(*) - COUNT(fillSAChecklistQuestion.auditorComplianceId) as "pendingElements"',
        'COUNT(fillSAChecklistQuestion.auditorComplianceId) * 100.0 / COUNT(*) as "fillPercentage"',
      ])
      .where(
        'fillAuditChecklist.id = :fillChecklistId AND fillSAChecklistQuestion.deleted = false',
        {
          fillChecklistId,
        },
      )
      .groupBy('SAQuestions.group')
      .orderBy('SAQuestions.group', 'ASC');

    // Get stage-wise statistics
    const stageStatsQuery = this.createQueryBuilder('fillSAChecklistQuestion')
      .leftJoin('fillSAChecklistQuestion.fillAuditChecklist', 'fillAuditChecklist')
      .leftJoin('fillSAChecklistQuestion.SAQuestions', 'SAQuestions')
      .select([
        'SAQuestions.stage as "stage"',
        'COUNT(*) as "totalElements"',
        'COUNT(fillSAChecklistQuestion.auditorComplianceId) as "savedElements"',
        'COUNT(*) - COUNT(fillSAChecklistQuestion.auditorComplianceId) as "pendingElements"',
        'COUNT(fillSAChecklistQuestion.auditorComplianceId) * 100.0 / COUNT(*) as "fillPercentage"',
      ])
      .where(
        'fillAuditChecklist.id = :fillChecklistId AND fillSAChecklistQuestion.deleted = false',
        {
          fillChecklistId,
        },
      )
      .groupBy('SAQuestions.stage')
      .orderBy('SAQuestions.stage', 'ASC');

    const [elementStats, overallStats, groupStats, stageStats] = await Promise.all([
      elementStatsQuery.getRawMany(),
      overallStatsQuery.getRawOne(),
      groupStatsQuery.getRawMany(),
      stageStatsQuery.getRawMany(),
    ]);

    // Process and organize the statistics
    const processedElementStats = elementStats.map((stat) => ({
      group: stat.group,
      stage: stat.stage,
      elementName: stat.elementName,
      elementCode: stat.elementCode,
      totalElements: parseInt(stat.totalElements),
      savedElements: parseInt(stat.savedElements),
      pendingElements: parseInt(stat.pendingElements),
      fillPercentage: parseFloat(stat.fillPercentage || '0').toFixed(2),
    }));

    const processedGroupStats = groupStats.map((stat) => ({
      group: stat.group,
      totalElements: parseInt(stat.totalElements),
      savedElements: parseInt(stat.savedElements),
      pendingElements: parseInt(stat.pendingElements),
      fillPercentage: parseFloat(stat.fillPercentage || '0').toFixed(2),
    }));

    const processedStageStats = stageStats.map((stat) => ({
      stage: stat.stage,
      totalElements: parseInt(stat.totalElements),
      savedElements: parseInt(stat.savedElements),
      pendingElements: parseInt(stat.pendingElements),
      fillPercentage: parseFloat(stat.fillPercentage || '0').toFixed(2),
    }));

    const processedOverallStats = {
      totalElements: parseInt(overallStats?.totalElements || '0'),
      savedElements: parseInt(overallStats?.savedElements || '0'),
      pendingElements: parseInt(overallStats?.pendingElements || '0'),
      overallFillPercentage: parseFloat(overallStats?.overallFillPercentage || '0').toFixed(2),
    };

    // Combine the results
    return {
      overall: processedOverallStats,
      byGroup: processedGroupStats,
      byStage: processedStageStats,
      byElement: processedElementStats,
    };
  }
  async createSAChecklistQuestion(
    data: Partial<FillSAChecklistQuestion>,
    user: TokenPayloadModel,
  ): Promise<FillSAChecklistQuestion> {
    return this.manager.transaction(async (manager) => {
      const newQuestion = manager.create(FillSAChecklistQuestion, {
        ...data,
        createdUserId: user.id,
      });
      return manager.save(FillSAChecklistQuestion, newQuestion);
    });
  }

  async updateSAChecklistQuestion(
    auditWorkspaceId: string,
    fillChecklistId: string,
    updateDto: UpdateFillSAChecklistQuestionDto,
    user: TokenPayloadModel,
    checklistStatus: string,
  ) {
    const questsDto = updateDto.fillQuestions;
    const questsDb = await this.getFillSAChecklistQuestions(fillChecklistId);

    const preparedFillQuests: Partial<FillSAChecklistQuestion>[] = [];
    const preparedRoF = [];
    const preparedDeletedRoF = [];

    // check fill check list status already finished ?
    const checklistDb = await this.manager.findOne(FillAuditChecklist, { id: fillChecklistId });
    const checklistStatusDb = checklistDb?.status;

    // check role
    const workspace = await this.manager.findOne(AuditWorkspace, { id: auditWorkspaceId });
    const auditors = workspace?.auditors;
    if (!auditors.includes(user.id)) {
      throw new BaseError({ message: 'workspace.AUDITOR_FORBIDDEN_RESOURCE' });
    }

    const foundIarByPR = await this.connection
      .getCustomRepository(InternalAuditReportRepository)
      .findOne({
        where: {
          planningRequestId: workspace.planningRequestId,
        },
        select: ['id'],
      });

    //  prepared data
    const findingItemBody = [];
    for (const questDto of questsDto) {
      const questDb = questsDb.fillSAChecklistQuestions.find(
        (questDb) => questDb.id === questDto.id,
      );

      if (!questDb) {
        throw new BaseError({ message: 'workspace.FILL_QUEST_ID_INVALID' });
      }

      // Check if finding status changed from true to false
      if (questDb.isFinding === true && questDto.isFinding === false) {
        // Store the ID to process later in transaction
        if (questDb.sAFindingItemId) {
          preparedDeletedRoF.push(questDb.sAFindingItemId);
        }
      }

      const findingReportDto = questDto?.saFindingItem;

      if (findingReportDto) {
        findingItemBody.push(findingReportDto);
        const sAFindingItemId = questDto?.saFindingItem?.id || Utils.strings.generateUUID();
        preparedRoF.push({
          id: sAFindingItemId,
          auditTypeId: findingReportDto?.auditTypeId,
          auditTypeName: findingReportDto?.auditTypeName,
          natureFindingId: findingReportDto?.natureFindingId,
          natureFindingName: findingReportDto?.natureFindingName,
          isUpdatedFinding: questsDb?.fillAuditChecklist?.auditWorkspace?.isGenerateROF
            ? true
            : false,
          internalAuditReportId: foundIarByPR ? foundIarByPR.id : null,
          selfAssessmentId: questDb?.fillAuditChecklist?.selfAssessmentId,
          elementMasterId: questDb?.elementMasterId,
          auditWorkspaceId,
          findingRemark: questDto?.saFindingItem?.findingRemark,
          findingComment: questDto?.saFindingItem?.findingComment,
          complianceId: questDto?.auditorComplianceId,
          createdUserId: user.id,
          companyId: user.companyId,
        });
        preparedFillQuests.push({
          id: questDb?.id,
          auditorComplianceId: questDto?.auditorComplianceId,
          auditorCompliance: questDto?.auditorCompliance,
          remarks: questDto?.remarks,
          attachments: questDto?.attachments,
          isFinding: questDto?.isFinding,
          sAFindingItemId: sAFindingItemId,
          updatedUserId: user.id,
        });
      } else {
        preparedFillQuests.push({
          id: questDb.id,
          auditorComplianceId: questDto.auditorComplianceId,
          auditorCompliance: questDto.auditorCompliance,
          remarks: questDto.remarks,
          attachments: questDto.attachments,
          isFinding: questDto.isFinding,
          updatedUserId: user.id,
          // If finding changed from true to false, set sAFindingItemId to null
          ...(questDb.isFinding === true && questDto.isFinding === false
            ? { sAFindingItemId: null }
            : {}),
        });
      }
    }

    // save or update ROF data
    const updatedFindings = await this.manager.save(SAFindingItem, preparedRoF);

    return await this.connection.transaction(async (manager) => {
      // Process deleted findings (finding status changed from true to false)
      if (preparedDeletedRoF.length > 0) {
        // Find all findings to be deleted
        const findingsToDelete = await manager.find(SAFindingItem, {
          where: { id: In(preparedDeletedRoF) },
          relations: ['car'],
        });

        // Get all CAR IDs to delete
        const carIdsToDelete: string[] = [];
        findingsToDelete.forEach((finding) => {
          if (finding.carId) {
            carIdsToDelete.push(finding.carId);
          }
        });

        // Delete associated CARs first (to maintain referential integrity)
        if (carIdsToDelete.length > 0) {
          // Find related CARs to check if they have multiple findings
          const carsToDelete = await manager.find(CorrectiveActionRequest, {
            where: { id: In(carIdsToDelete) },
            relations: ['saFindingItems'],
          });

          for (const car of carsToDelete) {
            // Only delete CAR if it's associated only with findings we're deleting
            // (if a CAR has multiple findings and we're only removing one, keep the CAR)
            if (car.saFindingItems.length <= 1) {
              // Delete CAP first
              await manager.delete(CorrectiveActionPlan, { carId: car.id });
              // Delete CAR verification if any
              await manager.delete(CarVerification, { carId: car.id });
              // Delete the CAR
              await manager.delete(CorrectiveActionRequest, { id: car.id });

              LoggerCommon.log(
                `[FillSAChecklistQuestionRepository] Deleted CAR ${car.id} related to finding that's no longer marked as finding`,
              );
            }
          }
        }

        // Delete findings
        if (preparedDeletedRoF.length > 0) {
          await manager.delete(SAFindingItem, { id: In(preparedDeletedRoF) });
          LoggerCommon.log(
            `[FillSAChecklistQuestionRepository] Deleted ${preparedDeletedRoF.length} findings that are no longer marked as findings`,
          );
        }
      }

      // save fill quest
      const updatedFillChkListQuestions = [];
      for (const preparedFillQuest of preparedFillQuests) {
        updatedFillChkListQuestions.push(
          await manager.update(
            FillSAChecklistQuestion,
            { id: preparedFillQuest.id },
            omit(preparedFillQuest, 'id'),
          ),
        );
      }

      // if fillChecklist is completed, release publishOn date, else return null
      const publishOn = checklistStatus == FillAuditChecklistStatus.COMPLETED ? new Date() : null;
      const submitOn = checklistDb.submitOn ? checklistDb.submitOn : new Date();
      const submitById = checklistDb.submitById ? checklistDb.submitById : user.id;
      // update checklist status
      await manager.update(
        FillAuditChecklist,
        { id: fillChecklistId },
        {
          status: checklistStatus,
          lastUpdatedById: user.id,
          updatedAt: new Date(),
          publishOn,
          submitOn,
          submitById,
        },
      );

      // trigger workspace status to DRAFT if exist checklist status equal COMPLETED
      if (
        checklistStatus !== FillAuditChecklistStatus.COMPLETED ||
        workspace.status === AuditWorkspaceStatus.DRAFT
      ) {
        await manager.update(
          AuditWorkspace,
          { id: auditWorkspaceId },
          {
            updatedUserId: user.id,
          },
        );
      } else {
        await manager.update(
          AuditWorkspace,
          { id: auditWorkspaceId },
          {
            status: AuditWorkspaceStatus.DRAFT,
            updatedUserId: user.id,
          },
        );
      }

      // trigger planing request status to In-Progress if checklistStatus is In-Progress
      if (checklistStatus === FillAuditChecklistStatus.IN_PROGRESS) {
        // Get user info
        const createdUser = await manager
          .getCustomRepository(UserRepository)
          ._getUserInfoForHistory(user.id);

        await manager
          .getCustomRepository(PlanningRequestRepository)
          ._triggerInProgressPlanningRequest(workspace.planningRequestId, user.id, createdUser);

        // Handle add Audit log
        await manager.getCustomRepository(AuditLogRepository).createAuditLog(
          {
            module: AuditModuleEnum.INSPECTION_WORKSPACE,
            planningId: workspace.planningRequestId,
          },
          user,
          [AuditActivityEnum.IN_PROGRESS],
          createdUser,
        );
      }

      //Trigger generate ROF
      const rof = await manager.getCustomRepository(ReportFindingFormRepository).findOne({
        planningRequestId: workspace.planningRequestId,
      });

      const rofItems = await this.listFindingItemsByPR(workspace.planningRequestId, user);

      if (!rof) {
        const createdUser = await manager
          .getCustomRepository(UserRepository)
          ._getUserInfoForHistory(user.id);

        // Map rofItems to CreateReportFindingItemDto
        const mappedRofItems = rofItems.map((item) => ({
          selfAssessmentId: item.selfAssessmentId,
          natureFindingId: item.natureFindingId,
          natureFindingName: item.natureFindingName,
          auditTypeId: item.auditTypeId,
          auditTypeName: item.auditTypeName,
          ...item,
        }));

        const rofForm: CreateReportFindingFormDto = {
          planningRequestId: workspace.planningRequestId,
          timezone: updateDto.timezone,
          saFindingItems: mappedRofItems,
          previousNCFindings: [],
          officeComment: '',
          comments: [],
          workflowRemark: '',
          isSubmit: false,
        };

        const rof = await manager
          .getCustomRepository(ReportFindingFormRepository)
          ._createSAReportFindingFormHelper(
            manager,
            rofForm,
            user,
            createdUser,
            true,
            fillChecklistId,
            auditWorkspaceId,
          );

        await manager.update(
          ReportFindingForm,
          { id: rof.id },
          {
            totalFindings: rofItems.length,
            totalNonConformity: rofItems.filter(
              (item) =>
                item?.natureFinding?.name === AppConst.NATURE_FINDING_DEFAULT.NON_CONFORMITY,
            ).length,
            totalObservation: rofItems.filter(
              (item) => item?.natureFinding?.name === AppConst.NATURE_FINDING_DEFAULT.OBSERVATION,
            ).length,
          },
        );
      } else {
        // Handle SA findings only
        const totals = await this._updateSAFindingsForExistingROF(
          manager,
          user,
          rof.id,
          workspace.planningRequestId,
          rofItems, // Pass all items, they should all be SA findings
          fillChecklistId,
          auditWorkspaceId,
        );

        // Update totals in the report finding form
        await manager.update(
          ReportFindingForm,
          { id: rof.id },
          {
            totalFindings: totals.totalFindings,
            totalNonConformity: totals.totalNonConformity,
            totalObservation: totals.totalObservation,
          },
        );
      }

      // Generate onboard finding items

      // Also get self assessment finding items
      const saFindingItems: SAFindingItem[] = await manager
        .createQueryBuilder(SAFindingItem, 'saFindingItems')
        .innerJoin('saFindingItems.auditWorkspace', 'auditWorkspace')
        .leftJoinAndSelect('saFindingItems.fillSAChecklistQuestion', 'fillSAChecklistQuestion')
        .leftJoinAndSelect('fillSAChecklistQuestion.fillAuditChecklist', 'fillAuditChecklist')
        .leftJoinAndSelect('saFindingItems.elementMaster', 'elementMaster')
        .where('auditWorkspace.id = :auditWorkspaceId', { auditWorkspaceId })
        .getMany();

      const isGenerateROF = await manager
        .getCustomRepository(AuditWorkspaceRepository)
        ._isGenerateROF(auditWorkspaceId);

      // Process self assessment findings for onboard items
      if (saFindingItems.length > 0 && isGenerateROF) {
        // Get the report finding form
        const rof = await manager.getCustomRepository(ReportFindingFormRepository).findOne({
          planningRequestId: workspace.planningRequestId,
        });

        if (rof) {
          // Update the reportFindingFormId in the SAFindingItems
          for (const item of saFindingItems) {
            await manager.update(SAFindingItem, { id: item.id }, { reportFindingFormId: rof.id });
          }

          // Generate onboard finding items for self assessment findings
          const preparedSAOnboardFinding = [];
          for (const finding of saFindingItems) {
            const reportFindingItemId = finding.id;
            // Get fillQuestion data from the database if needed
            const fillQuestion = await manager.findOne(FillSAChecklistQuestion, {
              where: { sAFindingItemId: finding.id },
              relations: ['fillAuditChecklist'],
            });

            const appInstanceId = fillQuestion?.fillAuditChecklist?.appInstance;
            const webInstanceId = fillQuestion?.fillAuditChecklist?.webInstance;
            const templateCode = 'SA'; // Self Assessment template code
            const questionName = finding.elementMaster?.name || 'Self Assessment Finding';
            const questionCode = finding.elementMaster?.code || finding.id.substring(0, 8);

            // Create a new OnboardFindingItem
            const onboardItem = new OnboardFindingItem();
            onboardItem.reportFindingItemId = reportFindingItemId;
            onboardItem.appInstanceId = appInstanceId;
            onboardItem.webInstanceId = webInstanceId;
            onboardItem.templateCode = templateCode;
            onboardItem.questionName = questionName;
            onboardItem.questionCode = questionCode;
            onboardItem.findingComment = finding.findingComment;
            onboardItem.natureFindingName = finding.natureFindingName;
            onboardItem.auditTypeId = finding.auditTypeId;
            onboardItem.auditTypeName = finding.auditTypeName;
            onboardItem.companyId = user.companyId;

            preparedSAOnboardFinding.push({ ...finding, onboardItem });
          }

          if (preparedSAOnboardFinding.length > 0) {
            await manager.save(OnboardFindingItem, preparedSAOnboardFinding);
          }
        }
      }

      return {};
    });
  }

  /**
   * Update SA findings for an existing Report of Finding (ROF)
   * @param manager EntityManager
   * @param user User information
   * @param rofId Report of Finding ID
   * @param planningRequestId Planning Request ID
   * @param saFindingItems SA Finding Items to update
   * @param fillChecklistId Fill Checklist ID
   */
  async _updateSAFindingsForExistingROF(
    manager: EntityManager,
    user: TokenPayloadModel,
    rofId: string,
    // planningRequestId is not used
    _planningRequestId: string,
    saFindingItems: any[],
    fillChecklistId: string,
    auditWorkspaceId: string,
  ) {
    try {
      // Get nature finding data
      const natureFindingIds = saFindingItems.map((x) => x.natureFindingId).filter((id) => !!id);

      const natureFindingMap: { [key: string]: { name: string; isPrimary: boolean } } = {};

      if (natureFindingIds.length > 0) {
        const natureFindingRepo = manager.getRepository('nature_finding');
        const natureFindingArr = await natureFindingRepo.find({
          where: { id: In(natureFindingIds) },
          relations: ['inspMapNatFinding'],
        });

        natureFindingArr.forEach((natureFinding: any) => {
          const isPrimary = natureFinding.inspMapNatFinding?.some((m: any) => m.isPrimaryFinding);
          natureFindingMap[natureFinding.id] = {
            name: natureFinding.name,
            isPrimary: !!isPrimary,
          };
        });
      }

      // Get audit type data
      const auditTypeIds = saFindingItems.map((x) => x.auditTypeId).filter((id) => !!id);

      const auditTypeMap: { [key: string]: string } = {};

      if (auditTypeIds.length > 0) {
        const auditTypeRepo = manager.getRepository('audit_type');
        const auditTypes = await auditTypeRepo.find({
          where: { id: In(auditTypeIds) },
        });

        auditTypes.forEach((auditType: any) => {
          auditTypeMap[auditType.id] = auditType.name;
        });
      }

      // Prepare SA finding items
      // const preparedSAFindingItems = [];

      // for (const item of saFindingItems) {
      //   const prepareSAFindingItem = {
      //     reportFindingFormId: rofId,
      //     selfAssessmentId: item.selfAssessmentId,
      //     isPrimaryFinding: natureFindingMap[item.natureFindingId]?.isPrimary || false,
      //     natureFindingId: item.natureFindingId,
      //     natureFindingName: natureFindingMap[item.natureFindingId]?.name || null,
      //     auditTypeId: item.auditTypeId,
      //     auditTypeName: auditTypeMap[item.auditTypeId] || null,
      //     isUpdatedFinding: item.isUpdatedFinding || false,
      //     findingRemark: item.remarks,
      //     companyId: user.companyId,
      //     createdUserId: user.id,
      //     updatedUserId: user.id,
      //   };

      //   preparedSAFindingItems.push({
      //     ...item,
      //     ...prepareSAFindingItem,
      //   });
      // }

      // // Save SA finding items
      // const savedItems = await manager.save(SAFindingItem, preparedSAFindingItems);

      // // Update fill SA checklist questions if needed
      // for (const item of savedItems) {
      //   if (item.selfAssessmentId) {
      //     await manager.update(
      //       FillSAChecklistQuestion,
      //       {
      //         fillAuditChecklistId: fillChecklistId,
      //         selfDeclarationId: item.selfAssessmentId,
      //       },
      //       {
      //         sAFindingItemId: item.id,
      //         isFinding: true,
      //         updatedUserId: user.id,
      //       },
      //     );
      //   }
      // }

      // Create CARs for non-conformity items
      if (rofId) {
        await manager
          .getCustomRepository(ReportFindingFormRepository)
          ._triggerCreateSACar(manager, user, auditWorkspaceId, false);
      }

      return {
        totalFindings: saFindingItems.length,
        totalNonConformity: saFindingItems.filter(
          (item) =>
            natureFindingMap[item.natureFindingId]?.name ===
            AppConst.NATURE_FINDING_DEFAULT.NON_CONFORMITY,
        ).length,
        totalObservation: saFindingItems.filter(
          (item) =>
            natureFindingMap[item.natureFindingId]?.name ===
            AppConst.NATURE_FINDING_DEFAULT.OBSERVATION,
        ).length,
      };
    } catch (error) {
      LoggerCommon.error(
        '[FillSAChecklistQuestionRepository] _updateSAFindingsForExistingROF error',
        error,
      );
      throw error;
    }
  }

  async listFindingItemsByPR(
    planningRequestId: string,
    user: TokenPayloadModel,
    companyId?: string,
  ) {
    const qb = this.connection
      .getRepository(SAFindingItem)
      .createQueryBuilder('saFindingItems')
      .innerJoin('saFindingItems.auditWorkspace', 'auditWorkspace')
      .leftJoin('saFindingItems.auditType', 'auditType')
      .leftJoin('saFindingItems.fillSAChecklistQuestion', 'fillSAChecklistQuestion')
      .leftJoin('saFindingItems.natureFinding', 'natureFinding')
      .where(
        'auditWorkspace.planningRequestId = :planningRequestId AND auditWorkspace.companyId = :companyId', // TODO: need check status of current audit workspace?
        { planningRequestId, companyId: user.companyId || companyId },
      )
      .select()
      .addSelect(['auditType.name', 'natureFinding.name', 'fillSAChecklistQuestion.standardName']);

    return qb.getMany();
  }

  /**
   * Get self-assessment compliance data with element details
   * @param auditWorkspaceId ID of the audit workspace
   * @param companyId ID of the company
   * @param fillAuditChecklistId Optional ID of the fill audit checklist to filter by
   * @returns Array of compliance data with element information
   */
  async getFillSAComplianceData(
    auditWorkspaceId: string,
    companyId: string,
    fillAuditChecklistId?: string,
  ) {
    try {
      // Base query
      let query = `SELECT 
        "fsacq"."id" as id, 
        "saq"."code" as "elementCode", 
        "saq"."name" as "elementName", 
        "saq"."number" as "elementNumber", 
        "fsacq"."stage" as stage, 
        "fsacq"."assessmentCompliance" as "assessmentCompliance", 
        "fsacq"."auditorCompliance" as "auditorCompliance", 
        "fsacq"."remarks" as remarks, 
        "saq"."id" as "elementId",
        "fsacq"."standardMasterId" as "standardMasterId",
        "fsacq"."fillAuditChecklistId" as "fillAuditChecklistId",
        "fsacq"."sAFindingItemId" as "sAFindingItemId",
        "fsacq"."isFinding" as "isFinding"
      FROM "fill_sa_checklist_question" "fsacq" 
      INNER JOIN "fill_audit_checklist" "fac" ON "fac"."id"="fsacq"."fillAuditChecklistId"  
      INNER JOIN "audit_workspace" "aws" ON "aws"."id"="fac"."auditWorkspaceId" AND ("aws"."id" = $1)  
      INNER JOIN "element_master" "saq" ON "saq"."id"="fsacq"."sAQuestionsId" 
      WHERE "aws"."id" = $2 AND aws."companyId" = $3`;

      // Parameters array
      const params = [auditWorkspaceId, auditWorkspaceId, companyId];

      // Add fillAuditChecklistId filter if provided
      if (fillAuditChecklistId) {
        query += ` AND "fsacq"."fillAuditChecklistId" = $4`;
        params.push(fillAuditChecklistId);
      }

      // Add ordering
      query += ` ORDER BY "saq"."code" ASC`;

      const data = await this.manager.query(query, params);

      return data;
    } catch (error) {
      LoggerCommon.error(
        '[FillSAChecklistQuestionRepository] getFillSAComplianceData error:',
        error,
      );
      throw error;
    }
  }
}
