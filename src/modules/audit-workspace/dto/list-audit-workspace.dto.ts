import { ApiProperty } from '@nestjs/swagger';
import { <PERSON>rrayUnique, IsArray, IsDateString, IsEnum, IsOptional, IsUUID } from 'class-validator';
import { IsGreaterThanOrEqual } from 'svm-nest-lib-v3';
import { ListQueryDto } from '../../../commons/dtos';
import { AuditEntity, AuditWorkspaceStatus } from '../../../commons/enums';
import { FilterField, DataType } from 'src/utils';

export class ListAuditWorkspaceDto extends ListQueryDto {
  @ApiProperty({
    enum: AuditWorkspaceStatus,
    required: false,
    description: 'Status in AuditWorkspaceStatus',
  })
  @IsOptional()
  @IsEnum(AuditWorkspaceStatus)
  status?: string; // common status

  @ApiProperty({
    enum: AuditEntity,
    required: false,
    description: 'Entity Type of AuditWorkspace',
  })
  @IsOptional()
  @IsEnum(AuditEntity)
  entityType?: string;

  @ApiProperty({
    type: 'string',
    description: 'ISO string',
    example: '2020-10-05T14:48:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsDateString({ strict: true })
  planningFrom?: string;

  @ApiProperty({
    type: 'string',
    description: 'ISO string',
    example: '2023-10-05T14:48:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsDateString({ strict: true })
  @IsGreaterThanOrEqual('planningFrom', { message: 'common.INVALID_DATE_RANGE' })
  planningTo?: string;

  @IsOptional()
  @IsUUID('all', { each: true })
  @IsArray()
  @ArrayUnique()
  ids?: string[];
}
export const AUDIT_WORKSPACE_FILTER_FIELDS: FilterField[] = [
  {
    field: 'vesselName',
    column: '"vessel_name"',
    type: DataType.TEXT,
  },
  {
    field: 'vesselTypeName',
    column: '"vesselType_name"',
    type: DataType.TEXT,
  },
  {
    field: 'auditCompany',
    column: '"companyVesselDocHolders_name"',
    type: DataType.TEXT,
  },
  {
    field: 'officeName',
    column: '"auditCompany_name"',
    type: DataType.TEXT,
  },
  {
    field: 'inspectionType',
    column: '"planningRequest_auditTypesName"',
    type: DataType.TEXT,
  },

  {
    field: 'inspectionCarriedIn',
    column: '"workSpace_inspectionCarriedIn"',
    type: DataType.TEXT,
  },
  {
    field: 'inspectionPlannedFromDate',
    column: '"planningRequest_plannedFromDate"',
    type: DataType.DATE,
  },
  {
    field: 'inspectionPlannedToDate',
    column: '"planningRequest_plannedToDate"',
    type: DataType.DATE,
  },
  {
    field: 'globalStatus',
    column: '"planningRequest_globalStatus"',
    type: DataType.TEXT,
  },
  {
    field: 'entityType',
    column: '"planningRequest_entityType"',
    type: DataType.TEXT,
  },
  {
    field: 'vesselCountryFlag',
    column: '"country_name"',
    type: DataType.TEXT,
  },
  {
    field: 'createdDate',
    column: '"workSpace_createdAt"',
    type: DataType.DATE,
  },
  {
    field: 'createdUser',
    column: '"createdUser_username"',
    type: DataType.TEXT,
  },
  {
    field: 'updatedDate',
    column: '"workSpace_updatedAt"',
    type: DataType.DATE,
  },
  {
    field: 'updatedUser',
    column: '"updatedUser_username"',
    type: DataType.TEXT,
  },
  {
    field: 'submittedDate',
    column: '"workSpace_submittedDate"',
    type: DataType.DATE,
  },
  {
    field: 'switchStatus',
    column: '"workSpace_switchStatus"',
    type: DataType.TEXT,
  },
  {
    field: 'department',
    column: '"departments_name"',
    type: DataType.TEXT,
  },
  {
    field: 'createCompanyName',
    column: '"company_name"',
    type: DataType.TEXT,
  },
  {
    field: 'inspectionStartDate',
    column: '"workSpace_mobileInspectionStartDate"',
    type: DataType.DATE,
  },
  {
    field: 'inspectionEndDate',
    column: '"workSpace_mobileInspectionEndDate"',
    type: DataType.DATE,
  },
  {
    field: 'sNo',
    column: '"planningRequest_auditNo"',
    type: DataType.TEXT,
  },
  {
    field: 'refId',
    column: '"workSpace_refNo"',
    type: DataType.TEXT,
  },

  {
    field: 'submittedDateMonth',
    column: '"workSpace_submittedDate_Month"',
    type: DataType.TEXT,
  },
  {
    field: 'submittedDateYear',
    column: '"workSpace_submittedDate_Year"',
    type: DataType.TEXT,
  },
  {
    field: 'voyageType',
    column: '"voyageType_name"',
    type: DataType.TEXT,
  },
  {
    field: 'doos',
    column: '"planningRequest_doos"',
    type: DataType.TEXT,
  },
];
export enum TitleAuditWorkspace {
  VESSEL_NAME = 'Vessel Name',
  VESSEL_TYPE = 'Vessel Type',
  COMPANY = 'Company',
  INSPECTION_TYPE = 'Inspection Type',
  INSPECTION_CARRIED_IN = 'Inspection Carried In',
  INSPECTION_PLANNED_FROM = 'Inspection Planned From Date',
  INSPECTION_PLANNED_TO = 'Inspection Planned To Date',
  GLOBAL_STATUS = 'Global Status',
  ENTITY = 'Entity',
  FLAG = 'Flag',
  CREATED_DATE = 'Created Date',
  CREATED_BY_USER = 'Created By User',
  UPDATED_DATE = 'Updated Date',
  UPDATED_BY_USER = 'Updated By User',
  SUBMISSION_DATE = 'Submission Date',
  SWICH_STATUS = 'Switch Status',
  DEPARTMENT = 'Department',
  CREATED_BY_COMPANY = 'Created By Company',
  INSPECTION_START_DATE = 'Inspection Start Date (App)',
  INSPECTION_END_DATE = 'Inspection End (App)',
  INSPECTION_S_NO = 'Inspection S.No',
  REF_ID = 'Ref.ID',
  SUBMITTED_DATE_MONTH = 'Submitted Date Month',
  SUBMITTED_DATE_YEAR = 'Submitted Date Year',
}
export const EXPORT_FIELDS_AUDIT_WORKSPACE = [
  {
    label: TitleAuditWorkspace.VESSEL_NAME,
    value: TitleAuditWorkspace.VESSEL_NAME,
    default: '-',
  },
  {
    label: TitleAuditWorkspace.VESSEL_TYPE,
    value: TitleAuditWorkspace.VESSEL_TYPE,
    default: '-',
  },
  {
    label: TitleAuditWorkspace.COMPANY,
    value: TitleAuditWorkspace.COMPANY,
    default: '-',
  },
  {
    label: TitleAuditWorkspace.INSPECTION_TYPE,
    value: TitleAuditWorkspace.INSPECTION_TYPE,
    default: '-',
  },
  {
    label: TitleAuditWorkspace.INSPECTION_CARRIED_IN,
    value: TitleAuditWorkspace.INSPECTION_CARRIED_IN,
    default: '-',
  },
  {
    label: TitleAuditWorkspace.INSPECTION_PLANNED_FROM,
    value: TitleAuditWorkspace.INSPECTION_PLANNED_FROM,
    default: '-',
  },
  {
    label: TitleAuditWorkspace.INSPECTION_PLANNED_TO,
    value: TitleAuditWorkspace.INSPECTION_PLANNED_TO,
    default: '-',
  },
  {
    label: TitleAuditWorkspace.GLOBAL_STATUS,
    value: TitleAuditWorkspace.GLOBAL_STATUS,
    default: '-',
  },
  {
    label: TitleAuditWorkspace.ENTITY,
    value: TitleAuditWorkspace.ENTITY,
    default: '-',
  },
  {
    label: TitleAuditWorkspace.FLAG,
    value: TitleAuditWorkspace.FLAG,
    default: '-',
  },
  {
    label: TitleAuditWorkspace.CREATED_DATE,
    value: TitleAuditWorkspace.CREATED_DATE,
    default: '-',
  },
  {
    label: TitleAuditWorkspace.CREATED_BY_USER,
    value: TitleAuditWorkspace.CREATED_BY_USER,
    default: '-',
  },
  {
    label: TitleAuditWorkspace.UPDATED_DATE,
    value: TitleAuditWorkspace.UPDATED_DATE,
    default: '-',
  },
  {
    label: TitleAuditWorkspace.UPDATED_BY_USER,
    value: TitleAuditWorkspace.UPDATED_BY_USER,
    default: '-',
  },
  {
    label: TitleAuditWorkspace.SUBMISSION_DATE,
    value: TitleAuditWorkspace.SUBMISSION_DATE,
    default: '-',
  },
  {
    label: TitleAuditWorkspace.SWICH_STATUS,
    value: TitleAuditWorkspace.SWICH_STATUS,
    default: '-',
  },
  {
    label: TitleAuditWorkspace.DEPARTMENT,
    value: TitleAuditWorkspace.DEPARTMENT,
    default: '-',
  },
  {
    label: TitleAuditWorkspace.CREATED_BY_COMPANY,
    value: TitleAuditWorkspace.CREATED_BY_COMPANY,
    default: '-',
  },
  {
    label: TitleAuditWorkspace.INSPECTION_START_DATE,
    value: TitleAuditWorkspace.INSPECTION_START_DATE,
    default: '-',
  },
  {
    label: TitleAuditWorkspace.INSPECTION_END_DATE,
    value: TitleAuditWorkspace.INSPECTION_END_DATE,
    default: '-',
  },
  {
    label: TitleAuditWorkspace.INSPECTION_S_NO,
    value: TitleAuditWorkspace.INSPECTION_S_NO,
    default: '-',
  },
  {
    label: TitleAuditWorkspace.REF_ID,
    value: TitleAuditWorkspace.REF_ID,
    default: '-',
  },
  {
    label: TitleAuditWorkspace.SUBMITTED_DATE_MONTH,
    value: TitleAuditWorkspace.SUBMITTED_DATE_MONTH,
    default: '-',
  },
  {
    label: TitleAuditWorkspace.SUBMITTED_DATE_YEAR,
    value: TitleAuditWorkspace.SUBMITTED_DATE_YEAR,
    default: '-',
  },
];
