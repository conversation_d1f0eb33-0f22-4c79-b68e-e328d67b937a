import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  ParseUUIDPipe,
  Post,
  Put,
  Query,
  Res,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { Response } from 'express';
import { I18n, I18nContext } from 'nestjs-i18n';
import {
  AuthGuard,
  LoggerCommon,
  RequiredPermissions,
  Roles,
  RoleScope,
  RolesGuard,
  TokenDecorator,
  TokenPayloadModel,
} from 'svm-nest-lib-v3';

import { AuditWorkspaceService } from './audit-workspace.service';
import { ListAuditWorkspaceDto } from './dto/list-audit-workspace.dto';
// import { CreateAuditWorkspaceDto } from './dto';
import { TimezoneDTO } from '../../commons/dtos';
import { ActionEnum, FeatureEnum, SubFeatureEnum } from '../../commons/enums';
import {
  CreateAuditWorkspaceDto,
  ListFillChecklistDetailDto,
  UpdateShowPopupAnalyticalReportDto,
} from './dto';

import { UploadDTO } from '../../commons/dtos/upload.dto';
import { FillAuditChecklistDto } from './dto/create-fill-questions.dto';
import { UpdateMasterOrChiefOfEngineerDto } from './dto/master-or-chief-of-engineer.dto';
import { FillChecklistQuickAnswersDto } from './dto/update-fill-checklist-quick-answer.dto';
import { UpdateReportFindingDto } from './dto/update-report-finding.dto';
import { SignSignatureDto } from './dto/sign-signature.dto';
import { PayloadAGGridDto } from 'src/utils';
import { FillSAChecklistQuestionService } from './fill-sa-checklist-question.service';
import { UpdateFillSAChecklistQuestionDto } from './dto/fill-sa-checklist-question.dto';
import { FileInterceptor } from '@nestjs/platform-express';
import { storageAuditCompliance } from '../transfer-data/storage.config';

@ApiTags('Audit Workspace')
@Controller('audit-workspace')
@UseGuards(AuthGuard, RolesGuard)
@ApiBearerAuth()
export class AuditWorkspaceController {
  constructor(
    private readonly auditWorkspaceService: AuditWorkspaceService,
    private readonly fillSAChecklistQuestionService: FillSAChecklistQuestionService,
  ) {}

  @ApiResponse({ description: 'Create updated ROF success', status: HttpStatus.OK })
  @ApiResponse({ description: 'Create updated ROF error', status: HttpStatus.BAD_REQUEST })
  @ApiBearerAuth()
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: 'workspace id',
  })
  @UseGuards(AuthGuard, RolesGuard)
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @Post('/:workspaceId/create-updated-pdf')
  async createUpdatedFilePdf(
    @Param('workspaceId', ParseUUIDPipe) workspaceId: string,
    @TokenDecorator() user: TokenPayloadModel,
  ) {
    return await this.auditWorkspaceService.createUpdatedFilePdf(workspaceId, user);
  }

  @ApiResponse({ description: 'Create updated ROF success', status: HttpStatus.OK })
  @ApiResponse({ description: 'Create updated ROF error', status: HttpStatus.BAD_REQUEST })
  @ApiBearerAuth()
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: 'workspace id',
  })
  @UseGuards(AuthGuard, RolesGuard)
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @Post('/:workspaceId/generate-rof-pdf')
  async createGenerateROFFilePdf(
    @Param('workspaceId', ParseUUIDPipe) workspaceId: string,
    @TokenDecorator() user: TokenPayloadModel,
    @Res() res: Response,
    @Body() body: any,
  ) {
    return await this.auditWorkspaceService.createGenerateROFFilePdf(workspaceId, user, res, body);
  }

  @ApiResponse({ description: 'Hanle sign signature success', status: HttpStatus.OK })
  @ApiResponse({ description: 'Handle sign signature error', status: HttpStatus.BAD_REQUEST })
  @ApiBody({ type: UploadDTO })
  @ApiBearerAuth()
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: 'workspace id',
  })
  @UseGuards(AuthGuard, RolesGuard)
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @Post('/:workspaceId/sign-signature')
  async handleSignSignature(
    @Param('workspaceId', ParseUUIDPipe) workspaceId: string,
    @Body() body: SignSignatureDto,
    @TokenDecorator() user: TokenPayloadModel,
  ) {
    return await this.auditWorkspaceService.handleSignSignature(workspaceId, user, body);
  }

  @ApiResponse({ description: 'List audit workspace success', status: HttpStatus.OK })
  @ApiResponse({ description: 'List audit workspace error', status: HttpStatus.BAD_REQUEST })
  @ApiQuery({
    description: 'Paginate params',
    type: ListAuditWorkspaceDto,
    required: false,
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_INSPECTION_WORKSPACE,
    action: ActionEnum.VIEW,
  })
  @Get('')
  async listAuditWorkspace(
    @Query() query: ListAuditWorkspaceDto,
    @TokenDecorator() user: TokenPayloadModel,
  ) {
    return this.auditWorkspaceService.listAuditWorkspace(query, user);
  }

  @ApiResponse({ description: 'List audit workspace success', status: HttpStatus.OK })
  @ApiResponse({ description: 'List audit workspace error', status: HttpStatus.BAD_REQUEST })
  @ApiQuery({
    description: 'Paginate params',
    type: ListAuditWorkspaceDto,
    required: false,
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_INSPECTION_WORKSPACE,
    action: ActionEnum.VIEW,
  })
  @Post('/list')
  async getListAuditWorkspace(
    @Query() query: ListAuditWorkspaceDto,
    @Body() body: PayloadAGGridDto,
    @TokenDecorator() user: TokenPayloadModel,
  ) {
    return this.auditWorkspaceService.listAuditWorkspace(query, user, body);
  }

  @ApiResponse({ description: 'Detail workspace success', status: HttpStatus.OK })
  @ApiResponse({ description: 'DetailWorkspace error', status: HttpStatus.BAD_REQUEST })
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: 'Workspace id',
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  // @RequiredPermissions({
  //   feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_INSPECTION_WORKSPACE,
  //   action: ActionEnum.VIEW,
  // })
  @Get('/:id')
  async getDetailAuditWorkspaceGeneral(
    @Param('id', ParseUUIDPipe) id: string,
    @TokenDecorator() token: TokenPayloadModel,
  ) {
    return this.auditWorkspaceService.getDetailAuditWorkspaceGeneral(id, token);
  }

  @ApiResponse({ description: 'Upload PDF success', status: HttpStatus.OK })
  @ApiResponse({
    description: 'Upload PDF error',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiBody({ type: UploadDTO })
  @ApiOperation({ summary: 'Upload PDF mobile', operationId: 'userSync' })
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: 'workspaceId',
  })
  @Post('/:id/signed-rof')
  @HttpCode(HttpStatus.OK)
  async addSignedROf(
    @Body() body: UploadDTO,
    @Param('id', ParseUUIDPipe) id: string,
    @TokenDecorator() token: TokenPayloadModel,
  ) {
    try {
      return await this.auditWorkspaceService.addSignedROf(body, id, token);
    } catch (error) {
      LoggerCommon.error('[AuditWorkspace] addSignedROf error ', error.message || error);
      throw error;
    }
  }

  @ApiResponse({
    description: 'Get list signed files by workspaceId success',
    status: HttpStatus.OK,
  })
  @ApiResponse({
    description: 'Upload PDF error',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiBody({ type: UploadDTO })
  @ApiOperation({ summary: 'Get list signed files by workspaceId', operationId: 'userSync' })
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: 'workspaceId',
  })
  @Get('/:id/signed-rof')
  @HttpCode(HttpStatus.OK)
  async listSignedFiles(
    @Param('id', ParseUUIDPipe) id: string,
    @TokenDecorator() token: TokenPayloadModel,
  ) {
    try {
      const rs = await this.auditWorkspaceService.listSignedFiles(id, token);
      return rs;
    } catch (error) {
      LoggerCommon.error('[AuditWorkspace] addSignedROf error ', error.message || error);
      throw error;
    }
  }

  @ApiResponse({ description: 'List fill checklist success', status: HttpStatus.OK })
  @ApiResponse({ description: 'List fill checklist error', status: HttpStatus.BAD_REQUEST })
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: 'Workspace id',
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  // @RequiredPermissions({
  //   feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_INSPECTION_WORKSPACE,
  //   action: ActionEnum.VIEW,
  // })
  @Get('/:id/fill-checklist')
  async listFillChecklist(
    @Param('id', ParseUUIDPipe) workSpaceId: string,
    @TokenDecorator() user: TokenPayloadModel,
  ) {
    return this.auditWorkspaceService.listFillChecklist(workSpaceId, user);
  }

  @ApiResponse({ description: 'List fill checklist export excel success', status: HttpStatus.OK })
  @ApiResponse({
    description: 'List fill checklist export excel error',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: 'Workspace id',
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_INSPECTION_WORKSPACE,
    action: ActionEnum.VIEW,
  })
  @Get('/:id/fill-checklist/export')
  async exportExcel(
    @Param('id', ParseUUIDPipe) workSpaceId: string,
    @Res() res: Response,
    @TokenDecorator() user: TokenPayloadModel,
  ) {
    const result = await this.auditWorkspaceService.exportExcel(workSpaceId, user);
    const workbook = result.workbook;
    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
    res.setHeader('Content-Disposition', 'attachment; filename=' + `${result.fileName}.xlsx`);
    return workbook.xlsx.write(res).then(function () {
      res.status(200).end();
    });
  }

  @ApiResponse({ description: 'Export Audit Workspace success', status: HttpStatus.OK })
  @ApiResponse({ description: 'Export Audit Workspace error', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({ summary: 'Export Audit Workspace', operationId: 'ExportAW' })
  @ApiBody({ type: PayloadAGGridDto })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_INSPECTION_WORKSPACE,
    action: ActionEnum.VIEW,
  })
  @Post('/exportAuditWorkspace')
  @HttpCode(HttpStatus.OK)
  async exportAuditWorkspace(
    @Query() query: ListAuditWorkspaceDto,
    @Body() body: PayloadAGGridDto,
    @TokenDecorator() token: TokenPayloadModel,
    @Res() res: Response,
  ) {
    return await this.auditWorkspaceService.exportAuditWorkSpace(query, token, body, res);
  }

  @ApiResponse({ description: 'List finding summary success', status: HttpStatus.OK })
  @ApiResponse({ description: 'List finding summary error', status: HttpStatus.BAD_REQUEST })
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: 'Workspace id',
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  // @RequiredPermissions({
  //   feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_INSPECTION_WORKSPACE,
  //   action: ActionEnum.VIEW,
  // })
  @Get('/:id/finding-summary')
  async listFindingSummaries(@Param('id', ParseUUIDPipe) workSpaceId: string) {
    return this.auditWorkspaceService.listFindingSummaries(workSpaceId);
  }

  @ApiResponse({ description: 'List onboard finding success', status: HttpStatus.OK })
  @ApiResponse({ description: 'List onboard finding error', status: HttpStatus.BAD_REQUEST })
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: 'Workspace id',
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @Get('/:id/onboard-finding')
  async listOnboardFinding(@Param('id', ParseUUIDPipe) workSpaceId: string) {
    return this.auditWorkspaceService.listOnboardFindingItem(workSpaceId);
  }

  @ApiResponse({ description: 'getFillCheckListDetail success', status: HttpStatus.OK })
  @ApiResponse({ description: 'getFillCheckListDetail error', status: HttpStatus.BAD_REQUEST })
  @ApiQuery({
    description: 'Paginate params',
    type: ListFillChecklistDetailDto,
    required: false,
  })
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: 'fill check list id',
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_INSPECTION_WORKSPACE,
    action: ActionEnum.VIEW,
  })
  @Get('/:id/fill-checklist/:fillChecklistId')
  async getFillCheckListDetail(
    @Query() query: ListFillChecklistDetailDto,
    @Param('id', ParseUUIDPipe) workSpaceId: string,
    @Param('fillChecklistId', ParseUUIDPipe) fillChecklistId: string,
    @TokenDecorator() user: TokenPayloadModel,
  ) {
    return this.auditWorkspaceService.getFillCheckListDetail(
      query,
      workSpaceId,
      fillChecklistId,
      user,
    );
  }

  @ApiResponse({ description: 'getGroupQuestions success', status: HttpStatus.OK })
  @ApiResponse({ description: 'getGroupQuestions error', status: HttpStatus.BAD_REQUEST })
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: 'fill check list id',
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_INSPECTION_WORKSPACE,
    action: ActionEnum.VIEW,
  })
  @Get('/:id/group-questions/:fillChecklistId')
  async getGroupQuestions(
    @Query() query: ListFillChecklistDetailDto,
    @Param('id', ParseUUIDPipe) workSpaceId: string,
    @Param('fillChecklistId', ParseUUIDPipe) fillChecklistId: string,
    @TokenDecorator() user: TokenPayloadModel,
  ) {
    return this.auditWorkspaceService.getGroupQuestions(query, workSpaceId, fillChecklistId, user);
  }

  @ApiResponse({ description: 'checkCompleteCheckList success', status: HttpStatus.OK })
  @ApiResponse({ description: 'checkCompleteCheckList error', status: HttpStatus.BAD_REQUEST })
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: 'fill check list id',
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_INSPECTION_WORKSPACE,
    action: ActionEnum.VIEW,
  })
  @Get('/:id/fill-checklist/:fillChecklistId/check-complete')
  async checkCompleteCheckList(
    @Param('id', ParseUUIDPipe) workSpaceId: string,
    @Param('fillChecklistId', ParseUUIDPipe) fillChecklistId: string,
    @TokenDecorator() user: TokenPayloadModel,
  ) {
    return this.auditWorkspaceService.checkCompleteCheckList(workSpaceId, fillChecklistId, user);
  }

  @ApiResponse({ description: 'update fill checklist success', status: HttpStatus.OK })
  @ApiResponse({ description: 'update fill checklist error', status: HttpStatus.BAD_REQUEST })
  @ApiParam({
    name: 'fillChecklistId',
    type: 'string',
    required: true,
    description: 'fill check list id',
  })
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: 'workspace id',
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_INSPECTION_WORKSPACE,
    action: ActionEnum.UPDATE,
  })
  @Put('/:id/fill-checklist/:fillChecklistId')
  async updatedFillChecklistDetail(
    @Param('id', ParseUUIDPipe) auditWorkspaceId: string,
    @Param('fillChecklistId', ParseUUIDPipe) fillChecklistId: string,
    @Body() body: FillAuditChecklistDto,
    @TokenDecorator() user: TokenPayloadModel,
    @I18n() i18n: I18nContext,
  ) {
    await this.auditWorkspaceService.updatedFillChecklistDetail(
      fillChecklistId,
      body,
      user,
      auditWorkspaceId,
    );
    return {
      message: await i18n.t('common.UPDATE_SUCCESS'),
    };
  }

  @ApiResponse({ description: 'quick answer success', status: HttpStatus.OK })
  @ApiResponse({ description: 'quick answer error', status: HttpStatus.BAD_REQUEST })
  @ApiParam({
    name: 'fillChecklistId',
    type: 'string',
    required: true,
    description: 'fill check list id',
  })
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: 'workspace id',
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_INSPECTION_WORKSPACE,
    action: ActionEnum.UPDATE,
  })
  @Post('/:id/quick-answers/:fillChecklistId')
  async updatedAnswers(
    @Param('id', ParseUUIDPipe) auditWorkspaceId: string,
    @Param('fillChecklistId', ParseUUIDPipe) fillChecklistId: string,
    @Body() body: FillChecklistQuickAnswersDto,
    @TokenDecorator() user: TokenPayloadModel,
    @I18n() i18n: I18nContext,
  ) {
    await this.auditWorkspaceService.updatedAnswers(fillChecklistId, body, user, auditWorkspaceId);
    return {
      message: await i18n.t('common.UPDATE_SUCCESS'),
    };
  }

  @ApiResponse({ description: 'submit success', status: HttpStatus.OK })
  @ApiResponse({ description: 'submit error', status: HttpStatus.BAD_REQUEST })
  @ApiParam({
    name: 'fillChecklistId',
    type: 'string',
    required: true,
    description: 'fill check list id',
  })
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: 'workspace id',
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_INSPECTION_WORKSPACE,
    action: ActionEnum.UPDATE,
  })
  @Put('/:id/fill-checklist/:fillChecklistId/submit')
  async submitFinalChecklist(
    @Param('id', ParseUUIDPipe) auditWorkspaceId: string,
    @Param('fillChecklistId', ParseUUIDPipe) fillChecklistId: string,
    // @Body() body: FillAuditChecklistDto,
    @TokenDecorator() user: TokenPayloadModel,
    @I18n() i18n: I18nContext,
  ) {
    await this.auditWorkspaceService.submitFinalChecklist(
      fillChecklistId,
      // body,
      user,
      auditWorkspaceId,
    );
    return {
      message: await i18n.t('common.SUBMIT_SUCCESS'),
    };
  }

  @ApiResponse({ description: 'submit workspace success', status: HttpStatus.OK })
  @ApiResponse({ description: 'submit workspace error', status: HttpStatus.BAD_REQUEST })
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: 'workspace id',
  })
  @ApiBody({ type: TimezoneDTO })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_INSPECTION_WORKSPACE,
    action: ActionEnum.UPDATE,
  })
  @Put('/:id/submit')
  async submitFinalWorkspace(
    @Param('id', ParseUUIDPipe) auditWorkspaceId: string,
    @TokenDecorator() user: TokenPayloadModel,
    @Body() body: TimezoneDTO,
    @I18n() i18n: I18nContext,
  ) {
    await this.auditWorkspaceService.submitFinalWorkspace(user, auditWorkspaceId, body);
    return {
      message: await i18n.t('common.SUBMIT_SUCCESS'),
    };
  }

  @ApiResponse({ description: 'update finding-summary success', status: HttpStatus.OK })
  @ApiResponse({ description: 'finding-summary error', status: HttpStatus.BAD_REQUEST })
  @ApiParam({
    name: 'findingItemId',
    type: 'string',
    required: true,
    description: 'findingItemId id',
  })
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: 'workspace id',
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_INSPECTION_WORKSPACE,
    action: ActionEnum.UPDATE,
  })
  @Put('/:id/finding-item/:findingItemId')
  async updateFindingItem(
    @Param('id', ParseUUIDPipe) auditWorkspaceId: string,
    @Param('findingItemId', ParseUUIDPipe) findingItemId: string,
    @Body() body: UpdateReportFindingDto,
    @TokenDecorator() user: TokenPayloadModel,
    @I18n() i18n: I18nContext,
  ) {
    await this.auditWorkspaceService.updateFindingItem(auditWorkspaceId, findingItemId, body, user);
    return {
      message: await i18n.t('common.UPDATED_SUCCESS'),
    };
  }

  @ApiResponse({
    description: 'trigger work-space and fill-checklist success',
    status: HttpStatus.OK,
  })
  @ApiResponse({
    description: 'trigger work-space and fill-checklist success',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiBody({ type: CreateAuditWorkspaceDto })
  @ApiParam({
    name: 'fillChecklistId',
    type: 'string',
    required: true,
    description: 'fill-checklist id',
  })
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: 'workspace id',
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @Post('/:id/fill-checklist/:fillChecklistId/trigger')
  async triggerWorkspace(
    @Param('id', ParseUUIDPipe) auditWorkspaceId: string,
    @Param('fillChecklistId', ParseUUIDPipe) fillChecklistId: string,
    @TokenDecorator() user: TokenPayloadModel,
    @Body() body: CreateAuditWorkspaceDto,
    @I18n() i18n: I18nContext,
  ) {
    await this.auditWorkspaceService.triggerAwsAndFillChkList(
      auditWorkspaceId,
      fillChecklistId,
      user,
      body,
    );
    return {
      message: await i18n.t('common.UPDATED_SUCCESS'),
    };
  }

  @ApiResponse({ description: 'Create audit workspace success', status: HttpStatus.OK })
  @ApiResponse({ description: 'Create audit workspace error', status: HttpStatus.BAD_REQUEST })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @Post('/:workspaceId/fill-check-list/:checklistId/create-pdf')
  async createAuditWorkspacePdf(
    @Param('workspaceId', ParseUUIDPipe) workspaceId: string,
    @Param('checklistId', ParseUUIDPipe) checklistId: string,
    @Res() res: Response,
    @TokenDecorator() user: TokenPayloadModel,
  ) {
    await this.auditWorkspaceService.createAuditWorkspacePdf(workspaceId, checklistId, user, res);
  }

  @ApiResponse({ description: 'Get summary workspace success', status: HttpStatus.OK })
  @ApiResponse({ description: 'Get summary workspace error', status: HttpStatus.BAD_REQUEST })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  // @RequiredPermissions({
  //   feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_INSPECTION_WORKSPACE,
  //   action: ActionEnum.VIEW,
  // })
  @Get('/:id/summary')
  async getSummaryData(
    @Param('id', ParseUUIDPipe) id: string,
    @TokenDecorator() token: TokenPayloadModel,
  ) {
    return this.auditWorkspaceService.getSummaryData(id, token);
  }

  @ApiResponse({
    description: 'update show popup analytical report success',
    status: HttpStatus.OK,
  })
  @ApiResponse({
    description: 'update show popup analytical report workspace error',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: 'workspace id',
  })
  @ApiBody({ type: UpdateShowPopupAnalyticalReportDto })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @Put('/:id/show-popup-analytical-report')
  async updateShowPopupAnalyticalReport(
    @Param('id', ParseUUIDPipe) auditWorkspaceId: string,
    @TokenDecorator() user: TokenPayloadModel,
    @Body() body: UpdateShowPopupAnalyticalReportDto,
    @I18n() i18n: I18nContext,
  ) {
    await this.auditWorkspaceService.updateShowPopupAnalyticalReport(user, auditWorkspaceId, body);
    return {
      message: await i18n.t('common.UPDATE_SUCCESS'),
    };
  }

  @ApiResponse({
    description: 'update master or chief of engineer workspace success',
    status: HttpStatus.OK,
  })
  @ApiResponse({
    description: 'update master or chief of engineer workspace error',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiBody({ type: UpdateMasterOrChiefOfEngineerDto })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @Put('/:id/master-or-chief-of-engineer')
  async updateMasterOrChiefOfEngineer(
    @Param('id', ParseUUIDPipe) auditWorkspaceId: string,
    @TokenDecorator() user: TokenPayloadModel,
    @Body() body: UpdateMasterOrChiefOfEngineerDto,
    @I18n() i18n: I18nContext,
  ) {
    await this.auditWorkspaceService.updateMasterOrChiefOfEngineer(user, auditWorkspaceId, body);
    return {
      message: await i18n.t('common.UPDATE_SUCCESS'),
    };
  }

  // Developed by - Bala Subramanian
  // check if the finding summary and checklist findings are mismatched this API can recreate the findings in findings summary table
  @ApiResponse({ description: 'check fill checklist success', status: HttpStatus.OK })
  @ApiResponse({ description: 'check fill checklist error', status: HttpStatus.BAD_REQUEST })
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: 'workspace id',
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_INSPECTION_WORKSPACE,
    action: ActionEnum.UPDATE,
  })
  @Put('/:id/checkFillChecklistAndFindings')
  async checkFillChecklistAndFindings(
    @Param('id', ParseUUIDPipe) auditWorkspaceId: string,
    @TokenDecorator() user: TokenPayloadModel,
    @I18n() i18n: I18nContext,
  ) {
    return await this.auditWorkspaceService.checkFillChecklistAndFindings(user, auditWorkspaceId);
  }

  @ApiResponse({ description: 'Export zip files fill checklist success', status: HttpStatus.OK })
  @ApiResponse({
    description: 'Export zip files fill checklist error',
    status: HttpStatus.BAD_REQUEST,
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @Post('/:workspaceId/fill-check-list/:checklistId/export-zip-files')
  async exportZipFilesChecklist(
    @Param('workspaceId', ParseUUIDPipe) workspaceId: string,
    @Param('checklistId', ParseUUIDPipe) checklistId: string,
    @Res() res: Response,
    @TokenDecorator() user: TokenPayloadModel,
  ) {
    await this.auditWorkspaceService.exportZipFilesChecklist(workspaceId, checklistId, user, res);
  }

  @ApiResponse({ description: 'Get fillSAChecklistQuestion by ID success', status: HttpStatus.OK })
  @ApiResponse({
    description: 'Get fillSAChecklistQuestion by ID with compliance statistics',
    status: HttpStatus.OK,
  })
  @ApiResponse({
    description: 'Get fillSAChecklistQuestion by ID error',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiParam({
    name: 'fillChecklistId',
    type: 'string',
    required: true,
    description: 'Fill Checklist ID',
  })
  @ApiParam({
    name: 'auditWorkspaceId',
    type: 'string',
    required: true,
    description: 'Audit Workspace ID',
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_INSPECTION_WORKSPACE,
    action: ActionEnum.VIEW,
  })
  @Get('/:auditWorkspaceId/fill-sa-checklist/:fillChecklistId')
  async getFillSAChecklistQuestionById(
    @Param('fillChecklistId', ParseUUIDPipe) fillChecklistId: string,
    @Param('auditWorkspaceId', ParseUUIDPipe) auditWorkspaceId: string,
    @Query() query: any,
    @TokenDecorator() user: TokenPayloadModel,
  ) {
    return await this.fillSAChecklistQuestionService.getFillSAChecklistQuestions(
      fillChecklistId,
      auditWorkspaceId,
      user,
      query,
    );
  }

  @ApiResponse({
    description: 'Get fill SA checklist element counts/statistics success',
    status: HttpStatus.OK,
  })
  @ApiResponse({
    description: 'Get fill SA checklist element counts/statistics error',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiParam({
    name: 'fillChecklistId',
    type: 'string',
    required: true,
    description: 'Fill Checklist ID',
  })
  @ApiParam({
    name: 'auditWorkspaceId',
    type: 'string',
    required: true,
    description: 'Audit Workspace ID',
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_INSPECTION_WORKSPACE,
    action: ActionEnum.VIEW,
  })
  @Get('/:auditWorkspaceId/fill-sa-checklist/:fillChecklistId/element-counts')
  async getFillSAChecklistElementCounts(
    @Param('fillChecklistId', ParseUUIDPipe) fillChecklistId: string,
    @Param('auditWorkspaceId', ParseUUIDPipe) auditWorkspaceId: string,
    @TokenDecorator() user: TokenPayloadModel,
  ) {
    return await this.fillSAChecklistQuestionService.getFillSAChecklistElementCounts(
      fillChecklistId,
      auditWorkspaceId,
      user,
    );
  }

  @ApiResponse({ description: 'Update fillSAChecklistQuestion success', status: HttpStatus.OK })
  @ApiResponse({
    description: 'Update fillSAChecklistQuestion error',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiParam({
    name: 'checklistId',
    type: 'string',
    required: true,
    description: 'Fill Checklist ID',
  })
  @ApiParam({
    name: 'selfAssessmentId',
    type: 'string',
    required: true,
    description: 'Self Assessment Question ID',
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_INSPECTION_WORKSPACE,
    action: ActionEnum.UPDATE,
  })
  @Put('/:auditWorkspaceId/fill-sa-checklist/:fillChecklistId')
  async updateFillSAChecklistQuestion(
    @Param('auditWorkspaceId', ParseUUIDPipe) auditWorkspaceId: string,
    @Param('fillChecklistId', ParseUUIDPipe) fillChecklistId: string,
    @Body() updateDto: UpdateFillSAChecklistQuestionDto,
    @TokenDecorator() user: TokenPayloadModel,
    @I18n() i18n: I18nContext,
  ) {
    return this.fillSAChecklistQuestionService.updateFillSAChecklistQuestion(
      auditWorkspaceId,
      fillChecklistId,
      updateDto,
      user,
      i18n,
    );
  }

  @ApiOperation({
    summary: 'Import self-assessment compliance data from Excel file',
    operationId: 'importSACompliance',
    description:
      'Upload an Excel file with self-assessment compliance data to update auditor compliance statuses',
  })
  @ApiConsumes('multipart/form-data')
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: 'Audit workspace ID',
  })
  @ApiQuery({
    name: 'fillAuditChecklistId',
    type: 'string',
    required: false,
    description: 'Fill Audit Checklist ID to filter by a specific standard master',
  })
  @ApiBody({
    schema: {
      type: 'object',
      required: ['file'],
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'Excel file containing self-assessment compliance data',
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Successfully imported self-assessment compliance data',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid file format or data structure',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Not authorized to access this resource',
  })
  @HttpCode(HttpStatus.OK)
  @Post('/:id/import-sa-compliance')
  @UseInterceptors(FileInterceptor('file', { ...storageAuditCompliance }))
  async importSACompliance(
    @UploadedFile() file: Express.Multer.File,
    @Param('id', ParseUUIDPipe) auditWorkspaceId: string,
    @TokenDecorator() user: TokenPayloadModel,
    @Res() res: Response,
    @Query('fillAuditChecklistId') fillAuditChecklistId?: string,
  ) {
    try {
      const result = await this.auditWorkspaceService.importSACompliance(
        file,
        user,
        auditWorkspaceId,
        fillAuditChecklistId,
      );
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      LoggerCommon.error(
        `[AuditWorkspace] importSACompliance error for workspace ${auditWorkspaceId}:`,
        error.message || error,
      );

      const status = error.status || HttpStatus.BAD_REQUEST;
      const message = error.message || 'Failed to import self-assessment compliance data';

      return res.status(status).json({
        error: message,
        statusCode: status,
      });
    }
  }

  @ApiOperation({
    summary: 'Download self-assessment compliance data as Excel file',
    operationId: 'downloadSACompliance',
    description:
      'Exports self-assessment compliance data to an Excel file for the specified audit workspace',
  })
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: 'Audit workspace ID',
  })
  @ApiQuery({
    name: 'fillAuditChecklistId',
    type: 'string',
    required: false,
    description: 'Fill Audit Checklist ID to filter by a specific standard master',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Excel file with self-assessment compliance data',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid request or audit workspace not found',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Not authorized to access this resource',
  })
  @Get('/:id/download-sa-compliance')
  async downloadSACompliance(
    @Param('id', ParseUUIDPipe) auditWorkspaceId: string,
    @TokenDecorator() user: TokenPayloadModel,
    @Res() res: Response,
    @Query('fillAuditChecklistId') fillAuditChecklistId?: string,
  ) {
    try {
      const { workbook, fileName } = await this.auditWorkspaceService.downloadSACompliance(
        auditWorkspaceId,
        user,
        fillAuditChecklistId,
      );

      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
      res.setHeader('Content-Disposition', 'attachment; filename=' + `${fileName}`);

      await workbook.xlsx.write(res);
      res.end();
    } catch (error) {
      LoggerCommon.error(
        `[AuditWorkspace] downloadSACompliance error for workspace ${auditWorkspaceId}:`,
        error.message || error,
      );

      const status = error.status || HttpStatus.BAD_REQUEST;
      const message = error.message || 'Failed to download self-assessment compliance data';

      return res.status(status).json({
        error: message,
        statusCode: status,
      });
    }
  }
}
