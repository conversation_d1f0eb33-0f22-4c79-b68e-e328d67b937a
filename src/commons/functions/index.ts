import * as https from 'https';
import { PDFDocument } from 'pdf-lib';
import APP_CONFIG from '../../configs/app.config';
import { VesselCharterer } from '../../modules/vessel/entity/vessel-charterer.entity';
import { VesselDocHolder } from '../../modules/vessel/entity/vessel-doc-holder.entity';
import { VesselOwner } from '../../modules/vessel/entity/vessel-owner.entity';
import { MyCrypto } from '../../utils/my-crypto';
import { EntityManager } from 'typeorm';
import { AuditEntity, COMPANY_TYPE_NAME_FIXED, CompanyLevelEnum, ROLE_NAME_FIXED } from '../enums';
import { TokenPayloadModel } from 'svm-nest-lib-v3';
import { Company } from '../../modules/company/company.entity';
import { User } from '../../modules/user/user.entity';
import path, { resolve } from 'path';
import fs from 'fs';
import extract from 'extract-zip';

export async function _supportWhereDOCChartererOwner(
  manager: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  companyId: string,
  table: string,
  keyRefVessel?: string,
  aliasVesselDocHolder?: string,
  aliasVesselCharterer?: string,
  aliasVesselOwner?: string,
) {
  let tableRefVessel = table;
  if (keyRefVessel) {
    tableRefVessel = keyRefVessel;
  }
  if (!aliasVesselDocHolder) {
    aliasVesselDocHolder = 'vesselDocHolders';
  }
  if (!aliasVesselCharterer) {
    aliasVesselCharterer = 'vesselCharterers';
  }
  if (!aliasVesselOwner) {
    aliasVesselOwner = 'vesselOwners';
  }

  const listDOCHolder = await manager.find(VesselDocHolder, {
    deleted: false,
    companyId: companyId,
  });

  const listVesselCharterers = await manager.find(VesselCharterer, {
    deleted: false,
    companyId: companyId,
  });

  const listVesselOwners = await manager.find(VesselOwner, {
    deleted: false,
    companyId: companyId,
  });
  let whereForExternalArr = [];
  let whereForMainAndInternalArr = [];
  if (listDOCHolder.length > 0) {
    for (const item of listDOCHolder) {
      const { whereForExternal, whereForMainAndInternal } = getConditionVesselQuery(
        aliasVesselDocHolder,
        tableRefVessel,
        item,
      );
      whereForExternalArr.push(whereForExternal);
      whereForMainAndInternalArr.push(whereForMainAndInternal);
    }
  }

  if (listVesselCharterers.length > 0) {
    for (const item of listVesselCharterers) {
      const { whereForExternal, whereForMainAndInternal } = getConditionVesselQuery(
        aliasVesselCharterer,
        tableRefVessel,
        item,
      );
      whereForExternalArr.push(whereForExternal);
      whereForMainAndInternalArr.push(whereForMainAndInternal);
    }
  }
  if (listVesselOwners.length > 0) {
    for (const item of listVesselOwners) {
      const { whereForExternal, whereForMainAndInternal } = getConditionVesselQuery(
        aliasVesselOwner,
        tableRefVessel,
        item,
      );
      whereForExternalArr.push(whereForExternal);
      whereForMainAndInternalArr.push(whereForMainAndInternal);
    }
  }
  const whereForExternal = whereForExternalArr.join('');
  const whereForMainAndInternal = whereForMainAndInternalArr.join('');
  return { whereForExternal, whereForMainAndInternal };
}

export function getConditionVesselQuery(table: string, tableRefVessel: string, item) {
  const whereForExternal = ` or (${table}.companyId = '${
    item.companyId
  }' and ${table}.fromDate >='${
    item.fromDate ? item.fromDate.toISOString() : new Date().toISOString()
  }' and ${table}.fromDate <='${
    item.toDate ? item.createdAt.toISOString() : new Date().toISOString()
  }' and ${table}.responsiblePartyInspection = true and ${tableRefVessel}.vesselId = '${
    item.vesselId
  }')`;
  const whereForMainAndInternal = ` or (${table}.companyId = '${item.companyId}' and ${tableRefVessel}.vesselId = '${item.vesselId}')`;

  return { whereForExternal, whereForMainAndInternal };
}

export async function _supportCheckRoleScopeForGetList(
  manager: EntityManager,
  queryBuilder,
  token: TokenPayloadModel,
  whereForMainAndInternal,
  whereForExternal,
  table: string,
  needJoin?: boolean,
  isForVessel?: boolean,
  currentModule?: string,
) {
  if (needJoin) {
    queryBuilder
      .leftJoin('planningRequest.userAssignments', 'userAssignments')
      .leftJoin('planningRequest.auditors', 'auditors')
      .leftJoin('userAssignments.user', 'user')
      .leftJoin('vessel.divisionMapping', 'divisionMapping')
      .leftJoin('divisionMapping.division', 'division')
      .leftJoin('division.users', 'users')
      .leftJoin('vessel.vesselDocHolders', 'vesselDocHolders')
      .leftJoin('vessel.vesselOwners', 'vesselOwners')
      .leftJoin('vessel.vesselCharterers', 'vesselCharterers')
      .leftJoin('vesselCharterers.company', 'companyVesselCharterers');
  }
  if (
    token.companyLevel === CompanyLevelEnum.EXTERNAL_COMPANY &&
    (await checkRoleAndCompanyType(manager, token))
  ) {
    if (currentModule === 'workSpace') {
      queryBuilder.andWhere(
        `((${table}.entityType = '${AuditEntity.VESSEL}' and (${table}.leadAuditorId = '${token.id}' or '${token.id}' = ANY(workSpace.auditors))) 
          or (${table}.entityType = '${AuditEntity.OFFICE}' and (${table}.leadAuditorId = '${token.id}' or '${token.id}' = ANY(workSpace.auditors))))`,
      );
    } else {
      queryBuilder.andWhere(
        `((${table}.entityType = '${AuditEntity.VESSEL}' and (auditors.id = '${token.id}' or user.id = '${token.id}')) 
          or (${table}.entityType = '${AuditEntity.OFFICE}' and (auditors.id = '${token.id}' or user.id = '${token.id}' or ${table}.auditCompanyId = '${token.explicitCompanyId}')))`,
      );
    }
  } else {
    if (isForVessel) {
      if (
        token.companyLevel === CompanyLevelEnum.MAIN_COMPANY ||
        token.companyLevel === CompanyLevelEnum.INTERNAL_COMPANY
      ) {
        queryBuilder.andWhere(`( users.id = '${token.id}' ` + whereForMainAndInternal + ')');
      } else {
        queryBuilder.andWhere(`( users.id = '${token.id}' ` + whereForExternal + ')');
      }
    } else if (token.companyLevel === CompanyLevelEnum.MAIN_COMPANY) {
      const orWhereConditionEntityOffice = await _supportWhereMainCompanyEntityTypeOffice(
        manager,
        token.companyId,
      );
      queryBuilder.andWhere(
        `((${table}.entityType = '${AuditEntity.VESSEL}' and ( users.id = '${token.id}' ` +
          whereForMainAndInternal +
          ` )) or (${table}.entityType = '${AuditEntity.OFFICE}' and (auditors.id = '${token.id}'
                 or user.id = '${token.id}' 
                 or ${table}.auditCompanyId = '${token.explicitCompanyId}' 
                 or ${orWhereConditionEntityOffice})))`,
      );
    } else if (token.companyLevel === CompanyLevelEnum.INTERNAL_COMPANY) {
      queryBuilder.andWhere(
        `((${table}.entityType = '${AuditEntity.VESSEL}' and ( users.id = '${token.id}' ` +
          whereForMainAndInternal +
          ` )) or (${table}.entityType = '${AuditEntity.OFFICE}' 
                and (auditors.id = '${token.id}' 
                or user.id = '${token.id}' 
                or ${table}.auditCompanyId = '${token.explicitCompanyId}')))`,
      );
    } else {
      queryBuilder.andWhere(
        `((${table}.entityType = '${AuditEntity.VESSEL}' and ( users.id = '${token.id}' ` +
          whereForExternal +
          ` )) or (${table}.entityType = '${AuditEntity.OFFICE}' 
                and (auditors.id = '${token.id}' 
                or user.id = '${token.id}' 
                or ${table}.auditCompanyId = '${token.explicitCompanyId}')))`,
      );
    }
  }
}

export async function _supportWhereMainCompanyEntityTypeOffice(
  manager: EntityManager,
  companyId: string,
) {
  const listCompany = await manager
    .getRepository(Company)
    .createQueryBuilder('company')
    .where('company.parentId = :companyId and company.deleted = false', {
      companyId,
    })
    .select(['company.id'])
    .getMany();
  let orWhereCondition = '1=1';
  if (listCompany.length > 0) {
    // get all user belong list company
    const listCompanyId = [];
    for (const company of listCompany) {
      orWhereCondition += ` or planningRequest.auditCompanyId = '${company.id}'`;
      listCompanyId.push(company.id);
    }
    const listUser = await manager
      .getRepository(User)
      .createQueryBuilder('user')
      .where('user.companyId IN (:...listCompanyId) and user.deleted = false', {
        listCompanyId,
      })
      .select(['user.id'])
      .getMany();

    for (const user of listUser) {
      orWhereCondition += ` or auditors.id = '${user.id}' or user.id = '${user.id}'`;
    }
  }
  return orWhereCondition;
}

export async function checkRoleAndCompanyType(manager: EntityManager, user: TokenPayloadModel) {
  const companyQuery = manager
    .getRepository(Company)
    .createQueryBuilder('company')
    .leftJoin('company.companyTypes', 'companyTypes')
    .andWhere(
      'company.id = :companyId and company.deleted = false and company.companyLevel = :companyLevel and companyTypes.companyType = :companyType',
      {
        companyId: user.explicitCompanyId,
        companyLevel: CompanyLevelEnum.EXTERNAL_COMPANY,
        companyType: COMPANY_TYPE_NAME_FIXED.INSPECTION_SERVICES,
      },
    );
  const rawQuery = `
              select
                ur."userId"
              from
                user_role ur
              left join role on
                ur."roleId" = role.id
              where
               ( role."companyId" = '${user.companyId}' OR role."companyId" IS NULL )
                and 
              role.name in ('${ROLE_NAME_FIXED.INSPECTOR}', '${ROLE_NAME_FIXED.INTERNAL_INSPECTOR}','${ROLE_NAME_FIXED.EXTERNAL_INSPECTOR}') 
                and ur."userId" = '${user.id}'
    `;
  const [company, listUserRole] = await Promise.all([
    companyQuery.getOne(),
    manager.query(rawQuery),
  ]);
  return company && listUserRole && listUserRole.length > 0;
}

export async function _supportWhereDOCChartererOwnerRawQuery(
  manager: EntityManager,
  companyId: string,
  aliasTable?: string,
  aliasVesselDocHolder?: string,
  aliasVesselCharterer?: string,
  aliasVesselOwner?: string,
) {
  if (!aliasTable) {
    aliasTable = 'pr';
  }
  if (!aliasVesselDocHolder) {
    aliasVesselDocHolder = 'vdh';
  }
  if (!aliasVesselCharterer) {
    aliasVesselCharterer = 'vc';
  }
  if (!aliasVesselOwner) {
    aliasVesselOwner = 'vo';
  }
  const listDOCHolder = await manager.find(VesselDocHolder, {
    deleted: false,
    companyId: companyId,
  });

  const listVesselCharterers = await manager.find(VesselCharterer, {
    deleted: false,
    companyId: companyId,
  });

  const listVesselOwners = await manager.find(VesselOwner, {
    deleted: false,
    companyId: companyId,
  });
  let whereForExternalArr = [];
  let whereForMainAndInternalArr = [];
  if (listDOCHolder.length > 0) {
    for (const item of listDOCHolder) {
      const { whereForExternal, whereForMainAndInternal } = getConditionVesselRawQuery(
        aliasVesselDocHolder,
        aliasTable,
        item,
      );
      whereForExternalArr.push(whereForExternal);
      whereForMainAndInternalArr.push(whereForMainAndInternal);
    }
  }

  if (listVesselCharterers.length > 0) {
    for (const item of listVesselCharterers) {
      const { whereForExternal, whereForMainAndInternal } = getConditionVesselRawQuery(
        aliasVesselCharterer,
        aliasTable,
        item,
      );
      whereForExternalArr.push(whereForExternal);
      whereForMainAndInternalArr.push(whereForMainAndInternal);
    }
  }
  if (listVesselOwners.length > 0) {
    for (const item of listVesselOwners) {
      const { whereForExternal, whereForMainAndInternal } = getConditionVesselRawQuery(
        aliasVesselOwner,
        aliasTable,
        item,
      );
      whereForExternalArr.push(whereForExternal);
      whereForMainAndInternalArr.push(whereForMainAndInternal);
    }
  }
  const whereForExternal = whereForExternalArr.join('');
  const whereForMainAndInternal = whereForMainAndInternalArr.join('');
  return { whereForExternal, whereForMainAndInternal };
}

export function getConditionVesselRawQuery(table: string, tableRefVessel: string, item) {
  const whereForExternal = ` or (${table}."companyId" = '${
    item.companyId
  }' and ${tableRefVessel}."createdAt" >='${
    item.fromDate ? item.fromDate.toISOString() : new Date().toISOString()
  }' and ${tableRefVessel}."createdAt" <='${
    item.toDate ? item.toDate.toISOString() : new Date().toISOString()
  }' and ${table}."responsiblePartyInspection" = true and ${tableRefVessel}."vesselId" = '${
    item.vesselId
  }' and ${table}.id = '${item.id}') `;

  const whereForMainAndInternal = ` or (${table}."companyId" = '${item.companyId}' and ${tableRefVessel}."vesselId" = '${item.vesselId}')`;

  return { whereForExternal, whereForMainAndInternal };
}

export function isChangeRiskValue(
  oldObservedRisk: number,
  observedRisk: number,
  isUpdate: boolean,
) {
  const isObservedRiskChange =
    observedRisk !== null && observedRisk !== undefined && oldObservedRisk !== observedRisk;

  return isUpdate && isObservedRiskChange;
}

export function hashAttachmentValues(attachments: string[]) {
  if (!attachments) {
    return [];
  }
  let hashedAttachments = attachments;
  if (attachments.length > 0) {
    hashedAttachments = attachments.map((attachment) => {
      const hashAttachmentId = MyCrypto.encryptFileId(
        attachment,
        APP_CONFIG.ENV.SHARE.SECURE.KEYS.ENCRYPT_KEY_ATTACH,
      );
      return hashAttachmentId;
    });
  }
  return hashedAttachments;
}

export function decryptAttachmentValues(ids: string[]) {
  if (!ids) {
    return [];
  }

  let decryptedAttachments = ids;
  if (ids.length > 0) {
    decryptedAttachments = ids.map((hashedId) => {
      const decryptId = MyCrypto.decryptFieldId(
        hashedId,
        APP_CONFIG.ENV.SHARE.SECURE.KEYS.ENCRYPT_KEY_ATTACH,
      );
      return decryptId;
    });
    return decryptedAttachments;
  }
  return decryptedAttachments;
}

export function hashImage(image: string) {
  if (image) {
    const hashAttachmentId = MyCrypto.encryptFileId(
      image,
      APP_CONFIG.ENV.SHARE.SECURE.KEYS.ENCRYPT_KEY_ATTACH,
    );
    return hashAttachmentId;
  }
  return image;
}

export function decryptImage(id: string) {
  if (id) {
    const decryptId = MyCrypto.decryptFieldId(
      id,
      APP_CONFIG.ENV.SHARE.SECURE.KEYS.ENCRYPT_KEY_ATTACH,
    );
    return decryptId;
  }
  return id;
}

export function replaceStringCustom(str: string, find: string, replace: string) {
  // if headerComment is null then no need to replace
  if (str === null) {
    return '';
  }
  return str.replace(new RegExp(find, 'g'), replace);
}

export function arrayToStringCustom(arr: any, key: string, signParam?: string) {
  if (arr.length < 0) {
    return '';
  }
  const sign = signParam ? signParam : '; ';
  const arrKey = arr.map((item) => item[key]);
  return arrKey.join(sign);
}

export function getFileBufferFromS3Link(s3Link) {
  return new Promise((resolve, reject) => {
    https
      .get(s3Link, (response) => {
        let fileBuffer = Buffer.from('');

        response.on('data', (chunk) => {
          fileBuffer = Buffer.concat([fileBuffer, chunk]);
        });

        response.on('end', () => {
          resolve(fileBuffer);
        });
      })
      .on('error', (err) => {
        console.error(err);
        reject(err);
      });
  });
}

export async function concatenatePDFBuffers(buffer1, buffer2) {
  // Load both buffers into new PDF documents
  const pdfDoc1 = await PDFDocument.load(buffer1);
  const pdfDoc2 = await PDFDocument.load(buffer2);

  // Get the number of pages in each document
  const pdf2Length = pdfDoc2.getPageCount();

  // Copy pages from second PDF into first PDF
  for (let i = 0; i < pdf2Length; i++) {
    const copiedPage = await pdfDoc1.copyPages(pdfDoc2, [i]);
    pdfDoc1.addPage(copiedPage[0]);
  }

  // Save and return the concatenated PDF buffer
  const pdfBytes = await pdfDoc1.save();
  return pdfBytes;
}

export async function changePathFileName(file: Express.Multer.File) {
  const destPath = file.destination;
  const oldFilename = file.filename;

  const oldFilePath = path.join(destPath, oldFilename);
  const newFilePath = path.join(destPath, file.originalname);
  await fs.promises.rename(oldFilePath, newFilePath);

  file.filename = file.originalname;
  file.path = newFilePath;
}

export async function extractZipFile(zipPath: string, destination: string) {
  const MAX_FILES = 10000;
  const MAX_SIZE = 1000000000; // 1 GB
  const THRESHOLD_RATIO = 10;
  let fileCount = 0;
  let totalSize = 0;
  await extract(zipPath, {
    dir: destination,
    onEntry: function (entry, zipFile) {
      fileCount++;
      if (fileCount > MAX_FILES) {
        throw 'Reached max. number of files';
      }

      // The uncompressedSize comes from the zip headers, so it might not be trustworthy.
      // Alternatively, calculate the size from the readStream.
      let entrySize = entry.uncompressedSize;
      totalSize += entrySize;
      if (totalSize > MAX_SIZE) {
        throw 'Reached max. size';
      }

      if (entry.compressedSize > 0) {
        let compressionRatio = entrySize / entry.compressedSize;
        if (compressionRatio > THRESHOLD_RATIO) {
          throw 'Reached max. compression ratio';
        }
      }
    },
  });
}
