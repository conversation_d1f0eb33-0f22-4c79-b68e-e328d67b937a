export * from './audit-checklist.enum';
export * from './role-permission.enum';
export * from './workflow-permission.enum';

export enum Gender {
  MALE = 'male',
  FEMALE = 'female',
}

export enum UserType {
  SUPER_ADMIN = 'super_admin',
  ADMIN = 'admin',
  AUDITOR = 'auditor',
  AUDITEE = 'auditee',
  BPO = 'BPO',
}

export enum ControlType {
  OFFICE = 'office',
  VESSEL = 'vessel',
  OTHERS = 'others',
}

export enum NetworkMode {
  MOBILE_DATA = 'mobile_data',
  WIFI = 'wifi',
  BOTH = 'both',
}

export enum RequestTrialType {
  AUDITOR = 'auditor',
  COMPANY = 'company',
}

export enum ExportType {
  PDF = 'pdf',
  CSV = 'csv',
}

export enum MailTemplate {
  COMPANY_CREATED_BY_SUPER_ADMIN = 'company-created-by-super-admin',
  COMPANY_CREATED_BY_ADMIN = 'company-created-by-admin',
  USER_CREATED = 'user-created',
  USER_UPDATED = 'user-updated',
  FORGOT_PASSWORD = 'forgot-password',
  RESET_PASSWORD = 'reset-password',
  REQUEST_TRIAL = 'request-trial',
  REQUEST_TRIAL_CLIENT = 'request-trial-client',
  CHANGE_RECORD_STATUS = 'change-record-status',
  UPDATE_RECORD_STATUS = 'update-record-status',
  RT_DATA_INSERTION_STATUS = 'rt-data-insertion-status',
  NYK_DATA_INSERTION_STATUS = 'nyk-data-insertion-status',
  PASSWORD_EXPIRATION = 'password-expiration',
  WARNING_EXCEEDING_VESSEL = 'warning-exceeding-vessel',
  UPDATE_ACKNOWLEDGE_REVIEW_SELF_ASSESSMENT = 'update-acknowledge-review-self-assessment',
  SUBMITTED_SELF_ASSESSMENT = 'submitted-self-assessment',
  REVIEWED_SELF_ASSESSMENT = 'reviewed-self-assessment',
  PASSWORD_CHANGE = 'password-change',
  USERS_LIMIT_REACHED = 'users-limit-reached',
  JOBS_LIMIT_REACHED = 'jobs-limit-reached',
  SUBSCRIPTION_END_DATE_REMAINDER = 'subscription-end-date-remainder',
}

export enum StatusCommon {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

export enum RankAndDepartmentType {
  SHIP = 'ship',
  SHORE = 'shore',
}

export enum DataChangeEvent {
  INSERT = 'insert',
  UPDATE = 'update',
  DELETE = 'delete',
  KEEP = 'keep',
}

export enum PortTypeEnum {
  INLAND = 'Inland',
  SEAPORT = 'Seaport',
  DRY_PORT = 'Dry port',
  WARM_WATER_PORT = 'Warm-water port',
}

export enum AuditTypeScope {
  INTERNAL = 'internal',
  EXTERNAL = 'external',
}

export enum DMSModule {
  SMS = 'sms',
  DUE_DILIGENCE = 'due_diligence',
}

export enum ReportTemplateModule {
  INTERNAL_AUDIT_REPORT = 'internal_audit_report',
}

export enum ReportHeaderTopicType {
  HEADER = 'header',
  SUB_HEADER = 'sub_header',
}
export enum ReportHeaderPrintOption {
  ALL = 'All',
  INTERNAL = 'Internal',
  EXTERNAL = 'External',
  NONE = 'None',
}
export enum ReportHeaderType {
  CK_EDITOR = 'CK editor',
  INSPECTION_HISTORY_TABLE = 'Inspection history table',
  INTERNAL_AUDIT_TABLE = 'Internal audit table',
  DYNAMIC = 'Dynamic',
}

export enum DefaultReportHeaderSerialNumber {
  OVERVIEW = '0.1',
  INSPECTION_HISTORY_AND_STATUS = '0.2',
}

export enum ViqTypeEnum {
  VIQ = 'VIQ',
}

export enum ViqVesselTypeEnum {
  LNG = 'LNG',
  LPG = 'LPG',
  PETROLEUM = 'Petroleum',
  CHEMICAL = 'Chemical',
  NON_TANKER = 'Non-Tanker',
}

export enum PotentialRiskEnum {
  VERY_HIGH = 'Very high',
  HIGH = 'High',
  MEDIUM = 'Medium',
  LOW = 'Low',
  VERY_LOW = 'Very low',
}

export enum CriticalityEnum {
  VERY_HIGH = 'Very high',
  HIGH = 'High',
  MEDIUM = 'Medium',
  LOW = 'Low',
  VERY_LOW = 'Very low',
}

export enum MainSubEnum {
  DRAFT = 'Draft',
  PUBLISHED = 'Published',
  CANCELLED = 'Cancelled',
}

//#region Planning and Request
export enum AuditorType {
  INTERNAL = 'internal',
  EXTERNAL = 'external',
}

export enum TypeOfAudit {
  PORT = 'port',
  SAILING = 'sailing',
}

export enum PlanningRequestStatus {
  DRAFT = 'draft',
  SUBMITTED = 'submitted',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  AUDITOR_ACCEPTED = 'auditor_accepted',
  PLANNED_SUCCESSFULLY = 'planned_successfully',
  IN_PROGRESS = 'In-progress',
  COMPLETED = 'Completed',
  CANCELLED = 'cancelled',
  REVIEWED_1 = 'reviewed_1',
  REVIEWED_2 = 'reviewed_2',
  REVIEWED_3 = 'reviewed_3',
  REVIEWED_4 = 'reviewed_4',
  REVIEWED_5 = 'reviewed_5',
}

export enum PRStatusFilter {
  DRAFT = 'draft',
  SUBMITTED = 'submitted',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  AUDITOR_ACCEPTED = 'auditor_accepted',
  PLANNED_SUCCESSFULLY = 'planned_successfully',
  IN_PROGRESS = 'In-progress',
  COMPLETED = 'Completed',
  CANCELLED = 'cancelled',
  REVIEWED = 'reviewed', // OLD STATUS
}
//#endregion Planning and Request

export enum AuditTimeTableStatus {
  DRAFT = 'draft',
  SUBMITTED = 'submitted',
  CLOSE_OUT = 'close_out',
  APPROVED = 'approved',
}
export enum AuditWorkspaceStatus {
  NEW = 'New',
  DRAFT = 'Draft',
  FINAL = 'Final',
}

export enum FillAuditChecklistStatus {
  YET_TO_START = 'Yet To Start',
  IN_PROGRESS = 'In-progress',
  COMPLETED = 'Completed',
}

export enum SwitchWorkSpaceStatus {
  TRANSFER_FROM_APP = 'transfer from app',
}

export enum ReportFindingFormStatus {
  DRAFT = 'Draft',
  SUBMITTED = 'Submitted',
  REVIEWED = 'Reviewed',
  REVIEWED_1 = 'reviewed_1',
  REVIEWED_2 = 'reviewed_2',
  REVIEWED_3 = 'reviewed_3',
  REVIEWED_4 = 'reviewed_4',
  REVIEWED_5 = 'reviewed_5',
  CLOSE_OUT = 'Close out',
  REASSIGNED = 'Reassigned',
  APPROVED = 'approved',
  IN_PROGRESS = 'In-progress',
  Under_Review = 'Under review',
}

export enum InternalAuditReportStatus {
  DRAFT = 'draft',
  SUBMITTED = 'submitted',
  REVIEWED_1 = 'reviewed_1',
  REVIEWED_2 = 'reviewed_2',
  REVIEWED_3 = 'reviewed_3',
  REVIEWED_4 = 'reviewed_4',
  REVIEWED_5 = 'reviewed_5',
  APPROVED = 'approved',
  REASSIGNED = 'reassigned',
  CLOSEOUT = 'closeout',
}

export enum FindingItemWorkflowStatus {
  OPEN = 'Opened',
  CLOSE_OUT = 'Close out',
  REASSIGNED = 'Reassigned',
}

export enum FindingStatus {
  OPEN = 'Opened',
  CLOSE = 'Closed',
}
export enum IARVerificationStatus {
  YET_TO_VERIFIED = 'Yet To Verify',
  PARTIALLY_VERIFIED = 'Partially Verified',
  ALL_VERIFIED = 'All Verified',
}

export enum MobileConfigType {
  SHORE = 'SHORE',
  SHIP = 'SHIP',
}

export enum DataGridTemplateType {
  GLOBAL = 'global',
  TEMPLATE = 'template',
}

export enum DeviceStatus {
  ACTIVE = 'Activate',
  DEACTIVE = 'De-activate',
  PURGE = 'Purge',
}

export enum UserRelationship {
  LEAD_AUDITOR = 'leadAuditor',
  AUDITOR = 'auditor',
  VESSEL_MANAGER = 'vesselManager',
}
export enum PrintOptionType {
  ALL = 'All',
  EXTERNAL = 'External',
  INTERNAL = 'Internal',
}

export enum GlobalStatusEnum {
  OPENING_SCHEDULE = 'Opening schedule',
  DISAPPROVED_REPORT = 'Disapproved report',
  SUBMITTED_REPORT = 'Submitted report/Under 1st approval',
  UNDER_2ND_APPROVAL = 'Under 2nd approval',
  UNDER_3RD_APPROVAL = 'Under 3rd approval',
  UNDER_4TH_APPROVAL = 'Under 4th approval',
  UNDER_5TH_APPROVAL = 'Under 5th approval',
  UNDER_6TH_APPROVAL = 'Under 6th approval',
  APPROVED_REPORT = 'Approved report',
  SENT_CAR_UNDER_CAP_PREPARATION = 'Sent CAR/Under CAP preparation',
  DISAPPROVED_CAP = 'Disapproved CAP/Waiting CAP approval',
  SUBMIT_CAP = 'Submit CAP/Waiting CAP approval',
  APPROVED_CAP_NO_VERIFICATION = 'Approved CAP/No need further verification/Closed',
  WAITING_VERIFICATION = 'Waiting Verification',
  APPROVED_VERIFICATION = 'Approved verification/Closed',
  CANCELLED = 'Cancelled',
}

export enum PlanningRequestWorkingType {
  REMOTE = 'Remote',
  PHYSICAL = 'Physical',
}

export enum FocusRequestAnswerType {
  YES = 'Yes',
  NO = 'No',
}

export enum UpdateMasterDataWhenEnum {
  CREATE = 'Create',
  UPDATE = 'Update',
  DELETE = 'Delete',
}

export enum TemplateTypeEnum {
  PLANNING_AND_REQUEST = 'planing-request-template',
  REPORT_FINDING_FORM = 'ReportFindingForm',
  INTERNAL_AUDIT_REPORT = 'iar-template',
}

export enum SelfAssessmentTypeEnum {
  REVIEW_PREP = 'Review Prep',
  WORKING = 'Working',
  OFFICIAL = 'Official',
}
export enum SelfAssessmentMonthEnum {
  JANUARY = 'Jan',
  FEBURARY = 'Feb',
  MARCH = 'Mar',
  APRIL = 'Apr',
  MAY = 'May',
  JUNE = 'Jun',
  JULY = 'Jul',
  AUGEST = 'Aug',
  SEPTEMBER = 'Sep',
  OCTOBER = 'Oct',
  NOVEMBER = 'Nov',
  DECEMBER = 'Dec',
}
export enum SelfAssessmentStatusEnum {
  OPEN = 'Open',
  CLOSED = 'Closed',
  INACTIVE = 'Inactive',
  INPROGRESS = 'InProgress',
  SUBMITTED = 'Submitted',
  REVIEWED = 'Reviewed',
}

export enum SelfAssessmentTabEnum {
  WORKING = 'Working',
  OFFICIAL = 'Official',
  INACTIVE = 'Inactive',
}

export enum SelfDeclarationCommentType {
  INTERNAL = 'Company internal comment',
  EXTERNAL = 'Company external comment',
  MANAGER = 'Operating manager comment',
}

export enum CarStatusEnum {
  OPEN = 'Open',
  SUBMITTED = 'Submitted',
  CLOSED = 'Closed',
}

export enum PeriodTypeEnum {
  DAY = 'Day',
  WEEK = 'Week',
  MONTH = 'Month',
}

export enum CarVerificationTypeEnum {
  NEXT_INSPECTION = 'NextInspection',
  ADDITIONAL_INSPECTION = 'AdditionalInspection',
  MANUALLY = 'Manually',
}
export enum CarVerificationNeedEnum {
  ON = 'On',
  OFF = 'Off',
}
export enum CarVerificationStatusEnum {
  PENDING = 'Pending',
  HOLDING = 'Holding',
  HOLD = 'Hold',
  VERIFIED_AND_CLOSE = 'VerifiedAndClose',
  OVERRIDING_CLOSURE = 'OverridingClosure',
}

export enum CapStatusEnum {
  DRAFT = 'Draft',
  SUBMITTED = 'Submitted',
  ACCEPTED = 'Accepted',
  DENIED = 'Denied',
  REVIEWED_1 = 'reviewed_1',
  REVIEWED_2 = 'reviewed_2',
  REVIEWED_3 = 'reviewed_3',
  REVIEWED_4 = 'reviewed_4',
  REVIEWED_5 = 'reviewed_5',
}

export enum FollowUpStatusEnum {
  DRAFT = 'Draft',
  IN_PROGRESS = 'InProgress',
  CLOSE_OUT = 'CloseOut',
}
export enum SelfDeclarationStatusEnum {
  PENDING = 'Pending',
  SAVED = 'Saved',
  APPROVED = 'Approved',
  SUBMITTED = 'Submitted',
  REASSIGN = 'Reassigned',
}

export enum StandardMasterListTypeEnum {
  NO_ELEMENT = 'no-element',
  ELEMENT = 'element',
  NO_SELF = 'no-self',
  SELF = 'self',
}

export enum ModuleTypeEnum {
  MODULE = 'Module',
  MENU = 'Menu',
  PAGE = 'Page',
  TAB = 'Tab',
  SUB_TAB = 'Sub-tab',
  SECTION = 'Section',
}

export enum ClassDispensationsEventTypeEnum {
  COC = 'COC',
  DISPENSATION = 'Dispensation',
}

export enum ClassDispensationsStatusEnum {
  OPEN = 'Open',
  CLOSED = 'Closed',
}

export enum SurveyClassInfoTypeEnum {
  CLASS_INFO = 'Class Info',
}

export enum ModuleEnum {
  PLANNING = 'Planning',
  INSPECTION_REPORT = 'Inspection Report',
  INSPECTION_FLOW_UP = 'Inspection Follow Up',
  REPORT_OF_FINDING = 'Report of Finding',
  CAP_SUBMISSION = 'CAP Submission',
  INCIDENT = 'Incident',
}

export enum StatisticPreviousFindingItemsTypeEnum {
  MANUAL = 'Manual',
  AUTOMATION = 'Automation',
}
export enum MailSendStatus {
  DRAFT = 'Draft',
  SENDING = 'Sending',
  SENT = 'Sent',
  FAILED = 'Failed',
}

export enum MaintenancePerformTypeEnum {
  Overdue_Maintenance = 'Overdue Maintenance',
}

export enum OtherTechRecordsPendingActionEnum {
  YES = 'Yes',
  NO = 'No',
}

export enum OtherTechRecordsActionStatusEnum {
  OPEN = 'Open',
  CLOSED = 'Closed',
}

export enum DryDockingEventTypeEnum {
  DRY_DOCKING = 'Dry docking',
}

export enum DryDockingStatus {
  PLANNED = 'Planned',
  IN_DOCK = 'In-Dock',
  COMPLETED = 'Completed',
  CANCELLED = 'Cancelled',
}

export enum LostTimeInjuryEnum {
  YES = 'Yes',
  NO = 'No',
}

export enum OtherSMSRecordsPendingActionEnum {
  YES = 'Yes',
  NO = 'No',
}

export enum OtherSMSRecordsActionStatusEnum {
  OPEN = 'Open',
  CLOSED = 'Closed',
}

export enum PortStateControlDetentionEnum {
  YES = 'Yes',
  No = 'No',
}

export enum PortStateInspectionReportStatusEnum {
  OPEN = 'Open',
  CLOSED = 'Closed',
}

export enum InternalInspectionsStatusEnum {
  OPEN = 'Open',
  CLOSE = 'Close',
}

export enum ExternalInspectionReportStatusEnum {
  OPEN = 'Open',
  CLOSED = 'Closed',
}

export enum LicenseCertificationTypeEnum {
  LICENSE = 'License',
  CERTIFICATION = 'Certification',
}

export enum IncidentInvestigationVesselAcceptableEnum {
  YES = 'Yes',
  NO = 'No',
}

export enum IncidentInvestigationIncidentStatusEnum {
  ACCEPTED = 'Accepted',
  COMPLETED = 'Completed',
  REVIEWED = 'Reviewed',
}

export enum TravelDocumentTypeEnum {
  PASSPORT = 'Passport',
  VISA = 'VISA',
}

export enum VesselScreeningStatusEnum {
  OPEN = 'Open',
  CLEARED = 'Cleared',
  IN_PROGRESS = 'In progress',
  DISAPPROVED = 'Disapproved',
}

export enum VesselScreeningUnitsEnum {
  METRIC_TONNE = 'Metric tonne',
  KILOGRAM = 'Kilogram',
}

export enum VesselScreeningPotentialRiskEnum {
  NEGLIGIBLE = 0,
  LOW = 10,
  MEDIUM = 20,
  HIGH = 30,
}

export enum VesselScreeningObservedRiskEnum {
  NEGLIGIBLE = 0,
  LOW = 10,
  MEDIUM = 20,
  HIGH = 30,
}

export enum VesselScreeningFinalRiskEnum {
  NEGLIGIBLE = 0,
  LOW = 10,
  MEDIUM = 20,
  HIGH = 30,
}

export enum RiskTypeEnum {
  NEGLIGIBLE = 'Negligible',
  LOW = 'Low',
  MEDIUM = 'Medium',
  HIGH = 'High',
}

export enum CauseTypeEnum {
  BASIC = 'Basic',
  IMMEDIATE = 'Immediate',
  CONTROL_ACTION_NEEDS = 'Control Action Needs',
  TYPE_OF_LOSS = 'Type of Loss',
}

export enum VesselScreeningFilterRiskEnum {
  POTENTIAL = 'potential',
  OBSERVED = 'observed',
  FINAL = 'final',
}

export enum IncidentInvestigationReviewStatus {
  PENDING = 'Pending',
  IN_PROGRESS = 'In progress',
  CLEARED = 'Cleared',
}

export enum PlanningType {
  ALL = 'All',
  ASSIGNED = 'Assigned',
  UNASSIGNED = 'Unassigned',
}

export enum PlanningTab {
  PLANNING = 'Planning',
  COMPLETED = 'Completed',
  UNPLANNED = 'YetToPlan',
  INSPECTOR_SCHEDULER = 'Inspector Scheduler',
  CANCELLED = 'Cancelled',
}

export enum SearchAvailabilityEnum {
  ALL = 'All',
  UNAVAILABLE = 'Unavailable',
  PARTIALLY = 'Partially',
  AVAILABLE = 'Available',
}

export enum ServiceEnum {
  INSPECTION = 'Inspection',
  QUALITY_ASSURANCE = 'Quality assurance',
}

export enum IndustryEnum {
  MARITIME = 'Maritime',
}

export enum IndustryCodeEnum {
  MCO001 = 'MCO001',
  MCO002 = 'MCO002',
  MCO003 = 'MCO003',
  MCO004 = 'MCO004',
  MCO005 = 'MCO005',
  MCO006 = 'MCO006',
  MCO007 = 'MCO007',
  MCO000 = 'MCO000',
}

export enum CompanyTypeEnum {
  SHIP_MAGEMENT = 'Ship management (DOC holder)',
  SHIP_OWNER = 'Ship Owner',
  CHARTERER = 'Charterer',
  PILOT_SERVICES = 'Pilot Services',
  TERMINAL = 'Terminal',
  INSPECTION_SERVICES = 'Inspection Services',
  SERVICE_PROVIDER = 'Service Provider',
  OTHERS = 'Others',
}

export enum ActorsEnum {
  CONSUMER = 'Consumer',
  PROVIDER = 'Provider',
  MAIN = 'Main',
  EXTERNAL = 'External',
}

export enum ApplyModule {
  ALL = 'All',
  INSPECTION = 'Inspection',
  QA = 'QA',
}

export enum CompanyLevelEnum {
  MAIN_COMPANY = 'Main Company',
  INTERNAL_COMPANY = 'Internal Company',
  EXTERNAL_COMPANY = 'External Company',
}

export enum Memorandum {
  YES = 'Yes',
  NO = 'No',
}

export enum PushTypeEnum {
  UPDATE_RECORD = 'Update record',
  CHANGE_RECORD_TYPE = 'Change record type',
  CHANGE_RECORD_STATUS = 'Change record status',
  ASSIGN_USER = 'Assign user',
  ALL_CAP_SUBMITTED = 'All cap submitted',
}

export enum EmailTypeEnum {
  COMPANY_CREATED = 'Company created',
  USER_CREATED = 'User created',
  USER_UPDATED = 'User updated',
  RECOVER_PASSWORD = 'Recover password',
  FORGOT_PASSWORD = 'Forgot password',
  REQUEST_TRIAL = 'Request trial',
  CREATE_PLANNING_REQUEST = 'Create planning request',
  UPDATE_PLANNING_REQUEST = 'Update planning request',
  UPDATE_RECORD = 'Update record',
  RT_DATA_INSERTION_STATUS = 'RT Data insertion status',
  NYK_DATA_INSERTION_STATUS = 'NYK Data insertion status',
  UPDATE_RECORD_STATUS = 'Update record status',
  CHANGE_RECORD_STATUS = 'Change record status',
  ASSIGN_USER = 'Assign user',
  PASSWORD_EXPIRATION = 'Password expiration',
  PASSWORD_CHANGE = 'Password change',
  USERS_LIMIT_REACHED = 'Users limit reached',
  JOBS_LIMIT_REACHED = 'Jobs limit reached',
  SUBSCRIPTION_END_DATE_REMAINDER = 'Subscriptoin end date remainder',
  INCIDENT_INVESTIGATION = 'Incident investigation',
  UPDATE_INCIDENT_INVESTIGATION = 'Update incident investigation',
}

export enum ModulePathEnum {
  // Inspection
  PLANNING_AND_REQUEST = 'planning-and-request-management',
  CHECKLIST_TEMPLATE_MASTER = 'audit-checklist-management',
  INSPECTION_MAPPING = 'inspection-mapping',
  AUDIT_TIMETABLE = 'audit-time-table-management',
  AUDIT_INSPECTION_WORKSPACE = 'audit-inspection-workspace',
  REPORT_OF_FINDING = 'report-of-finding-management',
  INTERNAL_AUDIT_REPORT = 'internal-audit-report',
  // QA
  SELF_ASSESSMENT = 'self-assessment',
  SAIL_GENERAL_REPORT = 'sail-general-report', // need to add tab and sub-tab query
  VESSEL_SCREENING = 'vessel-screening',
  INCIDENTS = 'incidents',
}

export enum ROLE_PILOT {
  PILOT = 'Pilot',
  PILOT_TERMINAL = 'Pilot / Terminal',
}

export enum ROLE_NAME_FIXED {
  OPERATOR_DOC_HOLDER = 'Operator / DOC Holder',
  VETTING_MANAGER = 'Vetting Manager',
  INSPECTOR = 'Inspector',
  INTERNAL_INSPECTOR = 'Inspector (Internal)',
  EXTERNAL_INSPECTOR = 'Inspector (External)',
}

export enum CONVERT_STATUS {
  REASSIGNED = 'Reassigned',
}

export enum PilotTerminalFeedbackStatusEnum {
  DRAFT = 'Draft',
  SUBMITTED = 'Submitted',
}

export enum VesselCompanyFeedbackStatusEnum {
  DRAFT = 'Draft',
  SUBMITTED = 'Submitted',
}

export enum ModuleEnum {
  CAR = 'Car',
  CAP = 'Cap',
  FINDING_ITEM = 'Finding Item',
}

export enum ActionValueChangeEnum {
  CREATE = 'Create',
  UPDATE = 'Update',
  REVIEW = 'Review',
  SUBMIT = 'Submit',
  SAVE = 'Save',
  ACCEPT = 'Accept',
  DENY = 'Deny',
}

export enum COMPANY_TYPE_NAME_FIXED {
  INSPECTION_SERVICES = 'Inspection Services',
  SHIP_MAGEMENT = 'Ship management (DOC holder)',
  PILOT_SERVICES = 'Pilot Services',
}

export enum ModuleConfigurationEnum {
  //Common
  DIVISION = 'Division',
  INSPECTION_TYPE = 'Inspection Type',
  LOCATION = 'Location',
  MAIN_CATEGORY = 'Main Category',
  //Inspection
  CHEMICAL_DISTRIBUTION_INSTITUTE = 'Chemical Distribution Institute',
  NATURE_OF_FINDING = 'Nature Of Finding',
  FOCUS_REQUEST = 'Focus Request',
  RANK = 'Rank',
  SECOND_CATEGORY = 'Second Category',
  THIRD_CATEGORY = 'Third Category',
  TOPIC = 'Topic',
  CHARTER_OWNER = 'Charter Owner',
  VALUE_MANAGEMENT = 'Value management',
  REPEATED_FINDING_CALCULATION = 'Repeated Finding Calculation',
  FLEET = 'Fleet',
  //Quality Assurance
  CARGO = 'Cargo',
  CARGO_TYPE = 'Cargo Type',
  INCIDENT_TYPE = 'Incident Type',
  SUB_INCIDENT_TYPE = 'Sub Incident Type',
  INJURY_BODY = 'Injury Body',
  PSC_ACTION = 'PSC Action',
  PSC_DEFICIENCY = 'PSC Deficiency',
  RISK_FACTOR = 'Risk Factor',
  TRANSFER_TYPE = 'Transfer Type',
  VESSEL_OWNER_BUSINESS = 'Vessel Owner Business',
  EVENT_TYPE = 'Event Type',
  INJURY_MASTER = 'Injury Master',
  TECH_ISSUE_NOTE = 'Tech Issue Note',
  CATEGORIZATION_MASTER = 'Categorization Master',
  VOYAGE_TYPE = 'Voyage Type',
}

export enum ModuleColumn {
  CODE = 'Code',
  NAME = 'Name',
  STATUS = 'Status',
  DESCRIPTION = 'Description',
  SCOPE = 'Scope',
  ACRONYM = 'Acronym',
  MODULE = 'Module',
  LTI = 'LTI',
  MODULES = 'Modules',
  VESSEL_TYPES = 'Vessel Types',
  PORT_MASTER = 'Port Master',
  NUMBER_DEPENDENTS = 'Number Dependents',
  TYPE = 'Type',
  QUESTION = 'Question',
  NUMBER = 'Number',
  TIMES_OF_REPEATING = 'Times Of Repeating',
  COEFFICIENT = 'Co-efficient',
  ENTITY_TYPE = 'Entity Type',
  SECTION_TYPE = 'Section Type', // tech issue note
  INCIDENT_NAME = 'Incident Name',
}

export enum HomepageBlockEnum {
  OPEN_ACTIONABLE = 'openActionable',
  OVERDUE_ACTIONABLE = 'overdueActionable',
  NEW_YET_TO_START = 'newYetToStart',
  OPEN_RECORDS = 'openRecords',
  OVERDUE = 'overdue',
  VESSEL_SCREENING = 'vesselScreening',
}

export enum HomepageModuleEnum {
  PLANNING_REQUEST = 'Planning request',
  INSPECTION = 'Inspection workspace',
  FOLLOW_UP = 'Follow up',
  SELF_ASSESSMENT = 'Self assessment',
  INCIDENTS = 'Incidents',
}

export enum WidgetModuleEnum {
  INSPECTION_DASHBOARD = 'Inspection Dashboard',
  MAIN_DASHBOARD = 'Main Dashboard',
  HOME_PAGE = 'Home Page',
  QUALITY_ASSURANCE_DASHBOARD = 'Quality Assurance Dashboard',
}

export enum ColorKeykEnum {
  OPEN_ACTIONABLE = 'openActionable',
  OVERDUE_ACTIONABLE = 'overdueActionable',
  NEW_YET_TO_START = 'newYetToStart',
  OPEN_RECORDS = 'openRecords',
  OVERDUE = 'overdue',
}

export enum DeviceUploadEnum {
  WEB = 'web',
  MOBILE = 'mobile',
}

export enum WorkingTypeEnum {
  REMOTE = 'Remote',
  PHYSICAL = 'Physical',
}

export enum FocusRequestEntityEnum {
  VESSEL = 'Vessel',
  OFFICE = 'Office',
  ALL = 'All',
}

export enum DefaultDLs {
  I_NAUTIX = '<EMAIL>',
}

export enum ApiOrManual {
  API = 'API',
  MANUAL = 'MANUAL',
}

export enum InspectionExportPdfHeaderEnum {
  Inspectionhistoryandstatus = 'Inspection history and status',
}

export enum FIXED_PACKAGE_ROLE_NAME {
  INAUTIX_INSPECTIONS_PACKAGE_ROLES = 'Inautix Inspections Package Roles',
  INAUTIX_QA_PACKAGE_ROLES = 'Inautix QA Package Roles',
  INAUTIX_INSPECTIONS_AND_QA_PACKAGE_ROLES = 'Inautix Inspections And QA Package Roles',
}

export enum FeedbackTypeEnum {
  VESSEL_FEEDBACK = 'Vessel Feedback',
  COMPANY_FEEDBACK = 'Company Feedback',
}

export enum StandardMaterScoringMethod {
  DEFAULT = 'default',
  DBMS = 'dbms',
}

export enum IncidentOriginEnum {
  MANUAL = 'Manual',
  RIGHT_SHIP = 'Right ship',
  OTHER_SOURCE = 'Other source',
}
